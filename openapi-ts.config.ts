import { loadEnvFile } from "process"
import { defineConfig } from "@hey-api/openapi-ts"

if (process.env.NODE_ENV === "development")
  loadEnvFile(`${process.cwd()}/.env.development`)

const swaggerApi = process.env.ONBOARDING_SWAGGER_API_URL!

export default defineConfig({
  input: swaggerApi,
  output: "src/client/onboarding",
  plugins: [
    {
      name: "@hey-api/client-axios",
      runtimeConfigPath: "./src/client/hey-api.ts",
    },
  ],
})
