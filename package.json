{"name": "argentex-portal-react", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "tsc -b && vite build", "copy-files": "cp ./staticwebapp.config.json ./dist/", "dev": "vite", "gen:onboarding-api": "NODE_ENV=development openapi-ts", "lint": "eslint .", "lint:prettier": "prettier --check .", "preview": "vite preview", "fix:prettier": "prettier --write .", "test": "vitest", "test:ci": "vitest --silent", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest watch", "prepare": "husky"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@hey-api/client-axios": "^0.6.1", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@sentry/react": "^9.4.0", "@sentry/vite-plugin": "^3.2.2", "@tanstack/react-form": "^0.41.3", "@tanstack/react-query": "^5.62.2", "@tanstack/react-query-devtools": "^5.69.0", "@tanstack/react-router": "^1.82.1", "@tanstack/react-store": "^0.7.0", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "lodash-es": "^4.17.21", "lucide-react": "^0.460.0", "motion": "^11.12.0", "next-themes": "^0.4.3", "react": "^18.3.1", "react-day-picker": "9.7.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-loqate": "^3.0.1", "react-pdf": "^9.2.1", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.25.1", "@hey-api/openapi-ts": "^0.64.5", "@iconify/react": "^5.0.2", "@nabla/vite-plugin-eslint": "^2.0.5", "@tanstack/eslint-plugin-query": "^5.60.1", "@tanstack/router-devtools": "^1.82.1", "@tanstack/router-plugin": "^1.81.9", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.9.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "^3.0.7", "@vitest/ui": "^3.0.7", "autoprefixer": "^10.4.20", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-perfectionist": "^4.12.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^15.5.1", "postcss": "^8.4.49", "prettier-plugin-tailwindcss": "^0.6.11", "shadcn-ui": "^0.9.3", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.31.0", "vite": "^6.3.3", "vite-plugin-eslint2": "^5.0.3", "vitest": "^3.0.7"}, "packageManager": "yarn@1.22.22"}