import { defineConfig } from "vite"
import path from "path"
import react from "@vitejs/plugin-react-swc"
import { TanStackRouterVite } from "@tanstack/router-plugin/vite"
import { sentryVitePlugin } from "@sentry/vite-plugin"
import eslint from "@nabla/vite-plugin-eslint"

const PORT = 4200

// https://vite.dev/config/
export default defineConfig({
  build: {
    outDir: "dist",
    sourcemap: true, // This is crucial for the source maps to be generated
    rollupOptions: {
      output: {
        manualChunks: undefined, // Disable code splitting
        inlineDynamicImports: true, // Inline all dynamic imports
      },
    },
    target: "esnext", // Use modern target to avoid unnecessary polyfills
  },
  plugins: [
    TanStackRouterVite(),
    react(),
    eslint(),
    sentryVitePlugin({
      authToken: process.env.SENTRY_AUTH_TOKEN, // Auth token from environment
      org: "argentex-0i", // Your Sentry organization slug
      project: "agp-client-portal", // Your Sentry project name
    }),
  ],
  preview: {
    open: true,
    port: PORT,
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: PORT,
  },
})
