import { describe, it, vi, expect, beforeEach } from "vitest"
import { render, screen, waitFor } from "@testing-library/react"
import { useAuth0 } from "@auth0/auth0-react"

import * as Root from "@/routes/__root"

const ACCESS_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpX"

vi.mock("@auth0/auth0-react", () => ({
  useAuth0: vi.fn(),
}))

vi.mock("@/components/base/loading-spinner/LoadingSpinner", () => ({
  LoadingSpinner: vi.fn(() => <div>Loading spinner</div>),
}))

vi.mock("@tanstack/react-router", () => ({
  createRootRouteWithContext: vi.fn(() =>
    vi.fn(() => ({ component: vi.fn() })),
  ),
  // createRootRoute: vi.fn(({ component }) => ({ component })),
  Outlet: () => <div data-testid="outlet">Outlet</div>,
}))

vi.mock("@/components/ui/sonner", () => ({
  Toaster: () => <div>Toaster</div>,
}))

describe("Root", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, "log").mockImplementation(() => {})
  })

  it("should get access token silently", async () => {
    ;(useAuth0 as any).mockReturnValue({
      getAccessTokenSilently: vi.fn().mockResolvedValue(ACCESS_TOKEN),
    })

    const { component: RouteComponent } = Root.Route as any
    render(<RouteComponent />)

    await waitFor(async () => {
      const token = await useAuth0().getAccessTokenSilently()
      expect(token).toEqual(ACCESS_TOKEN)
    })
  })

  it.skip("should render outlet", async () => {
    ;(useAuth0 as any).mockReturnValue({
      getAccessTokenSilently: vi.fn().mockResolvedValue(ACCESS_TOKEN),
    })

    const { component: RouteComponent } = Root.Route as any
    render(<RouteComponent />)

    await waitFor(() => expect(screen.getByText("Outlet")).toBeInTheDocument())
  })

  it.skip("should render loading spinner", async () => {
    ;(useAuth0 as any).mockReturnValue({
      getAccessTokenSilently: vi.fn().mockResolvedValueOnce(ACCESS_TOKEN),
      isLoading: true,
      isAuthenticated: true,
    })

    const { component: RouteComponent } = Root.Route as any
    render(<RouteComponent />)

    await waitFor(() =>
      expect(screen.getByText("Loading spinner")).toBeInTheDocument(),
    )
  })
})
