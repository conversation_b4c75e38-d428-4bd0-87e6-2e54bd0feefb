import { describe, it, vi, expect } from "vitest"
import { render, screen } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/accounts/index.lazy"
import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useAccountsListQuery } from "@/data/account/account.query"

vi.mock("@/components/pages/accounts/Page", () => ({
  AccountsPage: vi.fn(() => <div>Mocked AccountsPage</div>),
}))

vi.mock("@/components/base/loading-spinner/LoadingSpinner", () => ({
  LoadingSpinner: vi.fn(() => <div>Mocked LoadingSpinner</div>),
}))

vi.mock("@tanstack/react-router", () => {
  const originalModule = vi.importActual("@tanstack/react-router")
  return {
    ...originalModule,
    createLazyFileRoute: vi.fn(() => (config: any) => ({
      ...config,
      useParams: vi.fn(() => ({ entityId: "test-entity-id" })),
    })),
    useParams: vi.fn(() => ({ entityId: "test-entity-id" })),
  }
})

vi.mock("@/data/account/account.query", () => ({
  useAccountsListQuery: vi.fn(),
}))

vi.mock("@/hooks/use-navigation-icon", () => ({
  useNavigationIcon: vi.fn(),
}))

const mockAccount = {
  id: "test-account-1",
  name: "Test Account",
  accountName: "Test Account Name",
  balances: [],
  virtualIban: "GB123456789",
  totalBalance: 1000,
  category: "Test Category",
  iban: "GB123456789",
  entityId: "entity-1",
  bankName: "Test Bank",
  bankAddress: "Test Address",
  clientAccountId: "client-account-1",
  clientId: "client-1",
  totalCurrency: "USD" as const,
}

describe("RouteComponent", () => {
  it("renders the LoadingSpinner when loading", () => {
    vi.mocked(useAccountsListQuery).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
      error: null,
      isPending: true,
      isLoadingError: false,
      isSuccess: false,
      isFetched: false,
      isFetchedAfterMount: false,
      isFetching: true,
      isRefetching: false,
      isStale: false,
      refetch: vi.fn(),
      status: "pending",
      fetchStatus: "fetching",
      isRefetchError: false,
      dataUpdatedAt: 0,
      errorUpdatedAt: 0,
      failureCount: 0,
      isPaused: false,
      failureReason: null,
      errorUpdateCount: 0,
      isInitialLoading: true,
      isPlaceholderData: false,
      promise: Promise.resolve([mockAccount]),
    })

    const { component: RouteComponent } = Route as any

    render(<RouteComponent />)

    expect(screen.getByText("Mocked LoadingSpinner")).toBeInTheDocument()

    expect(useNavigationIcon).toHaveBeenCalledWith("/accounts")

    expect(screen.queryByText("Mocked AccountsPage")).not.toBeInTheDocument()
  })

  it("renders the AccountsPage when data is loaded", () => {
    vi.mocked(useAccountsListQuery).mockReturnValue({
      data: [mockAccount],
      isLoading: false,
      isError: false,
      error: null,
      failureReason: null,
      errorUpdateCount: 0,
      isInitialLoading: false,
      isPlaceholderData: false,
      promise: Promise.resolve([mockAccount]),
      isPending: false,
      isLoadingError: false,
      isSuccess: true,
      isFetched: true,
      isFetchedAfterMount: true,
      isFetching: false,
      isRefetching: false,
      isStale: false,
      refetch: vi.fn(),
      status: "success",
      fetchStatus: "idle",
      isRefetchError: false,
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: Date.now(),
      failureCount: 0,
      isPaused: false,
    })

    const { component: RouteComponent } = Route as any

    render(<RouteComponent />)

    expect(screen.getByText("Mocked AccountsPage")).toBeInTheDocument()

    expect(useNavigationIcon).toHaveBeenCalledWith("/accounts")

    expect(screen.queryByText("Mocked LoadingSpinner")).not.toBeInTheDocument()
  })
})
