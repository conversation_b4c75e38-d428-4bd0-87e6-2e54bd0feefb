import { describe, it, vi, expect, beforeEach } from "vitest"
import { render, screen } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/user-admin"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

vi.mock("@/hooks/use-navigation-icon", () => ({
  useNavigationIcon: vi.fn(),
}))

vi.mock("@tanstack/react-router", () => ({
  createFileRoute: vi.fn(() => (config: any) => ({
    ...config,
    useParams: vi.fn(() => ({ entityId: "test-entity-id" })),
  })),
  Outlet: vi.fn(() => <div data-testid="outlet">Outlet Content</div>),
}))

vi.mock("@/components/ui/tabs", () => ({
  Tabs: ({ children, defaultValue }: any) => (
    <div data-default-value={defaultValue} data-testid="tabs">
      {children}
    </div>
  ),
  TabsList: ({ children }: any) => (
    <div data-testid="tabs-list">{children}</div>
  ),
  TabsTrigger: ({ children, value }: any) => (
    <button
      data-state={value === "users" ? "active" : "inactive"}
      data-value={value}
      role="tab"
    >
      {children}
    </button>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-testid={`tabs-content-${value}`}>{children}</div>
  ),
}))

vi.mock("@/components/ui/table", () => ({
  Table: ({ children }: any) => <table>{children}</table>,
  TableHeader: ({ children }: any) => <thead>{children}</thead>,
  TableBody: ({ children }: any) => <tbody>{children}</tbody>,
  TableRow: ({ children }: any) => <tr>{children}</tr>,
  TableHead: ({ children }: any) => <th>{children}</th>,
  TableCell: ({ children }: any) => <td>{children}</td>,
}))

vi.mock("@/components/ui/badge", () => ({
  Badge: ({ children, className }: any) => (
    <span className={className} data-testid="badge">
      {children}
    </span>
  ),
}))

vi.mock("lucide-react", () => ({
  MoreHorizontal: () => <span data-testid="more-horizontal-icon" />,
}))

vi.mock("@/components/ui/input", () => ({
  Input: (props: any) => <input {...props} />,
}))

vi.mock("@/components/ui/button", () => ({
  Button: ({ children, className, variant }: any) => (
    <button className={className} data-variant={variant}>
      {children}
    </button>
  ),
}))

describe("User admin Route Component", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render the Outlet component", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(screen.getByTestId("outlet")).toBeInTheDocument()
    expect(screen.getByText("Outlet Content")).toBeInTheDocument()
  })

  it("should render the admin page with correct navigation", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useBreadcrumb).toHaveBeenCalledWith("user-admin", {
      path: "user-admin",
      label: "User admin",
    })
  })

  it("should render the admin page with correct breadcrumb correctly", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useBreadcrumb).toHaveBeenCalledWith("user-admin", {
      path: "user-admin",
      label: "User admin",
    })
  })
})
