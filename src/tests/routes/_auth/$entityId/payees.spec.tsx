import { describe, it, vi, expect } from "vitest"
import { render, screen } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/payees"
import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

vi.mock("@/hooks/use-navigation-icon", () => ({
  useNavigationIcon: vi.fn(),
}))

vi.mock("@tanstack/react-router", () => ({
  createFileRoute: vi.fn(() => (config: any) => ({
    ...config,
    useParams: vi.fn(() => ({ entityId: "test-entity-id" })),
  })),
  Outlet: vi.fn(() => <div data-testid="outlet">Outlet Content</div>),
}))

describe("Payees Route", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render the Outlet component", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(screen.getByTestId("outlet")).toBeInTheDocument()
    expect(screen.getByText("Outlet Content")).toBeInTheDocument()
  })

  it("should set up navigation icon correctly", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useNavigationIcon).toHaveBeenCalledWith("/payees")
  })

  it("should set up breadcrumb correctly", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useBreadcrumb).toHaveBeenCalledWith("payees", {
      path: "payees",
      label: "Payees",
    })
  })
})
