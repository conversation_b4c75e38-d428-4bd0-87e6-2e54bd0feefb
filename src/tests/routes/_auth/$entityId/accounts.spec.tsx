import { describe, it, vi, expect } from "vitest"
import { render } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/accounts"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

vi.mock("@tanstack/react-router", () => ({
  createFileRoute: vi.fn(() => (config: any) => ({
    ...config,
    useParams: vi.fn(() => ({ entityId: "test-entity-id" })),
  })),
  Outlet: vi.fn(() => <div data-testid="outlet">Outlet Content</div>),
}))

describe("RouteComponent", () => {
  it("renders the Outlet component", () => {
    const { component: RouteComponent } = Route as any
    const { getByTestId } = render(<RouteComponent />)
    expect(getByTestId("outlet")).toBeInTheDocument()
  })

  it("sets up the breadcrumb correctly", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useBreadcrumb).toHaveBeenCalledWith("accounts", {
      path: "accounts",
      label: "Accounts",
    })
  })
})
