import { describe, it, vi, expect } from "vitest"
import { render } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/payees/index.lazy"
import { PayeePage } from "@/components/pages/payees/Page"

vi.mock("@/components/pages/payees/Page", () => ({
  PayeePage: vi.fn(() => <div>Mocked PayeePage</div>),
}))

vi.mock("@tanstack/react-router", () => ({
  createLazyFileRoute: vi.fn(() => (config: any) => ({
    ...config,
  })),
}))

describe("RouteComponent", () => {
  it("renders the PayeePage with the correct entityId", () => {
    const { component: RouteComponent } = Route as any
    const { getByText } = render(<RouteComponent />)

    expect(getByText("Mocked PayeePage")).toBeInTheDocument()

    expect(PayeePage).toHaveBeenCalledWith({}, {})
  })
})
