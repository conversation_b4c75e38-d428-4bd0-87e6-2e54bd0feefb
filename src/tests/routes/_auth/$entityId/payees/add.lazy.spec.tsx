import { describe, it, vi, expect } from "vitest"
import { render } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/payees/add.lazy" // Update this path
import { AddPayeePage } from "@/components/pages/add-payee/AddPayeePage"

vi.mock("@/components/pages/add-payee/AddPayeePage", () => ({
  AddPayeePage: vi.fn(() => <div>Mocked AddPayeePage</div>),
}))

vi.mock("@tanstack/react-router", () => ({
  createLazyFileRoute: vi.fn(() => (config: any) => ({
    ...config,
    useParams: vi.fn(() => ({
      entityId: "test-entity-id",
    })),
  })),
}))

describe("RouteComponent", () => {
  it("renders the AddPayeePage with the correct entityId", () => {
    const { component: RouteComponent } = Route as any
    const { getByText } = render(<RouteComponent />)

    expect(getByText("Mocked AddPayeePage")).toBeInTheDocument()

    expect(AddPayeePage).toHaveBeenCalledWith(
      {
        entityId: "test-entity-id",
      },
      {},
    )
  })
})
