import { describe, it, vi, expect } from "vitest"
import { render, screen } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/index.lazy"
import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { DashboardPage } from "@/components/pages/dashboard/Page"

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

vi.mock("@/hooks/use-navigation-icon", () => ({
  useNavigationIcon: vi.fn(),
}))

vi.mock("@/components/pages/dashboard/Page", () => ({
  DashboardPage: vi.fn(() => <div>Mocked DashboardPage</div>),
}))

vi.mock("@tanstack/react-router", () => ({
  createLazyFileRoute: vi.fn(() => (config: any) => ({
    ...config,
    useParams: vi.fn(() => ({ entityId: "test-entity-id" })),
  })),
  useLoaderData: vi.fn(() => ({
    entity: { id: "test-entity-id", name: "Test Entity" },
  })),
}))

describe("EntityDashboard Route", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render the dashboard page", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(screen.getByText("Mocked DashboardPage")).toBeInTheDocument()
  })

  it("should set up navigation icon correctly", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useNavigationIcon).toHaveBeenCalledWith("")
  })

  it("should set up breadcrumb correctly", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useBreadcrumb).toHaveBeenCalledWith("dashboard", {
      path: "",
      label: "Dashboard",
    })
  })

  it("should pass the correct props to DashboardPage", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(DashboardPage).toHaveBeenCalledWith({}, {})
  })
})
