import { describe, it, vi, expect } from "vitest"
import { render } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/payments/send.lazy"
import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { SendPaymentsPage } from "@/components/pages/send-payments/Page"

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

vi.mock("@/hooks/use-navigation-icon", () => ({
  useNavigationIcon: vi.fn(),
}))

vi.mock("@/components/pages/send-payments/Page", () => ({
  SendPaymentsPage: vi.fn(() => <div>Mocked SendPaymentsPage</div>),
}))

vi.mock("@tanstack/react-router", () => ({
  createLazyFileRoute: vi.fn(() => (config: any) => ({
    ...config,
    useParams: vi.fn(() => ({ entityId: "test-entity-id" })),
  })),
}))

describe("Route", () => {
  it("renders the SendPaymentsPage with the correct entityId", () => {
    const { component: RouteComponent } = Route as any
    const { getByText } = render(<RouteComponent />)

    expect(getByText("Mocked SendPaymentsPage")).toBeInTheDocument()

    expect(SendPaymentsPage).toHaveBeenCalledWith(
      { entityId: "test-entity-id" },
      {},
    )
  })

  it("calls useNavigationIcon with the correct path", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useNavigationIcon).toHaveBeenCalledWith("/payments")
  })

  it("calls useBreadcrumb with the correct path and label", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useBreadcrumb).toHaveBeenCalledWith("create-payment", {
      path: "send",
      label: "Send Payments",
    })
  })
})
