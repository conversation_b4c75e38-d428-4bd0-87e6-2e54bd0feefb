import { describe, it, vi, expect } from "vitest"
import { render } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/payments/$paymentId.lazy"
import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { ApprovePaymentsPage } from "@/components/pages/approve-payments/Page"

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

vi.mock("@/hooks/use-navigation-icon", () => ({
  useNavigationIcon: vi.fn(),
}))

vi.mock("@/components/pages/approve-payments/Page", () => ({
  ApprovePaymentsPage: vi.fn(() => <div>Mocked ApprovePaymentsPage</div>),
}))

vi.mock("@tanstack/react-router", () => ({
  createLazyFileRoute: vi.fn(() => (config: any) => ({
    ...config,
    useParams: vi.fn(() => ({
      entityId: "test-entity-id",
      paymentId: "test-payment-id",
    })),
  })),
}))

describe("RouteComponent", () => {
  it("renders the ApprovePaymentsPage with the correct entityId and paymentId", () => {
    const { component: RouteComponent } = Route as any
    const { getByText } = render(<RouteComponent />)

    expect(getByText("Mocked ApprovePaymentsPage")).toBeInTheDocument()

    expect(ApprovePaymentsPage).toHaveBeenCalledWith(
      {
        entityId: "test-entity-id",
        paymentId: "test-payment-id",
      },
      {},
    )
  })

  it("calls useNavigationIcon with the correct path", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useNavigationIcon).toHaveBeenCalledWith("/payments")
  })

  it("calls useBreadcrumb with the correct path and label", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(useBreadcrumb).toHaveBeenCalledWith("payment-details", {
      path: "send",
      label: "Approve payment",
    })
  })
})
