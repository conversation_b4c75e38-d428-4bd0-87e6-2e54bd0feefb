import { describe, it, vi, expect } from "vitest"
import { render, screen } from "@testing-library/react"

import { Route } from "@/routes/_auth/$entityId/user-admin/add-new-user.lazy"

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

vi.mock("@tanstack/react-router", () => ({
  createLazyFileRoute: vi.fn(() => (config: any) => ({
    ...config,
  })),
  useNavigate: () => vi.fn(),
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "1",
        legalEntity: {
          name: "Argentex",
        },
      },
    })),
  })),
}))

describe("RouteComponent", () => {
  it("renders the AddNewPayeePage with the correct entityId", () => {
    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(screen.getByText("Enter user details")).toBeInTheDocument()
    expect(screen.getByText("First name")).toBeInTheDocument()
    expect(screen.getByText("Last name")).toBeInTheDocument()
    expect(screen.getByText("Date of birth")).toBeInTheDocument()
    expect(screen.getByText("Email")).toBeInTheDocument()
    expect(
      screen.getByText("What role will this user have within Argentex"),
    ).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Add user" })).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument()
  })
})
