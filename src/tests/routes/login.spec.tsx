import { describe, it, vi, expect } from "vitest"
import { render } from "@testing-library/react"
import { useAuth0 } from "@auth0/auth0-react"

import { Route } from "@/routes/login"

vi.mock("@auth0/auth0-react", () => ({
  useAuth0: vi.fn(),
}))

vi.mock("@tanstack/react-router", () => ({
  createFileRoute: vi.fn(() => (config: any) => ({
    ...config,
  })),
  Navigate: () => <div>Navigate to /</div>,
  useRouter: vi.fn(),
  useSearch: vi.fn(() => ({
    redirect: "/",
  })),
}))

describe("Login Route", () => {
  const mockLoginWithRedirect = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should redirect to home when already authenticated", () => {
    ;(useAuth0 as any).mockReturnValue({
      isAuthenticated: true,
      loginWithRedirect: mockLoginWithRedirect,
    })

    const { component: RouteComponent } = Route as any
    render(<RouteComponent />)

    expect(mockLoginWithRedirect).not.toHaveBeenCalled()
  })
})
