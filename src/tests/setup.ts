import "@testing-library/jest-dom"
import { vi, beforeAll, expect } from "vitest"
import { cleanup } from "@testing-library/react"
import * as matchers from "@testing-library/jest-dom/matchers"

expect.extend(matchers)

Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

const mockIntersectionObserver = vi.fn()

mockIntersectionObserver.mockReturnValue({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null,
})

window.IntersectionObserver = mockIntersectionObserver

vi.mock("react-dom/client", async () => {
  const createRoot = vi.fn((container) => ({
    render: vi.fn(),
    unmount: vi.fn(),
  }))
  return {
    createRoot,
    default: {
      createRoot,
    },
  }
})

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

beforeAll(() => {
  const root = document.createElement("div")
  root.id = "root"
  document.body.appendChild(root)

  // @ts-ignore
  window.ResizeObserver = ResizeObserver
})

afterEach(() => {
  cleanup()
})
