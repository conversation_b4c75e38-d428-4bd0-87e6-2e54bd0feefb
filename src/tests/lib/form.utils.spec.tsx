import { describe, it, expect, vi } from "vitest"
import { ReactFormExtendedApi, FieldMeta } from "@tanstack/react-form"

import {
  required,
  validateIBan,
  resetFieldMeta,
  mergeDeep,
} from "@/lib/form.utils"
import { ICountry } from "@/data/global/global.interface"

describe("Form Utils", () => {
  describe("required", () => {
    it("should return undefined for non-empty values", () => {
      expect(required("test")).toBeUndefined()
      expect(required("  test  ")).toBeUndefined()
    })

    it("should return default error message for empty values", () => {
      expect(required("")).toBe("This field is required")
      expect(required("   ")).toBe("This field is required")
      expect(required(undefined)).toBeUndefined()
    })

    it("should return custom error message when provided", () => {
      const customMessage = "Custom error message"
      expect(required("", customMessage)).toBe(customMessage)
      expect(required("   ", customMessage)).toBe(customMessage)
      expect(required(undefined, customMessage)).toBeUndefined()
    })
  })

  describe("validateIBan", () => {
    const mockCountry: ICountry = {
      id: "1",
      name: "United Kingdom",
      formalName: "United Kingdom of Great Britain and Northern Ireland",
      codeIso2: "GB",
      codeIso3: "GBR",
      codeIso3Numeric: "826",
      phoneCode: "44",
      ibanLength: 22,
      ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
      ibanSupported: true,
      accountNumberType: "IBAN",
      nationalIdType: "SortCode",
      isSepaCountry: true,
      paymentPurposeCodeRequired: false,
      createdAt: "2024-01-01T00:00:00Z",
      createdBy: "system",
    }

    it("should return error when country is not provided", () => {
      expect(validateIBan("test")).toBe("Country is required")
    })

    it("should return required error for empty IBAN", () => {
      expect(validateIBan("", mockCountry)).toBe("IBAN is required")
      expect(validateIBan("   ", mockCountry)).toBe("IBAN is required")
    })

    it("should validate IBAN length", () => {
      const validIban = "*****************65432".replace(/\s/g, "")
      const invalidIban = "*****************"

      expect(validateIBan(validIban, mockCountry)).toBeUndefined()
      expect(validateIBan(invalidIban, mockCountry)).toBe(
        "IBAN must be 22 characters long",
      )
    })

    it("should handle IBAN with spaces and dashes", () => {
      const ibanWithSpaces = "GB82 WEST 1234 5698 7654 32"
      const ibanWithDashes = "GB82-WEST-1234-5698-7654-32"

      expect(validateIBan(ibanWithSpaces, mockCountry)).toBeUndefined()
      expect(validateIBan(ibanWithDashes, mockCountry)).toBeUndefined()
    })
  })

  describe("resetFieldMeta", () => {
    it("should reset field meta to expected values", () => {
      const mockForm = {
        setFieldMeta: vi.fn(),
      } as unknown as ReactFormExtendedApi<any, undefined>

      resetFieldMeta(mockForm, "testField")

      expect(mockForm.setFieldMeta).toHaveBeenCalledWith(
        "testField",
        expect.any(Function),
      )

      const setFieldMetaCall = vi.mocked(mockForm.setFieldMeta).mock.calls[0]
      const metaUpdater = setFieldMetaCall[1] as (meta: FieldMeta) => FieldMeta
      const result = metaUpdater({} as FieldMeta)

      expect(result).toEqual({
        errors: [],
        errorMap: {
          onMount: undefined,
          onChange: undefined,
        },
        isBlurred: true,
        isDirty: true,
        isPristine: false,
        isTouched: true,
        isValidating: false,
      })
    })
  })

  describe("mergeDeep", () => {
    it("should merge simple objects", () => {
      const defaults = { a: 1, b: 2 }
      const source = { b: 3, c: 4 }
      const expected = { a: 1, b: 3, c: 4 }

      expect(mergeDeep(defaults, source)).toEqual(expected)
    })

    it("should handle nested objects", () => {
      interface TestType {
        a?: number
        b?: number
        nested?: {
          x?: number
          y?: number
          z?: number
        }
      }

      const defaults: TestType = {
        a: 1,
        nested: { x: 1, y: 2 },
      }
      const source: TestType = {
        b: 2,
        nested: { y: 3, z: 4 },
      }
      const expected = {
        a: 1,
        b: 2,
        nested: { x: 1, y: 3, z: 4 },
      }

      expect(mergeDeep(defaults, source)).toEqual(expected)
    })

    it("should handle undefined source", () => {
      const defaults = { a: 1, b: 2 }
      expect(mergeDeep(defaults, undefined)).toEqual(defaults)
    })

    it("should handle empty objects", () => {
      expect(mergeDeep({}, {})).toEqual({})
      expect(mergeDeep({ a: 1 }, {})).toEqual({ a: 1 })
      expect(mergeDeep({}, { b: 2 })).toEqual({ b: 2 })
    })

    it("should preserve source values when no defaults", () => {
      const defaults = {}
      const source = { a: 1, nested: { b: 2 } }
      expect(mergeDeep(defaults, source)).toEqual(source)
    })
  })
})
