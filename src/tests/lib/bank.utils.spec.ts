import { describe, it, expect } from "vitest"

import {
  formatBankDetails,
  formatAccountName,
  getAccountName,
  formatAccountNumber,
} from "@/lib/bank.utils"
import { IBankData } from "@/data/payees/payees.interface"
import { Account } from "@/data/account/account.interface"

describe("Bank Utils", () => {
  describe("formatBankDetails", () => {
    it("should format complete bank details correctly", () => {
      const bankData: IBankData = {
        name: "Test Bank",
        bankCode: "TESTBANK",
        payeeBankId: "123456",
        nationalId: "TEST123",
        nationalIdType: "TEST",
        swiftBic: "TESTBIC",
        postalCode: "12345",
        streetName: "Main St",
        buildingNumber: "123",
        regionState: "Test State",
        city: "Test City",
        country: "Test Country",
        address: {
          buildingNumber: "123",
          buildingName: "",
          streetName: "Main St",
          city: "Test City",
          regionState: "Test State",
          postalCode: "12345",
          country: { name: "Test Country", value: "Test Country" },
          addressLine1: "123 Main St",
          addressLine2: "Test City, Test State",
        },
      }

      expect(formatBankDetails(bankData)).toBe(
        "Test Bank - 123, Main St, Test City, Test State, 12345, Test Country",
      )
    })

    it("should handle missing address parts", () => {
      const bankData: IBankData = {
        name: "Test Bank",
        bankCode: "TESTBANK",
        payeeBankId: "123456",
        nationalId: "TEST123",
        nationalIdType: "TEST",
        swiftBic: "TESTBIC",
        postalCode: "12345",
        streetName: "Main St",
        buildingNumber: "",
        regionState: "",
        city: "Test City",
        country: "Test Country",
        address: {
          buildingNumber: "",
          buildingName: "",
          streetName: "Main St",
          city: "Test City",
          regionState: "",
          postalCode: "12345",
          country: { name: "Test Country", value: "Test Country" },
          addressLine1: "Main St, Test City, 12345, Test Country",
          addressLine2: "Test City",
        },
      }

      expect(formatBankDetails(bankData)).toBe(
        "Test Bank - Main St, Test City, 12345, Test Country",
      )
    })
  })

  describe("formatAccountName", () => {
    it("should format account name with IBAN correctly", () => {
      expect(
        formatAccountName({
          name: "Test Account",
          iban: "**********************",
        }),
      ).toBe("Test Account (...3000)")
    })

    it("should handle missing name", () => {
      expect(formatAccountName({ iban: "**********************" })).toBe(
        "- (...3000)",
      )
    })

    it("should handle missing IBAN", () => {
      expect(formatAccountName({ name: "Test Account" })).toBe(
        "Test Account (-)",
      )
    })

    it("should handle short IBAN", () => {
      expect(formatAccountName({ name: "Test Account", iban: "123" })).toBe(
        "Test Account (-)",
      )
    })

    it("should handle missing both name and IBAN", () => {
      expect(formatAccountName({})).toBe("- (-)")
    })
  })

  describe("getAccountName", () => {
    it("should format account details correctly", () => {
      const account: Account = {
        accountName: "Test Account",
        virtualIban: "**********************",
      } as Account

      expect(getAccountName(account)).toBe("Test Account (...3000)")
    })

    it("should handle undefined account", () => {
      expect(getAccountName(undefined)).toBe("Select account")
    })

    it("should handle missing account details", () => {
      const account = {} as Account
      expect(getAccountName(account)).toBe("- (-)")
    })
  })

  describe("formatAccountNumber", () => {
    it("should mask account number correctly", () => {
      expect(formatAccountNumber("**********")).toBe("(...7890)")
    })

    it("should handle short account numbers", () => {
      expect(formatAccountNumber("123")).toBe("123")
    })

    it("should handle exactly 4 digits", () => {
      expect(formatAccountNumber("1234")).toBe("1234")
    })

    it("should handle undefined account number", () => {
      expect(formatAccountNumber(undefined)).toBe("-")
    })

    it("should handle empty account number", () => {
      expect(formatAccountNumber("")).toBe("-")
    })
  })
})
