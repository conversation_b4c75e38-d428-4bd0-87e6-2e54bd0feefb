import { describe, it, expect, vi, beforeEach } from "vitest"
import { MouseEvent } from "react"

import { createDocumentBadgeClickHandler } from "@/lib/document.utils"
import { DocumentsMetadata } from "@/data/onboarding/onboarding.interface"

describe("document.utils", () => {
  describe("createDocumentBadgeClickHandler", () => {
    // Mocks
    let mockForm: any
    let mockHandleDownload: ReturnType<typeof vi.fn>
    let mockEvent: Partial<MouseEvent>
    let documentMeta: DocumentsMetadata
    let mockLocalFiles: File[]

    beforeEach(() => {
      // Setup mocks
      mockLocalFiles = [
        new File(["test content"], "test-file.pdf", {
          type: "application/pdf",
        }),
      ]

      mockForm = {
        getFieldValue: vi.fn((fieldName) => {
          if (fieldName === "testFieldName") {
            return mockLocalFiles
          }
          return []
        }),
      }

      mockHandleDownload = vi.fn()

      documentMeta = {
        id: "doc-123",
        name: "test-document.pdf",
      }

      // Mock event with target element
      const mockTarget = document.createElement("div")
      mockEvent = {
        target: mockTarget,
      }

      // Mock console.log to avoid cluttering test output
      vi.spyOn(console, "log").mockImplementation(() => {})
    })

    it("should create a function that retrieves local files and calls handleDownload", () => {
      // Create the handler function
      const handler = createDocumentBadgeClickHandler(
        mockForm,
        "testFieldName",
        mockHandleDownload,
      )

      // Call the handler
      handler(mockEvent as MouseEvent, documentMeta)

      // Verify form.getFieldValue was called with the correct field name
      expect(mockForm.getFieldValue).toHaveBeenCalledWith("testFieldName")

      // Verify handleDownload was called with document and local files
      expect(mockHandleDownload).toHaveBeenCalledWith(
        documentMeta,
        mockLocalFiles,
      )
    })

    it("should not call handleDownload when clicking on delete button", () => {
      // Create a delete button element
      const deleteButton = document.createElement("button")
      deleteButton.classList.add("delete-button")

      // Set up event with target inside delete button
      const target = document.createElement("span")
      deleteButton.appendChild(target)
      mockEvent.target = target

      // Mock closest method to return the delete button
      target.closest = vi.fn((selector) => {
        if (selector === ".delete-button") {
          return deleteButton
        }
        return null
      })

      // Create the handler function
      const handler = createDocumentBadgeClickHandler(
        mockForm,
        "testFieldName",
        mockHandleDownload,
      )

      // Call the handler
      handler(mockEvent as MouseEvent, documentMeta)

      // Verify handleDownload was not called
      expect(mockHandleDownload).not.toHaveBeenCalled()
    })

    it("should handle empty local files", () => {
      // Setup form to return empty array
      mockForm.getFieldValue.mockReturnValue([])

      // Create the handler function
      const handler = createDocumentBadgeClickHandler(
        mockForm,
        "testFieldName",
        mockHandleDownload,
      )

      // Call the handler
      handler(mockEvent as MouseEvent, documentMeta)

      // Verify handleDownload was called with empty array
      expect(mockHandleDownload).toHaveBeenCalledWith(documentMeta, [])
    })

    it("should handle null local files", () => {
      // Setup form to return null
      mockForm.getFieldValue.mockReturnValue(null)

      // Create the handler function
      const handler = createDocumentBadgeClickHandler(
        mockForm,
        "testFieldName",
        mockHandleDownload,
      )

      // Call the handler
      handler(mockEvent as MouseEvent, documentMeta)

      // Verify handleDownload was called with empty array (default)
      expect(mockHandleDownload).toHaveBeenCalledWith(documentMeta, [])
    })
  })
})
