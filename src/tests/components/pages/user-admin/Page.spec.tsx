import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { TestRouter } from "@/tests/utils/TestRouter"
import { UserAdminPage } from "@/components/pages/user-admin/Page"

// Mock useLoaderData to provide the required entity data
vi.mock("@tanstack/react-router", async () => {
  const actual = await vi.importActual("@tanstack/react-router")
  return {
    ...actual,
    useLoaderData: vi.fn(() => ({
      entity: { id: "test-entity-id" },
      entities: [
        {
          entityId: "test-entity-id",
          entityName: "Test Entity",
          entityStatus: "Client",
          roles: ["Administrator"],
        },
      ],
    })),
  }
})

vi.mock("@/components/pages/user-admin/components/Users/<USER>", () => ({
  Users: () => <div aria-label="Users page">Users</div>,
}))

vi.mock("@/components/pages/user-admin/components/Approver/Page", () => ({
  ApproverSignatoriesAndRules: () => (
    <div aria-label="Approver page">Approver</div>
  ),
}))

vi.mock(
  "@/components/pages/user-admin/components/RolesAndPermissions/Page",
  () => ({
    RolesandPermissions: () => (
      <div aria-label="Roles and permissions page">
        Roles and permissions page
      </div>
    ),
  }),
)

describe("UserAdminPage", () => {
  const _user = userEvent.setup()

  it("should render user admin page", async () => {
    render(<UserAdminPage />, { wrapper: TestRouter })

    expect(screen.getByLabelText("User admin page")).toBeInTheDocument()
  })
})
