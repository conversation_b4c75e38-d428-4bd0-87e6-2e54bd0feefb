import { describe, expect, it, <PERSON>ck } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { TestRouter } from "@/tests/utils/TestRouter"
import { useGetAllUsersQry } from "@/data/entity/entity.query"
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Users } from "@/components/pages/user-admin/components/Users/<USER>"
import { GetAllUsersWithAccessToEntityResponseDto } from "@/client/onboarding/types.gen"

vi.mock("@tanstack/react-router", async (importOriginal) => {
  const actual = await importOriginal()

  // Create a safe version of actual to spread
  const safeActual = actual as Record<string, unknown>

  return {
    ...safeActual,
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "1",
      },
    })),
    useNavigate: () => vi.fn(),
  }
})

vi.mock("@/data/entity/entity.query", () => ({
  useGetAllUsersQry: vi.fn(),
}))

const setup = () => {
  const user = userEvent.setup()

  const component = render(
    <Tabs>
      <TabsList>
        <TabsTrigger value="Users">Users</TabsTrigger>
      </TabsList>

      <TabsContent aria-label="Users page" value="Users">
        <Users />
      </TabsContent>
    </Tabs>,
    { wrapper: TestRouter },
  )

  return {
    component,
    user,
  }
}

describe("Users", () => {
  const user = userEvent.setup()

  it("should render users component", async () => {
    setup()
    ;(useGetAllUsersQry as Mock).mockReturnValue({
      isLoading: false,
      data: [] as GetAllUsersWithAccessToEntityResponseDto,
    })

    await user.click(screen.getByText("Users"))

    expect(screen.getByLabelText("Users page")).toBeInTheDocument()
  })
})
