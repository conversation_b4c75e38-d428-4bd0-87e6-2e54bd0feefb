import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import type { Currency } from "@/lib/constants/currency.constants"
import type { Account } from "@/data/account/account.interface"
import type { AccountBalance } from "@/data/account/account.interface"

import { TransactionFilters } from "@/components/pages/transactions/components/TransactionFilters"

// Mock components
vi.mock("@/components/pages/account/components/AccountSelector", () => ({
  AccountSelector: ({ value, onChange }: any) => (
    <button data-testid="account-selector" onClick={() => onChange("acc2")}>
      Select Account
    </button>
  ),
}))

vi.mock("@/components/base/currency/CurrencySelector", () => ({
  CurrencySelector: ({ currency, onChange }: any) => (
    <button data-testid="currency-selector" onClick={() => onChange("EUR")}>
      {currency}
    </button>
  ),
}))

vi.mock(
  "@/components/pages/transactions/components/TransactionDateRangePicker",
  () => ({
    TransactionDateRangePicker: ({ onDateChange }: any) => (
      <button
        data-testid="date-picker"
        onClick={() => onDateChange({ from: new Date(), to: new Date() })}
      >
        Pick Date
      </button>
    ),
  }),
)

const mockAccounts = [
  {
    id: "acc1",
    name: "Test Account",
    accountName: "Test Account Name",
    virtualIban: "GB123456789",
    balances: [
      {
        id: "balance1",
        currency: "USD" as const,
        balance: 1000,
      },
      {
        id: "balance2",
        currency: "EUR" as const,
        balance: 2000,
      },
    ],
    totalBalance: 3000,
    category: "Test Category",
    iban: "GB123456789",
    entityId: "entity-1",
    bankName: "Test Bank",
    bankAddress: "123 Bank Street, London, UK",
    clientAccountId: "client-account-1",
    clientId: "client-1",
    totalCurrency: "USD" as const,
  },
]

describe("TransactionFilters", () => {
  const mockProps = {
    accounts: mockAccounts,
    vBan: "acc1",
    currency: "USD" as Currency,
    availableCurrencies: ["USD", "EUR"] as Currency[],
    onAccountChange: vi.fn(),
    onCurrencyChange: vi.fn(),
    onDateChange: vi.fn(),
  }

  it("should handle all filter interactions", async () => {
    const user = userEvent.setup()
    render(<TransactionFilters {...mockProps} />)

    // Check rendering and interactions
    const filters = ["account-selector", "currency-selector", "date-picker"]
    for (const filter of filters) {
      const element = screen.getByTestId(filter)
      expect(element).toBeInTheDocument()
      await user.click(element)
    }

    expect(mockProps.onAccountChange).toHaveBeenCalledWith("acc2")
    expect(mockProps.onCurrencyChange).toHaveBeenCalledWith("EUR")
    expect(mockProps.onDateChange).toHaveBeenCalled()
  })
})
