import { render, screen } from "@testing-library/react"

import { TransactionDetailRow } from "@/components/pages/transactions/components/TransactionDetailRow"

describe("TransactionDetailRow", () => {
  it("renders label and string value correctly", () => {
    render(<TransactionDetailRow label="Test Label" value="Test Value" />)

    expect(screen.getByText("Test Label")).toBeInTheDocument()
    expect(screen.getByRole("paragraph")).toHaveTextContent("Test Value")
  })

  it("renders with secondary value", () => {
    render(
      <TransactionDetailRow
        label="Amount"
        secondaryValue=" USD"
        value="100.00"
      />,
    )

    expect(screen.getByText("Amount")).toBeInTheDocument()
    const paragraph = screen.getByRole("paragraph")
    expect(paragraph).toHaveTextContent("100.00")
    expect(paragraph).toHaveTextContent("USD")
  })

  it("renders with React node value", () => {
    render(
      <TransactionDetailRow
        label="Custom"
        value={<div data-testid="custom-value">Custom Node</div>}
      />,
    )

    expect(screen.getByText("Custom")).toBeInTheDocument()
    expect(screen.getByTestId("custom-value")).toHaveTextContent("Custom Node")
  })
})
