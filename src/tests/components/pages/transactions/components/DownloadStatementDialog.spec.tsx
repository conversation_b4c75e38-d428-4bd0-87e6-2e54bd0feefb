import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import type { Currency } from "@/lib/constants/currency.constants"

import { Account } from "@/data/account/account.interface"
import { DownloadStatementDialog } from "@/components/pages/transactions/components/DownloadStatementDialog"

// Mock components
vi.mock("@/components/ui/popover", () => ({
  Popover: ({ children }: any) => <div>{children}</div>,
  PopoverTrigger: ({ children }: any) => <div>{children}</div>,
  PopoverContent: ({ children }: any) => (
    <div data-testid="popover-content">{children}</div>
  ),
}))

vi.mock("@/components/ui/radio-group", () => ({
  RadioGroup: ({ children, onValueChange }: any) => (
    <div onChange={(e: any) => onValueChange(e.target.value)}>{children}</div>
  ),
  RadioGroupItem: ({ value }: any) => (
    <input data-testid={`radio-${value}`} type="radio" value={value} />
  ),
}))

vi.mock("@/components/ui/checkbox", () => ({
  Checkbox: ({ onCheckedChange, id }: any) => (
    <input
      data-testid={`checkbox-${id}`}
      onChange={(e) => onCheckedChange(e.target.checked)}
      type="checkbox"
    />
  ),
}))

// Mock download mutation
const mockMutateAsync = vi.fn().mockResolvedValue(new Blob())
vi.mock("@/data/transactions/transactions.query", () => ({
  useDownloadTransactionsMutation: () => ({
    mutateAsync: mockMutateAsync,
  }),
}))

// Mock formatAccountNumber
vi.mock("@/lib/bank.utils", () => ({
  formatAccountNumber: (accountNumber: string) => accountNumber,
}))

describe("DownloadStatementDialog", () => {
  const mockProps = {
    dateRange: {
      from: new Date("2024-02-01"),
      to: new Date("2024-02-29"),
    },
    currencies: ["USD", "EUR"] as Currency[],
    accountBalanceId: "balance-1",
    selectedAccount: {
      accountName: "Test Account",
      name: "Test Account",
      virtualIban: "GB123456789",
      balances: [],
      totalBalance: 1000,
      category: "Test Category",
      id: "test-account-1",
      iban: "GB123456789",
      entityId: "entity-1",
      bankName: "Test Bank",
      bankAddress: "Test Address",
      clientAccountId: "client-account-1",
      clientId: "client-1",
      totalCurrency: "USD" as const,
    } as Account,
    hideText: false,
    hasTransactions: true,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, "error").mockImplementation(() => {})
  })

  it("should handle download flow correctly", async () => {
    const user = userEvent.setup()
    render(<DownloadStatementDialog {...mockProps} />)

    await user.click(screen.getByText("Download statement"))
    await user.click(screen.getByText("Download"))

    expect(mockMutateAsync).toHaveBeenCalledWith({
      accountId: "balance-1",
      fileType: "pdf",
      currency: "USD",
      fromDate: "2024-02-01",
      toDate: "2024-02-29",
    })
  })

  it("should disable download button when there are no currencies", async () => {
    const user = userEvent.setup()
    render(<DownloadStatementDialog {...mockProps} currencies={[]} />)

    await user.click(screen.getByText("Download statement"))
    expect(screen.getByText("Download")).toBeDisabled()
  })

  // TODO: Reverting this back. Seba needs to look into this
  it.skip("should display date range correctly", async () => {
    const user = userEvent.setup()
    render(<DownloadStatementDialog {...mockProps} />)

    await user.click(screen.getByText("Download statement"))

    // Use a more flexible text matcher
    const dateText = screen.getByText(
      (content) =>
        content.includes("01/02/2024") && content.includes("29/02/2024"),
    )
    expect(dateText).toBeInTheDocument()
  })

  it("should not display text when hideText is true", () => {
    render(<DownloadStatementDialog {...mockProps} hideText={true} />)
    expect(screen.queryByText("Download statement")).not.toBeInTheDocument()
  })

  it("should display text when hideText is false", () => {
    render(<DownloadStatementDialog {...mockProps} hideText={false} />)
    expect(screen.getByText("Download statement")).toBeInTheDocument()
  })

  it("should disable download button when there are no transactions", () => {
    render(<DownloadStatementDialog {...mockProps} hasTransactions={false} />)
    expect(screen.getByText("Download statement")).toBeDisabled()
  })

  it("should enable download button when there are transactions", () => {
    render(<DownloadStatementDialog {...mockProps} hasTransactions={true} />)
    expect(screen.getByText("Download statement")).not.toBeDisabled()
  })
})
