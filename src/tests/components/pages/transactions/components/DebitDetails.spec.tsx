import { vi } from "vitest"

// Mock CurrencyText component
vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: ({ amount }: { amount: number }) => (
    <span>{amount.toLocaleString()}</span>
  ),
}))

import { render, screen } from "@testing-library/react"

import { IDebitDetailsResponse } from "@/data/transactions/transactions.interface"
import { DebitDetails } from "@/components/pages/transactions/components/DebitDetails"

describe("DebitDetails", () => {
  const mockDetails: IDebitDetailsResponse = {
    type: "DEBIT",
    id: "123",
    date: "2024-12-11",
    fromAccount: {
      accountNumber: "9873",
      currency: "GBP",
      balance: 13159.5,
      id: "9873",
      name: "<PERSON><PERSON>",
    },
    toAccount: {
      accountNumber: "9874",
      currency: "EUR",
      balance: 2468.4,
      id: "9874",
      name: "Trading Account",
    },
    amount: 2000.0,
    fxRate: 1.2342,
    fee: {
      type: "SEPA",
      amount: 25.0,
      currency: "GBP",
    },
    purpose: "Business expense",
    reference: "INV-2024-001",
    createdBy: {
      name: "<PERSON> Doe",
      email: "<EMAIL>",
    },
    approvedBy: [
      {
        email: "<EMAIL>",
        grade: "SENIOR",
      },
    ],
  }

  it("renders without optional fields", () => {
    const minimalDetails: IDebitDetailsResponse = {
      type: "DEBIT",
      id: "123",
      date: "2024-12-11",
      fromAccount: {
        accountNumber: "9873",
        currency: "GBP",
        balance: 13159.5,
        id: "9873",
        name: "Barclays",
      },
      toAccount: {
        accountNumber: "9874",
        currency: "GBP",
        balance: 2468.4,
        id: "9874",
        name: "Trading Account",
      },
      amount: 2000.0,
    }

    render(<DebitDetails details={minimalDetails} />)

    // Check required fields are present
    expect(screen.getByText("From")).toBeInTheDocument()
    expect(screen.getByText("To")).toBeInTheDocument()

    // Check optional fields are not rendered
    expect(screen.queryByText("FX rate")).not.toBeInTheDocument()
    expect(screen.queryByText("Payment Type/Fee")).not.toBeInTheDocument()
    expect(screen.queryByText("Reference")).not.toBeInTheDocument()
    expect(screen.queryByText("Purpose")).not.toBeInTheDocument()
    expect(screen.queryByText("Created by")).not.toBeInTheDocument()
    expect(screen.queryByText("Approved by")).not.toBeInTheDocument()
  })

  it("shows N/A for FX rate when currencies are the same", () => {
    const sameCurrencyDetails = {
      ...mockDetails,
      toAccount: {
        ...mockDetails.toAccount,
        currency: "GBP",
      },
    }

    render(<DebitDetails details={sameCurrencyDetails} />)
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })
})
