import { vi } from "vitest"

// Mock CurrencyText component
vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: ({ amount }: { amount: number }) => (
    <span>{amount.toLocaleString()}</span>
  ),
}))

import { render, screen } from "@testing-library/react"

import { ICreditDetailsResponse } from "@/data/transactions/transactions.interface"
import { CreditDetails } from "@/components/pages/transactions/components/CreditDetails"

describe("CreditDetails", () => {
  const mockDetails: ICreditDetailsResponse = {
    type: "CREDIT",
    id: "123",
    date: "2024-12-11",
    fromAccount: {
      accountNumber: "9873",
      currency: "GBP",
      balance: 13159.5,
      id: "9873",
      name: "Client Account",
    },
    toAccount: {
      accountNumber: "9874",
      currency: "GBP",
      balance: 2468.4,
      id: "9874",
      name: "<PERSON><PERSON>",
    },
    amount: 2000.0,
    reference: "Payment for services",
  }

  it("renders all credit details when all data is provided", () => {
    render(<CreditDetails details={mockDetails} />)

    // Check account details
    expect(screen.getByText("From")).toBeInTheDocument()
    expect(screen.getByText("Client Account (9873)")).toBeInTheDocument()
    expect(screen.getByText("To")).toBeInTheDocument()
    expect(screen.getByText("Barclays (9874)")).toBeInTheDocument()

    // Check reference
    expect(screen.getByText("Reference")).toBeInTheDocument()
    expect(screen.getByText("Payment for services")).toBeInTheDocument()
  })

  it("renders without optional reference field", () => {
    const minimalDetails: ICreditDetailsResponse = {
      ...mockDetails,
      reference: undefined,
    }

    render(<CreditDetails details={minimalDetails} />)

    // Check required fields are present
    expect(screen.getByText("From")).toBeInTheDocument()
    expect(screen.getByText("Client Account (9873)")).toBeInTheDocument()
    expect(screen.getByText("To")).toBeInTheDocument()
    expect(screen.getByText("Barclays (9874)")).toBeInTheDocument()

    // Check optional reference is not rendered
    expect(screen.queryByText("Reference")).not.toBeInTheDocument()
  })
})
