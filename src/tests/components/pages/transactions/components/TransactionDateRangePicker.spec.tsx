import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { TransactionDateRangePicker } from "@/components/pages/transactions/components/TransactionDateRangePicker"

// Mock components
vi.mock("@/components/ui/calendar", () => ({
  Calendar: ({ onSelect }: any) => (
    <div data-testid="calendar">
      <button
        data-testid="select-date-range"
        onClick={() =>
          onSelect({
            from: new Date("2024-02-01"),
            to: new Date("2024-02-29"),
          })
        }
      >
        Select Range
      </button>
    </div>
  ),
}))

vi.mock("@/components/ui/popover", () => ({
  Popover: ({ children }: any) => <div>{children}</div>,
  PopoverTrigger: ({ children }: any) => <div>{children}</div>,
  PopoverContent: ({ children }: any) => (
    <div data-testid="popover-content">{children}</div>
  ),
}))

describe("TransactionDateRangePicker", () => {
  const mockProps = {
    onDateChange: vi.fn(),
    date: undefined,
  }

  it("should handle date range selection flow", async () => {
    const user = userEvent.setup()
    render(<TransactionDateRangePicker {...mockProps} />)

    // Initial state
    expect(screen.getByText("Pick a date")).toBeInTheDocument()

    // Open custom range
    await user.click(screen.getByText("Pick a date"))
    await user.click(screen.getByText("Custom range"))

    // Select date range
    await user.click(screen.getByTestId("select-date-range"))
    await user.click(screen.getByText("Confirm range"))

    expect(mockProps.onDateChange).toHaveBeenCalledWith({
      from: expect.any(Date),
      to: expect.any(Date),
    })
  })

  it("should handle preset date selections", async () => {
    const user = userEvent.setup()
    render(<TransactionDateRangePicker {...mockProps} />)

    await user.click(screen.getByText("Pick a date"))

    // Test preset buttons
    const presets = [
      "Today",
      "Previous working day",
      "This month",
      "Last month",
      "Last 12 months",
    ]
    for (const preset of presets) {
      await user.click(screen.getByText(preset))
      expect(mockProps.onDateChange).toHaveBeenCalled()
    }
  })

  it("should handle today selection correctly", async () => {
    const user = userEvent.setup()
    render(<TransactionDateRangePicker {...mockProps} />)

    await user.click(screen.getByText("Pick a date"))
    await user.click(screen.getByText("Today"))

    expect(mockProps.onDateChange).toHaveBeenCalledWith({
      from: expect.any(Date),
      to: expect.any(Date),
    })
  })

  it("should handle previous working day selection correctly", async () => {
    const user = userEvent.setup()
    render(<TransactionDateRangePicker {...mockProps} />)

    await user.click(screen.getByText("Pick a date"))
    await user.click(screen.getByText("Previous working day"))

    expect(mockProps.onDateChange).toHaveBeenCalledWith({
      from: expect.any(Date),
      to: expect.any(Date),
    })
  })

  it("should display selected date range", () => {
    const date = {
      from: new Date("2024-02-01"),
      to: new Date("2024-02-29"),
    }

    render(<TransactionDateRangePicker {...mockProps} date={date} />)
    expect(screen.getByText("01 Feb 2024 - 29 Feb 2024")).toBeInTheDocument()
  })
})
