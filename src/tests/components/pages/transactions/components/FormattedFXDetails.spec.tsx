import { vi } from "vitest"

// Mock CurrencyText component with more realistic formatting
vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: ({
    amount,
    currency,
    displayModes = ["code", "amount"],
  }: {
    amount: number
    currency: string
    displayModes?: string[]
  }) => {
    const formattedAmount = amount.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
    const parts = []
    if (displayModes.includes("amount")) {
      parts.push(formattedAmount)
    }
    if (displayModes.includes("code")) {
      parts.push(` ${currency}`)
    }
    return <span>{parts.join("")}</span>
  },
}))

import { render, screen } from "@testing-library/react"

import { IFXDetailsResponse } from "@/data/transactions/transactions.interface"
import { formattedFXDetails } from "@/components/pages/transactions/components/FormattedFXDetails"

describe("formattedFXDetails", () => {
  const mockDetails: IFXDetailsResponse = {
    type: "FX_TRADE",
    id: "123",
    date: "2024-12-11",
    fromAccount: {
      accountNumber: "9873",
      currency: "GBP",
      balance: 13159.5,
      id: "9873",
      name: "Barclays GBP",
    },
    toAccount: {
      accountNumber: "9874",
      currency: "EUR",
      balance: 2468.4,
      id: "9874",
      name: "Barclays EUR",
    },
    amount: 2000.0,
    fxRate: 1.2342,
  }

  it("formats FX details correctly for different currencies", () => {
    const { container } = render(formattedFXDetails({ details: mockDetails }))

    expect(screen.getByText("GBP:EUR 1.2342")).toBeInTheDocument()
    // The amount is calculated as: 2000.0 * 1.2342 = 2468.4
    expect(screen.getByText("2,468.40 EUR")).toBeInTheDocument()
  })

  it("returns N/A when currencies are the same", () => {
    const sameCurrencyDetails = {
      ...mockDetails,
      toAccount: {
        ...mockDetails.toAccount,
        currency: "GBP",
      },
    }

    const { container } = render(
      formattedFXDetails({ details: sameCurrencyDetails }),
    )
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })

  it("returns N/A when fxRate is missing", () => {
    const noFxRateDetails = {
      ...mockDetails,
      fxRate: undefined,
    }

    const { container } = render(
      formattedFXDetails({ details: noFxRateDetails }),
    )
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })
})
