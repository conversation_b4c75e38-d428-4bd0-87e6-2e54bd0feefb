import { vi } from "vitest"

// Mock CurrencyText component
vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: ({ amount }: { amount: number }) => (
    <span>{amount.toLocaleString()}</span>
  ),
}))

import { render, screen } from "@testing-library/react"

import { IFeeDetailsResponse } from "@/data/transactions/transactions.interface"
import { FeeDetails } from "@/components/pages/transactions/components/FeeDetails"

describe("FeeDetails", () => {
  const mockDetails: IFeeDetailsResponse = {
    type: "FEE",
    id: "123",
    date: "2024-12-11",
    fromAccount: {
      accountNumber: "9873",
      currency: "GBP",
      balance: 13159.5,
      id: "9873",
      name: "<PERSON><PERSON>",
    },
    toAccount: {
      accountNumber: "BANK001",
      currency: "GBP",
      balance: 25.0,
      id: "BANK001",
      name: "Bank Fee Account",
    },
    amount: 25.0,
    fee: {
      type: "MAINTENANCE",
      amount: 25.0,
      currency: "GBP",
    },
    fxRate: 1.2342,
    invoiceId: "INV-DEC2024-9873",
  }

  it("renders without optional fields", () => {
    const minimalDetails: IFeeDetailsResponse = {
      type: "FEE",
      id: "123",
      date: "2024-12-11",
      fromAccount: {
        accountNumber: "9873",
        currency: "GBP",
        balance: 13159.5,
        id: "9873",
        name: "Barclays",
      },
      toAccount: {
        accountNumber: "BANK001",
        currency: "GBP",
        balance: 25.0,
        id: "BANK001",
        name: "Bank Fee Account",
      },
      amount: 25.0,
      fee: {
        type: "MAINTENANCE",
        amount: 25.0,
        currency: "GBP",
      },
    }

    render(<FeeDetails details={minimalDetails} />)

    // Check required fields are present
    expect(screen.getByText("From")).toBeInTheDocument()
    expect(screen.getByText("To")).toBeInTheDocument()
    expect(screen.getByText("Payment Type/Fee")).toBeInTheDocument()

    // Check optional fields are not rendered
    expect(screen.queryByText("FX rate")).not.toBeInTheDocument()
    expect(screen.queryByText("Invoice ID")).not.toBeInTheDocument()
  })

  it("shows N/A for FX rate when currencies are the same", () => {
    const sameCurrencyDetails = {
      ...mockDetails,
      fromAccount: {
        ...mockDetails.fromAccount,
        currency: "GBP",
      },
      toAccount: {
        ...mockDetails.toAccount,
        currency: "GBP",
      },
    }

    render(<FeeDetails details={sameCurrencyDetails} />)
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })
})
