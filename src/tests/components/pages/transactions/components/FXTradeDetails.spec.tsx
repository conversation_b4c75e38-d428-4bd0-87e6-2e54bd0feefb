import { vi } from "vitest"

// Mock CurrencyText component
vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: ({ amount }: { amount: number }) => (
    <span>{amount.toLocaleString()}</span>
  ),
}))

import { render, screen } from "@testing-library/react"

import { IFXDetailsResponse } from "@/data/transactions/transactions.interface"
import { FXTradeDetails } from "@/components/pages/transactions/components/FXTradeDetails"

describe("FXTradeDetails", () => {
  const mockDetails: IFXDetailsResponse = {
    type: "FX_TRADE",
    id: "123",
    date: "2024-12-11",
    fromAccount: {
      accountNumber: "9873",
      currency: "GBP",
      balance: 13159.5,
      id: "9873",
      name: "Barclays GBP",
    },
    toAccount: {
      accountNumber: "9874",
      currency: "EUR",
      balance: 2468.4,
      id: "9874",
      name: "Barclays EUR",
    },
    amount: 2000.0,
    fxRate: 1.2342,
    fee: {
      type: "CONVERSION",
      amount: 25.0,
      currency: "GBP",
    },
    purpose: "Currency conversion to EUR",
    createdBy: {
      name: "John Doe",
      email: "<EMAIL>",
    },
    approvedBy: [
      {
        email: "<EMAIL>",
        grade: "SENIOR",
      },
    ],
  }

  it("renders without optional fields", () => {
    const minimalDetails: IFXDetailsResponse = {
      type: "FX_TRADE",
      id: "123",
      date: "2024-12-11",
      fromAccount: {
        accountNumber: "9873",
        currency: "GBP",
        balance: 13159.5,
        id: "9873",
        name: "Barclays GBP",
      },
      toAccount: {
        accountNumber: "9874",
        currency: "EUR",
        balance: 2468.4,
        id: "9874",
        name: "Barclays EUR",
      },
      amount: 2000.0,
      fxRate: 1.2342,
    }

    render(<FXTradeDetails details={minimalDetails} />)

    // Check required fields are present
    expect(screen.getByText("From")).toBeInTheDocument()
    expect(screen.getByText("To")).toBeInTheDocument()
    expect(screen.getByText("FX rate")).toBeInTheDocument()

    // Check fee shows N/A when missing
    expect(screen.getByText("Payment type/fee")).toBeInTheDocument()
    expect(screen.getByText("N/A")).toBeInTheDocument()

    // Check optional fields are not rendered
    expect(screen.queryByText("Created by")).not.toBeInTheDocument()
    expect(screen.queryByText("Approved by")).not.toBeInTheDocument()
  })

  it("renders multiple approvers correctly", () => {
    const multipleApproversDetails = {
      ...mockDetails,
      approvedBy: [
        { email: "<EMAIL>", grade: "SENIOR" },
        { email: "<EMAIL>", grade: "MANAGER" },
      ],
    }

    render(<FXTradeDetails details={multipleApproversDetails} />)

    expect(screen.getByText("Approved by")).toBeInTheDocument()
    expect(
      screen.getByText(
        /approver1@example\.com \(SENIOR\).*approver2@example\.com \(MANAGER\)/s,
      ),
    ).toBeInTheDocument()
  })

  it("shows N/A for FX rate when currencies are the same", () => {
    const sameCurrencyDetails = {
      ...mockDetails,
      fromAccount: {
        ...mockDetails.fromAccount,
        currency: "GBP",
      },
      toAccount: {
        ...mockDetails.toAccount,
        currency: "GBP",
      },
    }

    render(<FXTradeDetails details={sameCurrencyDetails} />)
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })

  it("shows N/A for fee when fee details are missing", () => {
    const noFeeDetails = {
      ...mockDetails,
      fee: undefined,
    }

    render(<FXTradeDetails details={noFeeDetails} />)
    expect(screen.getAllByText("N/A")[0]).toBeInTheDocument()
  })
})
