import { describe, expect, it } from "vitest"
import { render, screen } from "@testing-library/react"

import { DashboardCard } from "@/components/pages/dashboard/components/DashboardCard"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to }: any) => <a href={to}>{children}</a>,
}))

describe("DashboardCard", () => {
  it("should render title and content", () => {
    render(
      <DashboardCard title="Test Card">
        <div>Test Content</div>
      </DashboardCard>,
    )

    expect(screen.getByText("Test Card")).toBeInTheDocument()
    expect(screen.getByText("Test Content")).toBeInTheDocument()
  })

  it("should render link when provided", () => {
    render(
      <DashboardCard
        link={{ to: "/$entityId/accounts" }}
        linkText="View Details"
        title="Test Card"
      >
        <div>Test Content</div>
      </DashboardCard>,
    )

    const link = screen.getByText("View Details")
    expect(link).toBeInTheDocument()
    expect(link.closest("a")).toHaveAttribute("href", "/$entityId/accounts")
  })

  it("should use default link text when not provided", () => {
    render(
      <DashboardCard link={{ to: "/$entityId/accounts" }} title="Test Card">
        <div>Test Content</div>
      </DashboardCard>,
    )

    expect(screen.getByText("View all")).toBeInTheDocument()
  })

  it("should render no data message when no children provided", () => {
    render(<DashboardCard title="Test Card" />)

    expect(screen.getByText("No data")).toBeInTheDocument()
  })

  it("should render custom no data message", () => {
    render(<DashboardCard noDataText="Nothing to see here" title="Test Card" />)

    expect(screen.getByText("Nothing to see here")).toBeInTheDocument()
  })

  it("should render empty div when no link provided", () => {
    render(<DashboardCard title="Test Card">Content</DashboardCard>)

    const header = screen.getByText("Test Card").parentElement
    expect(header?.children[1]).toBeEmptyDOMElement()
  })
})
