import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { NotificationCard } from "@/components/pages/dashboard/components/NotificationCard"

describe("NotificationCard", () => {
  const mockNotifications = [
    {
      id: 1,
      message: "Test notification 1",
      timestamp: "2024-01-01 10:00",
    },
    {
      id: 2,
      message: "Test notification 2",
      timestamp: "2024-01-01 11:00",
    },
  ]

  it("should render notifications correctly", () => {
    render(
      <NotificationCard notifications={mockNotifications} onClose={() => {}} />,
    )

    expect(screen.getByText("Test notification 1")).toBeInTheDocument()
    expect(screen.getByText("2024-01-01 10:00")).toBeInTheDocument()
    expect(screen.getByText("Test notification 2")).toBeInTheDocument()
    expect(screen.getByText("2024-01-01 11:00")).toBeInTheDocument()
  })

  it("should call onClose when close button is clicked", async () => {
    const mockOnClose = vi.fn()
    const user = userEvent.setup()

    render(
      <NotificationCard
        notifications={mockNotifications}
        onClose={mockOnClose}
      />,
    )

    const closeButtons = screen.getAllByLabelText("Close notification")
    await user.click(closeButtons[0])

    expect(mockOnClose).toHaveBeenCalled()
  })

  it("should render warning icons", () => {
    render(
      <NotificationCard notifications={mockNotifications} onClose={() => {}} />,
    )

    const warningIcons = document.querySelectorAll(".text-warning")
    expect(warningIcons).toHaveLength(mockNotifications.length)
  })

  it("should handle empty notifications array", () => {
    render(<NotificationCard notifications={[]} onClose={() => {}} />)

    const container = screen.getByTestId("notifications-container")
    expect(container).toBeEmptyDOMElement()
  })

  it("should maintain correct notification order", () => {
    render(
      <NotificationCard notifications={mockNotifications} onClose={() => {}} />,
    )

    const messages = screen.getAllByText(/Test notification/)
    expect(messages[0]).toHaveTextContent("Test notification 1")
    expect(messages[1]).toHaveTextContent("Test notification 2")
  })
})
