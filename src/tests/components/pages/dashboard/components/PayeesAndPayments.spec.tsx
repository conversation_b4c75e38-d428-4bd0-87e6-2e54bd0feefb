import { describe, expect, it, vi } from "vitest"
import { render, screen } from "@testing-library/react"

import { Payment } from "@/data/payments/payments.interface"
import { IPayeeData } from "@/data/payees/payees.interface"
import { useGlobalStore } from "@/data/global/global.store"
import { PayeesAndPayments } from "@/components/pages/dashboard/components/PayeesAndPayments"

vi.mock("@tanstack/react-router", () => ({
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
      },
    })),
  })),
}))

vi.mock("@/components/pages/dashboard/components/DashboardCard", () => ({
  DashboardCard: ({ title, children, noDataText }: any) => (
    <div data-testid={`dashboard-card-${title.toLowerCase()}`}>
      <h2>{title}</h2>
      {children?.props?.children?.length > 0 ? children : <p>{noDataText}</p>}
    </div>
  ),
}))

vi.mock("@/components/pages/dashboard/components/PayeeTile", () => ({
  PayeeTile: ({ payee }: any) => (
    <div data-testid="payee-tile">{payee.accountName}</div>
  ),
}))

vi.mock("@/components/pages/dashboard/components/PaymentsTile", () => ({
  PaymentsTile: ({ payment }: any) => (
    <div data-testid="payment-tile">{payment.payee.accountName}</div>
  ),
}))

describe("PayeesAndPayments", () => {
  const mockPayees = [
    {
      id: "1",
      accountName: "John Doe",
      type: "Individual",
      bank: { name: "Test Bank" },
      iban: "**********************",
    },
    {
      id: "2",
      accountName: "Jane Smith",
      type: "Individual",
      bank: { name: "Test Bank" },
      iban: "**********************",
    },
  ] as IPayeeData[]

  const mockPayments = [
    {
      id: "1",
      payee: {
        accountName: "John Doe",
        type: "Individual",
        bank: { name: "Test Bank" },
        iban: "**********************",
      },
      amount: 1000,
      currency: "GBP",
      createdAt: "2024-02-20T14:30:00Z",
    },
    {
      id: "2",
      payee: {
        accountName: "Jane Smith",
        type: "Individual",
        bank: { name: "Test Bank" },
        iban: "**********************",
      },
      amount: 2000,
      currency: "GBP",
      createdAt: "2024-02-20T15:30:00Z",
    },
  ] as Payment[]

  it("should render both payees and payments sections", () => {
    render(<PayeesAndPayments payees={mockPayees} payments={mockPayments} />)

    expect(screen.getByTestId("dashboard-card-payees")).toBeInTheDocument()
    expect(screen.getByTestId("dashboard-card-payments")).toBeInTheDocument()
  })

  it("should render payee tiles for each payee", () => {
    render(<PayeesAndPayments payees={mockPayees} payments={mockPayments} />)

    const payeeTiles = screen.getAllByTestId("payee-tile")
    expect(payeeTiles).toHaveLength(2)
    expect(payeeTiles[0]).toHaveTextContent("John Doe")
    expect(payeeTiles[1]).toHaveTextContent("Jane Smith")
  })

  it("should render payment tiles for each payment", () => {
    render(<PayeesAndPayments payees={mockPayees} payments={mockPayments} />)

    const paymentTiles = screen.getAllByTestId("payment-tile")
    expect(paymentTiles).toHaveLength(2)
    expect(paymentTiles[0]).toHaveTextContent("John Doe")
    expect(paymentTiles[1]).toHaveTextContent("Jane Smith")
  })

  it("should handle empty payees list", () => {
    render(<PayeesAndPayments payees={[]} payments={mockPayments} />)

    const payeesCard = screen.getByTestId("dashboard-card-payees")
    expect(payeesCard).toHaveTextContent("No payees found.")
    expect(screen.queryAllByTestId("payee-tile")).toHaveLength(0)
  })

  it("should handle empty payments list", () => {
    render(<PayeesAndPayments payees={mockPayees} payments={[]} />)

    const paymentsCard = screen.getByTestId("dashboard-card-payments")
    expect(paymentsCard).toHaveTextContent("No payments found.")
    expect(screen.queryAllByTestId("payment-tile")).toHaveLength(0)
  })

  it("should handle undefined payees and payments", () => {
    render(
      <PayeesAndPayments
        payees={undefined as any}
        payments={undefined as any}
      />,
    )

    expect(screen.getByTestId("dashboard-card-payees")).toHaveTextContent(
      "No payees found.",
    )
    expect(screen.getByTestId("dashboard-card-payments")).toHaveTextContent(
      "No payments found.",
    )
  })

  it.skip("should use entity ID from global store for links", () => {
    ;(useGlobalStore as any).mockReturnValue({
      entity: { id: "custom-entity" },
    })

    render(<PayeesAndPayments payees={mockPayees} payments={mockPayments} />)

    expect(screen.getByTestId("dashboard-card-payees")).toBeInTheDocument()
    expect(screen.getByTestId("dashboard-card-payments")).toBeInTheDocument()
  })

  it("should handle null entity ID gracefully", () => {
    render(<PayeesAndPayments payees={mockPayees} payments={mockPayments} />)

    expect(screen.getByTestId("dashboard-card-payees")).toBeInTheDocument()
    expect(screen.getByTestId("dashboard-card-payments")).toBeInTheDocument()
  })

  it("should maintain grid layout on all screen sizes", () => {
    const { container } = render(
      <PayeesAndPayments payees={mockPayees} payments={mockPayments} />,
    )

    const gridContainer = container.firstChild as HTMLElement
    expect(gridContainer).toHaveClass(
      "grid",
      "grid-cols-1",
      "lg:grid-cols-2",
      "gap-6",
    )
  })
})
