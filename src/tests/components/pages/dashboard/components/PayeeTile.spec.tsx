import { describe, expect, it } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { IPayeeData } from "@/data/payees/payees.interface"
import { PayeeTile } from "@/components/pages/dashboard/components/PayeeTile"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to, params, search }: any) => {
    const href = to.replace("$entityId", params.entityId)
    return <a href={`${href}?to=${search?.to}`}>{children}</a>
  },
  useLocation: () => ({
    href: "/test-entity/payments/send",
  }),
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
  })),
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
        name: "Test Entity",
      },
    })),
  })),
}))

// Mock dropdown menu components
vi.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children }: any) => (
    <div data-testid="dropdown-menu">{children}</div>
  ),
  DropdownMenuTrigger: ({ children }: any) => (
    <div data-testid="dropdown-trigger">{children}</div>
  ),
  DropdownMenuContent: ({ children }: any) => (
    <div data-testid="dropdown-content">{children}</div>
  ),
  DropdownMenuItem: ({ children }: any) => (
    <div data-testid="dropdown-item">{children}</div>
  ),
}))

vi.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, className }: any) => (
    <button className={className} data-testid="button" onClick={onClick}>
      {children}
    </button>
  ),
}))

describe("PayeeTile", () => {
  const mockPayee: IPayeeData = {
    id: "1",
    accountName: "John Doe",
    accountNumber: "********",
    iban: "**********************",
    type: "Individual",
    status: "Active",
    displayName: "John Doe",
    accountDetailsConfirmed: true,
    accountDetailsConfirmedAt: "2024-01-01T00:00:00Z",
    currentStatus: "Active",
    createdAt: "2024-01-01T00:00:00Z",
    createdBy: {
      id: "1",
      email: "<EMAIL>",
      displayName: "System",
    },
    client: {
      id: "1",
      name: "Test Client",
      registrationNumber: "123456",
      address: "123 Test Street",
    },
    isScaCompleted: true,
    bank: {
      name: "Test Bank",
      nationalId: "123456",
      nationalIdType: "Sort Code",
      swiftBic: "TESTGB2L",
      country: {
        id: "1",
        name: "United Kingdom",
        formalName: "United Kingdom of Great Britain and Northern Ireland",
        codeIso2: "GB",
        codeIso3: "GBR",
        codeIso3Numeric: "826",
        phoneCode: "44",
        ibanLength: 22,
        ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
        ibanSupported: true,
        accountNumberType: "IBAN",
        nationalIdType: "SortCode",
        isSepaCountry: true,
        paymentPurposeCodeRequired: false,
        createdAt: "2024-01-01T00:00:00Z",
        createdBy: "system",
      },
    },
  }

  it("should render payee details correctly", () => {
    render(<PayeeTile payee={mockPayee} />)

    expect(screen.getByText("John Doe")).toBeInTheDocument()
    expect(screen.getByText("Test Bank (...6819)")).toBeInTheDocument()
  })

  it("should render avatar with correct initials", () => {
    render(<PayeeTile payee={mockPayee} />)

    expect(screen.getByText("JD")).toBeInTheDocument()
  })

  it("should handle missing bank name", () => {
    const payeeWithoutBank = {
      ...mockPayee,
      bank: {
        name: "",
        swiftBic: "",
        nationalId: "",
        nationalIdType: "SC",
        country: {
          name: "",
          code: "",
        } as any,
      },
    }

    render(<PayeeTile payee={payeeWithoutBank} />)

    expect(screen.getByText("(...6819)")).toBeInTheDocument()
  })

  it("should handle missing account name", () => {
    const payeeWithoutName = {
      ...mockPayee,
      accountName: "",
    }

    render(<PayeeTile payee={payeeWithoutName} />)

    expect(screen.getByTestId("avatar-fallback")).toBeEmptyDOMElement()
  })

  it("should handle single word account name", () => {
    const payeeWithSingleName = {
      ...mockPayee,
      accountName: "John",
    }

    render(<PayeeTile payee={payeeWithSingleName} />)

    expect(screen.getByText("J")).toBeInTheDocument()
  })
})
