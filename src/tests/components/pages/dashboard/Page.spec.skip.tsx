import { describe, expect, it, Mock, vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { usePaymentsListQuery } from "@/data/payments/payments.query"
import { usePayeesListQuery } from "@/data/payees/payees.query"
import { useGlobalStore } from "@/data/global/global.store"
import { useAccountsListQuery } from "@/data/account/account.query"
import { DashboardPage } from "@/components/pages/dashboard/Page"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

vi.mock("@/data/account/account.query", () => ({
  useAccountsListQuery: vi.fn(),
}))

vi.mock("@/data/payees/payees.query", () => ({
  usePayeesListQuery: vi.fn(),
}))

vi.mock("@/data/payments/payments.query", () => ({
  usePaymentsListQuery: vi.fn(),
}))

vi.mock("@/components/layout/entity/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "1",
    },
  })),
}))

// vi.mock("@/data/global/global.store", () => ({
//   useGlobalStore: vi.fn(() => ({
//     entity: { id: "1" },
//   })),
// }));

vi.mock("@tanstack/react-router", () => ({
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "1",
      },
    })),
  })),
}))

vi.mock("@/components/pages/dashboard/components/AccountCarousel", () => ({
  AccountsCarousel: ({ accounts }: { accounts: any[] }) => (
    <div data-testid="accounts-carousel">
      {accounts.map((account) => (
        <div data-testid="account-item" key={account.id}>
          {account.accountName}
        </div>
      ))}
    </div>
  ),
}))

vi.mock("@/components/pages/dashboard/components/NotificationCard", () => ({
  NotificationCard: ({
    notifications,
    onClose,
  }: {
    notifications: any[]
    onClose: () => void
  }) => (
    <div data-testid="notification-card">
      {notifications.map((notification) => (
        <div data-testid="notification-item" key={notification.id}>
          {notification.message}
        </div>
      ))}
      <button onClick={onClose}>Close</button>
    </div>
  ),
}))

vi.mock("@/components/pages/dashboard/components/PayeesAndPayments", () => ({
  PayeesAndPayments: ({
    payees,
    payments,
  }: {
    payees: any[]
    payments: any[]
  }) => (
    <div data-testid="payees-and-payments">
      <div data-testid="payees-list">
        {payees.map((payee) => (
          <div data-testid="payee-item" key={payee.id}>
            {payee.accountName}
          </div>
        ))}
      </div>
      <div data-testid="payments-list">
        {payments.map((payment) => (
          <div data-testid="payment-item" key={payment.id}>
            {payment.payee?.accountName}
          </div>
        ))}
      </div>
    </div>
  ),
}))

describe("DashboardPage", () => {
  const mockAccounts = [
    { id: "1", accountName: "Account 1", virtualIban: "GB1234" },
    { id: "2", accountName: "Account 2", virtualIban: "GB5678" },
  ]

  const mockPayees = {
    items: [
      {
        id: "1",
        accountName: "Payee 1",
        type: "Individual",
        bank: { name: "Bank 1" },
        iban: "GB12345678",
      },
      {
        id: "2",
        accountName: "Payee 2",
        type: "Individual",
        bank: { name: "Bank 2" },
        iban: "GB87654321",
      },
    ],
    totalItems: 2,
  }

  const mockPayments = {
    data: [
      {
        id: "1",
        paymentType: "OutboundPayment",
        payee: {
          accountName: "Payment 1",
          type: "Individual",
          bank: { name: "Bank 1" },
        },
        amount: 1000,
        currency: "GBP",
        createdAt: "2024-02-20T14:30:00Z",
      },
      {
        id: "2",
        paymentType: "OutboundPayment",
        payee: {
          accountName: "Payment 2",
          type: "Individual",
          bank: { name: "Bank 2" },
        },
        amount: 2000,
        currency: "EUR",
        createdAt: "2024-02-20T15:30:00Z",
      },
    ],
    totalItems: 2,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render loading state", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: true,
      data: null,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: true,
      data: null,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: true,
      data: null,
    })

    render(<DashboardPage />)
    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument()
  })

  it("should render error state", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      isError: true,
      data: null,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: null,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: null,
    })

    render(<DashboardPage />)
    expect(screen.getByText("Failed to load data.")).toBeInTheDocument()
  })

  it("should render dashboard with data", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockAccounts,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayees,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayments,
    })

    render(<DashboardPage />)

    expect(screen.getByTestId("accounts-carousel")).toBeInTheDocument()
    expect(screen.getByTestId("payees-and-payments")).toBeInTheDocument()

    // Check accounts are rendered
    const accountItems = screen.getAllByTestId("account-item")
    expect(accountItems).toHaveLength(2)
    expect(accountItems[0]).toHaveTextContent("Account 1")
    expect(accountItems[1]).toHaveTextContent("Account 2")

    // Check payees are rendered
    const payeeItems = screen.getAllByTestId("payee-item")
    expect(payeeItems).toHaveLength(2)
    expect(payeeItems[0]).toHaveTextContent("Payee 1")
    expect(payeeItems[1]).toHaveTextContent("Payee 2")

    // Check payments are rendered
    const paymentItems = screen.getAllByTestId("payment-item")
    expect(paymentItems).toHaveLength(2)
    expect(paymentItems[0]).toHaveTextContent("Payment 1")
    expect(paymentItems[1]).toHaveTextContent("Payment 2")
  })

  it("should pass correct pagination parameters to queries", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockAccounts,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayees,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayments,
    })

    render(<DashboardPage />)

    expect(usePayeesListQuery).toHaveBeenCalledWith({
      entityId: "1",
      pageNumber: 1,
      pageSize: 4,
    })

    expect(usePaymentsListQuery).toHaveBeenCalledWith({
      entityId: "1",
      pageNumber: 1,
      pageSize: 10,
      variant: "default",
    })
  })

  it("should handle empty data states", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: [],
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: { items: [], totalItems: 0 },
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: { data: [], totalItems: 0 },
    })

    render(<DashboardPage />)

    expect(screen.getByTestId("accounts-carousel")).toBeInTheDocument()
    expect(screen.getByTestId("payees-and-payments")).toBeInTheDocument()
    expect(screen.queryAllByTestId("account-item")).toHaveLength(0)
    expect(screen.queryAllByTestId("payee-item")).toHaveLength(0)
    expect(screen.queryAllByTestId("payment-item")).toHaveLength(0)
  })

  it("should use entity ID from loader", () => {
    ;(useLoaderData as Mock).mockReturnValue({
      entity: {
        id: "test-entity",
      },
    })
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockAccounts,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayees,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayments,
    })

    render(<DashboardPage />)

    expect(useAccountsListQuery).toHaveBeenCalledWith()
    expect(usePayeesListQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        entityId: "test-entity",
      }),
    )
    expect(usePaymentsListQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        entityId: "test-entity",
      }),
    )
  })

  it("should handle mixed loading states", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: true,
      data: null,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayees,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayments,
    })

    render(<DashboardPage />)
    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument()
  })

  it("should handle null entity ID gracefully", () => {
    ;(useLoaderData as Mock).mockReturnValue({
      entity: {
        id: undefined,
      },
    })
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: null,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: null,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: null,
    })

    render(<DashboardPage />)

    expect(useAccountsListQuery).toHaveBeenCalledWith()
    expect(usePayeesListQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        entityId: undefined,
      }),
    )
    expect(usePaymentsListQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        entityId: undefined,
      }),
    )
  })

  it("should handle undefined data from queries", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: undefined,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: undefined,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: undefined,
    })

    render(<DashboardPage />)

    expect(screen.getByTestId("accounts-carousel")).toBeInTheDocument()
    expect(screen.getByTestId("payees-and-payments")).toBeInTheDocument()
    expect(screen.queryAllByTestId("account-item")).toHaveLength(0)
    expect(screen.queryAllByTestId("payee-item")).toHaveLength(0)
    expect(screen.queryAllByTestId("payment-item")).toHaveLength(0)
  })

  it("should handle data updates", () => {
    const initialAccounts = [...mockAccounts]
    const updatedAccounts = [
      ...mockAccounts,
      { id: "3", accountName: "Account 3", virtualIban: "GB9012" },
    ]

    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: initialAccounts,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayees,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayments,
    })

    const { rerender } = render(<DashboardPage />)
    expect(screen.getAllByTestId("account-item")).toHaveLength(2)
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      data: updatedAccounts,
    })

    rerender(<DashboardPage />)
    expect(screen.getAllByTestId("account-item")).toHaveLength(3)
  })

  it("should handle error messages from queries", () => {
    ;(useAccountsListQuery as any).mockReturnValue({
      isLoading: false,
      isError: true,
      error: new Error("Custom error message"),
      data: null,
    })
    ;(usePayeesListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayees,
    })
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: false,
      data: mockPayments,
    })

    render(<DashboardPage />)
    expect(screen.getByText("Failed to load data.")).toBeInTheDocument()
  })
})
