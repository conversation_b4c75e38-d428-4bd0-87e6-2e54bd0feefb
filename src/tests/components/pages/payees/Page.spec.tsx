import { describe, expect, vi, test } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { usePayeesListQuery } from "@/data/payees/payees.query"
import { useGlobalStore } from "@/data/global/global.store"
import { PayeePage } from "@/components/pages/payees/Page"
import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"

// Mock the route tree module to prevent the _addFileChildren error
vi.mock("@/routeTree.gen", () => ({
  routeTree: {
    _addFileChildren: vi.fn().mockReturnThis(),
    _addFileTypes: vi.fn().mockReturnThis(),
  },
  rootRoute: {
    _addFileChildren: vi.fn().mockReturnThis(),
    _addFileTypes: vi.fn().mockReturnThis(),
  },
}))

// Mock the entity loader hook
vi.mock("@/components/layout/entity/$entityId.loader", () => ({
  useLoaderData: () => ({
    entity: { id: "test-entity" },
  }),
}))

// Mock the entity access query hook
vi.mock("@/data/onboarding/entity-access.query", () => ({
  useEntityAccessQuery: vi.fn((entityId) => ({
    data: [
      {
        entityId: "test-entity",
        entityName: "Test Entity",
        entityStatus: "Active",
        roles: ["Administrator"],
      },
    ],
  })),
}))

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to }: any) => <a href={to}>{children}</a>,
  useParams: () => ({ entityId: "test-entity" }),
  useSearch: () => ({}),
  useRouter: () => ({
    navigate: vi.fn(),
  }),
  useNavigate: () => vi.fn(),
  createRootRouteWithContext: vi.fn().mockImplementation(() => {
    return vi.fn().mockReturnValue({
      component: vi.fn(),
    })
  }),
  createRouter: vi.fn().mockImplementation(() => ({
    navigate: vi.fn(),
    mount: vi.fn(),
  })),
  Route: {
    _addFileChildren: vi.fn().mockReturnThis(),
    _addFileTypes: vi.fn().mockReturnThis(),
  },
}))

// Mock all required functions from the payees query module
vi.mock("@/data/payees/payees.query", () => ({
  usePayeesListQuery: vi.fn(),
  usePayeeByIdQuery: vi.fn(() => ({
    data: null,
    isLoading: false,
    isError: false,
  })),
}))

// Mock the delete payee mutation
vi.mock("@/data/payees/payees.mutation", () => ({
  useDeletePayeeMutation: vi.fn(() => ({
    mutate: vi.fn(),
    isPending: false,
  })),
  useStartPayeeVerificationMutation: vi.fn(() => ({
    mutate: vi.fn(),
    isPending: false,
    isError: false,
    error: null,
    reset: vi.fn(),
  })),
  useVerifyPayeeMutation: vi.fn(() => ({
    mutate: vi.fn(),
    isPending: false,
    isError: false,
    error: null,
  })),
}))

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(),
}))

const mockPayees = [
  {
    id: "1",
    accountName: "John Doe",
    iban: "GB123456789",
    bank: { name: "Test Bank" },
    type: "Individual",
    status: "Active" as const,
    isScaCompleted: false,
  },
  {
    id: "2",
    accountName: "Jane Smith",
    iban: "GB987654321",
    bank: { name: "Other Bank" },
    type: "Business",
    status: "Active" as const,
    isScaCompleted: true,
  },
]

// Mock PayeeDetailsSheet component to prevent it from causing issues
vi.mock("@/components/pages/payees/components/PayeeDetailsSheet", () => ({
  default: () => <div data-testid="payee-details-sheet">Payee Details</div>,
}))

// Mock VerificationModal component
vi.mock(
  "@/components/pages/send-payments/components/modals/VerificationModal",
  () => ({
    VerificationModal: () => (
      <div data-testid="verification-modal">Verification Modal</div>
    ),
  }),
)

const queryClient = new QueryClient({
  defaultOptions: { queries: { retry: false } },
})

const renderPayeePage = () => {
  return render(
    <QueryClientProvider client={queryClient}>
      <PayeePage />
    </QueryClientProvider>,
  )
}

describe("PayeePage", () => {
  beforeEach(() => {
    queryClient.clear()
    vi.mocked(useGlobalStore).mockReturnValue({
      entity: { id: "test-entity" },
    } as any)

    vi.mocked(usePayeesListQuery).mockReturnValue({
      data: {
        items: mockPayees,
        totalItems: mockPayees.length,
      },
      isLoading: false,
      isError: false,
    } as any)
  })

  test("should render payees list", () => {
    renderPayeePage()

    expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument()
    expect(screen.getByText("Add Payee")).toBeInTheDocument()
  })

  test("should handle loading state", () => {
    vi.mocked(usePayeesListQuery).mockReturnValue({
      isLoading: true,
    } as any)

    renderPayeePage()
  })

  test("should handle error state", () => {
    vi.mocked(usePayeesListQuery).mockReturnValue({
      isError: true,
    } as any)

    renderPayeePage()
    expect(screen.getByText(/error fetching payees/i)).toBeInTheDocument()
  })

  test("should filter payees by search query", async () => {
    const user = userEvent.setup()
    renderPayeePage()

    const searchInput = screen.getByPlaceholderText(/search/i)
    await user.type(searchInput, "John")

    expect(screen.getByDisplayValue("John")).toBeInTheDocument()
  })

  test("should handle pagination", async () => {
    const user = userEvent.setup()
    vi.mocked(usePayeesListQuery).mockReturnValue({
      data: {
        items: mockPayees,
        totalItems: 20,
      },
      isLoading: false,
      isError: false,
    } as any)

    renderPayeePage()

    // Use a more reliable way to check for pagination
    const nextButton = screen.getByText(/next/i)
    expect(nextButton).toBeInTheDocument()

    await user.click(nextButton)
  })

  test("should search across all relevant fields", async () => {
    const user = userEvent.setup()
    renderPayeePage()

    const searchInput = screen.getByPlaceholderText(/search/i)
    await user.type(searchInput, "John")

    expect(screen.getByDisplayValue("John")).toBeInTheDocument()
  })
})
