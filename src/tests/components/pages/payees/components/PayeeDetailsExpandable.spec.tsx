import { describe, expect, it, vi } from "vitest"
import { toast } from "sonner"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { IPayeeData } from "@/data/payees/payees.interface"
import { PayeeDetailsExpandable } from "@/components/pages/payees/components/PayeeDetailsExpandable"

vi.mock("sonner", () => ({
  toast: vi.fn(),
}))

const mockPayee: IPayeeData = {
  id: "1",
  accountName: "<PERSON>",
  accountNumber: "********",
  iban: "**********************",
  accountDetailsConfirmed: true,
  accountDetailsConfirmedAt: "2024-01-01T00:00:00Z",
  type: "Individual",
  status: "Active",
  displayName: "John <PERSON>",
  currentStatus: "Active",
  createdAt: "2024-01-01T00:00:00Z",
  createdBy: {
    id: "1",
    email: "<EMAIL>",
    displayName: "System",
  },
  client: {
    id: "1",
    name: "Test Client",
    registrationNumber: "123456",
    address: "123 Test Street",
  },
  isScaCompleted: true,
  bank: {
    name: "Test Bank",
    nationalId: "123456",
    nationalIdType: "Sort Code",
    swiftBic: "TESTGB2L",
    country: {
      id: "1",
      name: "United Kingdom",
      formalName: "United Kingdom of Great Britain and Northern Ireland",
      codeIso2: "GB",
      codeIso3: "GBR",
      codeIso3Numeric: "826",
      phoneCode: "44",
      ibanLength: 22,
      ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
      ibanSupported: true,
      accountNumberType: "IBAN",
      nationalIdType: "SortCode",
      isSepaCountry: true,
      paymentPurposeCodeRequired: false,
      createdAt: "2024-01-01T00:00:00Z",
      createdBy: "system",
    },
  },
}

describe("PayeeDetailsExpandable", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should show copy notification when clicking copy button", async () => {
    const user = userEvent.setup()
    render(<PayeeDetailsExpandable payee={mockPayee} />)

    await user.click(screen.getByText("Bank details"))
    const copyButton = screen
      .getAllByRole("button")
      .find((btn) => btn.querySelector("svg.lucide-copy"))
    await user.click(copyButton!)

    expect(toast).toHaveBeenCalledWith(
      "Copied to clipboard",
      expect.any(Object),
    )
  })

  it("should render collapsed by default", () => {
    render(<PayeeDetailsExpandable payee={mockPayee} />)

    expect(screen.getByText("Bank details")).toBeInTheDocument()
    expect(screen.queryByText("Swift BIC")).not.toBeInTheDocument()
  })

  it.skip("should expand and show details when clicked", async () => {
    const user = userEvent.setup()
    render(<PayeeDetailsExpandable payee={mockPayee} />)

    await user.click(screen.getByText("Bank details"))

    expect(screen.getByText("Close bank details")).toBeInTheDocument()
    expect(screen.getByText("Swift BIC")).toBeInTheDocument()
    expect(screen.getByText("TESTGB22")).toBeInTheDocument()
    expect(screen.getByText("Sort code")).toBeInTheDocument()
    expect(screen.getByText("12-34-56")).toBeInTheDocument()
    expect(screen.getByText("Account number")).toBeInTheDocument()
    expect(screen.getByText("********")).toBeInTheDocument()
    expect(screen.getByText("IBAN")).toBeInTheDocument()
    expect(screen.getByText("**********************")).toBeInTheDocument()
  })

  it("should handle missing optional fields", () => {
    const payeeWithoutOptionals = {
      ...mockPayee,
      accountNumber: "",
      iban: "",
      bank: {
        ...mockPayee.bank,
        swiftBic: "",
        nationalId: "",
      },
    }

    render(<PayeeDetailsExpandable payee={payeeWithoutOptionals} />)

    const button = screen.getByRole("button")
    expect(button).toHaveTextContent("Bank details")
  })

  it("should format sort code correctly", async () => {
    const user = userEvent.setup()
    const payeeWithDifferentSortCode = {
      ...mockPayee,
      bank: {
        ...mockPayee.bank,
        nationalId: "12-34-56",
      },
    }

    render(<PayeeDetailsExpandable payee={payeeWithDifferentSortCode} />)

    await user.click(screen.getByText("Bank details"))
    expect(screen.getByText("12-34-56")).toBeInTheDocument()
  })

  it("should apply custom className", () => {
    render(
      <PayeeDetailsExpandable className="custom-class" payee={mockPayee} />,
    )

    const container = screen.getByRole("button").closest("div")
    expect(container).toHaveClass("custom-class")
  })
})
