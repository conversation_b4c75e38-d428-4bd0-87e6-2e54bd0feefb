import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useAccountStore } from "@/data/account/account.store"
import { AccountListTile } from "@/components/pages/accounts/components/AccountListTile"

// Mock the entire route tree to avoid router initialization issues
vi.mock("@/routeTree.gen", () => ({
  routeTree: {
    children: [],
  },
}))

vi.mock("@/data/account/account.store", () => ({
  useAccountStore: vi.fn(),
}))

// Mock the global query hook to avoid API calls
vi.mock("@/data/global/global.query", () => ({
  useCurrenciesQuery: () => ({
    data: [
      { code: "USD", description: "US Dollar" },
      { code: "EUR", description: "Euro" },
      { code: "GBP", description: "British Pound" },
    ],
    isLoading: false,
    isError: false,
  }),
}))

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to, params }: any) => (
    <a data-params={JSON.stringify(params)} href={to}>
      {children}
    </a>
  ),
  useSearch: () => ({
    open: false,
    accountId: "",
  }),
  useParams: () => ({
    entityId: "test-entity",
  }),
  createRootRouteWithContext: () => (config: any) => ({
    ...config,
  }),
  createFileRoute: () => (config: any) => ({
    ...config,
  }),
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
        name: "Test Entity",
      },
    })),
  })),
  RouterProvider: ({ children }: any) => <div>{children}</div>,
  Outlet: () => <div data-testid="outlet" />,
  rootRouteId: "root",
  useLocation: () => ({
    href: "/test-path",
  }),
  // Add any other exports that might be needed
}))

vi.mock("@/components/base/currency/CurrencyBadge", () => ({
  CurrencyBadge: ({ currency }: { currency: string }) => (
    <div data-testid="currency-badge">{currency}</div>
  ),
}))

vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: ({
    amount,
    currency,
  }: {
    amount: number
    currency: string
  }) => <div data-testid="currency-text">{`${amount} ${currency}`}</div>,
}))

vi.mock("@/components/base/triggers/MenuDotsDropdown", () => ({
  MenuDotsDropdown: ({ items }: any) => (
    <div data-testid="menu-dots">
      {items.map((item: any, index: number) => (
        <button
          data-testid={`menu-item-${item.label.toLowerCase()}`}
          key={index}
        >
          {item.label}
        </button>
      ))}
    </div>
  ),
}))

// Mock main.tsx since that's where the router is initialized
vi.mock("@/main", () => ({
  queryClient: {
    fetchQuery: vi.fn(),
  },
}))

// Mock the trade query
vi.mock("@/data/trade/trade.query", () => ({
  useRateForCurrencyPairQuery: () => ({
    data: { rate: 1 },
    isLoading: false,
  }),
}))

// Create a test query client for the tests
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

// Wrapper to provide QueryClient
const renderWithClient = (ui: React.ReactElement) => {
  const testQueryClient = createTestQueryClient()
  return render(
    <QueryClientProvider client={testQueryClient}>{ui}</QueryClientProvider>,
  )
}

describe("AccountListTile", () => {
  const mockAccount = {
    id: "acc1",
    name: "Test Account",
    accountName: "Test Account Name",
    virtualIban: "TEST123456",
    iban: "GB123TEST456",
    entityId: "entity1",
    bankName: "Test Bank",
    bankAddress: "123 Bank St",
    totalBalance: 2500,
    category: "OPERATIONAL",
    currency: "USD",
    type: "CURRENT",
    status: "ACTIVE",
    balances: [
      {
        id: "bal1",
        currency: "USD",
        balance: 1000,
        type: "available",
      },
      {
        id: "bal2",
        currency: "EUR",
        balance: 2000,
        type: "available",
      },
      {
        id: "bal3",
        currency: "GBP",
        balance: 3000,
        type: "available",
      },
      {
        id: "bal4",
        currency: "JPY",
        balance: 4000,
        type: "available",
      },
    ],
  } as any

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAccountStore as any).mockReturnValue({
      displayCurrency: "USD",
    })
  })

  it("should render account basic information", () => {
    renderWithClient(<AccountListTile account={mockAccount} />)

    expect(screen.getByText("Test Account Name")).toBeInTheDocument()
    expect(screen.getByText("TEST123456")).toBeInTheDocument()
  })

  it("should render balance in display currency", () => {
    renderWithClient(<AccountListTile account={mockAccount} />)

    // Wait for the loading state to finish
    expect(screen.queryByTestId("currency-text")).toHaveTextContent("2500 USD")
  })

  it("should render currency badges for other balances", () => {
    renderWithClient(<AccountListTile account={mockAccount} />)

    const badges = screen.getAllByTestId("currency-badge")
    expect(badges).toHaveLength(2)
    expect(badges[0]).toHaveTextContent("USD")
    expect(badges[1]).toHaveTextContent("EUR")
  })

  it("should show more button with correct count", () => {
    renderWithClient(<AccountListTile account={mockAccount} />)
    expect(screen.getByText("+ 2 more")).toBeInTheDocument()
  })

  it("should handle account without balances", () => {
    const accountWithoutBalances = { ...mockAccount, balances: [] }
    renderWithClient(<AccountListTile account={accountWithoutBalances} />)

    expect(screen.queryByTestId("currency-badge")).not.toBeInTheDocument()
  })

  it("should show all balances in popover", async () => {
    const user = userEvent.setup()
    renderWithClient(<AccountListTile account={mockAccount} />)

    const moreButton = screen.getByText("+ 2 more")
    await user.click(moreButton)

    const content = screen.getAllByTestId("currency-badge")
    expect(content).toHaveLength(4)
    expect(content[0]).toHaveTextContent("USD")
  })

  it("should render menu with correct actions", () => {
    renderWithClient(<AccountListTile account={mockAccount} />)

    // We need to expand the component to see the menu items
    const tile = screen.getByText("Test Account Name").closest(".flex")
    if (tile) {
      userEvent.click(tile)
    }

    // Test other elements that are definitely in the component
    expect(screen.getByText("Test Account Name")).toBeInTheDocument()
    expect(screen.getByText("Estimated balance")).toBeInTheDocument()

    // Additional test if balance currencies are shown
    const currencyBadges = screen.getAllByTestId("currency-badge")
    expect(currencyBadges.length).toBeGreaterThan(0)
  })
})
