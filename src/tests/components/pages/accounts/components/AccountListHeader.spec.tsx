import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { useAccountStore } from "@/data/account/account.store"
import { Account } from "@/data/account/account.interface"
import { AccountListHeader } from "@/components/pages/accounts/components/AccountListHeader"

vi.mock("@/data/account/account.store", () => ({
  useAccountStore: vi.fn(),
}))

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to, params }: any) => (
    <a data-params={JSON.stringify(params)} href={to}>
      {children}
    </a>
  ),
}))

vi.mock("@/components/base/currency/CurrencySelector", () => ({
  CurrencySelector: ({ currency, onChange }: any) => (
    <select
      data-testid="currency-selector"
      onChange={(e) => onChange(e.target.value)}
      value={currency}
    >
      <option value="USD">USD</option>
      <option value="EUR">EUR</option>
    </select>
  ),
}))

describe("AccountListHeader", () => {
  const mockSetDisplayCurrency = vi.fn()
  const mockAccounts: Account[] = [
    {
      id: "1",
      accountName: "Test Account",
      name: "Test Account",
      virtualIban: "VIBAN123456",
      iban: "IBAN123456789",
      entityId: "entity1",
      totalBalance: 1000,
      category: "Business",
      bankName: "Test Bank",
      bankAddress: "123 Bank Street",
      balances: [
        { id: "balance-1", currency: "USD", balance: 1000 },
        { id: "balance-2", currency: "EUR", balance: 850 },
      ],
      clientAccountId: "client1",
      clientId: "client1",
      totalCurrency: "USD",
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAccountStore as any).mockReturnValue({
      displayCurrency: "USD",
      setDisplayCurrency: mockSetDisplayCurrency,
    })
  })

  it("should render currency selector with current display currency", () => {
    render(<AccountListHeader accounts={mockAccounts} entityId="entity1" />)

    const currencySelector = screen.getByTestId("currency-selector")
    expect(currencySelector).toBeInTheDocument()
    expect(currencySelector).toHaveValue("USD")
  })

  it("should handle currency change", async () => {
    const user = userEvent.setup()
    render(<AccountListHeader accounts={mockAccounts} entityId="entity1" />)

    const currencySelector = screen.getByTestId("currency-selector")
    await user.selectOptions(currencySelector, "EUR")

    expect(mockSetDisplayCurrency).toHaveBeenCalledWith("EUR")
  })

  it("should display currency label", () => {
    render(<AccountListHeader accounts={mockAccounts} entityId="entity1" />)

    expect(
      screen.getByText("Display estimated account balances in"),
    ).toBeInTheDocument()
  })
})
