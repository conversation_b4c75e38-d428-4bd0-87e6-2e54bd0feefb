import { describe, expect, it } from "vitest"
import { render, screen } from "@testing-library/react"

import { AccountsPage } from "@/components/pages/accounts/Page"

vi.mock("@/components/pages/accounts/components/AccountListHeader", () => ({
  AccountListHeader: ({ entityId }: { entityId: string }) => (
    <div data-testid="account-list-header">Header: {entityId}</div>
  ),
}))

vi.mock("@/components/pages/accounts/components/AccountListTile", () => ({
  AccountListTile: ({ account }: { account: any }) => (
    <div data-testid="account-list-tile">Account: {account.accountName}</div>
  ),
}))

describe("AccountsPage", () => {
  const mockAccounts = [
    {
      id: "acc1",
      name: "Test Account",
      accountName: "Test Account 1",
      virtualIban: "TEST123456",
      iban: "GB123TEST456",
      entityId: "entity1",
      bankName: "Test Bank",
      bankAddress: "123 Bank St",
      totalBalance: 2500,
      category: "OPERATIONAL",
      currency: "USD",
      type: "CURRENT",
      status: "ACTIVE",
      balances: [
        {
          id: "bal1",
          currency: "USD",
          balance: 1000,
          amount: 1000,
          type: "available",
        },
      ],
    },
    {
      id: "acc2",
      name: "Test Account 2",
      accountName: "Test Account 2",
      virtualIban: "TEST789012",
      iban: "GB789TEST012",
      entityId: "entity1",
      bankName: "Test Bank",
      bankAddress: "123 Bank St",
      totalBalance: 1500,
      category: "OPERATIONAL",
      currency: "EUR",
      type: "CURRENT",
      status: "ACTIVE",
      balances: [],
    },
  ] as any

  it("should render nothing when no accounts are provided", () => {
    render(<AccountsPage entityId="entity1" />)
    expect(screen.queryByTestId("account-list-header")).not.toBeInTheDocument()
    expect(screen.queryByTestId("account-list-tile")).not.toBeInTheDocument()
  })

  it("should render header and account tiles when accounts are provided", () => {
    render(<AccountsPage accounts={mockAccounts} entityId="entity1" />)

    expect(screen.getByTestId("account-list-header")).toBeInTheDocument()
    expect(screen.getByText("Header: entity1")).toBeInTheDocument()

    const accountTiles = screen.getAllByTestId("account-list-tile")
    expect(accountTiles).toHaveLength(2)
    expect(screen.getByText("Account: Test Account 1")).toBeInTheDocument()
    expect(screen.getByText("Account: Test Account 2")).toBeInTheDocument()
  })

  it("should pass correct props to child components", () => {
    render(<AccountsPage accounts={mockAccounts} entityId="entity1" />)

    expect(screen.getByText("Header: entity1")).toBeInTheDocument()

    const accountTiles = screen.getAllByTestId("account-list-tile")
    expect(accountTiles[0]).toHaveTextContent("Test Account 1")
    expect(accountTiles[1]).toHaveTextContent("Test Account 2")
  })
})
