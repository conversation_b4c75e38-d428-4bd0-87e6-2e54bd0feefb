import { describe, expect, it, vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { ErrorRow } from "@/components/pages/bulk-payment/components/ErrorRow"

describe("ErrorRow", () => {
  const defaultProps = {
    rowNumber: 23,
    payee: "Andy Capp",
    error: "Invalid SWIFT/BIC, Invalid payment type",
    onResolve: vi.fn(),
  }

  it("renders row data correctly", () => {
    render(
      <table>
        <tbody>
          <ErrorRow {...defaultProps} />
        </tbody>
      </table>,
    )

    expect(screen.getByText(defaultProps.rowNumber)).toBeInTheDocument()
    expect(screen.getByText(defaultProps.payee)).toBeInTheDocument()
    expect(screen.getByText(defaultProps.error)).toBeInTheDocument()
    expect(screen.getByText("Resolve")).toBeInTheDocument()
  })

  it("calls onResolve when resolve button is clicked", () => {
    render(
      <table>
        <tbody>
          <ErrorRow {...defaultProps} />
        </tbody>
      </table>,
    )

    const resolveButton = screen.getByText("Resolve")
    fireEvent.click(resolveButton)

    expect(defaultProps.onResolve).toHaveBeenCalledTimes(1)
  })

  it("hides resolve button by default and shows on row hover", () => {
    render(
      <table>
        <tbody>
          <ErrorRow {...defaultProps} />
        </tbody>
      </table>,
    )

    const resolveButton = screen.getByText("Resolve")
    expect(resolveButton).toHaveClass("opacity-0")

    // Find the parent row element
    const row = resolveButton.closest("tr")
    expect(row).toHaveClass("group")

    // Verify button becomes visible on hover
    fireEvent.mouseEnter(row!)
    expect(resolveButton).toHaveClass("group-hover:opacity-100")
  })

  it("renders without onResolve handler", () => {
    const { onResolve, ...propsWithoutHandler } = defaultProps
    render(
      <table>
        <tbody>
          <ErrorRow {...propsWithoutHandler} />
        </tbody>
      </table>,
    )

    const resolveButton = screen.getByText("Resolve")
    fireEvent.click(resolveButton)
    // Should not throw error when clicking without handler
  })
})
