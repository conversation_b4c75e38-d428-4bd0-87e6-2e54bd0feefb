import { describe, expect, it, vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { IPendingPayment } from "@/data/payments/payments.interface"
import { ResolveErrorsTable } from "@/components/pages/bulk-payment/components/ResolveErrorsTable"

describe("ResolveErrorsTable", () => {
  const mockErrorData: IPendingPayment[] = [
    {
      id: "1",
      rowNumber: 1,
      status: "Error",
      paymentType: "BulkPayment",
      clientId: "client1",
      originatingAccountIban: "GB**********",
      payeeAccountName: "Andy Capp",
      payeeAccountNumber: "**********",
      paymentAmount: "100",
      paymentCurrency: "GBP",
      paymentReference: "REF001",
      paymentPurpose: "Transfer",
      paymentDate: "2024-03-20",
      payeeIBAN: "GB9876543210",
      payeeSWIFTBIC: "ABCD1234",
      payeeCountry: "GB",
      payeeCity: "London",
      payeePostalCode: "SW1A 1AA",
      payeeStreetName: "Downing Street",
      payeeBuildingNumber: "10",
      payeeType: "Individual",
      payeeLocalBankCode: "123456",
      payeeRegionState: "England",
      payeeNationality: "GB",
      validationErrors: [
        {
          type: "payeeSWIFTBIC",
          message: "Invalid SWIFT/BIC, Invalid payment type",
        },
      ],
      createdAt: "2024-03-20T10:00:00Z",
    },
    {
      id: "2",
      rowNumber: 2,
      status: "Error",
      paymentType: "BulkPayment",
      clientId: "client1",
      originatingAccountIban: "GB**********",
      payeeAccountName: "Jane Smith",
      payeeAccountNumber: "**********",
      paymentAmount: "200",
      paymentCurrency: "GBP",
      paymentReference: "REF002",
      paymentPurpose: "Transfer",
      paymentDate: "2024-03-20",
      payeeIBAN: "GB**********",
      payeeSWIFTBIC: "EFGH5678",
      payeeCountry: "GB",
      payeeCity: "Manchester",
      payeePostalCode: "M1 1AA",
      payeeStreetName: "Deansgate",
      payeeBuildingNumber: "100",
      payeeType: "Individual",
      payeeLocalBankCode: "654321",
      payeeRegionState: "England",
      payeeNationality: "GB",
      validationErrors: [
        { type: "payeeAccountNumber", message: "Invalid account number" },
      ],
      createdAt: "2024-03-20T10:00:00Z",
    },
  ]

  // All payments and errors are the same in this test case
  const mockPayments = [...mockErrorData]

  const defaultProps = {
    payments: mockPayments,
    errors: mockErrorData,
    onResolve: vi.fn(),
  }

  it("renders table headers correctly", () => {
    render(<ResolveErrorsTable {...defaultProps} />)

    expect(screen.getByText("Row")).toBeInTheDocument()
    expect(screen.getByText("Payee")).toBeInTheDocument()
    expect(screen.getByText("Error")).toBeInTheDocument()
  })

  it("displays correct error count in title", () => {
    render(<ResolveErrorsTable {...defaultProps} />)

    expect(
      screen.getByText(
        `Errors ${mockErrorData.length} out of ${mockPayments.length} payments`,
      ),
    ).toBeInTheDocument()
  })

  it("renders all error rows", () => {
    render(<ResolveErrorsTable {...defaultProps} />)

    mockErrorData.forEach((error) => {
      expect(screen.getByText(error.payeeAccountName)).toBeInTheDocument()
      expect(
        screen.getByText(error.validationErrors[0].message),
      ).toBeInTheDocument()
      expect(screen.getByText(error.rowNumber.toString())).toBeInTheDocument()
    })
  })

  it("should render table with errors", () => {
    render(
      <ResolveErrorsTable errors={mockErrorData} payments={mockPayments} />,
    )

    expect(
      screen.getByText(
        `Errors ${mockErrorData.length} out of ${mockPayments.length} payments`,
      ),
    ).toBeInTheDocument()
    expect(screen.getByText("Andy Capp")).toBeInTheDocument()
    expect(screen.getByText("Jane Smith")).toBeInTheDocument()
    expect(
      screen.getByText("Invalid SWIFT/BIC, Invalid payment type"),
    ).toBeInTheDocument()
    expect(screen.getByText("Invalid account number")).toBeInTheDocument()
  })

  it("should handle error resolution", () => {
    const onResolve = vi.fn()
    render(
      <ResolveErrorsTable
        errors={mockErrorData}
        onResolve={onResolve}
        payments={mockPayments}
      />,
    )

    const resolveButtons = screen.getAllByRole("button", { name: /resolve/i })
    fireEvent.click(resolveButtons[0])

    expect(onResolve).toHaveBeenCalledWith(1)
  })
})
