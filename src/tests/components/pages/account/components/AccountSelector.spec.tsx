import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { AccountSelector } from "@/components/pages/account/components/AccountSelector"

vi.mock("@/components/ui/select", () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select">
      {typeof children === "function" ? children({ value }) : children}
      <button onClick={() => onValueChange("TEST789012")}>Change Value</button>
    </div>
  ),
  SelectTrigger: ({ children, className }: any) => (
    <button className={className} role="combobox">
      {children}
    </button>
  ),
  SelectValue: ({ children, asChild, placeholder }: any) =>
    asChild ? children : <span>{placeholder || children}</span>,
  SelectContent: ({ children }: any) => (
    <div data-testid="select-content">{children}</div>
  ),
  SelectItem: ({ children, value }: any) => (
    <div data-value={value} onClick={() => {}} role="option">
      {children}
    </div>
  ),
}))

describe("AccountSelector", () => {
  const mockAccounts = [
    {
      id: "acc1",
      name: "Test Account",
      accountName: "Test Account Name",
      virtualIban: "TEST123456",
      iban: "GB123TEST456",
      entityId: "entity1",
      bankName: "Test Bank",
      bankAddress: "123 Bank St",
      totalBalance: 2500,
      category: "OPERATIONAL",
      balances: [],
      clientAccountId: "client_acc_1",
      clientId: "client_1",
      totalCurrency: "USD" as const,
    },
    {
      id: "acc2",
      name: "Second Account",
      accountName: "",
      virtualIban: "TEST789012",
      iban: "GB789TEST012",
      entityId: "entity1",
      bankName: "Test Bank",
      bankAddress: "123 Bank St",
      totalBalance: 1500,
      category: "OPERATIONAL",
      balances: [],
      clientAccountId: "client_acc_2",
      clientId: "client_1",
      totalCurrency: "EUR" as const,
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render with placeholder when no account is selected", () => {
    render(
      <AccountSelector accounts={mockAccounts} onChange={() => {}} value="" />,
    )

    expect(screen.getByText("Select an account")).toBeInTheDocument()
  })

  it("should render selected account details", () => {
    render(
      <AccountSelector
        accounts={mockAccounts}
        onChange={() => {}}
        value="TEST123456"
      />,
    )

    const accountNames = screen.getAllByText("Test Account Name")
    expect(accountNames.length).toBeGreaterThan(0)

    const ibanTexts = screen.getAllByText("(..3456)")
    expect(ibanTexts.length).toBeGreaterThan(0)
  })

  it("should display empty account name for accounts without accountName", () => {
    render(
      <AccountSelector
        accounts={mockAccounts}
        onChange={() => {}}
        value="TEST789012"
      />,
    )

    const emptyNameSpan = screen
      .getAllByRole("option")[1]
      .querySelector("span.font-medium")

    const ibanTexts = screen.getAllByText("(..9012)")
    expect(ibanTexts.length).toBeGreaterThan(0)
  })

  it("should render all accounts in dropdown", async () => {
    const user = userEvent.setup()

    render(
      <AccountSelector
        accounts={mockAccounts}
        onChange={() => {}}
        value="TEST123456"
      />,
    )

    await user.click(screen.getByRole("combobox"))

    const options = screen.getAllByRole("option")
    expect(options).toHaveLength(2)

    const firstOption = options[0]
    expect(firstOption).toHaveAttribute("data-value", "TEST123456")

    const accountNames = screen.getAllByText("Test Account Name")
    expect(accountNames.length).toBeGreaterThan(0)

    const firstIbanTexts = screen.getAllByText("(..3456)")
    expect(firstIbanTexts.length).toBeGreaterThan(0)

    const secondOption = options[1]
    expect(secondOption).toHaveAttribute("data-value", "TEST789012")

    const secondIbanTexts = screen.getAllByText("(..9012)")
    expect(secondIbanTexts.length).toBeGreaterThan(0)
  })
})
