import { describe, expect, it, vi } from "vitest"
import { toast } from "sonner"
import userEvent from "@testing-library/user-event"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"

import { AccountDetailsExpandable } from "@/components/pages/account/components/AccountDetailsExpandable"

vi.mock("sonner", () => ({
  toast: vi.fn(),
}))

describe("AccountDetailsExpandable", () => {
  const mockAccount = {
    id: "1",
    name: "Test Account",
    accountName: "Test Account Name",
    virtualIban: "GB123456789",
    iban: "GB123456789",
    entityId: "entity-1",
    bankName: "Test Bank",
    bankAddress: "Test Address",
    totalBalance: 1000,
    category: "Test Category",
    balances: [],
    clientAccountId: "client-account-1",
    clientId: "client-1",
    totalCurrency: "USD" as const,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render collapsed by default", () => {
    render(<AccountDetailsExpandable account={mockAccount} />)

    expect(screen.getByText("Show account details")).toBeInTheDocument()
    expect(screen.queryByText("Account name")).not.toBeInTheDocument()
  })

  it("should expand and collapse when clicked", async () => {
    const user = userEvent.setup()
    render(<AccountDetailsExpandable account={mockAccount} />)

    await user.click(screen.getByText("Show account details"))
    expect(screen.getByText("Close account details")).toBeInTheDocument()
    expect(screen.getByText("Account name")).toBeInTheDocument()
    expect(screen.getByText("Test Account Name")).toBeInTheDocument()

    await user.click(screen.getByText("Close account details"))
    expect(screen.getByText("Show account details")).toBeInTheDocument()
    expect(screen.queryByText("Account name")).not.toBeInTheDocument()
  })

  it("should display all account details when expanded", async () => {
    const user = userEvent.setup()
    render(<AccountDetailsExpandable account={mockAccount} />)

    await user.click(screen.getByText("Show account details"))

    const details = [
      { label: "Account name", value: mockAccount.accountName },
      { label: "IBAN", value: mockAccount.virtualIban },
      { label: "Bank name", value: mockAccount.bankName },
      { label: "Bank address", value: mockAccount.bankAddress },
    ]

    details.forEach(({ label, value }) => {
      expect(screen.getByText(label)).toBeInTheDocument()
      expect(screen.getByText(value)).toBeInTheDocument()
    })
  })

  it("should copy value to clipboard when copy button is clicked", async () => {
    const user = userEvent.setup()
    const mockClipboard = vi.fn()

    vi.spyOn(navigator.clipboard, "writeText").mockImplementation(mockClipboard)

    render(<AccountDetailsExpandable account={mockAccount} />)
    await user.click(screen.getByText("Show account details"))

    const copyButtons = screen.getAllByRole("button", { name: "" })
    await user.click(copyButtons[0])

    expect(mockClipboard).toHaveBeenCalledWith(mockAccount.accountName)
    expect(toast).toHaveBeenCalledWith("Copied to clipboard", {
      description: `Account name: ${mockAccount.accountName}`,
      icon: "📋",
      duration: 1000,
    })
  })

  it("should apply custom className", () => {
    const { container } = render(
      <AccountDetailsExpandable
        account={mockAccount}
        className="custom-class"
      />,
    )

    expect(container.firstChild).toHaveClass(
      "custom-class",
      "flex",
      "flex-col",
      "items-end",
      "w-full",
      "sm:w-[300px]",
    )
  })

  it("should truncate long values", async () => {
    const user = userEvent.setup()
    const longValueAccount = {
      ...mockAccount,
      bankAddress: "A".repeat(200),
    }

    render(<AccountDetailsExpandable account={longValueAccount} />)
    await user.click(screen.getByText("Show account details"))

    const valueContainer = screen.getByText("A".repeat(200)).parentElement
    expect(valueContainer?.querySelector(".truncate")).toBeTruthy()
  })
})
