import { describe, expect, it, vi } from "vitest"
import { render, screen } from "@testing-library/react"

import { AccountBalanceTile } from "@/components/pages/account/components/AccountBalanceTile"

vi.mock("@/components/base/currency/CurrencyFlag", () => ({
  CurrencyFlag: ({ currency }: { currency: string }) => (
    <div data-testid="currency-flag">{currency}</div>
  ),
}))

vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: ({
    amount,
    currency,
  }: {
    amount: number
    currency: string
  }) => <div data-testid="currency-text">{`${amount} ${currency}`}</div>,
}))

vi.mock("@/components/base/currency/CurrencyName", () => ({
  CurrencyName: ({ currency }: { currency: string }) => (
    <div data-testid="currency-name">{currency}</div>
  ),
}))

vi.mock("@/components/base/triggers/MenuDotsDropdown", () => ({
  MenuDotsDropdown: () => <div data-testid="menu-dots">Menu</div>,
}))

vi.mock("@tanstack/react-router", () => ({
  useLocation: () => ({
    href: "/test-entity/payments/send",
  }),
}))

describe("AccountBalanceTile", () => {
  const mockBalance = {
    id: "1",
    amount: 1000,
    balance: 1000,
    currency: "USD",
    type: "available",
  } as any

  const mockAccount = {
    id: "1",
    name: "Test Account",
    accountName: "Test Account",
    virtualIban: "GB123456789",
    entityId: "entity1",
    type: "CURRENT",
    status: "ACTIVE",
  } as any

  it("should render balance information correctly", () => {
    render(
      <AccountBalanceTile
        account={
          {
            id: "1",
            name: "Test Account",
            accountName: "Test Account",
            virtualIban: "GB123456789",
            entityId: "entity1",
            status: "ACTIVE",
            balances: [],
            totalBalance: 1000,
            category: "BUSINESS",
            iban: "GB123456789",
            currency: "USD",
            country: "GB",
          } as any
        }
        balance={{
          id: "1",
          balance: 1000,
          currency: "USD" as const,
        }}
        entityId="entity1"
      />,
    )

    expect(screen.getByTestId("currency-flag")).toHaveTextContent("USD")
    expect(screen.getByTestId("currency-text")).toHaveTextContent("1000 USD")
    expect(screen.getByTestId("currency-name")).toHaveTextContent("USD")

    expect(screen.getByTestId("menu-dots")).toBeInTheDocument()
  })

  it("should apply correct layout classes", () => {
    const { container } = render(
      <AccountBalanceTile
        account={mockAccount}
        balance={mockBalance}
        entityId="entity1"
      />,
    )

    expect(container.firstChild).toHaveClass(
      "flex",
      "items-center",
      "justify-between",
    )
  })
})
