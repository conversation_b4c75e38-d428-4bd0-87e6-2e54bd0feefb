import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { useNavigate } from "@tanstack/react-router"

import AccountPage from "@/components/pages/account/Page"

vi.mock("@tanstack/react-router", () => {
  const navigate = vi.fn()
  return {
    useNavigate: () => navigate,
    Link: () => null,
  }
})

vi.mock("@/components/pages/account/components/AccountSelector", () => ({
  AccountSelector: ({
    value,
    onChange,
  }: {
    value: string
    onChange: (value: string) => void
  }) => (
    <select
      data-testid="account-selector"
      onChange={(e) => onChange(e.target.value)}
      value={value}
    >
      <option value="acc1">Test Account</option>
      <option value="acc2">Second Account</option>
    </select>
  ),
}))

vi.mock("@/components/pages/account/components/AccountBalanceTile", () => ({
  AccountBalanceTile: ({
    balance,
  }: {
    balance: { type: string; amount: number; currency: string }
  }) => (
    <div data-testid="balance-tile">
      <span>{balance.type}</span>
      <span>{balance.amount}</span>
      <span>{balance.currency}</span>
    </div>
  ),
}))

describe("AccountPage", () => {
  const mockAccount: any = {
    id: "acc1",
    name: "Test Account",
    accountName: "Test Account",
    virtualIban: "TEST123456",
    iban: "GB123TEST456",
    entityId: "entity1",
    bankName: "Test Bank",
    bankAddress: "123 Bank St",
    totalBalance: 2500,
    category: "OPERATIONAL",
    currency: "USD",
    type: "CURRENT",
    status: "ACTIVE",
    balances: [
      {
        id: "bal1",
        amount: 1000,
        balance: 1000,
        currency: "USD",
        type: "available",
      },
      {
        id: "bal2",
        amount: 1500,
        balance: 1500,
        currency: "USD",
        type: "current",
      },
    ],
  }

  const mockAccounts: any = [
    mockAccount,
    {
      ...mockAccount,
      id: "acc2",
      name: "Second Account",
      accountName: "Second Account",
      virtualIban: "TEST789012",
      totalBalance: 0,
      balances: [],
    },
  ]

  it("renders nothing when no account is provided", () => {
    const { container } = render(
      <AccountPage
        accountId="acc1"
        accounts={mockAccounts}
        entityId="entity1"
      />,
    )
    expect(container.firstChild).toBeNull()
  })

  it("renders account details when account is provided", () => {
    render(
      <AccountPage
        account={mockAccount}
        accountId="acc1"
        accounts={mockAccounts}
        entityId="entity1"
      />,
    )

    expect(screen.getByText("Balances")).toBeInTheDocument()
    mockAccount.balances?.forEach((balance: any) => {
      const balanceTile = screen.getByText(balance.type)
      expect(balanceTile).toBeInTheDocument()
      expect(screen.getByText(balance.amount.toString())).toBeInTheDocument()
    })
  })

  it("renders without accounts array", () => {
    render(
      <AccountPage account={mockAccount} accountId="acc1" entityId="entity1" />,
    )

    expect(screen.getByText("Balances")).toBeInTheDocument()
    expect(screen.queryByRole("combobox")).not.toBeInTheDocument()
  })

  it("handles account with no balances", () => {
    const accountWithNoBalances = {
      ...mockAccount,
      id: "acc3",
      name: "No Balance Account",
      accountName: "No Balance Account",
      balances: [],
    }

    render(
      <AccountPage
        account={accountWithNoBalances}
        accountId="acc3"
        entityId="entity1"
      />,
    )

    expect(screen.getByText("Balances")).toBeInTheDocument()
    expect(
      screen.queryByText("available", { exact: false }),
    ).not.toBeInTheDocument()
    expect(
      screen.queryByText("current", { exact: false }),
    ).not.toBeInTheDocument()
  })
})
