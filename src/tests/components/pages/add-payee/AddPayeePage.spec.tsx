import { describe, expect, it, vi } from "vitest"
import { toast } from "sonner"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { AddPayeePage } from "@/components/pages/add-payee/AddPayeePage"

vi.mock("@/components/pages/add-payee/components/AddPayeeStepper", () => ({
  AddPayeeStepper: (props: any) => (
    <div data-testid="stepper">
      <button onClick={() => props.onComplete({ id: "test-id" })}>Next</button>
      {props.onClose && <button onClick={props.onClose}>Close</button>}
    </div>
  ),
}))

vi.mock("@/components/pages/add-payee/components/PayeeSuccessPage", () => ({
  PayeeSuccessPage: (props: any) => (
    <div data-testid="success">
      <div>Success: {props.payeeId}</div>
      <button onClick={props.onClose}>Done</button>
    </div>
  ),
}))

// Mock the router
vi.mock("@tanstack/react-router", () => ({
  useRouter: () => ({
    navigate: vi.fn(),
  }),
}))

// Mock sonner toast
vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
  },
}))

describe("AddPayeePage", () => {
  const user = userEvent.setup()

  it("should show stepper and handle payee creation flow", async () => {
    const onPayeeAdded = vi.fn()
    render(<AddPayeePage entityId="123" onPayeeAdded={onPayeeAdded} />)

    expect(screen.getByTestId("add-payee-page")).toBeInTheDocument()

    await user.click(screen.getByText("Next"))

    expect(onPayeeAdded).toHaveBeenCalledWith("test-id")
  })

  it("should handle modal close", async () => {
    const onClose = vi.fn()
    render(<AddPayeePage entityId="123" modal onClose={onClose} />)

    await user.click(screen.getByText("Next"))
    expect(onClose).toHaveBeenCalled()
  })
})
