import { vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { PayeeSuccessPage } from "@/components/pages/add-payee/components/PayeeSuccessPage"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, onClick }: any) => (
    <button onClick={onClick}>{children}</button>
  ),
  useParams: () => ({ entityId: "test-entity" }),
  useLocation: () => ({
    href: "/test-entity/payments/send",
  }),
}))

describe("PayeeSuccessPage", () => {
  it("renders success message", () => {
    render(<PayeeSuccessPage entityId="test-entity" />)

    expect(screen.getByText("New payee added successfully")).toBeInTheDocument()
    expect(
      screen.getByText("The payee has been added to your list of payees."),
    ).toBeInTheDocument()
  })

  it("renders non-modal buttons correctly", () => {
    render(<PayeeSuccessPage entityId="test-entity" payeeId="test-payee" />)

    expect(screen.getByText("Send payment to payee")).toBeInTheDocument()
    expect(screen.getByText("Finish")).toBeInTheDocument()
  })

  it("renders modal button and handles close", async () => {
    const onClose = vi.fn()
    const user = userEvent.setup()

    render(
      <PayeeSuccessPage
        entityId="test-entity"
        modal={true}
        onClose={onClose}
      />,
    )

    const continueButton = screen.getByText("Continue to payment")
    await user.click(continueButton)

    expect(onClose).toHaveBeenCalledTimes(1)
  })
})
