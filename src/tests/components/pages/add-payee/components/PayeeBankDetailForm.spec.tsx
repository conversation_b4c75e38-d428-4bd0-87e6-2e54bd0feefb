import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, waitFor, cleanup } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { PayeeBankDetailForm } from "@/components/pages/add-payee/components/PayeeBankDetailForm"

vi.mock("@tanstack/react-router", () => ({
  useParams: () => ({ entityId: "test-entity" }),
  useRouter: () => ({
    navigate: vi.fn(),
  }),
}))

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}
window.ResizeObserver = ResizeObserver

vi.mock("react-dom/client", async () => {
  const createRoot = vi.fn((container) => ({
    render: vi.fn(),
    unmount: vi.fn(),
  }))
  return {
    createRoot,
    default: {
      createRoot,
    },
  }
})

vi.mock("@/data/global/global.query", () => ({
  useCountriesQuery: () => ({
    data: [
      {
        name: "United Kingdom",
        code: "GB",
        formalName: "United Kingdom of Great Britain and Northern Ireland",
        codeIso2: "GB",
        codeIso3: "GBR",
        codeIso3Numeric: "826",
        phoneCode: "44",
        nationalIdType: "SORT_CODE",
        accountNumberType: "BBAN",
      },
      {
        name: "United States",
        code: "US",
        formalName: "United States of America",
        codeIso2: "US",
        codeIso3: "USA",
        codeIso3Numeric: "840",
        phoneCode: "1",
        nationalIdType: "ROUTING_NUMBER",
        accountNumberType: "BBAN",
      },
    ],
    isLoading: false,
  }),
}))

vi.mock("@/data/payees/payees.api", () => ({
  fetchShortCode: vi.fn(async (code: string) => {
    if (code === "123456") {
      return {
        bankName: "Test Bank",
        branchName: "Test Branch",
        address: "123 Test St",
      }
    }
    throw new Error("Invalid sort code")
  }),
}))

vi.mock("@/lib/form.utils", () => ({
  resetFieldMeta: vi.fn(),
}))

// Mock the entire command.tsx module instead of the primitive components
vi.mock("@/components/ui/command", () => {
  const Command = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="command-root">{children}</div>
  )
  const CommandInput = (props: any) => <input {...props} />
  const CommandList = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="command-list">{children}</div>
  )
  const CommandEmpty = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="command-empty">{children}</div>
  )
  const CommandGroup = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="command-group">{children}</div>
  )
  const CommandItem = ({
    children,
    ...props
  }: { children: React.ReactNode } & any) => (
    <div data-testid="command-item" role="option" {...props}>
      {children}
    </div>
  )
  const CommandSeparator = () => <div data-testid="command-separator" />

  return {
    Command,
    CommandInput,
    CommandList,
    CommandEmpty,
    CommandGroup,
    CommandItem,
    CommandSeparator,
  }
})

// Remove the other command-related mocks
vi.mock("cmdk", () => ({}))
vi.mock("@radix-ui/react-command", () => ({}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

describe("PayeeBankDetailForm", () => {
  it("should render form with default values", () => {
    const onNext = vi.fn()
    renderWithQueryClient(
      <PayeeBankDetailForm entityId="test-entity-id" onNext={onNext} />,
    )

    expect(screen.getByText("Payee is a business")).toBeInTheDocument()
    expect(screen.getByText("Country of payee bank")).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Next" })).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument()
  })

  it("should handle business switch toggle", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    renderWithQueryClient(
      <PayeeBankDetailForm entityId="test-entity-id" onNext={onNext} />,
    )

    const businessSwitch = screen.getByRole("switch")
    expect(businessSwitch).toBeChecked()

    await user.click(businessSwitch)
    expect(businessSwitch).not.toBeChecked()

    expect(screen.getByText("Full name on account")).toBeInTheDocument()
  })

  it("should handle form reset on cancel", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    const onInvalid = vi.fn()
    renderWithQueryClient(
      <PayeeBankDetailForm
        entityId="test-entity-id"
        onInvalid={onInvalid}
        onNext={onNext}
      />,
    )

    // Wait for form to be initialized
    await waitFor(() => {
      expect(screen.getByText("Name on account")).toBeInTheDocument()
    })

    // Fill some fields
    const countryInput = screen.getByRole("button", {
      name: "Select a country",
    })
    await user.click(countryInput)
    await user.click(screen.getByText("United Kingdom (GB)"))

    // Find input by its parent div that contains the label
    const nameLabel = screen.getByText("Name on account")
    const accountNameInput = screen.getByTestId("account-name-input")
    expect(accountNameInput).toBeInTheDocument()

    await user.type(accountNameInput, "Test Account")

    // Click cancel
    const cancelButton = screen.getByRole("button", { name: "Cancel" })
    await user.click(cancelButton)

    // Verify form reset
    await waitFor(() => {
      expect(onInvalid).toHaveBeenCalled()
    })

    // Clean up the previous render
    cleanup()

    // Re-render the form to verify reset
    renderWithQueryClient(
      <PayeeBankDetailForm entityId="test-entity-id" onNext={onNext} />,
    )

    // Check that the input is empty after re-render
    const resetInput = screen.getByTestId("account-name-input")
    expect(resetInput).toHaveValue("")
  })
})
