import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { PayeeDetailsForm } from "@/components/pages/add-payee/components/PayeesDetailsForm"

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}
window.ResizeObserver = ResizeObserver

vi.mock("react-dom/client", async () => {
  const createRoot = vi.fn((container) => ({
    render: vi.fn(),
    unmount: vi.fn(),
  }))
  return {
    createRoot,
    default: {
      createRoot,
    },
  }
})

vi.mock("@/data/global/global.query", () => ({
  useCountriesQuery: () => ({
    data: [
      {
        id: "1",
        name: "United Kingdom",
        code: "GB",
        formalName: "United Kingdom of Great Britain and Northern Ireland",
        codeIso2: "GB",
        codeIso3: "GBR",
        codeIso3Numeric: "826",
        phoneCode: "44",
        ibanLength: 22,
        ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
        ibanSupported: true,
        accountNumberType: "IBAN",
        nationalIdType: "SortCode",
        isSepaCountry: true,
        paymentPurposeCodeRequired: false,
        createdAt: "2024-01-01T00:00:00Z",
        createdBy: "system",
      },
      {
        id: "2",
        name: "United States",
        code: "US",
        formalName: "United States of America",
        codeIso2: "US",
        codeIso3: "USA",
        codeIso3Numeric: "840",
        phoneCode: "1",
        ibanLength: 0,
        ibanRegex: "",
        ibanSupported: false,
        accountNumberType: "AccountNumber",
        nationalIdType: "RoutingNumber",
        isSepaCountry: false,
        paymentPurposeCodeRequired: true,
        createdAt: "2024-01-01T00:00:00Z",
        createdBy: "system",
      },
    ],
    isLoading: false,
  }),
}))

const queryClient = new QueryClient()

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

describe("PayeeDetailsForm", () => {
  it("should render form with default values", () => {
    const onNext = vi.fn()
    renderWithQueryClient(<PayeeDetailsForm onNext={onNext} />)

    expect(screen.getByText("Display name")).toBeInTheDocument()
    expect(screen.getByText("Email")).toBeInTheDocument()
    expect(screen.getByText("Address details")).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Next" })).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument()
  })

  it("should handle display name input and validation", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    renderWithQueryClient(<PayeeDetailsForm onNext={onNext} />)

    const displayNameInput = screen.getByTestId("display-name-input")
    await user.type(displayNameInput, "John Doe")
    expect(displayNameInput).toHaveValue("John Doe")

    await user.clear(displayNameInput)
    await user.tab()
    expect(screen.getByText("Display name is required")).toBeInTheDocument()
  })

  it("should handle email input and validation", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    renderWithQueryClient(<PayeeDetailsForm onNext={onNext} />)

    const emailInput = screen.getByTestId("email-input")

    await user.type(emailInput, "invalid-email")
    await user.tab()
    expect(screen.getByText("Invalid email")).toBeInTheDocument()

    await user.clear(emailInput)
    await user.type(emailInput, "<EMAIL>")
    await user.tab()
    expect(screen.queryByText("Invalid email")).not.toBeInTheDocument()
  })

  it("should handle address fields input and validation", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    renderWithQueryClient(<PayeeDetailsForm onNext={onNext} />)

    const buildingInput = screen.getByTestId("building-number-input")
    const streetInput = screen.getByTestId("street-input")
    const cityInput = screen.getByTestId("city-input")
    const postcodeInput = screen.getByTestId("postcode-input")

    await user.type(buildingInput, "123")
    await user.type(streetInput, "Main Street")
    await user.type(cityInput, "London")
    await user.type(postcodeInput, "SW1A 1AA")

    expect(buildingInput).toHaveValue("123")
    expect(streetInput).toHaveValue("Main Street")
    expect(cityInput).toHaveValue("London")
    expect(postcodeInput).toHaveValue("SW1A 1AA")
  })

  it("should handle form submission with valid data", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    renderWithQueryClient(<PayeeDetailsForm onNext={onNext} />)

    // Fill required fields
    await user.type(screen.getByTestId("display-name-input"), "John Doe")
    await user.type(screen.getByTestId("email-input"), "<EMAIL>")

    await user.type(screen.getByTestId("building-number-input"), "123")
    await user.type(screen.getByTestId("street-input"), "Main Street")
    await user.type(screen.getByTestId("city-input"), "London")
    await user.type(screen.getByTestId("postcode-input"), "SW1A 1AA")

    // Submit form
    const submitButton = screen.getByRole("button", { name: "Next" })
    await user.click(submitButton)
  })

  it("should handle form reset on cancel", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    const onInvalid = vi.fn()
    renderWithQueryClient(
      <PayeeDetailsForm onInvalid={onInvalid} onNext={onNext} />,
    )

    await user.type(screen.getByTestId("display-name-input"), "John Doe")
    await user.type(screen.getByTestId("email-input"), "<EMAIL>")

    const cancelButton = screen.getByRole("button", { name: "Cancel" })
    await user.click(cancelButton)

    expect(screen.getByTestId("display-name-input")).toHaveValue("")
    expect(screen.getByTestId("email-input")).toHaveValue("")
    expect(onInvalid).toHaveBeenCalled()
  })

  it("should handle pre-filled details", () => {
    const details = {
      displayName: "John Doe",
      email: "<EMAIL>",
      nationality: "United Kingdom",
      searchTextAddress: "",
      selectedAddress: "",
      address: {
        buildingNumber: "123",
        street: "Main Street",
        city: "London",
        state: "Greater London",
        postalCode: "SW1A 1AA",
        country: "United Kingdom",
      },
      countryDetails: {
        id: "1",
        name: "United Kingdom",
        formalName: "United Kingdom of Great Britain and Northern Ireland",
        codeIso2: "GB",
        codeIso3: "GBR",
        codeIso3Numeric: "826",
        phoneCode: "44",
        ibanLength: 22,
        ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
        ibanSupported: true,
        accountNumberType: "IBAN",
        nationalIdType: "SortCode",
        isSepaCountry: true,
        paymentPurposeCodeRequired: false,
        createdAt: "2024-01-01T00:00:00Z",
        createdBy: "system",
      },
    }
    renderWithQueryClient(
      <PayeeDetailsForm details={details} onNext={vi.fn()} />,
    )

    expect(screen.getByTestId("display-name-input")).toHaveValue(
      details.displayName,
    )
    expect(screen.getByTestId("email-input")).toHaveValue(details.email)
    expect(screen.getByTestId("building-number-input")).toHaveValue(
      details.address.buildingNumber,
    )
    expect(screen.getByTestId("street-input")).toHaveValue(
      details.address.street,
    )
    expect(screen.getByTestId("city-input")).toHaveValue(details.address.city)
    expect(screen.getByTestId("postcode-input")).toHaveValue(
      details.address.postalCode,
    )
  })
})
