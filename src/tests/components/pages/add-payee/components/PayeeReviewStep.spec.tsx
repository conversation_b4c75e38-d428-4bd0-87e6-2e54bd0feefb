import { describe, expect, it, vi } from "vitest"
import React from "react"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { IPayeeForm } from "@/data/payees/payees.interface"
import { PayeeReviewStep } from "@/components/pages/add-payee/components/PayeeReviewStep"

// Mock the router
vi.mock("@tanstack/react-router", () => ({
  useNavigate: () => vi.fn(),
  useSearch: () => ({}),
}))

vi.mock("@/data/payees/payees.mutation", () => ({
  addPayeeMutation: () => ({
    mutate: vi.fn((data, { onSuccess }) => onSuccess?.({ id: "test-id" })),
    isPending: false,
    isError: false,
    error: null,
    reset: vi.fn(),
  }),
  useStartPayeeVerificationMutation: () => ({
    mutate: vi.fn(),
    isPending: false,
    isError: false,
    error: null,
    reset: vi.fn(),
  }),
  useVerifyPayeeMutation: () => ({
    mutate: vi.fn(),
    isPending: false,
    isError: false,
    error: null,
  }),
}))

// Add this mock for the VerificationModal at the top of the file
vi.mock(
  "@/components/pages/send-payments/components/modals/VerificationModal",
  () => ({
    VerificationModal: ({ onVerify, open }: any) => {
      // If the modal is open, immediately call onVerify with a test code
      if (open) {
        setTimeout(() => onVerify("123456"), 0)
      }
      return <div data-testid="verification-modal">Verification Modal</div>
    },
  }),
)

const mockPayee: Partial<IPayeeForm> = {
  bankDetails: {
    isBusiness: false,
    country: "United Kingdom",
    countryDetails: {
      id: "1",
      name: "United Kingdom",
      formalName: "United Kingdom of Great Britain and Northern Ireland",
      codeIso2: "GB",
      codeIso3: "GBR",
      codeIso3Numeric: "826",
      phoneCode: "44",
      ibanLength: 22,
      ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
      ibanSupported: true,
      accountNumberType: "IBAN",
      nationalIdType: "SortCode",
      isSepaCountry: true,
      paymentPurposeCodeRequired: false,
      createdAt: "2024-01-01T00:00:00Z",
      createdBy: "system",
    },
    bankCodeValue: "123456",
    accountNumberValue: "********",
    accountName: "John Doe",
    bankCodeDetails: {
      bankCode: "123456",
      payeeBankId: "BANK123",
      name: "Test Bank",
      nationalId: "123456",
      nationalIdType: "Sort Code",
      swiftBic: "TESTGB2L",
      city: "London",
      country: "United Kingdom",
      postalCode: "SW1A 1AA",
      streetName: "Bank Street",
      buildingNumber: "1",
      regionState: "Greater London",
    },
  },
  payeeDetails: {
    displayName: "John",
    email: "<EMAIL>",
    nationality: "United Kingdom",
    searchTextAddress: "",
    selectedAddress: "",
    address: {
      buildingNumber: "45",
      street: "Main St",
      city: "London",
      postalCode: "SW1A 1AA",
      state: "",
      country: "United Kingdom",
    },
  },
}

const queryClient = new QueryClient({
  defaultOptions: { queries: { retry: false } },
})

const renderReviewStep = (props = {}) => {
  return render(
    <QueryClientProvider client={queryClient}>
      <PayeeReviewStep payee={mockPayee} entityId="test-entity" {...props} />
    </QueryClientProvider>,
  )
}

describe("PayeeReviewStep", () => {
  beforeEach(() => {
    queryClient.clear()
  })

  it("displays bank details correctly", () => {
    renderReviewStep()
    expect(screen.getByText("Individual")).toBeInTheDocument()
    expect(screen.getAllByText("United Kingdom")[0]).toBeInTheDocument()
    expect(screen.getByText("123456")).toBeInTheDocument()
  })

  it("displays payee details correctly", () => {
    renderReviewStep()
    expect(screen.getByText("John")).toBeInTheDocument()
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument()
    expect(screen.getByText(/45.*Main St.*London/)).toBeInTheDocument()
  })

  it("handles edit actions", async () => {
    const onEdit = vi.fn()
    const user = userEvent.setup()
    renderReviewStep({ onEdit })

    // Find the edit button inside the card header with "Bank details"
    const bankDetailsSections = screen.getAllByText("Bank details")
    // Use the first match which is the section header
    const bankDetailsSection = bankDetailsSections[0].closest("div")
    const editButton = bankDetailsSection?.querySelector("button")
    await user.click(editButton!)
    expect(onEdit).toHaveBeenCalledWith(0)
  })
})
