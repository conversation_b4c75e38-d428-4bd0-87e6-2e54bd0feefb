import { describe, expect, it } from "vitest"
import { render, screen } from "@testing-library/react"

import { DetailRow } from "@/components/pages/add-payee/components/DetailRow"

vi.mock("@/components/ui/label", () => ({
  Label: ({ children, className }: any) => (
    <label className={className} data-testid="label">
      {children}
    </label>
  ),
}))

describe("DetailRow", () => {
  it("should render label and value", () => {
    render(<DetailRow label="Test Label" value="Test Value" />)

    expect(screen.getByTestId("label")).toHaveTextContent("Test Label")
    expect(screen.getByRole("paragraph")).toHaveTextContent("Test Value")
  })

  it("should handle undefined value", () => {
    render(<DetailRow label="Test Label" />)

    expect(screen.getByTestId("label")).toHaveTextContent("Test Label")
    expect(screen.getByRole("paragraph")).toBeEmptyDOMElement()
  })

  it("should handle empty string value", () => {
    render(<DetailRow label="Test Label" value="" />)

    expect(screen.getByTestId("label")).toHaveTextContent("Test Label")
    expect(screen.getByRole("paragraph")).toBeEmptyDOMElement()
  })

  it("should pass className to Label component", () => {
    render(<DetailRow label="Test Label" value="Test Value" />)

    const label = screen.getByTestId("label")
    expect(label).toHaveClass("text-foreground/90", "flex-none", "text-sm")
  })
})
