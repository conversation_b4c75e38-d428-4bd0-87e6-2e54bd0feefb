import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { AddPayeeStepper } from "@/components/pages/add-payee/components/AddPayeeStepper"

vi.mock("@/components/base/triggers/Stepper", () => ({
  Stepper: ({ children, value }: any) => (
    <div data-step={value} data-testid="stepper">
      {children}
    </div>
  ),
  Step: ({ children, title }: any) => (
    <div data-testid="step" data-title={title}>
      {children}
    </div>
  ),
}))

vi.mock("@/components/pages/add-payee/components/PayeeBankDetailForm", () => ({
  PayeeBankDetailForm: ({ onNext, onInvalid }: any) => (
    <div data-testid="bank-details-form">
      <button onClick={() => onNext({ bank: "test" })}>Next Bank</button>
      <button data-testid="bank-invalid" onClick={onInvalid}>
        Invalid
      </button>
    </div>
  ),
}))

vi.mock("@/components/pages/add-payee/components/PayeesDetailsForm", () => ({
  PayeeDetailsForm: ({ onNext, onInvalid }: any) => (
    <div data-testid="payee-details-form">
      <button onClick={() => onNext({ name: "test" })}>Next Details</button>
      <button data-testid="details-invalid" onClick={onInvalid}>
        Invalid
      </button>
    </div>
  ),
}))

vi.mock("@/components/pages/add-payee/components/PayeeReviewStep", () => ({
  PayeeReviewStep: ({ onEdit, onComplete, payee }: any) => (
    <div data-testid="review-step">
      <button onClick={() => onEdit(0)}>Edit Bank</button>
      <button onClick={() => onEdit(1)}>Edit Details</button>
      <button onClick={() => onComplete({ id: "123" })}>Complete</button>
      <pre>{JSON.stringify(payee)}</pre>
    </div>
  ),
}))

describe("AddPayeeStepper", () => {
  it("should start with bank details step", () => {
    render(<AddPayeeStepper entityId="123" />)
    expect(screen.getByTestId("bank-details-form")).toBeInTheDocument()
    expect(screen.getByTestId("stepper")).toHaveAttribute("data-step", "0")
  })

  it("should progress through steps when forms are completed", async () => {
    const user = userEvent.setup()
    const onComplete = vi.fn()

    render(<AddPayeeStepper entityId="123" onComplete={onComplete} />)

    await user.click(screen.getByText("Next Bank"))
    expect(screen.getByTestId("payee-details-form")).toBeInTheDocument()

    await user.click(screen.getByText("Next Details"))
    expect(screen.getByTestId("review-step")).toBeInTheDocument()

    await user.click(screen.getByText("Complete"))
    expect(onComplete).toHaveBeenCalledWith({ id: "123" })
  })

  it("should handle invalid form submissions", async () => {
    const user = userEvent.setup()
    render(<AddPayeeStepper entityId="123" />)

    await user.click(screen.getByTestId("bank-invalid"))
    expect(screen.getByTestId("stepper")).toHaveAttribute("data-step", "0")

    await user.click(screen.getByText("Next Bank"))
    await user.click(screen.getByTestId("details-invalid"))
    expect(screen.getByTestId("stepper")).toHaveAttribute("data-step", "1")
  })

  it("should allow editing previous steps", async () => {
    const user = userEvent.setup()
    render(<AddPayeeStepper entityId="123" />)

    await user.click(screen.getByText("Next Bank"))
    await user.click(screen.getByText("Next Details"))

    await user.click(screen.getByText("Edit Bank"))
    expect(screen.getByTestId("bank-details-form")).toBeInTheDocument()

    await user.click(screen.getByText("Next Bank"))
    await user.click(screen.getByText("Edit Details"))
    expect(screen.getByTestId("payee-details-form")).toBeInTheDocument()
  })

  it("should handle modal mode", () => {
    const onClose = vi.fn()
    render(<AddPayeeStepper entityId="123" modal onClose={onClose} />)

    expect(screen.getByTestId("stepper")).toBeInTheDocument()
    expect(screen.getByTestId("bank-details-form")).toBeInTheDocument()
  })
})
