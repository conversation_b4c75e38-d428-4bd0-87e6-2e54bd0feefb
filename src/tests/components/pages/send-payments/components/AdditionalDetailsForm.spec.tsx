import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { AdditionalDetailsForm } from "@/components/pages/send-payments/components/AdditionalDetailsForm"
class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}
window.ResizeObserver = ResizeObserver

// Mock React DOM
vi.mock("react-dom/client", async () => {
  const createRoot = vi.fn((container) => ({
    render: vi.fn(),
    unmount: vi.fn(),
  }))
  return {
    createRoot,
    default: {
      createRoot,
    },
  }
})

// mock usePaymentPurposeQuery from src/data/payments/payments.query.ts
vi.mock("@/data/payments/payments.query", () => ({
  usePaymentPurposeQuery: vi.fn(() => ({
    data: [],
    isLoading: false,
    isError: false,
  })),
  useFxValueDatesQuery: vi.fn(() => ({
    data: {
      valueDateOptions: ["2024-01-01", "2024-01-02", "2024-01-03"],
    },
    isLoading: false,
    isError: false,
  })),
}))

describe("AdditionalDetailsForm", () => {
  it("should render form with default values", () => {
    const onNext = vi.fn()
    render(<AdditionalDetailsForm entityId="123" onNext={onNext} />)

    expect(screen.getByText("Purpose of payment")).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Next" })).toBeInTheDocument()
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument()
  })

  it("should handle payment reference input", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    render(<AdditionalDetailsForm entityId="123" onNext={onNext} />)

    const referenceInput = screen.getByPlaceholderText(
      "Enter payment reference",
    )
    await user.type(referenceInput, "Test payment reference")

    expect(referenceInput).toHaveValue("Test payment reference")
    expect(screen.getByText("22/140")).toBeInTheDocument()
  })

  it("should handle form validation", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    const onInvalid = vi.fn()
    render(
      <AdditionalDetailsForm
        entityId="123"
        onInvalid={onInvalid}
        onNext={onNext}
      />,
    )

    // Try to submit without filling required fields
    const submitButton = screen.getByRole("button", { name: "Next" })
    await user.click(submitButton)

    expect(
      screen.getByText("Payment reference is required"),
    ).toBeInTheDocument()
    expect(onNext).not.toHaveBeenCalled()
    expect(onInvalid).toHaveBeenCalled()
  })

  it("should handle form reset on cancel", async () => {
    const user = userEvent.setup()
    const onNext = vi.fn()
    const onCancel = vi.fn()
    render(
      <AdditionalDetailsForm
        entityId="123"
        onCancel={onCancel}
        onNext={onNext}
      />,
    )

    // Enter reference
    const referenceInput = screen.getByPlaceholderText(
      "Enter payment reference",
    )
    await user.type(referenceInput, "Test payment reference")

    const cancelButton = screen.getByRole("button", { name: "Cancel" })
    await user.click(cancelButton)

    // user should be redirected to payment list
    expect(window.location.pathname).toBe("/")
  })

  it("should handle pre-filled details", () => {
    const details = {
      paymentDate: "2024-02-01",

      paymentReference: "Pre-filled reference",
      purpose: "purpose1",
    }

    render(
      <AdditionalDetailsForm
        entityId="123"
        onNext={vi.fn()}
        payment={{ additionalDetails: details }}
      />,
    )

    expect(screen.getByPlaceholderText("Enter payment reference")).toHaveValue(
      details.paymentReference,
    )
    expect(screen.getByText("20/140")).toBeInTheDocument() // Length counter updated to match actual length
  })
})
