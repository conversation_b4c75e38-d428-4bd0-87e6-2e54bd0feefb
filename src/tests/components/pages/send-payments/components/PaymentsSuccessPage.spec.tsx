import { describe, expect, it, vi } from "vitest"
import { render, screen } from "@testing-library/react"

import { PaymentsSuccessPage } from "@/components/pages/send-payments/components/PaymentsSuccessPage"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children }: { children: React.ReactNode }) => children,
  createRootRoute: vi.fn(),
  createFileRoute: vi.fn(),
}))

describe("PaymentsSuccessPage", () => {
  it("renders success message and view payments button", () => {
    render(<PaymentsSuccessPage entityId="123" />)

    expect(
      screen.getByText("Payment submitted successfully"),
    ).toBeInTheDocument()
    expect(
      screen.getByRole("button", { name: /view payments/i }),
    ).toBeInTheDocument()
  })

  it("shows custom message when provided", () => {
    const customMessage = "Custom success message"
    render(<PaymentsSuccessPage entityId="123" message={customMessage} />)

    expect(screen.getByText(customMessage)).toBeInTheDocument()
  })

  it("shows approval notification for non-self-approver", () => {
    render(<PaymentsSuccessPage entityId="123" isSelfApprover={false} />)

    expect(
      screen.getByText(
        "Approval notifications for this payment have been sent.",
      ),
    ).toBeInTheDocument()
  })

  it("hides approval notification for self-approver", () => {
    render(<PaymentsSuccessPage entityId="123" isSelfApprover={true} />)

    expect(
      screen.queryByText(
        "Approval notifications for this payment have been sent.",
      ),
    ).not.toBeInTheDocument()
  })
})
