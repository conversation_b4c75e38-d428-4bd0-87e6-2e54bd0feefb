import { describe, expect, it } from "vitest"
import { render, screen } from "@testing-library/react"

import { DottedPaymentFlow } from "@/components/pages/send-payments/components/DottedPaymentFlow"

describe("DottedPaymentFlow", () => {
  it("should render with default initials when no name provided", () => {
    const { container } = render(<DottedPaymentFlow />)

    // Find the AvatarFallback element and check its content
    const fallback = container.querySelector(".text-sm")
    expect(fallback).toBeInTheDocument()
    // The fallback contains a space character, which is hard to test directly
    // So instead we check that the element exists and its content is whitespace
    expect(fallback?.textContent?.trim()).toBe("")
  })

  it("should render correct initials for single name", () => {
    render(<DottedPaymentFlow name="John" />)

    expect(screen.getByText("J")).toBeInTheDocument()
  })

  it("should render correct initials for full name", () => {
    render(<DottedPaymentFlow name="<PERSON>" />)

    expect(screen.getByText("JDS")).toBeInTheDocument()
  })

  it("should render Argentex icon", () => {
    const { container } = render(<DottedPaymentFlow />)

    const iconContainer = container.querySelector(
      ".flex.items-center.justify-center.w-10.h-10.rounded-full.bg-primary.text-background",
    )
    expect(iconContainer).toBeInTheDocument()

    const svg = iconContainer?.querySelector("svg")
    expect(svg).toBeInTheDocument()
    expect(svg).toHaveAttribute("viewBox", "0 0 44 44")
  })

  it("should apply default styling", () => {
    const { container } = render(<DottedPaymentFlow />)

    const wrapper = container.firstChild
    expect(wrapper).toHaveClass(
      "relative",
      "flex",
      "flex-col",
      "items-center",
      "w-20",
      "mb-16",
      "mt-12",
    )
  })

  it("should apply custom className", () => {
    const { container } = render(<DottedPaymentFlow className="custom-class" />)

    const wrapper = container.firstChild
    expect(wrapper).toHaveClass("custom-class")
  })

  it("should render dotted line and arrow", () => {
    const { container } = render(<DottedPaymentFlow />)

    // Check dotted line
    const dottedLine = container.querySelector(".border-dotted")
    expect(dottedLine).toBeInTheDocument()
    expect(dottedLine).toHaveClass(
      "border-l-2",
      "border-dotted",
      "border-foreground/25",
    )

    // Check arrow
    const arrow = container.querySelector(".border-t-8")
    expect(arrow).toBeInTheDocument()
    expect(arrow).toHaveClass(
      "border-l-8",
      "border-r-8",
      "border-l-transparent",
      "border-r-transparent",
      "border-t-8",
      "border-foreground/25",
    )
  })

  it("should render avatar with correct styling", () => {
    const { container } = render(<DottedPaymentFlow />)

    const avatar = container.querySelector(".w-10.h-10.bg-muted")
    expect(avatar).toBeInTheDocument()
    expect(avatar).toHaveClass(
      "w-10",
      "h-10",
      "bg-muted",
      "text-muted-foreground",
      "border",
      "border-muted-foreground/20",
    )
  })
})
