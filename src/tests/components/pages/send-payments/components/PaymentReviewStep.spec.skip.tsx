import { describe, expect, it, vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { IPaymentForm } from "@/data/payments/payments.interface"
import { PaymentReviewStep } from "@/components/pages/send-payments/components/PaymentReviewStep"

const mockNavigate = vi.fn()
const mockGetAvailableBalance = vi.fn().mockReturnValue(500)

vi.mock("@tanstack/react-router", () => ({
  useNavigate: () => mockNavigate,
}))

vi.mock("@/components/base/currency/CurrencyFlag", () => ({
  CurrencyFlag: () => <div data-testid="currency-flag" />,
}))

vi.mock("@/components/base/currency/CurrencyText", () => ({
  CurrencyText: () => <div data-testid="currency-text" />,
}))

vi.mock("../../payees/components/PayeeDetailsExpandable", () => ({
  PayeeDetailsExpandable: () => <div data-testid="payee-details" />,
}))

vi.mock("./DottedPaymentFlow", () => ({
  DottedPaymentFlow: () => (
    <div className="mb-4 mt-4" data-testid="dotted-flow" />
  ),
}))

vi.mock("@/components/base/form/form", () => ({
  FormLayout: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  FormButtons: ({ nextButtonProps, nextButtonText }: any) => (
    <button
      disabled={nextButtonProps?.loading}
      onClick={nextButtonProps?.onClick}
    >
      {nextButtonProps?.loading && <div data-testid="loading-spinner" />}
      {nextButtonText}
    </button>
  ),
}))

const mockMutate = vi.fn()
const mockReset = vi.fn()

interface MutationState {
  mutate: typeof mockMutate
  isPending: boolean
  isError: boolean
  error: Error | null
  reset: typeof mockReset
  isSuccess: boolean
  isIdle: boolean
  data: unknown
  variables: unknown
}

let mockMutationState: MutationState = {
  mutate: mockMutate,
  isPending: false,
  isError: false,
  error: null,
  reset: mockReset,
  isSuccess: false,
  isIdle: true,
  data: undefined,
  variables: undefined,
}

vi.mock("@/data/payments/payments.mutation", () => ({
  addPaymentMutation: () => mockMutationState,
}))

describe("PaymentReviewStep", () => {
  const mockPayment: Partial<IPaymentForm> = {
    details: {
      fromDetails: {
        id: "from-1",
        accountName: "Main Account",
        virtualIban: "GB********9",
        balances: [
          {
            id: "balance-1",
            currency: "USD" as const,
            balance: 1000,
          },
        ],
        name: "Main Account",
        iban: "GB********9",
        entityId: "entity-1",
        bankName: "Test Bank",
        bankAddress: "Test Address",
        category: "Personal",
        totalBalance: 1000,
        clientAccountId: "client-account-1",
        clientId: "client-1",
        totalCurrency: "USD" as const,
      },
      toDetails: {
        id: "1",
        accountName: "John Doe",
        accountDetailsConfirmed: true,
        accountDetailsConfirmedAt: "2024-01-01T00:00:00Z",
        accountNumber: "********",
        iban: "**********************",
        type: "Individual",
        status: "Active",
        displayName: "John Doe",
        currentStatus: "Active",
        createdAt: "2024-01-01T00:00:00Z",
        createdBy: {
          id: "1",
          email: "<EMAIL>",
          displayName: "System",
        },
        client: {
          id: "1",
          name: "Test Client",
          registrationNumber: "123456",
          address: "123 Test Street",
        },
        isScaCompleted: true,
        bank: {
          name: "Test Bank",
          nationalId: "123456",
          nationalIdType: "Sort Code",
          swiftBic: "TESTGB2L",
          country: {
            id: "1",
            name: "United Kingdom",
            formalName: "United Kingdom of Great Britain and Northern Ireland",
            codeIso2: "GB",
            codeIso3: "GBR",
            codeIso3Numeric: "826",
            phoneCode: "44",
            ibanLength: 22,
            ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
            ibanSupported: true,
            accountNumberType: "IBAN",
            nationalIdType: "SortCode",
            isSepaCountry: true,
            paymentPurposeCodeRequired: false,
            createdAt: "2024-01-01T00:00:00Z",
            createdBy: "system",
          },
        },
      },
      currencySend: "USD",
      currencyReceive: "USD",
      sendAmount: "1000",
      receiveAmount: "1000",
    },
    additionalDetails: {
      paymentReference: "TEST-REF-001",
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockMutationState = {
      mutate: mockMutate,
      isPending: false,
      isError: false,
      error: null,
      reset: mockReset,
      isSuccess: false,
      isIdle: true,
      data: undefined,
      variables: undefined,
    }
  })

  it("renders payment details correctly", () => {
    render(
      <PaymentReviewStep
        entityId="entity-1"
        getAvailableBalance={mockGetAvailableBalance}
        payment={mockPayment}
      />,
    )

    // Check account details
    expect(screen.getByText("Main Account (...6789)")).toBeInTheDocument()
    expect(screen.getByText("Test Payee")).toBeInTheDocument()

    expect(screen.getByTestId("currency-flag")).toBeInTheDocument()
    expect(screen.getByTestId("currency-text")).toBeInTheDocument()

    expect(screen.getByText("January 1st, 2024")).toBeInTheDocument()
    expect(screen.getByText("TEST-REF-001")).toBeInTheDocument()

    expect(
      screen.getByText(/edit payee and amount details/i),
    ).toBeInTheDocument()
    expect(screen.getByText(/edit additional details/i)).toBeInTheDocument()
  })

  it("handles edit actions correctly", () => {
    const onEdit = vi.fn()
    render(
      <PaymentReviewStep
        entityId="entity-1"
        getAvailableBalance={mockGetAvailableBalance}
        onEdit={onEdit}
        payment={mockPayment}
      />,
    )

    fireEvent.click(screen.getByText(/edit payee and amount details/i))
    expect(onEdit).toHaveBeenCalledWith(0)

    fireEvent.click(screen.getByText(/edit additional details/i))
    expect(onEdit).toHaveBeenCalledWith(1)
  })

  it("displays error message and allows dismissal", () => {
    mockMutationState = {
      ...mockMutationState,
      isError: true,
      error: new Error("Payment failed"),
    }

    render(
      <PaymentReviewStep
        entityId="entity-1"
        getAvailableBalance={mockGetAvailableBalance}
        payment={mockPayment}
      />,
    )

    expect(screen.getByText("Error")).toBeInTheDocument()
    expect(screen.getByText("Payment failed")).toBeInTheDocument()

    const dismissButton = screen.getByRole("button", {
      name: "",
    })
    fireEvent.click(dismissButton)
    expect(mockReset).toHaveBeenCalled()
  })

  it("shows loading state during payment submission", () => {
    mockMutationState = {
      ...mockMutationState,
      isPending: true,
    }

    render(
      <PaymentReviewStep
        entityId="entity-1"
        getAvailableBalance={mockGetAvailableBalance}
        payment={mockPayment}
      />,
    )

    const confirmButton = screen.getByRole("button", {
      name: /submit for approval/i,
    })
    expect(confirmButton).toBeDisabled()
    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument()
  })

  it("renders correctly with minimal payment data", () => {
    const minimalPayment: Partial<IPaymentForm> = {
      details: {
        fromDetails: {
          id: "from-1",
          accountName: "Main Account",
          virtualIban: "GB********9",
          balances: [{ id: "1", currency: "USD", balance: 1000 }],
          name: "Main Account",
          iban: "GB********9",
          entityId: "entity-1",
          bankName: "Test Bank",
          bankAddress: "Test Address",
          category: "Personal",
          totalBalance: 1000,
          clientAccountId: "client-account-1",
          clientId: "client-1",
          totalCurrency: "USD" as const,
        },
        toDetails: {
          id: "to-1",
          accountName: "Test Payee",
          accountNumber: "**********",
          iban: "GB987654321",
          accountDetailsConfirmed: true,
          accountDetailsConfirmedAt: "2024-01-01T00:00:00Z",
          type: "Individual",
          status: "Active",
          displayName: "Test Payee",
          currentStatus: "Active",
          createdAt: "2024-01-01T00:00:00Z",
          createdBy: {
            id: "1",
            email: "<EMAIL>",
            displayName: "System",
          },
          client: {
            id: "1",
            name: "Test Client",
            registrationNumber: "123456",
            address: "123 Test Street",
          },
          isScaCompleted: true,
          bank: {
            name: "Test Bank",
            nationalId: "123456",
            nationalIdType: "Sort Code",
            swiftBic: "TESTBIC1",
            country: {
              id: "1",
              name: "United Kingdom",
              formalName:
                "United Kingdom of Great Britain and Northern Ireland",
              codeIso2: "GB",
              codeIso3: "GBR",
              codeIso3Numeric: "826",
              phoneCode: "44",
              ibanLength: 22,
              ibanRegex:
                "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
              ibanSupported: true,
              accountNumberType: "IBAN",
              nationalIdType: "SortCode",
              isSepaCountry: true,
              paymentPurposeCodeRequired: false,
              createdAt: "2024-01-01T00:00:00Z",
              createdBy: "system",
            },
          },
        },
        currencySend: "USD",
        currencyReceive: "USD",
        sendAmount: "1000",
        receiveAmount: "1000",
      },
      additionalDetails: {},
    }

    render(
      <PaymentReviewStep
        entityId="entity-1"
        getAvailableBalance={mockGetAvailableBalance}
        payment={minimalPayment}
      />,
    )

    expect(screen.getByText("Main Account (...6789)")).toBeInTheDocument()
    expect(screen.getByText("Test Payee")).toBeInTheDocument()
    expect(screen.getByTestId("currency-flag")).toBeInTheDocument()
    expect(screen.getByTestId("currency-text")).toBeInTheDocument()
    expect(screen.getByText("January 1st, 2024")).toBeInTheDocument()
  })

  it("handles payment submission error", () => {
    mockMutationState = {
      ...mockMutationState,
      isError: true,
      error: new Error("Payment failed"),
    }

    render(
      <PaymentReviewStep
        entityId="entity-1"
        getAvailableBalance={mockGetAvailableBalance}
        payment={mockPayment}
      />,
    )

    const confirmButton = screen.getByRole("button", {
      name: /submit for approval/i,
    })
    fireEvent.click(confirmButton)

    expect(screen.getByText("Payment failed")).toBeInTheDocument()
  })

  it("submits payment correctly", () => {
    render(
      <PaymentReviewStep
        entityId="entity-1"
        getAvailableBalance={mockGetAvailableBalance}
        payment={mockPayment}
      />,
    )
  })
})
