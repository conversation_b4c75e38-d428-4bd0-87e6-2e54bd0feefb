import { describe, expect, it, vi } from "vitest"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { SendPaymentsPage } from "@/components/pages/send-payments/Page"

const mockSendPaymentsStepper = vi.fn()

vi.mock(
  "@/components/pages/send-payments/components/SendPaymentsStepper",
  () => ({
    SendPaymentsStepper: (props: { entityId: string }) => {
      mockSendPaymentsStepper(props)
      return <div data-testid="payment-stepper">Payment Stepper</div>
    },
  }),
)

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

const renderWithProviders = (ui: React.ReactElement) => {
  const testQueryClient = createTestQueryClient()
  return render(
    <QueryClientProvider client={testQueryClient}>{ui}</QueryClientProvider>,
  )
}

describe("SendPaymentsPage", () => {
  const entityId = "test-entity"

  beforeEach(() => {
    mockSendPaymentsStepper.mockClear()
  })

  it("should render payment stepper", () => {
    renderWithProviders(<SendPaymentsPage entityId={entityId} />)
    expect(screen.getByTestId("payment-stepper")).toBeInTheDocument()
  })

  it("should pass entityId to stepper component", () => {
    renderWithProviders(<SendPaymentsPage entityId={entityId} />)
    expect(mockSendPaymentsStepper).toHaveBeenCalledWith(
      expect.objectContaining({ entityId }),
    )
  })
})
