import { describe, expect, it, Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, within } from "@testing-library/react"
import { useNavigate } from "@tanstack/react-router"

import { IAddNewUser } from "@/data/user/user.interface"
import { AddNewUserForm } from "@/components/pages/add-new-user/components/AddNewUserForm"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

const mockGlobalStore = {
  entity: { id: "test-entity", name: "Argentex" },
}

vi.mock("@/components/layout/entity/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
      legalEntity: {
        name: "Argentex",
      },
    },
  })),
}))

const mockForm: Partial<IAddNewUser> = {
  firstName: "John",
  lastName: "Doe",
  dateOfBirth: "01/01/1997",
  email: "<EMAIL>",
  role: "",
  signatory: "Testing",
}

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(() => mockGlobalStore),
}))

vi.mock("@tanstack/react-router", () => {
  const navigate = vi.fn()

  return {
    useNavigate: () => navigate,
    Link: ({ children, to }: any) => <a href={to}>{children}</a>,
    getRouteApi: vi.fn(() => ({
      useLoaderData: vi.fn(() => ({
        entity: {
          id: "test-entity",
          legalEntity: {
            name: "Argentex",
          },
        },
      })),
    })),
  }
})

const setup = (formData = ({} = mockForm)) => {
  const user = userEvent.setup()
  const form = render(<AddNewUserForm {...{ ...mockForm, ...formData }} />)

  // title
  const formHeader = screen.getByText("Enter user details")

  window.HTMLElement.prototype.scrollIntoView = vi.fn()
  window.HTMLElement.prototype.hasPointerCapture = vi.fn()

  const [
    firstNameInput,
    lastNameInput,
    dateOfBirthInput,
    emailInput,
    roleSelect,
    addUserBtn,
    cancelBtn,
  ] = [
    screen.getByRole("textbox", { name: /first name/i }),
    screen.getByRole("textbox", { name: /last name/i }),
    screen.getByLabelText(/date of birth/i),
    screen.getByRole("textbox", { name: /email/i }),
    screen.getByRole("combobox", { name: /role/i }),
    screen.getByRole("button", { name: /add user/i }),
    screen.getByRole("button", { name: /cancel/i }),
  ]
  return {
    formHeader,
    firstNameInput,
    lastNameInput,
    dateOfBirthInput,
    emailInput,
    roleSelect,
    addUserBtn,
    cancelBtn,
    ...form,
    user,
  }
}

describe("AddNewUserForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.doUnmock
  })

  it("Should show add new user form", async () => {
    const {
      formHeader,
      firstNameInput,
      lastNameInput,
      dateOfBirthInput,
      emailInput,
      roleSelect,
      addUserBtn,
      user,
    } = setup()

    expect(formHeader).toBeInTheDocument()
    expect(firstNameInput).toBeInTheDocument()
    expect(lastNameInput).toBeInTheDocument()
    expect(dateOfBirthInput).toBeInTheDocument()
    expect(emailInput).toBeInTheDocument()

    // ** role selector **
    expect(roleSelect).toBeInTheDocument()
    await user.click(roleSelect)
    expect(roleSelect).toHaveAttribute("aria-expanded", "true")

    const [administator, submitter, approver, viewer] = [
      screen.getByRole("option", { name: "Administrator" }),
      screen.getByRole("option", { name: "Submitter" }),
      screen.getByRole("option", { name: "Approver" }),
      screen.getByRole("option", { name: "Viewer" }),
    ]

    // assert the options in the selector
    expect(administator).toBeInTheDocument()
    expect(submitter).toBeInTheDocument()
    expect(approver).toBeInTheDocument()
    expect(viewer).toBeInTheDocument()

    await user.click(approver)

    // selector must be closed and the value should be approver
    expect(roleSelect).toHaveAttribute("aria-expanded", "false")
    expect(within(roleSelect).getByText("Approver")).toBeInTheDocument()

    // ** signatory type selector **
    const signatorySelect = screen.getByRole("combobox", {
      name: /signatory/i,
    })
    expect(signatorySelect).toBeInTheDocument()

    await user.click(signatorySelect)
    expect(signatorySelect).toHaveAttribute("aria-expanded", "true")

    const signatoryViewer = screen.getByRole("option", { name: "Viewer" })
    expect(signatoryViewer).toBeInTheDocument()

    await user.click(signatoryViewer)
    expect(signatorySelect).toHaveAttribute("aria-expanded", "false")
    expect(within(signatorySelect).getByText("Viewer")).toBeInTheDocument()
  })

  it("Should handle null entity ID gracefully", async () => {
    ;(useLoaderData as Mock).mockReturnValueOnce({
      entity: undefined,
    })

    render(<AddNewUserForm />)

    expect(screen.getByText("Entity id not defined")).toBeInTheDocument()
  })

  it("Should prevent user from submitting form if validation fails", async () => {
    const {
      formHeader,
      firstNameInput,
      lastNameInput,
      dateOfBirthInput,
      emailInput,
      addUserBtn,
      user,
    } = setup({ firstName: "" })

    expect(formHeader).toBeInTheDocument()
    expect(firstNameInput).toBeInTheDocument()
    expect(lastNameInput).toBeInTheDocument()
    expect(dateOfBirthInput).toBeInTheDocument()
    expect(emailInput).toBeInTheDocument()
    expect(addUserBtn).toBeInTheDocument()

    await user.click(addUserBtn)

    // Look for error message text instead of role
    const firstNameError = screen.getByText("First name is required")
    expect(firstNameError).toBeInTheDocument()
  })

  it("Should navigate user when form is cancelled", async () => {
    const {
      formHeader,
      firstNameInput,
      lastNameInput,
      dateOfBirthInput,
      emailInput,
      addUserBtn,
      cancelBtn,
      user,
    } = setup({ firstName: "" })

    const navigate = useNavigate()

    expect(formHeader).toBeInTheDocument()
    expect(firstNameInput).toBeInTheDocument()
    expect(lastNameInput).toBeInTheDocument()
    expect(dateOfBirthInput).toBeInTheDocument()
    expect(emailInput).toBeInTheDocument()
    expect(addUserBtn).toBeInTheDocument()
    expect(cancelBtn).toBeInTheDocument()

    await user.click(cancelBtn)

    expect(navigate).toHaveBeenCalledWith({
      to: "/$entityId/user-admin",
      params: { entityId: "test-entity" },
    })
  })
})
