import { describe, expect, it, vi } from "vitest"
import { render, screen } from "@testing-library/react"

import { AddNewUserPage } from "@/components/pages/add-new-user/Page"

vi.mock("@tanstack/react-router", () => {
  const navigate = vi.fn()

  return {
    useNavigate: () => navigate,
  }
})

vi.mock("@/components/layout/entity/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "1",
    },
  })),
}))

describe("AddNewUserPage", () => {
  it("should show add new user form", async () => {
    render(<AddNewUserPage entityId="1" />)

    expect(screen.getByText("Enter user details")).toBeInTheDocument()
  })
})
