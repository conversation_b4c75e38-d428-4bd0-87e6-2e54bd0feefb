import { describe, expect, it } from "vitest"
import { render, screen } from "@testing-library/react"

import { PaymentSuccessPage } from "@/components/pages/approve-payments/components/ApproveSuccessPage"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children }: { children: React.ReactNode }) => (
    <a href="#">{children}</a>
  ),
}))

describe("PaymentSuccessPage", () => {
  it("should render approval success message", () => {
    render(<PaymentSuccessPage approve={true} entityId="test-entity" />)

    expect(
      screen.getByText("Payment approved successfully"),
    ).toBeInTheDocument()
    expect(screen.getByText("View Payments")).toBeInTheDocument()
    expect(screen.getByRole("link")).toBeInTheDocument()
  })

  it("should render creation success message", () => {
    render(<PaymentSuccessPage approve={false} entityId="test-entity" />)

    expect(screen.getByText("Payment created successfully")).toBeInTheDocument()
  })

  it("should render creation message by default", () => {
    render(<PaymentSuccessPage entityId="test-entity" />)

    expect(screen.getByText("Payment created successfully")).toBeInTheDocument()
  })

  it("should render success icon", () => {
    render(<PaymentSuccessPage entityId="test-entity" />)

    const icon = screen.getByTestId("success-icon")
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveClass("text-primary")
  })
})
