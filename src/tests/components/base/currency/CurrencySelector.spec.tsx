import { expect, describe, it, beforeAll, vi } from "vitest"
import { render } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { CurrencySelector } from "@/components/base/currency/CurrencySelector"

// Mock the global query hook
vi.mock("@/data/global/global.query", () => ({
  useCurrenciesQuery: () => ({
    data: [
      { code: "USD", description: "US Dollar" },
      { code: "EUR", description: "Euro" },
      { code: "GBP", description: "British Pound" },
    ],
    isLoading: false,
    isError: false,
  }),
}))

// Mock react-dom/client
vi.mock("react-dom/client", () => {
  return {
    default: {
      createRoot: vi.fn(() => ({
        render: vi.fn(),
        unmount: vi.fn(),
      })),
    },
    createRoot: vi.fn(() => ({
      render: vi.fn(),
      unmount: vi.fn(),
    })),
  }
})

// Mock ResizeObserver
beforeAll(() => {
  class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }

  // @ts-ignore
  window.ResizeObserver = ResizeObserver
})

// Create a test query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

// Helper function to render with the query client
const renderWithClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

describe("CurrencySelector", () => {
  it("should render", () => {
    const { container } = renderWithClient(
      <CurrencySelector currency="USD" onChange={() => {}} />,
    )
    expect(container).toBeTruthy()
  })

  it("should be same with snapshot", () => {
    const { container } = renderWithClient(
      <CurrencySelector currency="USD" onChange={() => {}} />,
    )

    const button = container.querySelector("button")
    expect(button).toBeTruthy()
    expect(button).toHaveTextContent("USD")
  })

  it("should render with USD", () => {
    const { container } = renderWithClient(
      <CurrencySelector currency="USD" onChange={() => {}} />,
    )
    const button = container.querySelector("button")
    expect(button).toHaveTextContent("USD")
  })

  it("should render currency flag", () => {
    const { container } = renderWithClient(
      <CurrencySelector currency="USD" onChange={() => {}} variant="select" />,
    )
    // check a valid svg exists
    const svg = container.querySelector("svg")
    expect(svg).toBeTruthy()
  })
})
