import { describe, expect, it, vi } from "vitest"
import { render, screen } from "@testing-library/react"

import { useCurrenciesQuery } from "@/data/global/global.query"
import { CurrencyName } from "@/components/base/currency/CurrencyName"

vi.mock("@/data/global/global.query", () => ({
  useCurrenciesQuery: vi.fn(),
}))

describe("CurrencyName", () => {
  const mockCurrencies = [
    { code: "GBP", description: "British Pound" },
    { code: "EUR", description: "Euro" },
    { code: "USD", description: "US Dollar" },
  ]

  it("should display loading state", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: true,
      isError: false,
      data: undefined,
    })

    render(<CurrencyName currency="GBP" />)
    expect(screen.getByText("Loading...")).toBeInTheDocument()
  })

  it("should display error state", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: false,
      isError: true,
      data: undefined,
    })

    render(<CurrencyName currency="GBP" />)
    expect(screen.getByText("Error")).toBeInTheDocument()
  })

  it("should display N/A when currency not found", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: false,
      isError: false,
      data: mockCurrencies,
    })

    render(<CurrencyName currency="JPY" />)
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })

  it("should display currency name when found", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: false,
      isError: false,
      data: mockCurrencies,
    })

    render(<CurrencyName currency="GBP" />)
    expect(screen.getByText("British Pound")).toBeInTheDocument()
  })

  it("should apply custom className", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: false,
      isError: false,
      data: mockCurrencies,
    })

    render(<CurrencyName className="custom-class" currency="GBP" />)
    expect(screen.getByText("British Pound")).toHaveClass("custom-class")
  })

  it("should handle undefined data", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: false,
      isError: false,
      data: undefined,
    })

    render(<CurrencyName currency="GBP" />)
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })

  it("should handle empty currencies array", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: false,
      isError: false,
      data: [],
    })

    render(<CurrencyName currency="GBP" />)
    expect(screen.getByText("N/A")).toBeInTheDocument()
  })

  it("should update when currency prop changes", () => {
    ;(useCurrenciesQuery as any).mockReturnValue({
      isLoading: false,
      isError: false,
      data: mockCurrencies,
    })

    const { rerender } = render(<CurrencyName currency="GBP" />)
    expect(screen.getByText("British Pound")).toBeInTheDocument()

    rerender(<CurrencyName currency="EUR" />)
    expect(screen.getByText("Euro")).toBeInTheDocument()
  })
})
