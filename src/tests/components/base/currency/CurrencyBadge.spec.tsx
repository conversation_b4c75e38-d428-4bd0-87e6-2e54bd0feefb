import { render } from "@testing-library/react"

import { CurrencyBadge } from "@/components/base/currency/CurrencyBadge"

describe("CurrencyBadge", () => {
  it("should render", () => {
    const { container } = render(<CurrencyBadge currency="USD" />)
    expect(container).toBeTruthy()
  })

  it("should apply default classes", () => {
    const { container } = render(<CurrencyBadge currency="EUR" />)
    const badge = container.firstChild as HTMLElement

    expect(badge).toHaveClass(
      "rounded-full",
      "text-xs",
      "px-2",
      "py-1",
      "bg-muted",
      "flex",
      "items-center",
      "gap-1",
    )
  })

  it("should apply custom className", () => {
    const { container } = render(
      <CurrencyBadge className="custom-class" currency="GBP" />,
    )
    const badge = container.firstChild as HTMLElement

    expect(badge).toHaveClass("custom-class")
  })

  it("should match snapshot", () => {
    const { container } = render(<CurrencyBadge currency="USD" />)
    expect(container).toMatchSnapshot()
  })
})
