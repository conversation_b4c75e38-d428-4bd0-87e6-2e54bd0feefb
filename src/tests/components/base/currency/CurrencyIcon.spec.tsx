import { render } from "@testing-library/react"

import { CurrencyIcon } from "@/components/base/currency/CurrencyIcon"

describe("CurrencyIcon", () => {
  it("should render", () => {
    const { container } = render(<CurrencyIcon currency="USD" />)
    expect(container).toBeTruthy()
  })

  it("should match snapshot", () => {
    const { container } = render(<CurrencyIcon currency="USD" />)
    expect(container).toMatchSnapshot()
  })

  it("should handle empty currency", () => {
    // @ts-expect-error
    const { container } = render(<CurrencyIcon currency="" />)
    expect(container.firstChild).toBeNull()
  })
})
