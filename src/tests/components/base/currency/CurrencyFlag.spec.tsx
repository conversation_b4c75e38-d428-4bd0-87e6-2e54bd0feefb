import { describe, expect, it, vi } from "vitest"
import { render, screen } from "@testing-library/react"

import { CurrencyFlag } from "@/components/base/currency/CurrencyFlag"

vi.mock("@/lib/constants/currency.constants", () => ({
  currencyFlags: {
    USD: "circle-flags:us",
    EUR: "circle-flags:eu",
  },
}))

vi.mock("@iconify/react", () => ({
  Icon: ({ icon, className }: { icon: string; className?: string }) => (
    <svg
      className={className}
      data-icon={icon}
      data-testid="mock-icon"
      height="24"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" />
    </svg>
  ),
}))

describe("CurrencyFlag", () => {
  it("should render with default size (sm)", () => {
    render(<CurrencyFlag currency="USD" />)
    const icon = screen.getByTestId("mock-icon")
    expect(icon).toHaveClass("w-4 h-4")
  })

  it("should render with medium size", () => {
    render(<CurrencyFlag currency="USD" size="md" />)
    const icon = screen.getByTestId("mock-icon")
    expect(icon).toHaveClass("w-6 h-6")
  })

  it("should render with large size", () => {
    render(<CurrencyFlag currency="USD" size="lg" />)
    const icon = screen.getByTestId("mock-icon")
    expect(icon).toHaveClass("w-8 h-8")
  })

  it("should apply custom className", () => {
    render(<CurrencyFlag className="custom-class" currency="USD" />)
    const icon = screen.getByTestId("mock-icon")
    expect(icon).toHaveClass("custom-class")
  })

  it("should return null when currency is empty", () => {
    const { container } = render(<CurrencyFlag currency="" />)
    expect(container.firstChild).toBeNull()
  })

  it("should return null when currency is not found in metadata", () => {
    const { container } = render(<CurrencyFlag currency="INVALID" />)
    expect(container.firstChild).toBeNull()
  })

  it("should match snapshot", () => {
    const { container } = render(<CurrencyFlag currency="USD" />)
    expect(container).toMatchSnapshot()
  })

  it("should use memoized icon value", () => {
    const { rerender } = render(<CurrencyFlag currency="USD" />)
    const firstIcon = screen.getByTestId("mock-icon")
    expect(firstIcon).toHaveAttribute("data-icon", "circle-flags:us")

    rerender(<CurrencyFlag currency="USD" />)
    const secondIcon = screen.getByTestId("mock-icon")
    expect(secondIcon).toHaveAttribute("data-icon", "circle-flags:us")

    rerender(<CurrencyFlag currency="EUR" />)
    const thirdIcon = screen.getByTestId("mock-icon")
    expect(thirdIcon).toHaveAttribute("data-icon", "circle-flags:eu")
  })

  it("should handle empty currency", () => {
    const { container } = render(<CurrencyFlag currency="" />)
    expect(container.firstChild).toBeNull()
  })

  it("should handle unknown currency", () => {
    const { container } = render(<CurrencyFlag currency="UNKNOWN" />)
    expect(container.firstChild).toBeNull()
  })

  it("should handle all size variants", () => {
    const sizes = {
      sm: "w-4 h-4",
      md: "w-6 h-6",
      lg: "w-8 h-8",
    }

    Object.entries(sizes).forEach(([size, expectedClass]) => {
      const { rerender } = render(
        <CurrencyFlag currency="USD" size={size as "sm" | "md" | "lg"} />,
      )
      expect(screen.getByTestId("mock-icon")).toHaveClass(expectedClass)
      rerender(<></>)
    })
  })

  it("should render with custom className", () => {
    const { container } = render(
      <CurrencyFlag className="test-class" currency="USD" />,
    )
    expect(container.firstChild).toHaveClass("test-class")
  })
})
