import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { CurrencyInput } from "@/components/base/currency/CurrencyInput"

const mockUseCurrenciesQuery = vi.fn(() => ({
  data: [
    {
      code: "GBP",
      amountDisplayFormat: "0.00",
    },
    {
      code: "JPY",
      amountDisplayFormat: "0",
    },
  ],
  isLoading: false,
  isError: false,
}))

vi.mock("@/data/global/global.query", () => ({
  useCurrenciesQuery: () => mockUseCurrenciesQuery(),
}))

describe("CurrencyInput", () => {
  it("should format value based on currency decimals", () => {
    render(<CurrencyInput currency="GBP" value={1000} />)
    expect(screen.getByDisplayValue("1,000.00")).toBeInTheDocument()

    render(<CurrencyInput currency="JPY" value={1000} />)
    expect(screen.getByDisplayValue("1,000")).toBeInTheDocument()
  })

  it("should handle user input correctly", async () => {
    const onChange = vi.fn()
    const user = userEvent.setup()

    render(<CurrencyInput currency="GBP" onChange={onChange} value={0} />)
    const input = screen.getByRole("textbox")

    await user.type(input, "1234")

    expect(onChange).toHaveBeenCalledWith(1234.0)
  })

  it("should handle loading state", () => {
    mockUseCurrenciesQuery.mockReturnValue({
      data: [
        {
          code: "GBP",
          amountDisplayFormat: "0.00",
        },
        {
          code: "JPY",
          amountDisplayFormat: "0",
        },
      ],
      isLoading: true,
      isError: false,
    })

    render(<CurrencyInput currency="GBP" value={0} />)
    expect(screen.getByRole("textbox")).toBeDisabled()
  })
})
