import { render, screen } from "@testing-library/react"

import {
  AlertBox,
  type AlertBoxProps,
  AlertTitle,
  AlertDescription,
} from "@/components/base/alert-box"

const setup = ({ severity, className, children }: AlertBoxProps = {}) => {
  const alertBox = render(
    <AlertBox className={className} severity={severity}>
      {children ?? (
        <>
          <AlertTitle>Test Title</AlertTitle>
          <AlertDescription>Test Description</AlertDescription>
        </>
      )}
    </AlertBox>,
  )

  return {
    ...alertBox,
  }
}

describe("AlertBox", () => {
  it("renders warning alert with correct styles and content", () => {
    setup({ severity: "warning" })

    expect(screen.getByRole("alert")).toHaveClass(
      "border-warning/20",
      "border-l-warning",
      "bg-warning-light",
    )
    expect(screen.getByText("Test Title")).toBeInTheDocument()
    expect(screen.getByText("Test Description")).toBeInTheDocument()
    expect(screen.getByTestId("alert-triangle-icon")).toBeInTheDocument()
  })

  it("renders success alert with correct styles and content", () => {
    setup({ severity: "success" })

    expect(screen.getByRole("alert")).toHaveClass(
      "border-success/20",
      "border-l-success",
      "bg-success/[0.05]",
    )
    expect(screen.getByText("Test Title")).toBeInTheDocument()
    expect(screen.getByText("Test Description")).toBeInTheDocument()
    expect(screen.getByTestId("check-circle-icon")).toBeInTheDocument()
  })

  it("renders info alert with correct styles and content", () => {
    setup({ severity: "info" })

    expect(screen.getByRole("alert")).toHaveClass(
      "border-info/20",
      "border-l-info",
      "bg-info/[0.05]",
    )

    expect(screen.getByText("Test Title")).toBeInTheDocument()
    expect(screen.getByText("Test Description")).toBeInTheDocument()
    expect(screen.getByTestId("info-icon")).toBeInTheDocument()
  })

  it("renders error alert with correct styles and content", () => {
    setup({ severity: "error" })

    expect(screen.getByRole("alert")).toHaveClass(
      "border-destructive",
      "border-l-destructive",
    )
    expect(screen.getByText("Test Title")).toBeInTheDocument()
    expect(screen.getByText("Test Description")).toBeInTheDocument()
    expect(screen.getByTestId("alert-circle-icon")).toBeInTheDocument()
  })

  it("returns success when no variant is provided", () => {
    setup()

    expect(screen.getByRole("alert")).toBeInTheDocument()
  })

  it("handles empty title and description", () => {
    setup({ children: <></> })

    const alert = screen.getByRole("alert")
    expect(alert).toBeInTheDocument()
    expect(alert.textContent).toBe("")
  })
})
