import { describe, expect, it, vi, beforeEach } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { FileUploadArea } from "@/components/base/FileUploadArea"

describe("FileUploadArea", () => {
  const mockOnFileSelect = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render with default props", () => {
    render(<FileUploadArea onFileSelect={mockOnFileSelect} />)

    expect(screen.getByText(/Drag and drop file or/)).toBeInTheDocument()
    expect(screen.getByText("browse")).toBeInTheDocument()
    expect(screen.getByText("Max file size: 5MB")).toBeInTheDocument()
  })

  it("should render with custom label and description", () => {
    const customLabel = "Custom upload label"
    const customDescription = "Custom description"

    render(
      <FileUploadArea
        description={customDescription}
        label={customLabel}
        onFileSelect={mockOnFileSelect}
      />,
    )

    expect(screen.getByText(customLabel)).toBeInTheDocument()
    expect(screen.getByText(customDescription)).toBeInTheDocument()
  })

  it("should handle file input change", () => {
    render(<FileUploadArea onFileSelect={mockOnFileSelect} />)

    const file = new File(["test"], "test.txt", { type: "text/plain" })
    const input = screen.getByRole("button", { name: "browse" })

    // Create a mock file input event
    const event = {
      target: {
        files: [file],
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>

    // Simulate clicking the browse button and selecting a file
    fireEvent.click(input)
    const hiddenInput = document.querySelector('input[type="file"]')
    fireEvent.change(hiddenInput!, event)

    expect(mockOnFileSelect).toHaveBeenCalledWith(file)
  })

  it("should handle drag and drop", () => {
    render(<FileUploadArea onFileSelect={mockOnFileSelect} />)

    const file = new File(["test"], "test.txt", { type: "text/plain" })
    const dropArea = screen.getByText(/Drag and drop file or/).parentElement!
      .parentElement!

    // Simulate drag events
    fireEvent.dragOver(dropArea)
    expect(dropArea).toHaveClass("border-primary")

    fireEvent.dragLeave(dropArea)
    expect(dropArea).not.toHaveClass("border-primary")

    // Simulate drop event
    const dropEvent = {
      preventDefault: vi.fn(),
      dataTransfer: {
        files: [file],
      },
    } as unknown as React.DragEvent

    fireEvent.drop(dropArea, dropEvent)
    expect(mockOnFileSelect).toHaveBeenCalledWith(file)
  })

  it("should validate file type", () => {
    const alertMock = vi.spyOn(window, "alert").mockImplementation(() => {})

    render(
      <FileUploadArea
        acceptedFileTypes={["image/jpeg"]}
        onFileSelect={mockOnFileSelect}
      />,
    )

    const invalidFile = new File(["test"], "test.txt", { type: "text/plain" })
    const dropArea = screen.getByText(/Drag and drop file or/).parentElement!
      .parentElement!

    // Simulate drop event with invalid file type
    const dropEvent = {
      preventDefault: vi.fn(),
      dataTransfer: {
        files: [invalidFile],
      },
    } as unknown as React.DragEvent

    fireEvent.drop(dropArea, dropEvent)

    expect(alertMock).toHaveBeenCalledWith("Please upload a valid file type")
    expect(mockOnFileSelect).not.toHaveBeenCalled()

    alertMock.mockRestore()
  })

  it("should validate file size", () => {
    const alertMock = vi.spyOn(window, "alert").mockImplementation(() => {})
    const maxFileSize = 1 // 1MB

    render(
      <FileUploadArea
        maxFileSize={maxFileSize}
        onFileSelect={mockOnFileSelect}
      />,
    )

    // Create a file larger than maxFileSize
    const largeFile = new File(
      [new ArrayBuffer(1024 * 1024 * 2)],
      "large.txt",
      {
        type: "text/plain",
      },
    )
    const dropArea = screen.getByText(/Drag and drop file or/).parentElement!
      .parentElement!

    // Simulate drop event with large file
    const dropEvent = {
      preventDefault: vi.fn(),
      dataTransfer: {
        files: [largeFile],
      },
    } as unknown as React.DragEvent

    fireEvent.drop(dropArea, dropEvent)

    expect(alertMock).toHaveBeenCalledWith("File size must be less than 1MB")
    expect(mockOnFileSelect).not.toHaveBeenCalled()

    alertMock.mockRestore()
  })

  it("should handle ref.reset()", () => {
    const ref = { current: null } as React.MutableRefObject<any>
    render(<FileUploadArea onFileSelect={mockOnFileSelect} ref={ref} />)

    const fileInput = document.querySelector(
      'input[type="file"]',
    ) as HTMLInputElement

    // Set a value to the file input
    Object.defineProperty(fileInput, "value", {
      value: "test.txt",
      writable: true,
    })
    expect(fileInput.value).toBe("test.txt")

    // Call reset through the ref
    ref.current.reset()
    expect(fileInput.value).toBe("")
  })
})
