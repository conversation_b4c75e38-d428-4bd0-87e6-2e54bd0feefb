import { vi } from "vitest"
import React from "react"
import { render, screen, fireEvent } from "@testing-library/react"

import { Stepper, Step } from "@/components/base/triggers/Stepper"

vi.mock("framer-motion", () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}))

window.ResizeObserver = vi.fn().mockImplementation((callback) => ({
  observe: () => {
    callback([
      {
        contentRect: {
          width: 1000,
        },
      },
    ])
  },
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

describe("Stepper", () => {
  const mockSteps = [
    { title: "Step 1", content: "Content 1" },
    { title: "Step 2", content: "Content 2" },
    { title: "Step 3", content: "Content 3" },
  ]

  it("should render horizontal stepper", () => {
    render(
      <Stepper value={0}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    expect(screen.getByText("Step 1")).toBeInTheDocument()
    expect(screen.getByText("Content 1")).toBeInTheDocument()
  })

  it("should render vertical stepper", () => {
    render(
      <Stepper value={0} variant="vertical">
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    expect(screen.getByText("Step 1")).toBeInTheDocument()
    expect(screen.getByText("Content 1")).toBeInTheDocument()
  })

  it("should handle step changes", () => {
    const onStepChange = vi.fn()
    render(
      <Stepper onStepChange={onStepChange} value={0}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    fireEvent.click(screen.getByText("Step 2"))
    expect(onStepChange).toHaveBeenCalledWith(1)
  })

  it("should render modal content", () => {
    render(
      <Stepper modal value={0}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    expect(screen.getByText("Content 1")).toBeInTheDocument()
  })

  it("should show back button when not on first step", () => {
    // The back button has been commented out in the component
    // Test navigation by clicking on a previous step instead
    const onStepChange = vi.fn()
    render(
      <Stepper onStepChange={onStepChange} value={1}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    // Click on the first step instead of the back button
    const firstStepButton = screen.getByText("Step 1")
    fireEvent.click(firstStepButton)
    expect(onStepChange).toHaveBeenCalledWith(0)
  })

  it("should handle invalid children", () => {
    render(
      <Stepper value={0}>
        <div>Invalid Child</div>
        <Step title="Valid Step">Content</Step>
      </Stepper>,
    )

    expect(screen.queryByText("Invalid Child")).not.toBeInTheDocument()
    expect(screen.getByText("Valid Step")).toBeInTheDocument()
  })

  it("should handle animation transitions", () => {
    const { rerender } = render(
      <Stepper value={0}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    expect(screen.getByText("Content 1")).toBeInTheDocument()

    rerender(
      <Stepper value={1}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    expect(screen.getByText("Content 2")).toBeInTheDocument()
  })

  it("should not show back button on small screens", () => {
    window.ResizeObserver = vi.fn().mockImplementation((callback) => ({
      observe: () => {
        callback([
          {
            contentRect: {
              width: 500,
            },
          },
        ])
      },
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }))

    render(
      <Stepper onStepChange={vi.fn()} value={1}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    expect(screen.queryByText("Back")).not.toBeInTheDocument()
  })

  it("should handle missing container ref", () => {
    const originalUseRef = React.useRef
    const mockUseRef = vi.fn(() => ({ current: null }))
    React.useRef = mockUseRef

    render(
      <Stepper value={0}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    React.useRef = originalUseRef

    expect(screen.getByText("Content 1")).toBeInTheDocument()
  })

  it("should handle animation variants with different directions", () => {
    const { rerender } = render(
      <Stepper value={0}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    rerender(
      <Stepper value={1}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )

    rerender(
      <Stepper value={0}>
        {mockSteps.map((step, i) => (
          <Step key={i} title={step.title}>
            {step.content}
          </Step>
        ))}
      </Stepper>,
    )
  })

  it("should handle Step props spreading", () => {
    const onClick = vi.fn()
    render(
      <Stepper value={0}>
        {[
          <Step
            className="custom-class"
            key="test"
            onClick={onClick}
            title="Test Step"
          >
            Content
          </Step>,
        ]}
      </Stepper>,
    )
    const button = screen.getByRole("button")
    expect(button).toHaveClass("custom-class")
  })

  it("should handle disabled Step", () => {
    render(
      <Stepper value={0}>
        {[
          <Step disabled key="test" title="Test Step">
            Content
          </Step>,
        ]}
      </Stepper>,
    )

    expect(screen.getByRole("button")).toBeDisabled()
  })
})
