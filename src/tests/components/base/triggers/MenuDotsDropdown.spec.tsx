import { vi } from "vitest"
import { Settings } from "lucide-react"
import { render, screen, fireEvent } from "@testing-library/react"

import { MenuDotsDropdown } from "@/components/base/triggers/MenuDotsDropdown"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, ...props }: any) => (
    <a {...props} data-testid="mock-link">
      {children}
    </a>
  ),
}))

vi.mock("lucide-react", () => ({
  MoreVertical: () => <span data-testid="more-vertical-icon" />,
  Settings: () => <span data-testid="settings-icon" />,
}))

vi.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  DropdownMenuTrigger: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  DropdownMenuContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dropdown-content">{children}</div>
  ),
  DropdownMenuItem: ({
    children,
    onClick,
  }: {
    children: React.ReactNode
    onClick?: () => void
  }) => (
    <div onClick={onClick} role="menuitem">
      {children}
    </div>
  ),
}))

describe("MenuDotsDropdown", () => {
  const mockItems = [
    {
      type: "link" as const,
      label: "Settings",
      icon: Settings,
      linkProps: {
        to: "/$entityId/accounts" as const,
        params: { entityId: "123" },
      },
    },
    {
      type: "button" as const,
      label: "Delete",
      icon: Settings,
      onClick: vi.fn(),
    },
    {
      type: "label" as const,
      label: "Info Label",
      icon: Settings,
    },
  ]

  it("should render dropdown trigger", () => {
    render(<MenuDotsDropdown items={mockItems} />)
    expect(screen.getByTestId("more-vertical-icon")).toBeInTheDocument()
  })

  it("should apply custom className", () => {
    const { container } = render(
      <MenuDotsDropdown className="custom-class" items={mockItems} />,
    )
    expect(container.firstChild).toHaveClass("custom-class")
  })

  it("should stop event propagation on trigger click", () => {
    render(<MenuDotsDropdown items={mockItems} />)

    const event = new MouseEvent("click", {
      bubbles: true,
      cancelable: true,
    })

    const trigger = screen.getByRole("button")
    Object.defineProperty(event, "stopPropagation", { value: vi.fn() })
    trigger.dispatchEvent(event)

    expect(event.stopPropagation).toHaveBeenCalled()
  })

  it("should render link items correctly", () => {
    render(<MenuDotsDropdown items={[mockItems[0]]} />)

    const link = screen.getByTestId("mock-link")
    expect(link).toHaveAttribute("to", "/$entityId/accounts")
    expect(screen.getByText("Settings")).toBeInTheDocument()
    expect(screen.getByTestId("settings-icon")).toBeInTheDocument()
  })

  it("should handle button click", () => {
    render(<MenuDotsDropdown items={[mockItems[1]]} />)

    const menuItem = screen.getByText("Delete").closest('[role="menuitem"]')
    fireEvent.click(menuItem!)

    expect(mockItems[1].onClick).toHaveBeenCalled()
  })

  it("should render label items", () => {
    render(<MenuDotsDropdown items={[mockItems[2]]} />)
    expect(screen.getByText("Info Label")).toBeInTheDocument()
  })
})
