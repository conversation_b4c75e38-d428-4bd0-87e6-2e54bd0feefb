import { render } from "@testing-library/react"

import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

describe("LoadingSpinner", () => {
  it("should render with default size", () => {
    const { container } = render(<LoadingSpinner />)
    const spinner = container.firstChild as HTMLElement

    expect(spinner).toHaveStyle({
      width: "6rem",
      height: "6rem",
    })
    expect(spinner).toHaveClass(
      "animate-spin",
      "border-t-2",
      "border-gray-300",
      "border-t-primary",
    )
  })

  it("should render with custom size", () => {
    const { container } = render(<LoadingSpinner size="8" />)
    const spinner = container.firstChild as HTMLElement

    expect(spinner).toHaveStyle({
      width: "8rem",
      height: "8rem",
    })
  })

  it("should apply custom className", () => {
    const { container } = render(<LoadingSpinner className="custom-class" />)
    const spinner = container.firstChild as HTMLElement

    expect(spinner).toHaveClass("custom-class")
  })
})
