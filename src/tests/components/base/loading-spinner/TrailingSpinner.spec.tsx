import { render, screen } from "@testing-library/react"

import { TrailingSpinner } from "@/components/base/loading-spinner/TrailingSpinner"

vi.mock("@iconify/react", () => ({
  Icon: ({ className }: { className: string }) => (
    <span className={className} data-testid="mock-icon" />
  ),
}))

describe("TrailingSpinner", () => {
  it("should render children without spinner when not loading", () => {
    render(
      <TrailingSpinner>
        <div>Test Content</div>
      </TrailingSpinner>,
    )

    expect(screen.getByText("Test Content")).toBeInTheDocument()
    expect(screen.queryByTestId("mock-icon")).not.toBeInTheDocument()
  })

  it("should render spinner when loading", () => {
    render(
      <TrailingSpinner isLoading>
        <div>Test Content</div>
      </TrailingSpinner>,
    )

    expect(screen.getByText("Test Content")).toBeInTheDocument()
    expect(screen.getByTestId("mock-icon")).toHaveClass("animate-spin")
  })

  it("should apply custom className", () => {
    const { container } = render(
      <TrailingSpinner className="custom-class">
        <div>Test Content</div>
      </TrailingSpinner>,
    )

    expect(container.firstChild).toHaveClass("custom-class", "relative")
  })
})
