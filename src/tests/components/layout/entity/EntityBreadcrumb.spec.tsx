import { describe, it, expect } from "vitest"
import { render, screen } from "@testing-library/react"

import { useGlobalStore } from "@/data/global/global.store"
import { EntityBreadcrumb } from "@/components/layout/entity/EntityBreadcrumb"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
}))

describe("EntityBreadcrumb", () => {
  it("renders breadcrumbs correctly", () => {
    const mockBreadcrumbs = [
      { key: "1", label: "Home", path: "/" },
      { key: "2", label: "Entity", path: "entity-1" },
      { key: "3", label: "Accounts", path: "accounts" },
    ]

    useGlobalStore.setState({ breadcrumbs: mockBreadcrumbs })

    render(<EntityBreadcrumb />)

    expect(screen.getByText("Home")).toBeInTheDocument()
    expect(screen.getByText("Entity")).toBeInTheDocument()
    expect(screen.getByText("Accounts")).toBeInTheDocument()

    const links = screen.getAllByRole("link")
    expect(links).toHaveLength(2)
    expect(links[0]).toHaveAttribute("href", "/")
    expect(links[1]).toHaveAttribute("href", "/entity-1")

    const lastItem = screen.getByText("Accounts")
    expect(lastItem.tagName).toBe("SPAN")
  })

  it("handles empty breadcrumbs", () => {
    useGlobalStore.setState({ breadcrumbs: [] })
    render(<EntityBreadcrumb />)

    expect(screen.queryByRole("list")).toBeInTheDocument()
    expect(screen.queryByRole("link")).not.toBeInTheDocument()
  })

  it("normalizes paths with multiple slashes and spaces", () => {
    const mockBreadcrumbs = [
      { key: "1", label: "Home", path: "///" },
      { key: "2", label: "Entity", path: "  entity-1  //" },
      { key: "3", label: "Section", path: "section  1///" },
    ]

    useGlobalStore.setState({ breadcrumbs: mockBreadcrumbs })
    render(<EntityBreadcrumb />)

    const links = screen.getAllByRole("link")
    expect(links[0]).toHaveAttribute("href", "/")
    expect(links[1]).toHaveAttribute("href", "/entity-1")
  })
})
