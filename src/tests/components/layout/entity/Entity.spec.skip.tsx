import { describe, it, vi, expect, beforeEach, afterEach, Mock } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import {
  QueryClient,
  QueryClientProvider,
  useSuspenseQuery,
} from "@tanstack/react-query"
import { useAuth0 } from "@auth0/auth0-react"

import Entity from "@/components/layout/entity/Entity"
import { GetEntityResponseDto } from "@/client/onboarding/types.gen"

vi.mock("@auth0/auth0-react", () => ({
  useAuth0: vi.fn(),
}))

vi.mock("@tanstack/react-query", () => ({
  useSuspenseQuery: vi.fn(),
  QueryClient: vi.fn(),
  useQuery: vi.fn(),
  QueryClientProvider: ({ children }: any) => <>{children}</>,
}))

vi.mock("@/data/onboarding/entity-details.query", () => ({
  useGetEntityQry: vi.fn(),
  entityQryOpts: vi.fn(() => ({
    data: { name: "Test Entity" },
    isLoading: false,
    isError: false,
  })),
}))

vi.mock("@/data/onboarding/onboarding.query", () => ({
  useEntityAccessQry: vi.fn(() => ({
    data: [
      { entityId: "1", entityName: "Entity 1" },
      { entityId: "2", entityName: "Entity 2" },
    ],
    isLoading: false,
  })),
}))

vi.mock("@tanstack/react-router", () => ({
  useRouter: () => ({
    invalidate: vi.fn(),
  }),
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
      },
    })),
  })),
  useParams: vi.fn(() => ({
    entityId: "test-entity",
  })),
  Navigate: ({ to, params }: { to: string; params?: any }) => (
    <div>
      Navigate to {params ? to.replace("$entityId", params.entityId) : to}
    </div>
  ),
  Link: ({ children, to, params }: any) => {
    const href = to.replace("$entityId", params?.entityId)
    return <a href={href}>{children}</a>
  },
}))

vi.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children }: any) => (
    <div data-testid="dropdown-menu">{children}</div>
  ),
  DropdownMenuContent: ({ children }: any) => (
    <div data-testid="dropdown-menu-content">{children}</div>
  ),
  DropdownMenuItem: ({ children, onClick }: any) => (
    <button data-testid="dropdown-menu-item" onClick={onClick}>
      {children}
    </button>
  ),
  DropdownMenuTrigger: ({ children, asChild }: any) => {
    if (asChild) return children
    return <button data-testid="dropdown-trigger">{children}</button>
  },
}))

vi.mock("@/components/ui/skeleton", () => ({
  Skeleton: () => <div data-testid="skeleton" />,
}))

vi.mock("@/components/layout/entity/EntityBreadcrumb", () => ({
  EntityBreadcrumb: vi.fn(),
}))

vi.mock("@/hooks/use-breadcrumb", () => ({
  useBreadcrumb: vi.fn(),
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

// TODO: Update the test. Its failing because of using real API instead of mocking
describe("Entity", () => {
  const mockLogout = vi.fn()
  const mockEntities = {
    id: "1",
    legalEntity: {
      name: "Entity 1",
    },
  } as GetEntityResponseDto

  let originalLocation: Location

  beforeEach(() => {
    vi.clearAllMocks()
    originalLocation = window.location
    // @ts-ignore
    delete window.location
    window.location = {
      ...originalLocation,
      pathname: "/other-path",
      origin: "http://localhost",
    } as string & Location

    Object.defineProperty(window, "matchMedia", {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    })
  })

  afterEach(() => {
    window.location = originalLocation as string & Location
  })

  it("should redirect to first entity when authenticated and on root path", () => {
    ;(useAuth0 as any).mockReturnValue({
      isAuthenticated: true,
    })
    ;(useSuspenseQuery as Mock).mockReturnValue({
      isLoading: false,
      data: mockEntities,
    })

    window.location = {
      ...window.location,
      pathname: "/",
    } as string & Location

    renderWithQueryClient(<Entity />)

    expect(screen.getByText("Entity 1")).toBeInTheDocument()
  })

  it("should handle logout when clicking logout menu item", async () => {
    const user = userEvent.setup()

    ;(useAuth0 as any).mockReturnValue({
      isAuthenticated: true,
      user: { name: "John Doe" },
      logout: mockLogout,
    })
    ;(useSuspenseQuery as Mock).mockReturnValue({
      isLoading: false,
      data: mockEntities,
    })

    renderWithQueryClient(<Entity />)

    const avatar = screen.getByTestId("avatar-button")
    await user.click(avatar)

    const logoutButton = screen.getByText(/logout/i)
    await user.click(logoutButton)

    expect(mockLogout).toHaveBeenCalledWith({
      logoutParams: {
        returnTo: "http://localhost",
      },
    })
  })
})
