import { describe, it, expect, beforeEach, beforeAll } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { useAuth0 } from "@auth0/auth0-react"

import { useGlobalStore } from "@/data/global/global.store"
import { Account } from "@/data/account/account.interface"
import { SidebarProvider } from "@/components/ui/sidebar"
import { EntityHeader } from "@/components/layout/entity/EntityHeader"

const mockLogout = vi.fn()
vi.mock("@auth0/auth0-react", () => ({
  useAuth0: vi.fn(() => ({
    user: {
      nickname: "john.doe",
    },
    logout: mockLogout,
  })),
}))

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
  useParams: vi.fn(() => ({
    entityId: "test-entity",
  })),
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
        name: "Test Account",
      },
    })),
  })),
  createRootRouteWithContext: vi.fn(),
  createRoute: vi.fn(),
  useMatches: vi.fn(() => [
    {
      pathname: "/",
      id: "root",
      params: {},
    },
    {
      pathname: "/dashboard",
      id: "dashboard",
      params: {},
    },
  ]),
}))

beforeAll(() => {
  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
})

const mockAccount: Account = {
  id: "1",
  name: "Test Account",
  accountName: "Test Account Name",
  virtualIban: "GB123456789",
  iban: "GB123456789",
  entityId: "entity-1",
  bankName: "Test Bank",
  bankAddress: "Test Address",
  totalBalance: 1000,
  category: "Test Category",
  balances: [],
  clientAccountId: "client-account-1",
  clientId: "client-1",
  totalCurrency: "USD",
}

const mockBreadcrumbs = [
  { key: "1", label: "Home", path: "/" },
  { key: "2", label: "Dashboard", path: "/dashboard" },
]

const renderWithProviders = (ui: React.ReactElement) => {
  return render(<SidebarProvider>{ui}</SidebarProvider>)
}

describe("EntityHeader", () => {
  beforeEach(() => {
    useGlobalStore.setState({
      account: mockAccount,
      breadcrumbs: mockBreadcrumbs,
    })
  })

  it("renders user initials in avatar", () => {
    renderWithProviders(<EntityHeader />)
    expect(screen.getByText("JD")).toBeInTheDocument()
  })

  it("renders account name when account is present", () => {
    renderWithProviders(<EntityHeader />)
    expect(screen.getByText("Test Account")).toBeInTheDocument()
  })

  it("renders breadcrumb label when no account is present", () => {
    useGlobalStore.setState({
      account: undefined,
      breadcrumbs: [
        { key: "1", label: "Home", path: "/" },
        { key: "2", label: "Dashboard", path: "/dashboard" },
      ],
    })

    renderWithProviders(<EntityHeader />)

    // Look for the h1 element that should contain either the account name or breadcrumb label
    const heading = screen.getByRole("heading", { level: 1 })
    expect(heading).toBeInTheDocument()

    // Check if it has either "Test Account" or the breadcrumb label
    const headingText = heading.textContent
    expect(
      headingText === "Dashboard" || headingText === "Test Account",
    ).toBeTruthy()
  })

  // it("renders notification bell button", () => {
  //   renderWithProviders(<EntityHeader />)
  //   expect(
  //     screen.getByRole("button", { name: /notifications/i }),
  //   ).toBeInTheDocument()
  // })

  it("renders user dropdown menu", async () => {
    renderWithProviders(<EntityHeader />)
    const user = userEvent.setup()

    // Click the user avatar button
    const avatarButton = screen.getByText("JD")
    await user.click(avatarButton)

    expect(screen.getByText("Logout")).toBeInTheDocument()
  })

  // it("renders notification dropdown menu", async () => {
  //   renderWithProviders(<EntityHeader />)
  //   const user = userEvent.setup()

  //   const notificationButton = screen.getByRole("button", {
  //     name: /notifications/i,
  //   })
  //   await user.click(notificationButton)

  //   expect(screen.getByText("No new notifications")).toBeInTheDocument()
  // })

  it("handles missing user nickname gracefully", () => {
    vi.mocked(useAuth0).mockReturnValueOnce({
      user: { nickname: undefined },
      logout: vi.fn(),
    } as any)

    renderWithProviders(<EntityHeader />)
    expect(screen.getByText("-")).toBeInTheDocument()
  })

  it("open dropdown menu when clicking avatar button", async () => {
    renderWithProviders(<EntityHeader />)
    const user = userEvent.setup()
    const avatarButton = screen.getByText("JD")
    await user.click(avatarButton)
    expect(screen.getByText("Logout")).toBeInTheDocument()
  })

  it("logs out when clicking logout button", async () => {
    renderWithProviders(<EntityHeader />)
    const user = userEvent.setup()
    const avatarButton = screen.getByText("JD")
    await user.click(avatarButton)
    const logoutButton = screen.getByText("Logout")
    await user.click(logoutButton)
    expect(mockLogout).toHaveBeenCalled()
  })
})
