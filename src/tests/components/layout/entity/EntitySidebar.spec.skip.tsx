import { describe, it, expect, beforeAll } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useEntityAccessQry } from "@/data/onboarding/onboarding.query"
import { useGlobalStore } from "@/data/global/global.store"
import { SidebarProvider } from "@/components/ui/sidebar"
import { EntitySidebar } from "@/components/layout/entity/EntitySidebar"

vi.mock("@tanstack/react-router", () => ({
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
  })),
  useParams: vi.fn(() => ({
    entityId: "test-entity",
  })),
  getRouteApi: vi.fn(() => ({
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
        name: "Test Entity",
      },
    })),
  })),
}))

vi.mock("@/components/base/icons/ArgentexIcon", () => ({
  default: () => <div data-testid="argentex-logo">ArgentexIcon</div>,
}))

vi.mock("@/components/base/icons/ArgentexIcon2", () => ({
  default: () => <div data-testid="argentex-logo-2">ArgentexIcon2</div>,
}))

vi.mock("@/data/onboarding/onboarding.query", () => ({
  useEntityAccessQry: vi.fn(() => ({
    data: [
      { entityId: "test-entity", entityName: "Test Entity" },
      { entityId: "2", entityName: "Entity 2" },
    ],
    isLoading: false,
  })),
}))

vi.mock("@/lib/constants/entity-navigation", () => ({
  getEntityNavItems: () => [
    { label: "Dashboard", to: "/test-entity", icon: () => <div>Icon</div> },
    {
      label: "Accounts",
      to: "/test-entity/accounts",
      icon: () => <div>Icon</div>,
    },
    { label: "Payees", to: "/test-entity/payees", icon: () => <div>Icon</div> },
    {
      label: "User admin",
      to: "/test-entity/user-admin",
      icon: () => <div>Icon</div>,
    },
  ],
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>
      <SidebarProvider>{ui}</SidebarProvider>
    </QueryClientProvider>,
  )
}

beforeAll(() => {
  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
})

describe("EntitySidebar", () => {
  it("renders navigation items", () => {
    renderWithQueryClient(<EntitySidebar />)

    expect(screen.getByText("Dashboard")).toBeInTheDocument()
    expect(screen.getByText("Accounts")).toBeInTheDocument()
    expect(screen.getByText("Payees")).toBeInTheDocument()
    expect(screen.getByText("User admin")).toBeInTheDocument()
  })

  it("renders entity selector", async () => {
    renderWithQueryClient(<EntitySidebar />)
    const user = userEvent.setup()

    // Find the entity selector by a more specific query
    const entityElements = screen.getAllByText("Test Entity")
    const selector = entityElements[0] // Take the first one
    await user.click(selector)

    // Use getAllByText and check that we have the elements in the dropdown
    const entities = screen.getAllByText("Test Entity")
    expect(entities.length).toBeGreaterThan(0)
    expect(screen.getByText("Entity 2")).toBeInTheDocument()
  })

  it("renders Argentex logo", () => {
    renderWithQueryClient(<EntitySidebar />)
    const logo = screen.getByTestId("argentex-logo")
    expect(logo).toBeInTheDocument()
  })

  it("renders navigation links with correct hrefs", () => {
    renderWithQueryClient(<EntitySidebar />)

    // Get all links and find the ones with specific hrefs
    const dashboardLink = screen.getByText("Dashboard").closest("a")
    const accountsLink = screen.getByText("Accounts").closest("a")
    const payeesLink = screen.getByText("Payees").closest("a")
    const userAdminLink = screen.getByText("User admin").closest("a")

    expect(dashboardLink).toHaveAttribute("href", "/test-entity")
    expect(accountsLink).toHaveAttribute("href", "/test-entity/accounts")
    expect(payeesLink).toHaveAttribute("href", "/test-entity/payees")
    expect(userAdminLink).toHaveAttribute("href", "/test-entity/user-admin")
  })

  it("shows loading state when entities are loading", () => {
    vi.mocked(useEntityAccessQry).mockReturnValueOnce({
      data: undefined,
      isLoading: true,
    } as any)

    renderWithQueryClient(<EntitySidebar />)
    const selector = screen.getByText("Select Entity")
    expect(selector).toBeInTheDocument()
  })
})
