import { describe, it, expect } from "vitest"
import { render } from "@testing-library/react"
import { QueryClient } from "@tanstack/react-query"

import { createWrapper } from "./test-utils"

describe("Test Utils", () => {
  describe("createWrapper", () => {
    it("should create a wrapper component with the provided query client", () => {
      const queryClient = new QueryClient()
      const Wrapper = createWrapper(queryClient)
      const TestComponent = () => <div>Test Content</div>

      const { container } = render(
        <Wrapper>
          <TestComponent />
        </Wrapper>,
      )

      expect(container.textContent).toBe("Test Content")
    })

    it("should use the provided query client for context", () => {
      const queryClient = new QueryClient()
      const Wrapper = createWrapper(queryClient)
      const TestComponent = () => {
        return <div data-testid="test-component">Test Content</div>
      }

      const { getByTestId } = render(
        <Wrapper>
          <TestComponent />
        </Wrapper>,
      )

      expect(getByTestId("test-component")).toBeDefined()
    })

    it("should handle nested components", () => {
      const queryClient = new QueryClient()
      const Wrapper = createWrapper(queryClient)
      const NestedComponent = () => <div>Nested Content</div>
      const ParentComponent = () => (
        <div>
          Parent Content
          <NestedComponent />
        </div>
      )

      const { container } = render(
        <Wrapper>
          <ParentComponent />
        </Wrapper>,
      )

      expect(container.textContent).toBe("Parent ContentNested Content")
    })

    it("should handle multiple children", () => {
      const queryClient = new QueryClient()
      const Wrapper = createWrapper(queryClient)

      const { container } = render(
        <Wrapper>
          <div>First Child</div>
          <div>Second Child</div>
        </Wrapper>,
      )

      expect(container.textContent).toBe("First ChildSecond Child")
    })
  })
})
