import { ReactNode } from "react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

export const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

export const createWrapper = (queryClient?: QueryClient) => {
  const testClient = queryClient || createTestQueryClient()
  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={testClient}>{children}</QueryClientProvider>
  )
}
