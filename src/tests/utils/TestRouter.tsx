import { useMemo } from "react"
import {
  Router<PERSON>rovider,
  createRouter,
  createRoute,
  createRootRouteWithContext,
} from "@tanstack/react-router"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { UseAuth } from "@/hooks/use-auth"

const testQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

interface RouterContext {
  auth: UseAuth
  queryClient: QueryClient
}

/**
 * Test router provider for testing components that use the router.
 */
export function TestRouter(props: React.PropsWithChildren) {
  const rootRoute = createRootRouteWithContext<RouterContext>()({
    component: () => props.children,
  })

  const router = useMemo(
    () =>
      createRouter({
        routeTree: rootRoute.addChildren([
          createRoute({
            path: "*",
            component: () => props.children,
            getParentRoute: () => rootRoute,
          }),
        ]),
        context: {
          auth: undefined!,
          queryClient: testQueryClient,
        },
      }),
    [props.children, rootRoute],
  )

  return (
    <QueryClientProvider client={testQueryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  )
}
