import { describe, it, expect, vi, beforeEach } from "vitest"
import { toast } from "sonner"
import { renderHook, waitFor } from "@testing-library/react"
import { act } from "react"

import { createWrapper } from "@/tests/utils/test-utils"
import { queryClient } from "@/main"
import {
  addPaymentMutation,
  approvePaymentMutation,
} from "@/data/payments/payments.mutation"
import { postPayment, approvePayment } from "@/data/payments/payments.api"
import { queryKeys } from "@/lib/constants/query.constants"

vi.mock("@/data/payments/payments.api", () => ({
  postPayment: vi.fn(),
  approvePayment: vi.fn(),
}))

vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock("@/main", () => ({
  queryClient: {
    invalidateQueries: vi.fn(),
  },
}))

describe("Payment Mutations", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("addPaymentMutation", () => {
    const mockPayment = {
      amount: 100,
      currency: "USD",
      description: "Test payment",
      details: {
        paymentType: "test",
        paymentMethod: "test",
        currencySend: "USD",
        sendAmount: 100,
        currencyReceive: "EUR",
        receiveAmount: 90,
      },
      additionalDetails: {},
      clientAccountId: "account123",
    }

    const mockResponse = {
      id: "payment123",
      ...mockPayment,
    }

    it("should handle successful payment submission", async () => {
      ;(postPayment as any).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => addPaymentMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate({ payment: mockPayment, isDraft: false })

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(postPayment).toHaveBeenCalledWith(mockPayment, false)
      expect(toast.success).toHaveBeenCalledWith(
        "Payment submitted for approval successfully",
        expect.objectContaining({
          duration: 5000,
        }),
      )

      // Verify cache invalidation
      expect(queryClient.invalidateQueries).toHaveBeenCalledWith({
        predicate: expect.any(Function),
      })

      // Test the predicate function
      const predicate = (queryClient.invalidateQueries as any).mock.calls[0][0]
        .predicate
      expect(predicate({ queryKey: ["payments"] })).toBe(true)
      expect(predicate({ queryKey: ["other"] })).toBe(false)
    })

    it.skip("should handle payment submission failure", async () => {
      const error = new Error("API Error")
      ;(postPayment as any).mockRejectedValueOnce(error)

      const { result } = renderHook(() => addPaymentMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate({ payment: mockPayment, isDraft: false })

      await waitFor(() => expect(result.current.isError).toBe(true))

      expect(toast.error).toHaveBeenCalledWith(
        "Failed to submit payment",
        expect.objectContaining({
          description: error.message,
          duration: 5000,
        }),
      )

      expect(queryClient.invalidateQueries).not.toHaveBeenCalled()
    })
  })
})
