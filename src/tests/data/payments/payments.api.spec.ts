import { describe, it, expect, vi, beforeEach, Mock } from "vitest"

import { bankingClient as client } from "@/client/banking"
import {
  IPaymentForm,
  IPaymentRejectionReason,
} from "@/data/payments/payments.interface"
import {
  fetchPayments,
  fetchPaymentById,
  postPayment,
  deletePayment,
  submitPayment,
  cancelPaymentWithReason,
} from "@/data/payments/payments.api"
import { fetchCurrencyWithQuery } from "@/data/global/global.query"

vi.mock("@/client/banking", () => ({
  bankingClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}))

vi.mock("@/data/global/global.query", () => ({
  fetchCurrencyWithQuery: vi.fn(),
}))

describe("Payment API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, "error").mockImplementation(() => {})
  })

  describe("fetchPayments", () => {
    const mockParams = {
      pageNumber: 1,
      pageSize: 10,
      orderByField: "createdAt",
      orderByDirection: "desc",
      currentStatus: "All",
      payeeName: "",
      variant: "default" as const,
    }

    const mockResponse = {
      data: [
        { id: "1", amount: 100 },
        { id: "2", amount: 200 },
      ],
      totalCount: 2,
    }

    it("should fetch payments successfully", async () => {
      ;(client.get as Mock).mockResolvedValueOnce({ data: mockResponse })

      const result = await fetchPayments(mockParams)

      expect(client.get).toHaveBeenCalledWith(
        "/api/v1/outbound-payments/search",
        {
          params: {
            PageNumber: mockParams.pageNumber,
            PageSize: mockParams.pageSize,
            OrderByField: mockParams.orderByField,
            OrderByDirection: mockParams.orderByDirection,
            CurrentStatus: mockParams.currentStatus,
            PayeeName: mockParams.payeeName,
          },
        },
      )
    })

    it("should handle empty response", async () => {
      ;(client.get as Mock).mockResolvedValueOnce({
        data: { data: [], totalCount: 0 },
      })

      const result = await fetchPayments(mockParams)

      expect(result).toEqual({ data: [], totalCount: 0 })
    })
  })

  describe("fetchPaymentById", () => {
    const mockPaymentId = "payment123"
    const mockPayment = { id: mockPaymentId, amount: 100 }

    it("should fetch payment by id successfully", async () => {
      ;(client.get as Mock).mockResolvedValueOnce({ data: mockPayment })

      const result = await fetchPaymentById(mockPaymentId)

      expect(client.get).toHaveBeenCalledWith(
        `/api/v1/outbound-payments/${mockPaymentId}`,
      )
      expect(result).toEqual(mockPayment)
    })

    it.skip("should handle error when fetching payment", async () => {
      const error = new Error("API Error")
      ;(client.get as Mock).mockRejectedValueOnce(error)

      await expect(fetchPaymentById(mockPaymentId)).rejects.toThrow(
        "Failed to fetch the payment. Please try again later.",
      )
    })
  })

  describe("postPayment", () => {
    const mockPaymentForm: IPaymentForm = {
      details: {
        currencySend: "USD",
        currencyReceive: "EUR",
        sendAmount: 1000,
        receiveAmount: 850,
        toDetails: {
          id: "1",
          accountName: "John Doe",
          accountNumber: "********",
          iban: "**********************",
          accountDetailsConfirmed: true,
          accountDetailsConfirmedAt: "2024-01-01T00:00:00Z",
          type: "Individual",
          status: "Active",
          displayName: "John Doe",
          currentStatus: "Active",
          createdAt: "2024-01-01T00:00:00Z",
          createdBy: {
            id: "1",
            email: "<EMAIL>",
            displayName: "System",
          },
          client: {
            id: "1",
            name: "Test Client",
            registrationNumber: "123456",
            address: "123 Test Street",
          },
          isScaCompleted: true,
          bank: {
            name: "Test Bank",
            nationalId: "123456",
            nationalIdType: "Sort Code",
            swiftBic: "TESTGB2L",
            country: {
              id: "1",
              name: "United Kingdom",
              formalName:
                "United Kingdom of Great Britain and Northern Ireland",
              codeIso2: "GB",
              codeIso3: "GBR",
              codeIso3Numeric: "826",
              phoneCode: "44",
              ibanLength: 22,
              ibanRegex:
                "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
              ibanSupported: true,
              accountNumberType: "IBAN",
              nationalIdType: "SortCode",
              isSepaCountry: true,
              paymentPurposeCodeRequired: false,
              createdAt: "2024-01-01T00:00:00Z",
              createdBy: "system",
            },
          },
        },
        fromDetails: {
          id: "acc123",
          name: "Test Account",
          accountName: "Test Account",
          iban: "DE********9",
          virtualIban: "DE********9",
          entityId: "entity123",
          bankName: "Test Bank",
          bankAddress: "123 Bank St",
          totalBalance: 1000,
          category: "Business",
          balances: [{ id: "balance123", currency: "EUR", balance: 1000 }],
          clientAccountId: "account123",
          clientId: "client123",
          totalCurrency: "EUR",
        },
      },
      additionalDetails: {
        paymentReference: "REF123",
      },
      clientAccountId: "account123",
    }

    const mockCurrencies = [{ code: "EUR", factor: 100 }]

    it("should handle error when posting payment", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(fetchCurrencyWithQuery as Mock).mockResolvedValueOnce(mockCurrencies)
      ;(client.post as Mock).mockRejectedValueOnce(error)

      await expect(postPayment(mockPaymentForm)).rejects.toThrow("API Error")
    })
  })

  describe("deletePayment", () => {
    const mockPaymentId = "payment123"

    it("should delete payment successfully", async () => {
      ;(client.delete as Mock).mockResolvedValueOnce({ data: null })

      await deletePayment(mockPaymentId)

      expect(client.delete).toHaveBeenCalledWith(
        `/api/v1/outbound-payments/${mockPaymentId}`,
      )
    })

    it("should handle error when deleting payment", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(client.delete as Mock).mockRejectedValueOnce(error)

      await expect(deletePayment(mockPaymentId)).rejects.toThrow("API Error")
    })
  })

  describe("submitPayment", () => {
    const mockPaymentId = "payment123"

    it("should submit payment successfully", async () => {
      ;(client.post as Mock).mockResolvedValueOnce({ data: null })

      await submitPayment(mockPaymentId)

      expect(client.post).toHaveBeenCalledWith(
        `/api/v1/outbound-payments/${mockPaymentId}/submit`,
      )
    })

    it("should handle error when submitting payment", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(client.post as Mock).mockRejectedValueOnce(error)

      await expect(submitPayment(mockPaymentId)).rejects.toThrow("API Error")
    })
  })

  describe("cancelPayment", () => {
    const mockPaymentId = "payment123"
    const mockRejectionReason: IPaymentRejectionReason = {
      key: "reason123",
      value: "Reason 1",
    }

    it("should cancel payment successfully", async () => {
      ;(client.post as Mock).mockResolvedValueOnce({ data: null })

      await cancelPaymentWithReason(mockPaymentId, mockRejectionReason)

      expect(client.post).toHaveBeenCalledWith(
        `/api/v1/outbound-payments/${mockPaymentId}/cancel-with-reason`,
        {
          rejectionReason: mockRejectionReason.key,
        },
      )
    })
  })
})
