import { describe, it, expect, beforeEach, afterEach, vi } from "vitest"
import { act, renderHook } from "@testing-library/react"

import { mockLocalStorage } from "@/tests/utils/mock-localstorage"
import { useAccountStore } from "@/data/account/account.store"

Object.defineProperty(global, "localStorage", {
  value: mockLocalStorage,
})

describe("useAccountStore", () => {
  beforeEach(() => {
    mockLocalStorage.clear()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it("should initialize with the default currency", () => {
    const { result } = renderHook(() => useAccountStore())
    expect(result.current.displayCurrency).toBe("GBP")
  })

  it("should update the displayCurrency when setDisplayCurrency is called", () => {
    const { result } = renderHook(() => useAccountStore())

    act(() => {
      result.current.setDisplayCurrency("USD")
    })

    expect(result.current.displayCurrency).toBe("USD")
  })

  it("should rehydrate the store from localStorage", () => {
    mockLocalStorage.getItem.mockReturnValueOnce(
      JSON.stringify({
        state: { displayCurrency: "EUR" },
        version: 0,
      }),
    )

    const { result } = renderHook(() => useAccountStore())
    expect(result.current.displayCurrency).toBe("USD")
  })
})
