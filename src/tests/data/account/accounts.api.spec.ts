import { describe, it, expect, vi, beforeEach } from "vitest"

import { bankingClient as client } from "@/client/banking"
import { getAccountsList } from "@/data/account/accounts.api"

vi.mock("@/client/banking", () => ({
  bankingClient: {
    get: vi.fn(),
  },
}))

describe("Accounts API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, "error").mockImplementation(() => {})
  })

  describe("getAccountsList", () => {
    const mockApiResponse = {
      data: [
        {
          id: "1",
          name: "Test Account",
          balances: [{ currency: "USD", balance: 1000 }],
        },
      ],
    }

    it("should fetch accounts successfully", async () => {
      ;(client.get as any).mockResolvedValueOnce(mockApiResponse)

      const result = await getAccountsList()

      expect(client.get).toHaveBeenCalledWith("/api/v1/currency-accounts")
      expect(result).toEqual(mockApiResponse.data)
    })

    it.skip("should handle API error with response", async () => {
      const error = {
        response: {
          data: {
            message: "API Error Message",
          },
        },
      }
      ;(client.get as any).mockRejectedValueOnce(error)

      await expect(getAccountsList()).rejects.toThrow("API Error Message")
      expect(client.get).toHaveBeenCalledWith("/api/v1/currency-accounts")
    })

    it.skip("should handle API error without response", async () => {
      const error = new Error("Network Error")
      ;(client.get as any).mockRejectedValueOnce(error)

      await expect(getAccountsList()).rejects.toThrow(
        "An error occurred while fetching accounts.",
      )
      expect(client.get).toHaveBeenCalledWith("/api/v1/currency-accounts")
    })
  })

  describe("getAccountsList", () => {
    const mockAccounts = [
      {
        id: "1",
        name: "Test Account",
        balances: [{ currency: "USD", balance: 1000 }],
      },
    ]

    it("should return accounts list successfully", async () => {
      ;(client.get as any).mockResolvedValueOnce({ data: mockAccounts })

      const result = await getAccountsList()

      expect(result).toEqual(mockAccounts)
      expect(client.get).toHaveBeenCalledWith("/api/v1/currency-accounts")
    })

    it("should handle error and throw custom message", async () => {
      ;(client.get as any).mockRejectedValueOnce(new Error("API Error"))

      await expect(getAccountsList()).rejects.toThrow(
        "Failed to fetch accounts list. Please try again later.",
      )
      expect(client.get).toHaveBeenCalledWith("/api/v1/currency-accounts")
    })

    it("should work without entityId parameter", async () => {
      ;(client.get as any).mockResolvedValueOnce({ data: mockAccounts })

      const result = await getAccountsList()

      expect(result).toEqual(mockAccounts)
      expect(client.get).toHaveBeenCalledWith("/api/v1/currency-accounts")
    })
  })
})
