import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook, waitFor } from "@testing-library/react"

import { createWrapper } from "@/tests/utils/create-wrapper"
import { queryClient } from "@/main"
import {
  useCountriesQuery,
  useCurrenciesQuery,
  fetchCurrencyWithQuery,
} from "@/data/global/global.query"
import { fetchCountries, fetchCurrencies } from "@/data/global/global.api"

vi.mock("@/data/global/global.api", () => ({
  fetchCountries: vi.fn(),
  fetchCurrencies: vi.fn(),
}))

vi.mock("@/main", () => ({
  queryClient: {
    fetchQuery: vi.fn(),
  },
}))

describe("Global Queries", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("useCountriesQuery", () => {
    const mockCountries = [
      { code: "US", name: "United States" },
      { code: "UK", name: "United Kingdom" },
    ]

    it("should fetch countries successfully", async () => {
      ;(fetchCountries as any).mockResolvedValue(mockCountries)

      const { result } = renderHook(() => useCountriesQuery(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(fetchCountries).toHaveBeenCalled()
      expect(result.current.data).toEqual(mockCountries)
    })

    it("should handle error states", async () => {
      const error = new Error("Failed to fetch countries")
      ;(fetchCountries as any).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useCountriesQuery(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => expect(result.current.isError).toBe(true))
      expect(result.current.error).toBeDefined()
    })
  })

  describe("useCurrenciesQuery", () => {
    const mockCurrencies = [
      { code: "USD", name: "US Dollar" },
      { code: "EUR", name: "Euro" },
    ]

    it("should fetch currencies successfully", async () => {
      ;(fetchCurrencies as any).mockResolvedValue(mockCurrencies)

      const { result } = renderHook(() => useCurrenciesQuery(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(fetchCurrencies).toHaveBeenCalled()
      expect(result.current.data).toEqual(mockCurrencies)
    })

    it("should handle error states", async () => {
      const error = new Error("Failed to fetch currencies")
      ;(fetchCurrencies as any).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useCurrenciesQuery(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => expect(result.current.isError).toBe(true))
      expect(result.current.error).toBeDefined()
    })
  })

  describe("fetchCurrencyWithQuery", () => {
    const mockCurrencies = [
      { code: "USD", name: "US Dollar" },
      { code: "EUR", name: "Euro" },
    ]

    it("should fetch currencies using queryClient", async () => {
      ;(queryClient.fetchQuery as any).mockResolvedValue(mockCurrencies)

      const result = await fetchCurrencyWithQuery()

      expect(result).toEqual(mockCurrencies)
    })

    it("should handle error states", async () => {
      const error = new Error("Failed to fetch currencies")
      ;(queryClient.fetchQuery as any).mockRejectedValueOnce(error)

      await expect(fetchCurrencyWithQuery()).rejects.toThrow(error)
    })
  })
})
