import { describe, it, expect, vi, beforeEach } from "vitest"

import { ICurrency } from "@/lib/constants/currency.constants"
import { bankingClient as client } from "@/client/banking"
import { ICountry } from "@/data/global/global.interface"
import { fetchCountries, fetchCurrencies } from "@/data/global/global.api"

vi.mock("@/client/banking", () => ({
  bankingClient: {
    get: vi.fn(),
  },
}))

describe("Global API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("fetchCountries", () => {
    const mockCountries: ICountry[] = [
      {
        id: "1",
        name: "United States",
        formalName: "United States of America",
        codeIso2: "US",
        codeIso3: "USA",
        codeIso3Numeric: "840",
        phoneCode: "1",
        ibanLength: 0,
        ibanRegex: "",
        ibanSupported: false,
        accountNumberType: "AccountNumber",
        nationalIdType: "RoutingNumber",
        isSepaCountry: false,
        paymentPurposeCodeRequired: true,
        createdAt: "2024-01-01T00:00:00Z",
        createdBy: "system",
      },
      {
        id: "2",
        name: "United Kingdom",
        formalName: "United Kingdom of Great Britain and Northern Ireland",
        codeIso2: "GB",
        codeIso3: "GBR",
        codeIso3Numeric: "826",
        phoneCode: "44",
        ibanLength: 22,
        ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
        ibanSupported: true,
        accountNumberType: "IBAN",
        nationalIdType: "SortCode",
        isSepaCountry: true,
        paymentPurposeCodeRequired: false,
        createdAt: "2024-01-01T00:00:00Z",
        createdBy: "system",
      },
    ]

    it("should fetch countries successfully", async () => {
      ;(client.get as any).mockResolvedValueOnce({ data: mockCountries })

      const result = await fetchCountries()

      expect(client.get).toHaveBeenCalledWith("/api/v1/countries")
      expect(result).toEqual(mockCountries)
    })

    it("should handle API errors", async () => {
      const error = new Error("API Error")
      ;(client.get as any).mockRejectedValueOnce(error)

      await expect(fetchCountries()).rejects.toThrow("API Error")
      expect(client.get).toHaveBeenCalledWith("/api/v1/countries")
    })
  })

  describe("fetchCurrencies", () => {
    const mockCurrencies: ICurrency[] = [
      {
        code: "USD",
        description: "US Dollar",
        amountDisplayFormat: "###,###.##",
        isTradingRestricted: false,
        canOnlyBeBought: false,
        htmlEncodedSymbol: "&#36;",
        isForPayerId: true,
        factor: 100,
      },
      {
        code: "EUR",
        description: "Euro",
        amountDisplayFormat: "###,###.##",
        isTradingRestricted: false,
        canOnlyBeBought: false,
        htmlEncodedSymbol: "&#8364;",
        isForPayerId: true,
        factor: 100,
      },
    ]

    it("should fetch currencies successfully", async () => {
      ;(client.get as any).mockResolvedValueOnce({ data: mockCurrencies })

      const result = await fetchCurrencies()

      expect(client.get).toHaveBeenCalledWith("/api/v1/currencies")
      expect(result).toEqual(mockCurrencies)
    })

    it.skip("should handle API errors", async () => {
      const error = new Error("API Error")
      ;(client.get as any).mockRejectedValueOnce(error)

      await expect(fetchCurrencies()).rejects.toThrow("API Error")
      expect(client.get).toHaveBeenCalledWith("/api/v1/currencies")
    })
  })
})
