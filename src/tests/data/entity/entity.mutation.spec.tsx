import { describe, expect, it, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"
import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import {
  AddNewUserToExistingEntityPayload,
  useAddNewUserToExistingEntityMutation,
} from "@/data/entity/entity.mutation"
import { addNewUserToExistingEntity } from "@/data/entity/entity.api"

vi.mock("@tanstack/react-query")

vi.mock("@/data/onboarding/$entityId.loader", () => ({
  useLoaderData: () => ({ entityId: "entity-1" }),
}))

vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock("@/data/entity/entity.api", () => ({
  addNewUserToExistingEntity: vi.fn(),
}))

describe("Entity Mutations", () => {
  const mockMutate = vi.fn()
  const mockInvalidateQueries = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Set up mocks for each test
    vi.mocked(useMutation).mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    } as any)

    vi.mocked(useQueryClient).mockReturnValue({
      invalidateQueries: mockInvalidateQueries,
    } as any)
  })

  describe("useAddNewUserMutation", () => {
    it("returns mutation object", () => {
      const { result } = renderHook(() =>
        useAddNewUserToExistingEntityMutation("entity-1"),
      )
      expect(result.current).toHaveProperty("mutate")
      expect(result.current).toHaveProperty("isPending")
    })

    it("calls API with correct data", () => {
      const { result } = renderHook(() =>
        useAddNewUserToExistingEntityMutation("entity-1"),
      )

      const userData: AddNewUserToExistingEntityPayload = {
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        phone: "1234567890",
        dialCode: "+1",
        role: "Administrator",
        signatory: "",
      }

      // When we call mutate
      result.current.mutate(userData)

      // Then our mock mutate function should have been called
      expect(mockMutate).toHaveBeenCalledWith(userData)

      // Access the mutation function directly from useMutation mock
      const mutationOptions = vi.mocked(useMutation).mock.calls[0][0] as any
      const mutationFn = mutationOptions.mutationFn

      // Call it manually to check the API call
      if (mutationFn) {
        mutationFn(userData)
      }

      // And addNewUserToEntity should have been called with the transformed data
      expect(addNewUserToExistingEntity).toHaveBeenCalledWith("entity-1", {
        email: "<EMAIL>",
        phoneNumber: "+11234567890",
        givenName: "Test",
        familyName: "User",
        approverLevel: null,
        roles: ["Administrator"],
      })
    })

    it("handles signatory values", () => {
      renderHook(() => useAddNewUserToExistingEntityMutation("entity-1"))

      const userData: AddNewUserToExistingEntityPayload = {
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        phone: "1234567890",
        dialCode: "+1",
        role: "Approver",
        signatory: "LevelA",
      }

      // Access the mutation function directly from useMutation mock
      const mutationOptions = vi.mocked(useMutation).mock.calls[0][0] as any
      const mutationFn = mutationOptions.mutationFn

      // Call it manually with signatory data
      if (mutationFn) {
        mutationFn(userData)
      }

      // Then addNewUserToEntity should be called with correct parameters
      expect(addNewUserToExistingEntity).toHaveBeenCalledWith("entity-1", {
        email: "<EMAIL>",
        givenName: "Test",
        familyName: "User",
        phoneNumber: "+11234567890",
        roles: ["Approver"],
        approverLevel: "LevelA",
      })
    })

    it("shows success toast", async () => {
      renderHook(() => useAddNewUserToExistingEntityMutation("entity-1"))

      // Get the onSuccess callback
      const mutationOptions = vi.mocked(useMutation).mock.calls[0][0] as any
      const onSuccess = mutationOptions.onSuccess

      // Call it manually
      if (onSuccess) {
        onSuccess()
      }

      // Verify success behavior
      expect(toast.success).toHaveBeenCalledWith("User added successfully")
    })

    it("invalidates queries on success", async () => {
      renderHook(() => useAddNewUserToExistingEntityMutation("entity-1"))

      // Get the onSuccess callback
      const mutationOptions = vi.mocked(useMutation).mock.calls[0][0] as any
      const onSettled = mutationOptions.onSettled

      // Call it manually
      if (onSettled) {
        onSettled()
      }

      // Verify success behavior
      expect(mockInvalidateQueries).toHaveBeenCalled()
    })
  })

  describe("mapUserToOnboardingFormat", () => {
    it("correctly maps user data to onboarding format", () => {
      const userData: AddNewUserToExistingEntityPayload = {
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        phone: "1234567890",
        dialCode: "+1",
        role: "Administrator",
        signatory: "",
      }

      const result = {
        name: `${userData.firstName} ${userData.lastName}`,
        email: userData.email,
        roles: userData.role,
        isSignatory: false,
      }

      expect(result).toEqual({
        name: "Test User",
        email: "<EMAIL>",
        roles: "Administrator",
        isSignatory: false,
      })
    })

    it("sets isSignatory to true when signatory has a value", () => {
      const userData: AddNewUserToExistingEntityPayload = {
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        phone: "1234567890",
        dialCode: "+1",
        role: "Approver",
        signatory: "LevelA",
      }

      const result = {
        name: `${userData.firstName} ${userData.lastName}`,
        email: userData.email,
        roles: userData.role,
        isSignatory: true,
      }

      expect(result).toEqual({
        name: "Test User",
        email: "<EMAIL>",
        roles: "Approver",
        isSignatory: true,
      })
    })
  })
})
