import { describe, expect, it, vi, beforeEach } from "vitest"
import {
  addNewUserToExistingEntity,
  fetchAllUsersWith,
} from "@/data/entity/entity.api"
import {
  usersGetAllUsersWithAccessToEntity,
  usersAddNewLoginWithRolesToExistingEntity,
} from "@/client/onboarding/sdk.gen"
import {
  AddNewLoginWithRolesToExistingEntityRequestDto,
  LoginEntityApproverLevel,
} from "@/client/onboarding/types.gen"
import { AddNewUserToExistingEntityPayload } from "@/data/entity/entity.mutation"

// Mock axios for isAxiosError
vi.mock("axios", () => ({
  isAxiosError: vi.fn((response) => {
    return response && response.status && response.status !== 200
  }),
  HttpStatusCode: {
    BadRequest: 400,
    NotFound: 404,
    Forbidden: 403,
    InternalServerError: 500,
  },
}))

// Mock the SDK calls
vi.mock("@/client/onboarding/sdk.gen", () => ({
  usersGetAllUsersWithAccessToEntity: vi.fn(),
  usersAddNewLoginWithRolesToExistingEntity: vi.fn(),
}))

// Mock error classes
vi.mock("@/data/global/global.exceptions", () => ({
  BadRequestError: class BadRequestError extends Error {
    constructor(message: string) {
      super(message)
    }
  },
  NotFoundError: class NotFoundError extends Error {
    constructor(message: string) {
      super(message)
    }
  },
}))

// Mock the exception-handler
vi.mock("@/data/onboarding/_exception-handler", () => ({
  assertResponse: vi.fn((response) => {
    // If status exists and not 200, it indicates an error response
    if (response && response.status && response.status !== 200) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }
    // Otherwise return the response as is (success case)
    return response
  }),
}))

beforeEach(() => {
  vi.resetAllMocks()
})

describe("Entity Api", () => {
  describe("fetchAllUsersWith", () => {
    it("fetches users for an entity", async () => {
      const mockUsers = [
        {
          id: "user-1",
          displayName: "John Doe",
          email: "<EMAIL>",
          entityAccess: {
            entityAccessId: "access-1",
            roles: [{ name: "Administrator" }],
          },
          status: "Active",
        },
      ]

      vi.mocked(usersGetAllUsersWithAccessToEntity).mockResolvedValue({
        status: 200,
        data: mockUsers,
      } as any)

      const result = await fetchAllUsersWith("entity-1")

      expect(usersGetAllUsersWithAccessToEntity).toHaveBeenCalledWith({
        path: { entityId: "entity-1" },
      })
      expect(result).toEqual(mockUsers)
    })

    it("throws an error when API call fails", async () => {
      vi.mocked(usersGetAllUsersWithAccessToEntity).mockResolvedValue({
        status: 404,
        statusText: "Not Found",
      } as any)

      await expect(async () => {
        await fetchAllUsersWith("invalid-entity")
      }).rejects.toThrow("API Error: 404 Not Found")
    })
  })

  describe("addNewUserToEntity", () => {
    it("adds a new user to an entity", async () => {
      const mockUserData: AddNewUserToExistingEntityPayload = {
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "1234567890",
        dialCode: "+1",
        role: "Administrator",
        signatory: "",
      }

      const mockResponse = { id: "new-user-1" }

      // Set up the mock with a successful response
      vi.mocked(usersAddNewLoginWithRolesToExistingEntity).mockResolvedValue({
        status: 200,
        data: mockResponse,
      } as any)

      const requestData = {
        email: mockUserData.email,
        givenName: mockUserData.firstName,
        familyName: mockUserData.lastName,
        phoneNumber: `${mockUserData.dialCode}${mockUserData.phone}`,
        roles: [mockUserData.role],
        approverLevel:
          (mockUserData.signatory as LoginEntityApproverLevel) || undefined,
      } as AddNewLoginWithRolesToExistingEntityRequestDto

      // Call the function - it doesn't return anything in the actual implementation
      await addNewUserToExistingEntity("entity-1", requestData)

      // Verify API was called with correct parameters
      expect(usersAddNewLoginWithRolesToExistingEntity).toHaveBeenCalledWith({
        path: { entityId: "entity-1" },
        body: {
          email: mockUserData.email,
          givenName: mockUserData.firstName,
          familyName: mockUserData.lastName,
          phoneNumber: "+11234567890",
          roles: ["Administrator"],
          approverLevel: undefined,
        },
      })
    })

    it("throws an error when API call fails", async () => {
      const mockUserData = {
        email: "<EMAIL>",
        givenName: "Jane",
        familyName: "Smith",
        phoneNumber: "+11234567890",
        roles: ["Administrator"],
      }

      vi.mocked(usersAddNewLoginWithRolesToExistingEntity).mockResolvedValue({
        status: 400,
        statusText: "Bad Request",
      } as any)

      await expect(async () => {
        await addNewUserToExistingEntity("entity-1", mockUserData)
      }).rejects.toThrow("API Error: 400 Bad Request")
    })
  })
})
