import { describe, it, expect, vi, beforeEach, Mock } from "vitest"

import { bankingClient as client } from "@/client/banking"
import {
  downloadTransactions,
  fetchTransactions,
  fetchTransactionById,
} from "@/data/transactions/transactions.api"

vi.mock("@/client/banking", () => ({
  bankingClient: {
    get: vi.fn(),
  },
}))

describe("Transactions API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, "error").mockImplementation(() => {})
  })

  describe("downloadTransactions", () => {
    const mockParams = {
      accountId: "acc123",
      fileType: "csv" as "csv" | "pdf",
      currency: "USD",
      fromDate: "2024-01-01",
      toDate: "2024-02-01",
    }

    it.skip("should handle error when downloading transactions", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(client.get as Mock).mockRejectedValueOnce(error)

      await expect(downloadTransactions(mockParams)).rejects.toThrow(
        "API Error",
      )
    })

    it.skip("should handle error without response data", async () => {
      const error = new Error("Network Error")
      ;(client.get as Mock).mockRejectedValueOnce(error)

      await expect(downloadTransactions(mockParams)).rejects.toThrow(
        "Failed to download transactions",
      )
    })
  })

  describe("fetchTransactions", () => {
    const mockParams = {
      accountId: "acc123",
      currency: "USD",
      fromDate: "2024-01-01",
      toDate: "2024-02-01",
    }

    const mockResponse = {
      data: [
        { id: "1", amount: 100 },
        { id: "2", amount: 200 },
      ],
      totalCount: 2,
    }

    it("should fetch transactions successfully", async () => {
      ;(client.get as Mock).mockResolvedValueOnce({ data: mockResponse })

      const result = await fetchTransactions(mockParams)

      expect(client.get).toHaveBeenCalledWith(
        `/api/v1/currency-accounts/${mockParams.accountId}`,
        {
          params: {
            fromDate: mockParams.fromDate,
            toDate: mockParams.toDate,
            pageNumber: 1,
            pageSize: 10,
          },
        },
      )
      expect(result).toEqual(mockResponse)
    })

    it("should handle error when fetching transactions", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(client.get as Mock).mockRejectedValueOnce(error)

      await expect(fetchTransactions(mockParams)).rejects.toThrow("API Error")
    })

    it("should handle error without response data", async () => {
      const error = new Error("Network Error")
      ;(client.get as Mock).mockRejectedValueOnce(error)

      await expect(fetchTransactions(mockParams)).rejects.toThrow(
        "Failed to fetch transactions",
      )
    })
  })
})
