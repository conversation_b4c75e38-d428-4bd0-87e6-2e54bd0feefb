import { describe, it, expect, vi, beforeEach, Mock } from "vitest"

import { bankingClient as client } from "@/client/banking"
import {
  getPayeesList,
  postPayee,
  fetchPayeeById,
  deletePayee,
} from "@/data/payees/payees.api"
import { IPayeeForm } from "@/data/payees/payees.interface"

const MOCK_API_KEY = "test-api-key"

vi.stubGlobal("import", {
  meta: {
    env: {
      VITE_LOQATE_API_KEY: MOCK_API_KEY,
    },
  },
})

vi.mock("axios", () => ({
  default: {
    get: vi.fn(),
  },
}))

vi.mock("@/client/banking", () => ({
  bankingClient: {
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn(),
  },
}))

describe("Payees API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, "error").mockImplementation(() => {})
  })

  describe("getPayeesList", () => {
    const mockParams = {
      entityId: "entity123",
      pageNumber: 1,
      pageSize: 10,
    }

    // Expected parameters with defaults supplied by the getPayeesList function
    const expectedParams = {
      entityId: "entity123",
      pageNumber: 1,
      pageSize: 10,
      createdAtStart: undefined,
      createdAtEnd: undefined,
      accountName: undefined,
      type: undefined,
      orderByField: "createdAt",
      orderByDirection: "desc",
    }

    const mockPayees = {
      data: {
        data: [
          { id: "1", name: "Payee 1" },
          { id: "2", name: "Payee 2" },
        ],
        totalCount: 2,
      },
    }

    it("should fetch payees list successfully", async () => {
      ;(client.get as Mock).mockResolvedValueOnce(mockPayees)

      const result = await getPayeesList(mockParams)

      expect(client.get).toHaveBeenCalledWith("/api/v1/payees", {
        params: expectedParams,
      })
      expect(result).toEqual({
        items: mockPayees.data.data,
        totalItems: mockPayees.data.totalCount,
      })
    })

    it("should handle empty response", async () => {
      ;(client.get as Mock).mockResolvedValueOnce({ data: {} })

      const result = await getPayeesList(mockParams)

      expect(result).toEqual({
        items: [],
        totalItems: 0,
      })
    })

    it("should handle API error", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(client.get as Mock).mockRejectedValueOnce(error)

      await expect(getPayeesList(mockParams)).rejects.toThrow("API Error")
    })
  })

  describe("postPayee", () => {
    const mockPayeeForm: IPayeeForm = {
      bankDetails: {
        accountName: "John Doe",
        isBusiness: false,
        countryDetails: {
          id: "1",
          name: "United Kingdom",
          formalName: "United Kingdom of Great Britain and Northern Ireland",
          codeIso2: "GB",
          codeIso3: "GBR",
          codeIso3Numeric: "826",
          phoneCode: "44",
          ibanLength: 22,
          ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
          ibanSupported: true,
          accountNumberType: "IBAN",
          nationalIdType: "SortCode",
          isSepaCountry: true,
          paymentPurposeCodeRequired: false,
          createdAt: "2024-01-01T00:00:00Z",
          createdBy: "system",
        },
        bankCodeValue: "123456",
        accountNumberValue: "**********************",
      },
      payeeDetails: {
        displayName: "John Doe",
        email: "<EMAIL>",
        nationality: "United Kingdom",
        searchTextAddress: "",
        selectedAddress: "",
        address: {
          buildingNumber: "123",
          street: "Main Street",
          city: "London",
          state: "Greater London",
          postalCode: "SW1A 1AA",
          country: "United Kingdom",
        },
        countryDetails: {
          id: "1",
          name: "United Kingdom",
          formalName: "United Kingdom of Great Britain and Northern Ireland",
          codeIso2: "GB",
          codeIso3: "GBR",
          codeIso3Numeric: "826",
          phoneCode: "44",
          ibanLength: 22,
          ibanRegex: "^[A-Z]{2}[0-9]{2}[A-Z]{4}[0-9]{7}[A-Z]{0,1}[0-9]{0,1}$",
          ibanSupported: true,
          accountNumberType: "IBAN",
          nationalIdType: "SortCode",
          isSepaCountry: true,
          paymentPurposeCodeRequired: false,
          createdAt: "2024-01-01T00:00:00Z",
          createdBy: "system",
        },
      },
    }

    const mockResponse = { id: "payee123" }

    it("should post payee successfully", async () => {
      ;(client.post as Mock).mockResolvedValueOnce({ data: mockResponse })

      const result = await postPayee(mockPayeeForm)

      expect(client.post).toHaveBeenCalledWith(
        "/api/v1/payees",
        expect.objectContaining({
          displayName: "John Doe",
          accountName: "John Doe",
          city: "London",
          postalCode: "SW1A 1AA",
          regionState: "Greater London",
          streetName: "Main Street",
          buildingNumber: "123",
          payeeType: "Individual",
          email: "<EMAIL>",
          iban: "**********************",
          nationalId: "123456",
          country: "GB",
        }),
      )
      expect(result).toEqual(mockResponse)
    })

    it.skip("should handle API error", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(client.post as Mock).mockRejectedValueOnce(error)

      await expect(postPayee(mockPayeeForm)).rejects.toThrow()
    })
  })

  describe("fetchPayeeById", () => {
    const mockPayeeId = "payee123"
    const mockPayee = { id: mockPayeeId, name: "Test Payee" }

    it("should fetch payee by id successfully", async () => {
      ;(client.get as Mock).mockResolvedValueOnce({ data: mockPayee })

      const result = await fetchPayeeById(mockPayeeId)

      expect(client.get).toHaveBeenCalledWith(`/api/v1/payees/${mockPayeeId}`)
      expect(result).toEqual(mockPayee)
    })

    it("should throw error if id is empty", async () => {
      await expect(fetchPayeeById("")).rejects.toThrow("Payee ID is required.")
      expect(client.get).not.toHaveBeenCalled()
    })

    it.skip("should handle API error", async () => {
      const error = { response: { data: { message: "API Error" } } }
      ;(client.get as Mock).mockRejectedValueOnce(error)

      await expect(fetchPayeeById(mockPayeeId)).rejects.toThrow("API Error")
    })
  })

  describe("deletePayee", () => {
    const mockPayeeId = "payee123"

    it("should delete payee successfully", async () => {
      const mockResponse = { success: true }
      ;(client.delete as Mock).mockResolvedValueOnce({ data: mockResponse })

      const result = await deletePayee(mockPayeeId)

      expect(client.delete).toHaveBeenCalledWith(
        `/api/v1/payees/${mockPayeeId}`,
      )
      expect(result).toEqual(mockResponse)
    })

    it("should throw error if id is empty", async () => {
      await expect(deletePayee("")).rejects.toThrow("Payee ID is required.")
      expect(client.delete).not.toHaveBeenCalled()
    })

    it("should handle complex error structure with errors array", async () => {
      const error = {
        response: {
          data: {
            errors: [
              {
                name: "error_0",
                reason:
                  "Payee could not be deleted due to having 1 or more outbound payments assigned to it.",
              },
            ],
          },
        },
      }
      ;(client.delete as Mock).mockRejectedValueOnce(error)

      await expect(deletePayee(mockPayeeId)).rejects.toThrow(
        "Payee could not be deleted due to having 1 or more outbound payments assigned to it.",
      )
    })

    it("should handle multiple errors in errors array", async () => {
      const error = {
        response: {
          data: {
            errors: [
              {
                name: "error_0",
                reason: "First error message",
              },
              {
                name: "error_1",
                reason: "Second error message",
              },
            ],
          },
        },
      }
      ;(client.delete as Mock).mockRejectedValueOnce(error)

      await expect(deletePayee(mockPayeeId)).rejects.toThrow(
        "First error message, Second error message",
      )
    })

    it("should fallback to message if errors array is not present", async () => {
      const error = {
        response: {
          data: {
            message: "Simple error message",
          },
        },
      }
      ;(client.delete as Mock).mockRejectedValueOnce(error)

      await expect(deletePayee(mockPayeeId)).rejects.toThrow(
        "Simple error message",
      )
    })

    it("should use default error message if no specific error is provided", async () => {
      const error = { response: { data: {} } }
      ;(client.delete as Mock).mockRejectedValueOnce(error)

      await expect(deletePayee(mockPayeeId)).rejects.toThrow(
        "Failed to delete payee. Please try again.",
      )
    })
  })
})
