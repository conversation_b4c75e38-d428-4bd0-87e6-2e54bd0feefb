import { describe, it, expect, vi, beforeEach, Mock } from "vitest"
import { toast } from "sonner"
import { renderHook, waitFor } from "@testing-library/react"

import { createWrapper } from "@/tests/utils/test-utils"
import { queryClient } from "@/main"
import {
  addPayeeMutation,
  useDeletePayeeMutation,
} from "@/data/payees/payees.mutation"
import { postPayee, deletePayee } from "@/data/payees/payees.api"

vi.mock("@/data/payees/payees.api", () => ({
  postPayee: vi.fn(),
  deletePayee: vi.fn(),
}))

vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock("@/main", () => ({
  queryClient: {
    invalidateQueries: vi.fn(),
  },
}))

describe("Payee Mutations", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("addPayeeMutation", () => {
    const mockPayee = {
      bankDetails: {
        bankCode: "TESTBANK",
        payeeBankId: "bank123",
        nationalId: "NAT123",
        nationalIdType: "TEST",
        swiftBic: "TESTBIC",
        country: "DE",
        isBusiness: true,
        accountName: "Test Business Account",
      },
      payeeDetails: {
        name: "Test Payee",
        displayName: "Test Payee Ltd",
        nationality: "Germany",
        accountNumber: "*********",
        iban: "**********************",
        searchTextAddress: "123 Test St",
        selectedAddress: "123 Test St, Test City",
        address: {
          street: "Test St",
          buildingNumber: "123",
          city: "Test City",
          state: "Test State",
          country: "Germany",
          postalCode: "12345",
        },
      },
    }

    const mockResponse = {
      id: "payee123",
      ...mockPayee,
    }

    it("should handle successful payee creation", async () => {
      ;(postPayee as Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => addPayeeMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockPayee)

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(postPayee).toHaveBeenCalledWith(mockPayee)
    })

    it("should handle payee creation failure", async () => {
      const error = new Error("API Error")
      ;(postPayee as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => addPayeeMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockPayee)

      await waitFor(() => expect(result.current.isError).toBe(true))

      expect(toast.error).toHaveBeenCalledWith(
        "Failed to add payee",
        expect.objectContaining({
          description: error.message,
        }),
      )

      expect(queryClient.invalidateQueries).not.toHaveBeenCalled()
    })

    it("should not invalidate cache on error", async () => {
      const error = new Error("API Error")
      ;(postPayee as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => addPayeeMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockPayee)

      await waitFor(() => expect(result.current.isError).toBe(true))
      expect(queryClient.invalidateQueries).not.toHaveBeenCalled()
    })
  })

  describe("useDeletePayeeMutation", () => {
    const mockPayeeId = "payee123"

    it("should handle successful payee deletion", async () => {
      const mockResponse = { success: true }
      ;(deletePayee as Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useDeletePayeeMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockPayeeId)

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(deletePayee).toHaveBeenCalledWith(mockPayeeId)

      expect(toast.success).toHaveBeenCalledWith(
        "Payee has been deleted successfully.",
        expect.objectContaining({
          duration: 5000,
        }),
      )
    })

    it("should handle payee deletion failure with complex error structure", async () => {
      const error = new Error(
        "Payee could not be deleted due to having 1 or more outbound payments assigned to it.",
      )
      ;(deletePayee as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useDeletePayeeMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockPayeeId)

      await waitFor(() => expect(result.current.isError).toBe(true))

      expect(toast.error).toHaveBeenCalledWith(
        "Cannot delete payee",
        expect.objectContaining({
          description:
            "Payee could not be deleted due to having 1 or more outbound payments assigned to it.",
          duration: 5000,
        }),
      )

      expect(queryClient.invalidateQueries).not.toHaveBeenCalled()
    })

    it("should handle payee deletion failure with simple error", async () => {
      const error = new Error("Simple error message")
      ;(deletePayee as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useDeletePayeeMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockPayeeId)

      await waitFor(() => expect(result.current.isError).toBe(true))

      expect(toast.error).toHaveBeenCalledWith(
        "Cannot delete payee",
        expect.objectContaining({
          description: "Simple error message",
          duration: 5000,
        }),
      )
    })

    it("should use default error message when no specific error is provided", async () => {
      const error = new Error("")
      ;(deletePayee as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useDeletePayeeMutation(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockPayeeId)

      await waitFor(() => expect(result.current.isError).toBe(true))

      expect(toast.error).toHaveBeenCalledWith(
        "Cannot delete payee",
        expect.objectContaining({
          description: "Failed to delete payee. Please try again.",
          duration: 5000,
        }),
      )
    })
  })
})
