import { describe, it, expect, vi, beforeEach, Mock } from "vitest"
import { renderHook, waitFor } from "@testing-library/react"

import { createWrapper } from "@/tests/utils/test-utils"
import { bankingClient as client } from "@/client/banking"
import { usePayeesListQuery } from "@/data/payees/payees.query"
import { getPayeesList, fetchShortCode } from "@/data/payees/payees.api"

vi.mock("@/client/banking", () => ({
  bankingClient: {
    get: vi.fn(),
  },
}))

vi.mock("@/data/payees/payees.api", () => ({
  fetchLoqateAddress: vi.fn(),
  fetchLoqateAddressesList: vi.fn(),
  getPayeesList: vi.fn(),
}))

describe("Payees Queries", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("usePayeesListQuery", () => {
    const mockPayees = {
      items: [
        { id: "1", name: "Payee 1" },
        { id: "2", name: "Payee 2" },
      ],
      total: 2,
    }

    it("should fetch payees list when entityId is provided", async () => {
      ;(getPayeesList as Mock).mockResolvedValueOnce(mockPayees)

      const { result } = renderHook(
        () => usePayeesListQuery({ entityId: "entity123" }),
        { wrapper: createWrapper() },
      )

      await waitFor(() => expect(result.current.isSuccess).toBe(true))
      expect(result.current.data).toEqual(mockPayees)
      expect(getPayeesList).toHaveBeenCalledWith({
        entityId: "entity123",
        pageNumber: 1,
        pageSize: 10,
        createdAtStart: undefined,
        createdAtEnd: undefined,
        accountName: undefined,
        type: undefined,
        orderByField: "createdAt",
        orderByDirection: "desc",
      })
    })

    it("should not fetch if entityId is not provided", () => {
      const { result } = renderHook(
        () => usePayeesListQuery({ entityId: "" }),
        { wrapper: createWrapper() },
      )

      expect(result.current.isLoading).toBe(false)
      expect(getPayeesList).not.toHaveBeenCalled()
    })

    it("should use custom pagination parameters", async () => {
      ;(getPayeesList as Mock).mockResolvedValueOnce(mockPayees)

      const { result } = renderHook(
        () =>
          usePayeesListQuery({
            entityId: "entity123",
            pageNumber: 2,
            pageSize: 20,
          }),
        { wrapper: createWrapper() },
      )

      await waitFor(() => expect(result.current.isSuccess).toBe(true))
      expect(getPayeesList).toHaveBeenCalledWith({
        entityId: "entity123",
        pageNumber: 2,
        pageSize: 20,
        createdAtStart: undefined,
        createdAtEnd: undefined,
        accountName: undefined,
        type: undefined,
        orderByField: "createdAt",
        orderByDirection: "desc",
      })
    })
  })
})
