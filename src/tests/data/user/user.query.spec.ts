import { describe, it, expect, beforeEach, vi } from "vitest"
import { renderHook, waitFor } from "@testing-library/react"
import { Mock } from "vitest"

import { useCurrentUser } from "@/data/user/user.query"
import { fetchCurrentUser } from "@/data/user/user.api"

// Mock the API function
vi.mock("@/data/user/user.api", () => ({
  fetchCurrentUser: vi.fn(),
}))

// Create wrapper for the query client
import { createWrapper } from "@/tests/utils/create-wrapper"

describe("User Queries", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("useCurrentUser", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      phoneNumber: "+447700900000",
      status: "Active",
      displayName: "Test User",
    }

    it("should fetch current user successfully", async () => {
      ;(fetchCurrentUser as Mock).mockResolvedValue(mockUser)

      const { result } = renderHook(() => useCurrentUser(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(fetchCurrentUser).toHaveBeenCalled()
      expect(result.current.data).toEqual(mockUser)
    })

    it("should handle error states", async () => {
      const error = new Error("Failed to fetch current user")
      ;(fetchCurrentUser as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useCurrentUser(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => expect(result.current.isError).toBe(true))
      expect(result.current.error).toBeDefined()
    })
  })
})
