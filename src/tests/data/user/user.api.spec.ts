import { describe, it, expect, vi, beforeEach } from "vitest"
import { Mo<PERSON> } from "vitest"

import {
  fetchCurrentUser,
  fetchAllUsersWithEntities,
} from "@/data/user/user.api"
import {
  usersGetAllUsersWithAccessToEntitiesForAdminUser,
  usersGetCurrentUser,
} from "@/client/onboarding/sdk.gen"

// Mock the SDK function
vi.mock("@/client/onboarding/sdk.gen", () => ({
  usersGetCurrentUser: vi.fn(),
  usersGetAllUsersWithAccessToEntitiesForAdminUser: vi.fn(),
}))

// Mock the exception handler
vi.mock("@/data/onboarding/_exception-handler", () => ({
  assertResponse: vi.fn((response) => {
    // If status exists and not 200, it indicates an error response
    if (response && response.status && response.status !== 200) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }
    // Otherwise return the response as is (success case)
    return response
  }),
}))

describe("User API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("fetchCurrentUser", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      phoneNumber: "+447700900000",
      status: "Active",
      displayName: "Test User",
    }

    it("should fetch current user successfully", async () => {
      ;(usersGetCurrentUser as Mock).mockResolvedValue({
        status: 200,
        data: mockUser,
      })

      const result = await fetchCurrentUser()

      expect(usersGetCurrentUser).toHaveBeenCalled()
      expect(result).toEqual(mockUser)
    })

    it("should handle error responses", async () => {
      ;(usersGetCurrentUser as Mock).mockResolvedValue({
        status: 400,
        statusText: "Bad Request",
      })

      await expect(fetchCurrentUser()).rejects.toThrow(
        "API Error: 400 Bad Request",
      )
    })
  })

  describe("fetchAllUsersWithEntities", () => {
    it("fetches all users with entities successfully", async () => {
      const mockUsers = [
        {
          id: "user-1",
          email: "<EMAIL>",
          displayName: "Existing User",
          status: "Active",
        },
        {
          id: "user-2",
          email: "<EMAIL>",
          displayName: "Another User",
          status: "Active",
        },
      ]

      vi.mocked(
        usersGetAllUsersWithAccessToEntitiesForAdminUser,
      ).mockResolvedValue({
        status: 200,
        data: mockUsers,
      } as any)

      const result = await fetchAllUsersWithEntities()

      expect(
        usersGetAllUsersWithAccessToEntitiesForAdminUser,
      ).toHaveBeenCalled()
      expect(result).toEqual(mockUsers)
    })

    it("throws an error when API call fails", async () => {
      vi.mocked(
        usersGetAllUsersWithAccessToEntitiesForAdminUser,
      ).mockResolvedValue({
        status: 500,
        statusText: "Server Error",
      } as any)

      await expect(async () => {
        await fetchAllUsersWithEntities()
      }).rejects.toThrow("API Error: 500 Server Error")
    })
  })
})
