import { useQuery } from "@tanstack/react-query"
import {
  fetchRolesAndPermissions,
  getSignatoryMatrix,
  getUsers,
} from "./user-admin.api"
import { GetUsersParams } from "./user-admin.interface"
import { STALE_TIME } from "../global/global"

export function useRolesAndPermissionsQuery() {
  return useQuery({
    staleTime: STALE_TIME,
    queryKey: ["roles-and-permissions"],
    queryFn: () => fetchRolesAndPermissions(),
    enabled: true,
  })
}

export function useSignatoryMatrixQuery() {
  return useQuery({
    queryKey: ["signatory-matrix"],
    queryFn: () => getSignatoryMatrix(),
    enabled: true,
  })
}

export function useUsersQuery(params: GetUsersParams) {
  return useQuery({
    queryKey: ["users", params],
    queryFn: () => getUsers(params),
    enabled: true,
  })
}
