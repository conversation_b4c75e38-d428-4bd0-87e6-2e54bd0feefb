import { SignatoryMatrixResponse } from "./user-admin.interface"

export interface Tier {
  start: number
  end: number
}

export interface SignatoryRule {
  A: number
  B: number
  C: number
}

export type DialogMode = "create" | "update"

export interface PaymentTiersDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tiers: Tier[]
  tierErrors: string[]
  onTierEndChange: (index: number, value: number) => void
  onAddTier: () => void
  onRemoveTier: (index: number) => void
  canAddTier: boolean
  signatoryRules: SignatoryRule[]
  setSignatoryRules: React.Dispatch<React.SetStateAction<SignatoryRule[]>>
  mode: DialogMode
  signatoryMatrix?: SignatoryMatrixResponse
}
