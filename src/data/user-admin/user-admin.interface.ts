/* Permissions Response */
interface PermissionRoleAccess {
  Administrator?: boolean
  Approver?: boolean
  Submitter?: boolean
  Viewer?: boolean
}

interface Permission {
  permission: string
  displayName: string
  roleAccess: PermissionRoleAccess
}

interface PermissionCategory {
  category: string
  permissions: Permission[]
}

export type PermissionsResponse = PermissionCategory[]

export enum ApproverLevel {
  LevelA = "LevelA",
  LevelB = "LevelB",
  LevelC = "LevelC",
}

/* Signatory Matrix Request */
interface CreateSignatoryRule {
  approverLevel: ApproverLevel
  requiredCount: number
}

interface CreateSignatoryAmountBand {
  maximumAmount: number
  signatoryRules: CreateSignatoryRule[]
}

export interface CreateSignatoryMatrixRequest {
  id?: string
  currency: string
  enabled: boolean
  signatoryAmountBands: CreateSignatoryAmountBand[]
}

/* Signatory Matrix Response */
interface SignatoryRule {
  id: string
  approverLevel: ApproverLevel
  requiredCount: number
}

interface SignatoryAmountBand {
  id: string
  maximumAmount: number
  signatoryRules: SignatoryRule[]
}

export interface SignatoryMatrixResponse {
  id: string
  clientId: string
  currency: string
  enabled: boolean
  signatoryAmountBands: SignatoryAmountBand[]
}

/* User Response */
interface Approver {
  id: string
  email: string
  phoneNumber: string
  displayName: string
  status: string
  approverLevel: string
}

export interface PagedApproverResponse {
  data: Approver[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFirstPage: boolean
  isLastPage: boolean
  pageStartIndex: number
  pageEndIndex: number
}

export interface GetUsersParams {
  displayName?: string | null
  email?: string | null
  phoneNumber?: string | null
  status?: string | null
  approverLevel?: string | null
  pageNumber?: number
  pageSize?: number
  orderByField?: string
  orderByDirection?: string
}
