import { useMutation } from "@tanstack/react-query"
import {
  postSignatoryMatrix,
  updateClient,
  updateSignatoryMatrix,
} from "./user-admin.api"
import { queryClient } from "@/main"
import { CreateSignatoryMatrixRequest } from "./user-admin.interface"
import { toast } from "sonner"
import { getHttpErrorMessage } from "@/lib/http.error.utils"

export const usePostSignatoryMatrixMutation = () => {
  return useMutation({
    mutationFn: postSignatoryMatrix,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["signatory-matrix"] })
    },
    onError: (error) => {
      console.error(error)
      toast.error("Failed to create signatory matrix")
    },
    onMutate: () => {
      queryClient.invalidateQueries({ queryKey: ["signatory-matrix"] })
    },
  })
}

export const useUpdateSignatoryMatrixMutation = () => {
  return useMutation({
    mutationKey: ["update-signatory-matrix"],
    mutationFn: ({
      id,
      payload,
    }: {
      id: string
      payload: CreateSignatoryMatrixRequest
    }) => updateSignatoryMatrix(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["signatory-matrix"] })
    },
    onError: (error) => {
      const message = getHttpErrorMessage(error)
      toast.error(message ?? "Failed to update signatory matrix")
    },
    onMutate: () => {
      queryClient.invalidateQueries({ queryKey: ["signatory-matrix"] })
    },
  })
}

export const useUpdateClientMutation = () => {
  return useMutation({
    mutationFn: updateClient,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["signatory-validation", 1000000, "GBP", false],
      })
    },
    onError: (error) => {
      console.error(error)
      toast.error("Failed to update client")
    },
  })
}
