import { bankingClient as client } from "@/client/banking"
import {
  CreateSignatoryMatrixRequest,
  GetUsersParams,
  PagedApproverResponse,
  PermissionsResponse,
  SignatoryMatrixResponse,
} from "./user-admin.interface"

export const fetchRolesAndPermissions =
  async (): Promise<PermissionsResponse> => {
    try {
      const { data } = await client.get<PermissionsResponse>(
        `/api/v1/role-permissions`,
      )

      return data
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
          "Failed to fetch roles and permissions",
      )
    }
  }

export const postSignatoryMatrix = async (
  payload: CreateSignatoryMatrixRequest,
): Promise<CreateSignatoryMatrixRequest> => {
  try {
    const { data } = await client.post<CreateSignatoryMatrixRequest>(
      `/api/v1/signatory-matrix`,
      payload,
    )

    return data
  } catch (error: any) {
    throw new Error("Failed to fetch signatory matrix")
  }
}

export const updateSignatoryMatrix = async (
  id: string,
  payload: CreateSignatoryMatrixRequest,
): Promise<CreateSignatoryMatrixRequest> => {
  const { data } = await client.patch<CreateSignatoryMatrixRequest>(
    `/api/v1/signatory-matrix/${id}`,
    payload,
  )
  return data
}

export const getSignatoryMatrix =
  async (): Promise<SignatoryMatrixResponse> => {
    try {
      const { data } = await client.get<SignatoryMatrixResponse>(
        `/api/v1/signatory-matrix`,
      )

      return data
    } catch (error: any) {
      throw new Error("Failed to fetch signatory matrix")
    }
  }

export const getUsers = async (
  params: GetUsersParams = {},
): Promise<PagedApproverResponse> => {
  try {
    const { data } = await client.get<PagedApproverResponse>(`/api/v1/users`, {
      params,
    })

    return data
  } catch (error: any) {
    throw new Error("Failed to fetch users")
  }
}

export const updateClient = async (
  canApproveOwnSubmission: boolean,
): Promise<{
  aggregateId: string
  correlationId: string
}> => {
  try {
    const { data } = await client.patch<{
      aggregateId: string
      correlationId: string
    }>(`/api/v1/clients`, { canApproveOwnSubmission })

    return data
  } catch (error: any) {
    throw new Error("Failed to update client")
  }
}
