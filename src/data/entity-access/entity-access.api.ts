import { HttpStatusCode } from "axios"

import {
  CreateEntityAccessRequestDto,
  UpdateEntityAccessRequestDto,
} from "@/client/onboarding/types.gen"
import {
  entityAccessCreateEntityAccess,
  entityAccessDeleteEntityAccess,
  entityAccessGetEntityAccess,
  entityAccessUpdateEntityAccess,
} from "@/client/onboarding/sdk.gen"

import { BadRequestError } from "../global/global.exceptions"
import { assertResponse } from "../onboarding/_exception-handler"

export async function fetchEntityAccess() {
  const response = await entityAccessGetEntityAccess()

  assertResponse(response, {
    [HttpStatusCode.NotFound]: new BadRequestError(
      "[Onboarding]: Entity access not found",
    ),
  })

  const filteredData = response.data?.map(({ entityId, ...entity }) => {
    return { ...entity, entityId: entityId?.toLowerCase() }
  })

  return filteredData
}

export async function createEntityAccess(
  payload: CreateEntityAccessRequestDto,
) {
  const response = await entityAccessCreateEntityAccess({
    body: payload,
  })

  assertResponse(response, {
    [HttpStatusCode.BadRequest]: new BadRequestError(
      "Unable to create entity access",
    ),
  })
}

export async function updateEntityAccess(
  entityAccessId: string,
  payload: UpdateEntityAccessRequestDto,
) {
  const response = await entityAccessUpdateEntityAccess({
    path: { entityAccessId },
    body: payload,
  })

  assertResponse(response, {
    [HttpStatusCode.BadRequest]: new BadRequestError(
      "Unable to update entity access",
    ),
  })
}

export async function deleteEntityAccess(entityAccessId: string) {
  const response = await entityAccessDeleteEntityAccess({
    path: { entityAccessId },
  })

  assertResponse(response)
}
