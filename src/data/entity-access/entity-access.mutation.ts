import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import {
  CreateEntityAccessRequestDto,
  UpdateEntityAccessRequestDto,
} from "@/client/onboarding/types.gen"

import { GET_ENTITY_ACCESS_QRY_KEY } from "./entity-access.query"
import * as api from "./entity-access.api"
import { GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY } from "../entity/entity.query"
import { GET_ALL_USERS_WITH_ENTITIES_QRY_KEY } from "../user"

export const CREATE_ENTITY_ACCESS_MUTATION_KEY = "create-entity-access"
export const UPDATE_ENTITY_ACCESS_MUTATION_KEY = "update-entity-access"
export const DELETE_ENTITY_ACCESS_MUTATION_KEY = "delete-entity-access"

export function useEntityAccessMutations(entityId: string) {
  const queryClient = useQueryClient()

  const { mutate: createEntityAccess } = useMutation({
    mutationKey: ["entity-access", CREATE_ENTITY_ACCESS_MUTATION_KEY, entityId],
    mutationFn: (payload: Omit<CreateEntityAccessRequestDto, "entityId">) =>
      api.createEntityAccess({
        entityId,
        ...payload,
      }),
    onError: (error) => {
      toast.error("Failed to update entity access", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Entity access created successfully", {
        duration: 5000,
      })
    },
    onSettled: async () => {
      queryClient.invalidateQueries({
        queryKey: ["entity-access", GET_ENTITY_ACCESS_QRY_KEY, entityId],
      })
    },
  })

  const { mutate: updateEntityAccess } = useMutation({
    mutationKey: ["entity-access", UPDATE_ENTITY_ACCESS_MUTATION_KEY],
    mutationFn: (props: {
      entityAccessId: string
      payload: UpdateEntityAccessRequestDto
    }) => api.updateEntityAccess(props.entityAccessId, props.payload),
    onError: (error) => {
      toast.error("Failed to update entity access", {
        description: error.message,
      })
    },
    onSuccess: () => {},
    onSettled: async () => {
      toast.success("Entity access update successfully", {
        duration: 5000,
      })

      queryClient.invalidateQueries({
        queryKey: ["entity-access", GET_ENTITY_ACCESS_QRY_KEY, entityId],
      })
    },
  })

  const { mutate: deleteEntityAccess } = useMutation({
    mutationKey: ["entity-access", DELETE_ENTITY_ACCESS_MUTATION_KEY, entityId],
    mutationFn: (payload: string) => api.deleteEntityAccess(payload),
    onError: (error) =>
      toast.error("Failed to delete user", {
        description: error.message,
      }),
    onSuccess: () =>
      toast.success("Entity access removed", {
        duration: 5000,
      }),
    onSettled: async () =>
      Promise.all([
        queryClient.invalidateQueries({
          queryKey: ["entity-access", GET_ENTITY_ACCESS_QRY_KEY, entityId],
        }),
        queryClient.invalidateQueries({
          queryKey: ["user", GET_ALL_USERS_WITH_ENTITIES_QRY_KEY],
        }),
        queryClient.invalidateQueries({
          queryKey: ["entity", GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY, entityId],
        }),
      ]),
  })

  return {
    createEntityAccess,
    updateEntityAccess,
    deleteEntityAccess,
  }
}
