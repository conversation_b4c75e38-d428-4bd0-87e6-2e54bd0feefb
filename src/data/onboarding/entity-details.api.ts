import { HttpStatusCode } from "axios"

import {
  CreateAddressRequestDto,
  GetAddressDto,
  UpdateAddressDto,
  UpdateEntityRequestDto,
} from "@/client/onboarding/types.gen"
import {
  entitiesGetEntity,
  entitiesUpdateEntity,
  investmentsGet,
  addressesGetAll,
  addressesPut,
  addressesPost,
} from "@/client/onboarding/sdk.gen"

import { assertResponse } from "./_exception-handler"
import { BadRequestError } from "../global/global.exceptions"

export async function fetchEntityById(entityId: string) {
  const response = await entitiesGetEntity({ path: { entityId } })

  assertResponse(response)

  return response.data!
}

export async function fetchInvestmentsById(entityId: string) {
  const response = await investmentsGet({ path: { entityId } })

  assertResponse(response)

  return response.data!
}

export async function fetchAllAddressById(entityId: string) {
  const response = await addressesGetAll({ path: { entityId } })

  assertResponse(response)

  return response?.data ?? []
}

interface CreateOrUpdateAddressProps {
  entityId: string
  addressId?: string
  payload?: CreateAddressRequestDto | UpdateAddressDto
}

async function createOrUpdateAddress({
  entityId,
  addressId,
  payload,
}: CreateOrUpdateAddressProps) {
  if (!payload || Object.keys(payload).length <= 0) return

  // create a new address
  if (!addressId) {
    const newAddress = await addressesPost({
      path: { entityId },
      body: payload,
    })

    assertResponse(newAddress)

    return
  }

  const updateAddress = await addressesPut({
    path: { entityId, addressId },
    body: payload,
  })

  assertResponse(updateAddress, {
    [HttpStatusCode.BadRequest]: new BadRequestError(
      `Unable to update address '${addressId}'`,
    ),
  })
}

interface UpdateAddressPayload {
  address?: GetAddressDto
  tradingAddress?: GetAddressDto
  residentialAddress?: GetAddressDto
}

export async function updateAddress(
  entityId: string,
  payload: UpdateAddressPayload,
) {
  const response = Promise.all([
    await createOrUpdateAddress({
      entityId,
      addressId: payload.address?.id,
      payload: payload.address,
    }),
    await createOrUpdateAddress({
      entityId,
      addressId: payload.tradingAddress?.id,
      payload: payload.tradingAddress,
    }),
    await createOrUpdateAddress({
      entityId,
      addressId: payload.residentialAddress?.id,
      payload: payload.residentialAddress,
    }),
  ])

  assertResponse(response)
}

interface UpdateEntityDetailsPayload {
  entity: UpdateEntityRequestDto
}

export async function updateEntity(
  entityId: string,
  payload: UpdateEntityDetailsPayload,
) {
  const updateEntityResponse = await entitiesUpdateEntity({
    path: { entityId },
    body: payload.entity,
  })

  assertResponse(updateEntityResponse)
}
