import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import {
  IOnboardingEntityDetailsAddressMutation,
  IOnboardingEntityDetailsMutation,
} from "./onboarding.interface"
import {
  GET_ADDRESSES_QRY_KEY,
  GET_ENTITY_QRY_KEY,
} from "./entity-details.query"
import { updateAddress, updateEntity } from "./entity-details.api"
import { useLoaderData } from "./$entityId.loader"

export const UPDATE_ADDRESS_MUTATION_KEY = "update-address"

export function useUpdateAddressMutation() {
  const queryClient = useQueryClient()

  const { entityId } = useLoaderData()

  return useMutation({
    mutationKey: ["entity-details", UPDATE_ADDRESS_MUTATION_KEY, entityId],
    mutationFn: (payload: IOnboardingEntityDetailsAddressMutation) =>
      updateAddress(entityId, payload),
    onError: (error) => {
      toast.error("Failed to update address", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Address updated successfully", {
        duration: 5000,
      })

      queryClient.invalidateQueries({
        queryKey: ["entity-details", GET_ADDRESSES_QRY_KEY, entityId],
      })
    },
  })
}

export const UPDATE_ENTITY_DETAILS_MUTATION_KEY = "update-entity-details"

export function useSaveEntityDetailsFormMutation() {
  const queryClient = useQueryClient()

  const { entityId } = useLoaderData()

  return useMutation({
    mutationKey: [
      "entity-details",
      UPDATE_ENTITY_DETAILS_MUTATION_KEY,
      entityId,
    ],
    mutationFn: (payload: IOnboardingEntityDetailsMutation) =>
      updateEntity(entityId, payload),
    onError: (error) => {
      toast.error("Failed to update Entity details", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Entity details uploaded successfully", {
        duration: 5000,
      })
    },
    onSettled: async () => {
      queryClient.invalidateQueries({
        queryKey: ["entity-details", GET_ENTITY_QRY_KEY, entityId],
      })
    },
  })
}
