import { LatestTermsAndConditions } from "@/client/onboarding/types.gen"
import {
  entitiesSubmitEntityV2,
  termsAndConditionsDownloadTermsAndConditions,
  termsAndConditionsGet,
} from "@/client/onboarding/sdk.gen"

import { assertResponse } from "./_exception-handler"

export async function submitOnboarding(
  entityId: string,
  termsAndConditions: LatestTermsAndConditions[] | undefined,
) {
  if (!termsAndConditions) return
  const signedTermsAndConditionsIds = termsAndConditions.map(
    (s) => s.termsAndConditionsId || "",
  )

  const response = await entitiesSubmitEntityV2({
    path: { entityId },
    body: { signedTermsAndConditionsIds },
  })

  assertResponse(response)
}

export async function fetchTermsAndConditions(entityId: string) {
  const response = await termsAndConditionsGet({ path: { entityId } })

  assertResponse(response)

  return response.data!.latestTermsAndConditions
}

export async function downloadTermsAndConditions(termsAndConditionsId: string) {
  const response = await termsAndConditionsDownloadTermsAndConditions({
    path: { termsAndConditionsId },
  })

  assertResponse(response)

  return response.data!
}
