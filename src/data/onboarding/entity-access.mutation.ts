import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { CreateEntityAccessRequestDto } from "@/client/onboarding/types.gen"

import { GET_ENTITY_ACCESS_QRY_KEY } from "./entity-access.query"
import { createEntityAccess, deleteEntityAccess } from "./entity-access.api"
import { useLoaderData } from "./$entityId.loader"

export const CREATE_ENTITY_ACCESS_MUTATION_KEY = "create-entity-access"

export function useCreateEntityAccessMutation() {
  const queryClient = useQueryClient()
  const { entityId } = useLoaderData()

  return useMutation({
    mutationKey: ["entity-access", CREATE_ENTITY_ACCESS_MUTATION_KEY, entityId],
    mutationFn: (payload: Omit<CreateEntityAccessRequestDto, "entityId">) =>
      createEntityAccess({
        entityId,
        ...payload,
      }),
    onError: (error) => {
      toast.error("Failed to update entity access", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Entity access created successfully", {
        duration: 5000,
      })
    },
    onSettled: async () => {
      queryClient.invalidateQueries({
        queryKey: ["entity-access", GET_ENTITY_ACCESS_QRY_KEY, entityId],
      })
    },
  })
}

export const DELETE_ENTITY_ACCESS_MUTATION_KEY = "delete-entity-access"

/** [DELETE] /api/v1/EntityAccess/{entityAccessId} */
export function useDeleteEntityAccessMutation() {
  const queryClient = useQueryClient()
  const { entityId } = useLoaderData()

  return useMutation({
    mutationKey: ["entity-access", DELETE_ENTITY_ACCESS_MUTATION_KEY, entityId],
    mutationFn: (payload: string) => deleteEntityAccess(payload),
    onError: (error) =>
      toast.error("Failed to delete user", {
        description: error.message,
      }),
    onSettled: async () => {
      toast.success("Entity access removed", {
        duration: 5000,
      })

      queryClient.invalidateQueries({
        queryKey: ["entity-access", GET_ENTITY_ACCESS_QRY_KEY, entityId],
      })
    },
  })
}
