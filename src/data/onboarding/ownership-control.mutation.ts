import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { updateOwnershipControl } from "./ownership-control.api"
import {
  GET_DOCUMENT_REGISTER_OF_DIRECTORS_QRY_KEY,
  GET_DOCUMENT_STRUCTURE_CHART_QRY_KEY,
} from "./onboarding.query"
import { IOnboardingOwnershipControlMutation } from "./onboarding.interface"
import { useLoaderData } from "./$entityId.loader"

export const UPDATE_OWNERSHIP_CONTROL_MUTATION_KEY = "update-ownership-control"

export function useUpdateOwnershipControlFormMutation() {
  const { entityId } = useLoaderData()
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [UPDATE_OWNERSHIP_CONTROL_MUTATION_KEY, entityId],
    mutationFn: ({ form }: IOnboardingOwnershipControlMutation) => {
      toast.dismiss()

      return updateOwnershipControl(entityId, {
        registerOfDirectors: form.directors.documentsToUpload,
        structureChart: form.structureChart.documentsToUpload,
      })
    },
    onError: (error) => {
      toast.error("Failed to upload documents", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Documents uploaded successfully", {
        duration: 5000,
      })

      queryClient.invalidateQueries({
        queryKey: [
          GET_DOCUMENT_REGISTER_OF_DIRECTORS_QRY_KEY,
          GET_DOCUMENT_STRUCTURE_CHART_QRY_KEY,
        ],
      })
    },
  })
}
