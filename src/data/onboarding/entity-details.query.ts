import { queryOptions, useSuspenseQuery } from "@tanstack/react-query"

import { QryOpts } from "./types"
import { fetchAllAddressById, fetchEntityById } from "./entity-details.api"
import { useLoaderData } from "./$entityId.loader"
import { STALE_TIME } from "../global/global"

export const GET_ADDRESSES_QRY_KEY = "get-addresses"

export const getAddressesQryOpts = (entityId: string, opts: QryOpts = {}) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: ["entity-details", GET_ADDRESSES_QRY_KEY, entityId],
    queryFn: () => fetchAllAddressById(entityId),
    ...opts,
  })

export function useGetAddressesSuspenseQry() {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(getAddressesQryOpts(entityId))
}

export const GET_ENTITY_QRY_KEY = "get-entity"

export const entityQryOpts = (entityId: string, opts: QryOpts = {}) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: ["entity-details", GET_ENTITY_QRY_KEY, entityId],
    queryFn: () => fetchEntityById(entityId),
    ...opts,
  })

export function useGetEntitySuspenseQry() {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(entityQryOpts(entityId))
}
