import { queryOptions, useSuspenseQuery } from "@tanstack/react-query"

import {
  fetchCountries,
  fetchCurrencies,
  fetchEntityTypes,
  fetchTransactionActivityTypes,
  fetchPurposeOfEntities,
  fetchEntityRegulation,
  fetchStockExchange,
  fetchGicsClassificationIndustries,
  fetchUserRole,
  fetchSourceOfFunds,
} from "./static-data.api"

export const GET_COUNTRIES_QRY_KEY = "countries"

export const getCountriesQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_COUNTRIES_QRY_KEY],
    queryFn: fetchCountries,
  })

/** [GET] /api/v1/StaticData/Country */
export function useGetCountriesSuspenseQry() {
  return useSuspenseQuery(getCountriesQryOpts())
}

export const GET_CURRENCIES_QRY_KEY = "currencies"

export const getCurrenciesQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_CURRENCIES_QRY_KEY],
    queryFn: fetchCurrencies,
  })

/** [GET] /api/v1/StaticData/Currency */
export function useGetCurrenciesSuspenseQry() {
  return useSuspenseQuery(getCurrenciesQryOpts())
}

export const GET_TRANSACTION_ACTIVITY_TYPES_QRY_KEY =
  "transaction-activity-types"

export const getTransactionActivityTypesQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_TRANSACTION_ACTIVITY_TYPES_QRY_KEY],
    queryFn: fetchTransactionActivityTypes,
  })

/** [GET] /api/v1/StaticData/TransactionActivityTypes */
export function useGetTransactionActivityTypesSuspenseQry() {
  return useSuspenseQuery(getTransactionActivityTypesQryOpts())
}

export const GET_ENTITY_TYPES_QRY_KEY = "entity-types"

export const getEntityTypesQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_ENTITY_TYPES_QRY_KEY],
    queryFn: fetchEntityTypes,
  })

/** [GET] /api/v1/StaticData/EntityType */
export function useGetEntityTypesSuspenseQry() {
  return useSuspenseQuery(getEntityTypesQryOpts())
}

export const GET_PURPOSE_OF_ENTITY_QRY_KEY = "purpose-of-entity"

export const getPurposeOfEntitiesQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_PURPOSE_OF_ENTITY_QRY_KEY],
    queryFn: fetchPurposeOfEntities,
  })

/** [GET] /api/v1/StaticData/PurposeOfEntity */
export function useGetPurposeOfEntitySuspenseQry() {
  return useSuspenseQuery(getPurposeOfEntitiesQryOpts())
}

export const GET_ENTITY_REGULATION_QRY_KEY = "entity-regulation"

export const getEntityRegulationQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_ENTITY_REGULATION_QRY_KEY],
    queryFn: fetchEntityRegulation,
  })

/** [GET] /api/v1/StaticData/EntityRegulation */
export function useGetEntityRegulationSuspenseQry() {
  return useSuspenseQuery(getEntityRegulationQryOpts())
}

export const GET_STOCK_EXCHANGE_QRY_KEY = "stock-exchange"

export const getStockExchangeQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_STOCK_EXCHANGE_QRY_KEY],
    queryFn: fetchStockExchange,
  })

/** [GET] /api/v1/StaticData/StockExchange */
export function useGetStockExchangeSuspenseQry() {
  return useSuspenseQuery(getStockExchangeQryOpts())
}

export const GET_GICS_CLASSIFICATION_INDUSTRIES =
  "gics-classification-industries"

export const getGicsClassificationIndustriesQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_GICS_CLASSIFICATION_INDUSTRIES],
    queryFn: fetchGicsClassificationIndustries,
  })

/** [GET] /api/v1/StaticData/GicsClassification/Industries */
export function useGetGicsClassificationIndustriesSuspenseQry() {
  return useSuspenseQuery(getGicsClassificationIndustriesQryOpts())
}

export const GET_USER_ROLE = "user-role"

export const getUserRoleQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_USER_ROLE],
    queryFn: fetchUserRole,
  })

/** [GET] /api/v1/StaticData/UserRole */
export function useGetUserRoleSuspenseQry() {
  return useSuspenseQuery(getUserRoleQryOpts())
}

export const GET_SOURCE_OF_FUNDS = "source-of-funds"

export const getSourceOfFundsQryOpts = () =>
  queryOptions({
    queryKey: ["static-data", GET_SOURCE_OF_FUNDS],
    queryFn: fetchSourceOfFunds,
  })

/** [GET] /api/v1/StaticData/UserRole */
export function useGetSourceOfFundsSuspenseQry() {
  return useSuspenseQuery(getSourceOfFundsQryOpts())
}
