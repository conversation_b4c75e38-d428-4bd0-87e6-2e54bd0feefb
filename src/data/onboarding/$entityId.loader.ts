import { useLoaderData as useCoreLoaderData } from "@tanstack/react-router"

export function useLoaderData() {
  const loaderData = useCoreLoaderData({ from: "/_auth/onboarding/$entityId" })

  return {
    user: loaderData.user,
    entity: loaderData?.entity,
    // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
    entityId: loaderData?.entity.id!,
    entityAccess: loaderData?.entityAccess,
    staticDataCountries: loaderData?.staticDataCountries,
    staticDataCurrencies: loaderData?.staticDataCurrencies,
    staticDataEntityTypes: loaderData?.staticDataEntityTypes,
    staticDataPurposeOfEntities: loaderData?.staticDataPurposeOfEntities,
    staticDataEntityRegulations: loaderData?.staticDataEntityRegulations,
    staticDataTransactionActivityTypes:
      loaderData?.staticDataTransactionActivityTypes,
    staticDataStockExchange: loaderData?.staticDataStockExchange,
    staticDataGicsClassificationIndustries:
      loaderData?.staticDataGicsClassificationIndustries,
    staticDataUserRoles: loaderData?.staticDataUserRoles,
    staticSourceOfFunds: loaderData?.staticSourceOfFunds,
  }
}
