import { queryOptions, useSuspenseQuery } from "@tanstack/react-query"

import { QryOpts } from "./types"
import { fetchExpectedTransactionActivity } from "./transaction-activity.api"
import { useLoaderData } from "./$entityId.loader"
import { STALE_TIME } from "../global/global"

export const GET_EXPECTED_TRANSACTION_ACTIVITY_QRY_KEY =
  "get-expected-transaction-activity"

export const expectedTransactionActivityQryOpts = (
  entityId: string,
  opts: QryOpts = {},
) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [GET_EXPECTED_TRANSACTION_ACTIVITY_QRY_KEY, entityId],
    queryFn: () => fetchExpectedTransactionActivity(entityId),
    ...opts,
  })

export function useExpectedTransactionActivitySuspenseQry() {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(expectedTransactionActivityQryOpts(entityId!))
}
