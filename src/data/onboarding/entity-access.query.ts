import { queryOptions, useQuery } from "@tanstack/react-query"

import { fetchEntityAccess } from "./entity-access.api"
import { STALE_TIME } from "../global/global"

export const GET_ENTITY_ACCESS_QRY_KEY = "get-entity-access"

export const getEntityAccessQryOpts = (entityId: string) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: ["entity-access", GET_ENTITY_ACCESS_QRY_KEY, entityId],
    queryFn: () => fetchEntityAccess(),
  })

/**
 * Query hook for entity access that requires passing an entityId parameter
 */
export function useEntityAccessQuery(entityId: string) {
  return useQuery(getEntityAccessQryOpts(entityId))
}
