import { toast } from "sonner"
import { useNavigate } from "@tanstack/react-router"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { useGetTermsAndConditionsSuspenseQry } from "@/data/onboarding/review.query"

import { submitOnboarding } from "./review.api"
import { GET_ENTITY_ACCESS_QRY_KEY } from "./onboarding.query"
import { useLoaderData } from "./$entityId.loader"

export const SUBMIT_ONBOARDING_MUTATION_KEY = "submit-onboarding"

export function useSubmitOnboardingMutation() {
  const navigate = useNavigate({ from: "/onboarding/$entityId" })
  const queryClient = useQueryClient()

  const { entityId } = useLoaderData()
  const { data: termsAndConditions } = useGetTermsAndConditionsSuspenseQry()

  return useMutation({
    mutationKey: [SUBMIT_ONBOARDING_MUTATION_KEY],
    mutationFn: () => {
      return submitOnboarding(entityId, termsAndConditions)
    },
    onError: (error) => {
      toast.error("Unable to submit", {
        description: error.message,
      })
    },
    onSuccess: async () => {
      toast.success("Submitted successfully")

      queryClient.invalidateQueries({
        queryKey: [GET_ENTITY_ACCESS_QRY_KEY],
      })

      navigate({ to: "/" })
    },
  })
}
