import { describe, it, expect, vi, beforeEach, Mock } from "vitest"
import { AxiosError } from "axios"

import { uploadDocuments } from "@/data/onboarding/onboarding.api"
import { fetchEntityAccess } from "@/data/onboarding/entity-access.api"
import { CreateDocumentResponseDto } from "@/client/onboarding/types.gen"
import {
  documentsPostDocumentMetadata,
  documentsPostDocumentFile,
  entityAccessGetEntityAccess,
} from "@/client/onboarding/sdk.gen"

vi.mock("@/client/banking", () => ({
  bankingClient: {
    get: vi.fn(),
    post: vi.fn(),
  },
}))

vi.mock("@/client/onboarding/sdk.gen", () => ({
  entityAccessGetEntityAccess: vi.fn(),
  documentsPostDocumentMetadata: vi.fn(),
  documentsPostDocumentFile: vi.fn(),
}))

describe("Onboarding API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("fetchOnboardingEntityAccess", () => {
    const mockResponse = {
      data: [
        { entityId: "1", entityName: "Entity 1" },
        { entityId: "2", entityName: "Entity 2" },
      ],
    }

    it("should fetch entity access successfully", async () => {
      ;(entityAccessGetEntityAccess as Mock).mockResolvedValue(mockResponse)

      const result = await fetchEntityAccess()

      expect(entityAccessGetEntityAccess).toHaveBeenCalled()
      expect(result).toEqual(mockResponse.data)
    })

    it.skip("should handle error when fetching entity access", async () => {
      // const error = new Error("API Error");

      const errorResponse: AxiosError = new AxiosError(
        "Request failed",
        "404",
        undefined,
        undefined,
        {
          status: 404,
          statusText: "Not Found",
          data: { message: "Entity access not found" },
          headers: {},
          config: {},
        } as any, // Type assertion to match AxiosResponse
      )

      ;(entityAccessGetEntityAccess as Mock).mockResolvedValueOnce(
        errorResponse,
      )

      await expect(fetchEntityAccess()).rejects.toThrow(
        "[Onboarding]: Entity access not found",
      )
    })
  })

  describe("uploadOnboardingDocuments", () => {
    const mockEntityId = "entity123"
    it("should upload files successfully", async () => {
      const mockResponse = { data: { success: true } }

      ;(documentsPostDocumentMetadata as Mock).mockResolvedValueOnce({
        data: {
          id: "some-id",
        },
      } as CreateDocumentResponseDto)
      ;(documentsPostDocumentFile as Mock).mockResolvedValueOnce(mockResponse)

      await uploadDocuments(mockEntityId, "FinancialStatements", [
        new File([""], "somefile.pdf"),
      ])

      expect(documentsPostDocumentFile).toHaveBeenCalled()
    })

    it.skip("should handle error when uploading files", async () => {
      const error = new Error("API Error")
      ;(documentsPostDocumentFile as Mock).mockRejectedValueOnce(error)

      await expect(documentsPostDocumentFile).rejects.toThrow("API Error")
    })
  })
})
