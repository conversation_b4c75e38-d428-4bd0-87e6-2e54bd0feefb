import { describe, it, expect, vi, beforeEach, Mock } from "vitest"
import { toast } from "sonner"
import { renderHook, waitFor } from "@testing-library/react"

import { createWrapper } from "@/tests/utils/test-utils"
import { useUpdateOwnershipControlFormMutation } from "@/data/onboarding/ownership-control.mutation"
import { IOnboardingOwnershipControlMutation } from "@/data/onboarding/onboarding.interface"
import { uploadDocuments } from "@/data/onboarding/onboarding.api"

vi.mock("@tanstack/react-router", () => {
  const navigate = vi.fn()

  return {
    useNavigate: () => navigate,
    Link: ({ children, to }: any) => <a href={to}>{children}</a>,
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
      },
    })),
  }
})

vi.mock("@/data/onboarding/onboarding.api", () => ({
  uploadDocuments: vi.fn(),
}))

vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    dismiss: vi.fn(),
  },
}))

vi.mock("@/main", () => ({
  queryClient: {
    invalidateQueries: vi.fn(),
  },
}))

describe("Onboarding Mutations", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("useUploadFilesMutation", () => {
    const mockFiles: IOnboardingOwnershipControlMutation = {
      form: {
        directors: {
          documentsMetadata: [{ id: "", name: "" }],
          documentsToUpload: [new File([""], "")],
        },
        structureChart: {
          documentsMetadata: [{ id: "", name: "" }],
          documentsToUpload: [new File([""], "")],
        },
      },
    }

    const mockResponse = {
      success: true,
    }

    it("should handle successful file upload", async () => {
      ;(uploadDocuments as Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(
        () => useUpdateOwnershipControlFormMutation(),
        {
          wrapper: createWrapper(),
        },
      )

      result.current.mutate(mockFiles)

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(uploadDocuments).toHaveBeenCalled()
      expect(toast.dismiss).toHaveBeenCalled()
      expect(toast.success).toHaveBeenCalledWith(
        "Documents uploaded successfully",
        expect.objectContaining({
          duration: 5000,
        }),
      )
    })

    it("should handle file upload failure", async () => {
      const error = new Error("API Error")
      ;(uploadDocuments as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(
        () => useUpdateOwnershipControlFormMutation(),
        {
          wrapper: createWrapper(),
        },
      )

      result.current.mutate(mockFiles)

      await waitFor(() => expect(result.current.isError).toBe(true))

      expect(toast.dismiss).toHaveBeenCalled()
      expect(toast.error).toHaveBeenCalledWith(
        "Failed to upload documents",
        expect.objectContaining({
          description: error.message,
        }),
      )
    })

    it("should not show success toast on error", async () => {
      const error = new Error("API Error")
      ;(uploadDocuments as Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(
        () => useUpdateOwnershipControlFormMutation(),
        {
          wrapper: createWrapper(),
        },
      )

      result.current.mutate(mockFiles)

      await waitFor(() => expect(result.current.isError).toBe(true))
      expect(toast.success).not.toHaveBeenCalled()
    })

    it("should dismiss existing toasts before showing new ones", async () => {
      ;(uploadDocuments as Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(
        () => useUpdateOwnershipControlFormMutation(),
        {
          wrapper: createWrapper(),
        },
      )

      result.current.mutate(mockFiles)

      await waitFor(() => expect(result.current.isSuccess).toBe(true))
      expect(toast.dismiss).toHaveBeenCalled()
    })
  })
})
