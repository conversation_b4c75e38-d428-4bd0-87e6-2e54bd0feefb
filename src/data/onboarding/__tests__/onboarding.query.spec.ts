import { describe, it, expect, vi, beforeEach, Mock } from "vitest"
import { renderHook, waitFor } from "@testing-library/react"

import type { GetEntityDocumentsDto } from "@/client/onboarding/types.gen"

import { createWrapper } from "@/tests/utils/test-utils"
import { useGetFinancialDocumentsSuspenseQry } from "@/data/onboarding/onboarding.query"
import { documentsGetEntityDocuments } from "@/client/onboarding/sdk.gen"

const entityId = "acme-ltd"

vi.mock("@tanstack/react-router", () => {
  const navigate = vi.fn()

  return {
    useNavigate: () => navigate,
    useLoaderData: vi.fn(() => ({
      entity: {
        id: entityId,
      },
    })),
  }
})

vi.mock("@/client/onboarding/sdk.gen", () => ({
  documentsGetEntityDocuments: vi.fn(),
}))

vi.mock("@/main", () => ({
  queryClient: {
    invalidateQueries: vi.fn(),
  },
}))

const mockDocuments = {
  id: "some-id",
  category: "FinancialStatements",
  expiryDate: "02-02-2026",
  name: "some-info.pdf",
} satisfies GetEntityDocumentsDto

describe("Onboarding Queries", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("useUploadFilesMutation", () => {
    const mockResponse = {
      data: [mockDocuments] satisfies GetEntityDocumentsDto[],
    }

    it("should retrieve financial information", async () => {
      ;(documentsGetEntityDocuments as Mock).mockResolvedValue(mockResponse)

      const { result } = renderHook(
        () => useGetFinancialDocumentsSuspenseQry(),
        {
          wrapper: createWrapper(),
        },
      )

      await waitFor(() => expect(result.current.isSuccess).toBe(true))

      expect(documentsGetEntityDocuments).toHaveBeenCalledWith({
        path: { entityId },
        query: { documentCategory: ["FinancialStatements"] },
      })
    })
  })
})
