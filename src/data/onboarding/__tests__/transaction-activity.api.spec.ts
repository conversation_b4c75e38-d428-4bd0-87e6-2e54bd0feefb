import { describe, it, expect, vi, beforeEach, Mock } from "vitest"

import {
  fetchExpectedTransactionActivity,
  updateTransactionActivity,
} from "@/data/onboarding/transaction-activity.api"
import { SetExpectedTransactionActivityDto } from "@/client/onboarding/types.gen"
import {
  expectedTransactionActivityGet,
  expectedTransactionActivityPut,
} from "@/client/onboarding/sdk.gen"

// Mock the SDK functions
vi.mock("@/client/onboarding/sdk.gen", () => ({
  expectedTransactionActivityGet: vi.fn(),
  expectedTransactionActivityPut: vi.fn(),
}))

describe("Transaction Activity API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("fetchExpectedTransactionActivity", () => {
    const entityId = "entity123"

    it("should fetch expected transaction activity successfully", async () => {
      const mockTransactionActivity = {
        activityBaseCurrency: "GBP",
        activityTypes: [
          { key: "PAYMENTS", display: "Payments" },
          { key: "FX_TRADING", display: "FX Trading" },
        ],
        expectedTransactionCurrencies: [
          { key: "GBP", display: "British Pound" },
          { key: "EUR", display: "Euro" },
        ],
        expectedTransactionJurisdictions: [
          { key: "GB", display: "United Kingdom" },
          { key: "EU", display: "European Union" },
        ],
        maximumTransactionSize: 100000,
        yearlyNumberOfPaymentsInbound: 50,
        yearlyNumberOfPaymentsOutbound: 75,
        yearlyValueOfPaymentsInbound: 500000,
        yearlyValueOfPaymentsOutbound: 750000,
      }

      ;(expectedTransactionActivityGet as Mock).mockResolvedValue({
        data: mockTransactionActivity,
        response: { status: 200 },
      })

      const result = await fetchExpectedTransactionActivity(entityId)

      expect(expectedTransactionActivityGet).toHaveBeenCalledWith({
        path: { entityId },
      })
      expect(result).toEqual(mockTransactionActivity)
    })

    it("should handle API errors", async () => {
      const axiosError = {
        isAxiosError: true,
        status: 500,
        response: {
          status: 500,
          data: { detail: "Internal server error" },
        },
      }

      ;(expectedTransactionActivityGet as Mock).mockResolvedValue(axiosError)

      await expect(fetchExpectedTransactionActivity(entityId)).rejects.toThrow()
    })

    it("should handle empty response data", async () => {
      ;(expectedTransactionActivityGet as Mock).mockResolvedValue({
        data: {},
        response: { status: 200 },
      })

      const result = await fetchExpectedTransactionActivity(entityId)
      expect(result).toEqual({})
    })

    it("should handle null response data", async () => {
      ;(expectedTransactionActivityGet as Mock).mockResolvedValue({
        data: null,
        response: { status: 200 },
      })

      const result = await fetchExpectedTransactionActivity(entityId)
      expect(result).toBeNull()
    })

    it("should call API with correct entity ID", async () => {
      const testEntityId = "test-entity-456"

      ;(expectedTransactionActivityGet as Mock).mockResolvedValue({
        data: { activityBaseCurrency: "USD" },
        response: { status: 200 },
      })

      await fetchExpectedTransactionActivity(testEntityId)

      expect(expectedTransactionActivityGet).toHaveBeenCalledWith({
        path: { entityId: testEntityId },
      })
    })
  })

  describe("updateTransactionActivity", () => {
    const entityId = "entity123"
    const mockPayload: SetExpectedTransactionActivityDto = {
      activityTypes: [{ code: 1 }, { code: 2 }],
      expectedTransactionCurrencies: ["EUR", "USD"],
      expectedTransactionJurisdictions: ["EU", "US"],
      maximumTransactionSize: 250000,
      yearlyNumberOfPaymentsInbound: 100,
      yearlyNumberOfPaymentsOutbound: 150,
      yearlyValueOfPaymentsInbound: 1000000,
      yearlyValueOfPaymentsOutbound: 1500000,
    }

    it("should update transaction activity successfully", async () => {
      ;(expectedTransactionActivityPut as Mock).mockResolvedValue({
        data: {},
        response: { status: 200 },
      })

      await updateTransactionActivity(entityId, mockPayload)

      expect(expectedTransactionActivityPut).toHaveBeenCalledWith({
        path: { entityId },
        body: mockPayload,
      })
    })

    it("should handle API errors during update", async () => {
      const axiosError = {
        isAxiosError: true,
        status: 400,
        response: {
          status: 400,
          data: { detail: "Bad request" },
        },
      }

      ;(expectedTransactionActivityPut as Mock).mockResolvedValue(axiosError)

      await expect(
        updateTransactionActivity(entityId, mockPayload),
      ).rejects.toThrow()
    })

    it("should handle server errors during update", async () => {
      const axiosError = {
        isAxiosError: true,
        status: 500,
        response: {
          status: 500,
          data: { detail: "Internal server error" },
        },
      }

      ;(expectedTransactionActivityPut as Mock).mockResolvedValue(axiosError)

      await expect(
        updateTransactionActivity(entityId, mockPayload),
      ).rejects.toThrow()
    })

    it("should call API with correct parameters", async () => {
      const testEntityId = "test-entity-789"
      const testPayload: SetExpectedTransactionActivityDto = {
        activityTypes: [{ code: 1 }],
        expectedTransactionCurrencies: ["JPY"],
        expectedTransactionJurisdictions: ["JP"],
        maximumTransactionSize: 50000,
        yearlyNumberOfPaymentsInbound: 25,
        yearlyNumberOfPaymentsOutbound: 30,
        yearlyValueOfPaymentsInbound: 250000,
        yearlyValueOfPaymentsOutbound: 300000,
      }

      ;(expectedTransactionActivityPut as Mock).mockResolvedValue({
        data: {},
        response: { status: 200 },
      })

      await updateTransactionActivity(testEntityId, testPayload)

      expect(expectedTransactionActivityPut).toHaveBeenCalledWith({
        path: { entityId: testEntityId },
        body: testPayload,
      })
    })

    it("should handle minimal payload", async () => {
      const minimalPayload: SetExpectedTransactionActivityDto = {
        activityTypes: [],
        expectedTransactionCurrencies: [],
        expectedTransactionJurisdictions: [],
        maximumTransactionSize: 0,
        yearlyNumberOfPaymentsInbound: 0,
        yearlyNumberOfPaymentsOutbound: 0,
        yearlyValueOfPaymentsInbound: 0,
        yearlyValueOfPaymentsOutbound: 0,
      }

      ;(expectedTransactionActivityPut as Mock).mockResolvedValue({
        data: {},
        response: { status: 200 },
      })

      await updateTransactionActivity(entityId, minimalPayload)

      expect(expectedTransactionActivityPut).toHaveBeenCalledWith({
        path: { entityId },
        body: minimalPayload,
      })
    })

    it("should handle large transaction values", async () => {
      const largeValuePayload: SetExpectedTransactionActivityDto = {
        activityTypes: [{ code: 1 }, { code: 2 }, { code: 3 }],
        expectedTransactionCurrencies: ["USD", "EUR", "GBP", "JPY"],
        expectedTransactionJurisdictions: ["US", "EU", "GB", "JP"],
        maximumTransactionSize: 10000000,
        yearlyNumberOfPaymentsInbound: 1000,
        yearlyNumberOfPaymentsOutbound: 2000,
        yearlyValueOfPaymentsInbound: 100000000,
        yearlyValueOfPaymentsOutbound: 200000000,
      }

      ;(expectedTransactionActivityPut as Mock).mockResolvedValue({
        data: {},
        response: { status: 200 },
      })

      await updateTransactionActivity(entityId, largeValuePayload)

      expect(expectedTransactionActivityPut).toHaveBeenCalledWith({
        path: { entityId },
        body: largeValuePayload,
      })
    })
  })
})
