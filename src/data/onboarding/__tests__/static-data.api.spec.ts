import { describe, it, expect, vi, beforeEach, Mock } from "vitest"

import {
  fetchCountries,
  fetchCurrencies,
  fetchTransactionActivityTypes,
} from "@/data/onboarding/static-data.api"
import {
  staticDataGetCurrencyList,
  staticDataGetCountryList,
  staticDataGetTransactionActivityTypesList,
} from "@/client/onboarding/sdk.gen"

vi.mock("@/client/onboarding/sdk.gen", () => ({
  staticDataGetCurrencyList: vi.fn(),
  staticDataGetCountryList: vi.fn(),
  staticDataGetTransactionActivityTypesList: vi.fn(),
}))

describe("Static data", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("Countries", () => {
    const mockResponse = {
      data: [
        { code: "US", name: "United States" },
        { code: "UK", name: "United Kingdom" },
      ],
    }

    it("should fetch countries successfully", async () => {
      ;(staticDataGetCountryList as Mock).mockResolvedValueOnce(mockResponse)

      const result = await fetchCountries()

      expect(staticDataGetCountryList).toHaveBeenCalled()

      expect(result).toEqual(mockResponse.data)
    })

    it("should handle error when fetching countries", async () => {
      const error = new Error("API Error")
      ;(staticDataGetCountryList as Mock).mockRejectedValueOnce(error)

      await expect(fetchCountries()).rejects.toThrow("API Error")
    })
  })

  describe("Currencies", () => {
    const mockResponse = {
      data: [
        { code: "USD", description: "US Dollar" },
        { code: "EUR", description: "Euro" },
      ],
    }

    it("should fetch currencies successfully", async () => {
      ;(staticDataGetCurrencyList as Mock).mockResolvedValueOnce(mockResponse)

      const result = await fetchCurrencies()

      expect(staticDataGetCurrencyList).toHaveBeenCalled()
      expect(result).toEqual(mockResponse.data)
    })

    it("should handle error when fetching currencies", async () => {
      const error = new Error("API Error")
      ;(staticDataGetCurrencyList as Mock).mockRejectedValueOnce(error)

      await expect(fetchCurrencies()).rejects.toThrow("API Error")
    })
  })

  describe("TransactionActivityType", () => {
    const mockResponse = {
      data: [
        { code: "1000", name: "Investment and Fund Related Activity" },
        { code: "2000", name: "Operational Activity" },
      ],
    }

    it("should fetch transaction activity types successfully", async () => {
      ;(
        staticDataGetTransactionActivityTypesList as Mock
      ).mockResolvedValueOnce(mockResponse)

      const result = await fetchTransactionActivityTypes()

      expect(staticDataGetTransactionActivityTypesList).toHaveBeenCalled()
      expect(result).toEqual(mockResponse.data)
    })

    it("should handle error when fetching transaction activity types", async () => {
      const error = new Error("API Error")
      ;(
        staticDataGetTransactionActivityTypesList as Mock
      ).mockRejectedValueOnce(error)

      await expect(fetchTransactionActivityTypes()).rejects.toThrow("API Error")
    })
  })
})
