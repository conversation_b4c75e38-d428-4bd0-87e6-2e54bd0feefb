import { describe, it, expect, vi, beforeEach, Mock } from "vitest"

import { submitOnboarding } from "@/data/onboarding/review.api"
import { LatestTermsAndConditions } from "@/client/onboarding/types.gen"
import { entitiesSubmitEntityV2 } from "@/client/onboarding/sdk.gen"

// Mock the SDK functions
vi.mock("@/client/onboarding/sdk.gen", () => ({
  entitiesSubmitEntityV2: vi.fn(),
}))

describe("Review API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("submitOnboarding", () => {
    const entityId = "entity123"

    it("should submit onboarding successfully with terms and conditions", async () => {
      const mockTermsAndConditions: LatestTermsAndConditions[] = [
        {
          termsAndConditionsId: "terms1",
          documentId: "doc1",
          documentName: "General Terms",
          versionNumber: 1,
          type: "TermsAndConditions",
        },
        {
          termsAndConditionsId: "terms2",
          documentId: "doc2",
          documentName: "Privacy Policy",
          versionNumber: 2,
          type: "TermsAndConditions",
        },
      ]

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue({
        data: { success: true },
        response: { status: 200 },
      })

      await submitOnboarding(entityId, mockTermsAndConditions)

      expect(entitiesSubmitEntityV2).toHaveBeenCalledWith({
        path: { entityId },
        body: {
          signedTermsAndConditionsIds: ["terms1", "terms2"],
        },
      })
    })

    it("should handle terms and conditions with null IDs", async () => {
      const mockTermsWithNullIds: LatestTermsAndConditions[] = [
        {
          termsAndConditionsId: "terms1",
          documentId: "doc1",
          documentName: "General Terms",
          versionNumber: 1,
          type: "TermsAndConditions",
        },
        {
          termsAndConditionsId: null as any,
          documentId: "doc2",
          documentName: "Privacy Policy",
          versionNumber: 2,
          type: "TermsAndConditions",
        },
      ]

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue({
        data: { success: true },
        response: { status: 200 },
      })

      await submitOnboarding(entityId, mockTermsWithNullIds)

      expect(entitiesSubmitEntityV2).toHaveBeenCalledWith({
        path: { entityId },
        body: {
          signedTermsAndConditionsIds: ["terms1", ""],
        },
      })
    })

    it("should handle empty terms and conditions array", async () => {
      const emptyTerms: LatestTermsAndConditions[] = []

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue({
        data: { success: true },
        response: { status: 200 },
      })

      await submitOnboarding(entityId, emptyTerms)

      expect(entitiesSubmitEntityV2).toHaveBeenCalledWith({
        path: { entityId },
        body: {
          signedTermsAndConditionsIds: [],
        },
      })
    })

    it("should return early when terms and conditions is undefined", async () => {
      await submitOnboarding(entityId, undefined)

      expect(entitiesSubmitEntityV2).not.toHaveBeenCalled()
    })

    it("should return early when terms and conditions is null", async () => {
      await submitOnboarding(entityId, null as any)

      expect(entitiesSubmitEntityV2).not.toHaveBeenCalled()
    })

    it("should handle API errors", async () => {
      const mockTermsAndConditions: LatestTermsAndConditions[] = [
        {
          termsAndConditionsId: "terms1",
          documentId: "doc1",
          documentName: "General Terms",
          versionNumber: 1,
          type: "TermsAndConditions",
        },
      ]

      const axiosError = {
        isAxiosError: true,
        status: 400,
        response: {
          status: 400,
          data: { detail: "Bad request" },
        },
      }

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue(axiosError)

      await expect(
        submitOnboarding(entityId, mockTermsAndConditions),
      ).rejects.toThrow()
    })

    it("should handle server errors", async () => {
      const mockTermsAndConditions: LatestTermsAndConditions[] = [
        {
          termsAndConditionsId: "terms1",
          documentId: "doc1",
          documentName: "General Terms",
          versionNumber: 1,
          type: "TermsAndConditions",
        },
      ]

      const axiosError = {
        isAxiosError: true,
        status: 500,
        response: {
          status: 500,
          data: { detail: "Internal server error" },
        },
      }

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue(axiosError)

      await expect(
        submitOnboarding(entityId, mockTermsAndConditions),
      ).rejects.toThrow()
    })

    it("should call API with correct entity ID", async () => {
      const testEntityId = "test-entity-456"
      const mockTermsAndConditions: LatestTermsAndConditions[] = [
        {
          termsAndConditionsId: "terms1",
          documentId: "doc1",
          documentName: "General Terms",
          versionNumber: 1,
          type: "TermsAndConditions",
        },
      ]

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue({
        data: { success: true },
        response: { status: 200 },
      })

      await submitOnboarding(testEntityId, mockTermsAndConditions)

      expect(entitiesSubmitEntityV2).toHaveBeenCalledWith({
        path: { entityId: testEntityId },
        body: {
          signedTermsAndConditionsIds: ["terms1"],
        },
      })
    })

    it("should handle single terms and conditions", async () => {
      const singleTerms: LatestTermsAndConditions[] = [
        {
          termsAndConditionsId: "single-terms",
          documentId: "doc3",
          documentName: "Updated Terms",
          versionNumber: 3,
          type: "TermsAndConditions",
        },
      ]

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue({
        data: { success: true },
        response: { status: 200 },
      })

      await submitOnboarding(entityId, singleTerms)

      expect(entitiesSubmitEntityV2).toHaveBeenCalledWith({
        path: { entityId },
        body: {
          signedTermsAndConditionsIds: ["single-terms"],
        },
      })
    })

    it("should handle multiple terms with mixed valid and invalid IDs", async () => {
      const mixedTerms: LatestTermsAndConditions[] = [
        {
          termsAndConditionsId: "valid-terms-1",
          documentId: "doc4",
          documentName: "Valid Terms 1",
          versionNumber: 1,
          type: "TermsAndConditions",
        },
        {
          termsAndConditionsId: "",
          documentId: "doc5",
          documentName: "Empty ID Terms",
          versionNumber: 2,
          type: "TermsAndConditions",
        },
        {
          termsAndConditionsId: "valid-terms-2",
          documentId: "doc6",
          documentName: "Valid Terms 2",
          versionNumber: 3,
          type: "TermsAndConditions",
        },
      ]

      ;(entitiesSubmitEntityV2 as Mock).mockResolvedValue({
        data: { success: true },
        response: { status: 200 },
      })

      await submitOnboarding(entityId, mixedTerms)

      expect(entitiesSubmitEntityV2).toHaveBeenCalledWith({
        path: { entityId },
        body: {
          signedTermsAndConditionsIds: ["valid-terms-1", "", "valid-terms-2"],
        },
      })
    })
  })
})
