import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { UpdateEntityRequestDto } from "@/client/onboarding/types.gen"

import { GET_DOCUMENT_PROOF_OF_EXISTENCE_QRY_KEY } from "./onboarding.query"
import { IOnboardingFurtherDetailsMutation } from "./onboarding.interface"
import { updateFurtherDetails } from "./further-details.api"
import { GET_ENTITY_QRY_KEY } from "./entity-details.query"
import { useLoaderData } from "./$entityId.loader"

export const UPDATE_FURTHER_DETAILS_MUTATION_KEY = "update-further-details"

export function useSaveFurtherDetailsFormMutation() {
  const queryClient = useQueryClient()
  const { entityId } = useLoaderData()

  return useMutation({
    mutationKey: [UPDATE_FURTHER_DETAILS_MUTATION_KEY, entityId],
    mutationFn: ({ entity, form }: IOnboardingFurtherDetailsMutation) =>
      updateFurtherDetails(entityId, {
        entity: {
          ...entity,
          phoneNumber: form.phoneNumber,
          website: form.website,
          legalEntity: {
            ...entity?.legalEntity,
            regulation:
              form.regulation[0] === "Not Regulated"
                ? "NotRegulated"
                : form.regulation.join(", "),
            gicsCode: Number(form.natureOfBusiness?.key),
          },
        } as UpdateEntityRequestDto,
        documents: form.documentsToUpload,
      }),
    onError: (error) => {
      toast.error("Unable to update Entity further details", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Entity further details uploaded successfully", {
        duration: 5000,
      })
    },
    onSettled: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["entity-details", GET_ENTITY_QRY_KEY, entityId],
      })

      await queryClient.invalidateQueries({
        queryKey: [GET_DOCUMENT_PROOF_OF_EXISTENCE_QRY_KEY, entityId],
      })
    },
  })
}
