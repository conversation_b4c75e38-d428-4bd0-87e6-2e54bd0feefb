import type { User } from "@/components/pages/_components/add-user-wizard/interface"
import type {
  CreateAddressRequestDto,
  EnumValueDto,
  GetCountryListDto,
  GetEntityResponseDto,
  GetExpectedTransactionActivityDto,
  UpdateAddressDto,
  UpdateEntityRequestDto,
  SourceOfFunds,
} from "@/client/onboarding/types.gen"

export type DocumentsMetadata = {
  id?: string
  name: string
}

export interface UploadDocuments {
  documentsMetadata: DocumentsMetadata[]
  documentsToUpload: File[]
}

/* Entity details */
export interface IOnboardingAddress {
  buildingNumber: string
  street: string
  state: string
  city: string
  postalCode: string
  country: string
}

export interface IOnboardingEntityDetails {
  entityName: string
  tradingNames: string[]
  entityType?: EnumValueDto
  address: IOnboardingAddress
  tradingAddress: IOnboardingAddress
  stockExchange?: EnumValueDto
  purposeOfEntity?: EnumValueDto
  dateOfIncorporation?: string
  jurisdictionOfIncorporation?: GetCountryListDto
  registrationNumber: string
  sameAsRegisteredAddress?: boolean
}

export interface IOnboardingEntityDetailsAddressMutation {
  address: CreateAddressRequestDto | UpdateAddressDto
  tradingAddress: CreateAddressRequestDto | UpdateAddressDto
}

export interface IOnboardingEntityDetailsMutation {
  entity: UpdateEntityRequestDto
}

/* Further details */

export interface IOnboardingFurtherDetails extends UploadDocuments {
  regulation: string[]
  natureOfBusiness?: EnumValueDto
  website: string
  phoneNumber: string
}

export interface IOnboardingFurtherDetailsMutation {
  form: IOnboardingFurtherDetails
  entity?: GetEntityResponseDto | UpdateEntityRequestDto
}

/* Ownership */
export interface IOnboardingOwnershipControl {
  directors: UploadDocuments
  structureChart: UploadDocuments
}

export interface IOnboardingOwnershipControlMutation {
  form: IOnboardingOwnershipControl
}

/* Expected Transaction Activity (aka Payment activity) */
export interface IOnboardingExpectedTransactionActivity {
  activityBaseCurrency: string
  expectedTransactionCurrencies: string[]
  expectedTransactionJurisdictions: string[]
  yearlyNumberOfPaymentsInbound: number
  yearlyNumberOfPaymentsOutbound: number
  yearlyValueOfPaymentsInbound: number
  yearlyValueOfPaymentsOutbound: number
  maximumTransactionSize: number
  activityTypes: string[]
}

export interface IOnboardingExpectedTransactionActivityMutation {
  form: IOnboardingExpectedTransactionActivity
  transactionActivityData: GetExpectedTransactionActivityDto
}

/* Financial information */

export interface IOnboardingFinancialInformation extends UploadDocuments {
  sourceOfFunds: SourceOfFunds | null
  sourceOfFundsOther: string | null
}

/* Users and signatories */

export interface IOnboardingUsersAndSignatories {
  users: User[]
}

export interface IOnboardingForm {
  entityDetails: IOnboardingEntityDetails
  furtherDetails: IOnboardingFurtherDetails
  ownershipControl: IOnboardingOwnershipControl
  financialInformation: IOnboardingFinancialInformation
  paymentActivity: IOnboardingExpectedTransactionActivity
}
