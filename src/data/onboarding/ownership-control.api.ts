import { uploadDocuments } from "./onboarding.api"

interface UpdateOwnershipControlPayload {
  registerOfDirectors: File[]
  structureChart: File[]
}

export async function updateOwnershipControl(
  entityId: string,
  { registerOfDirectors, structureChart }: UpdateOwnershipControlPayload,
) {
  await Promise.all([
    uploadDocuments(entityId, "RegisterOfDirectors", registerOfDirectors),
    uploadDocuments(entityId, "StructureChart", structureChart),
  ])
}
