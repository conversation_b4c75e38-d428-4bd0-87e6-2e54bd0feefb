import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { SetExpectedTransactionActivityTypeDto } from "@/client/onboarding/types.gen"

import { GET_EXPECTED_TRANSACTION_ACTIVITY_QRY_KEY } from "./transaction-activity.query"
import { updateTransactionActivity } from "./transaction-activity.api"
import { IOnboardingExpectedTransactionActivityMutation } from "./onboarding.interface"
import { useLoaderData } from "./$entityId.loader"

export const UPDATE_EXPECTED_TRANSACTION_ACTIVITY_MUTATION_KEY =
  "update-expected-transaction-activity"

export function useUpdateTransactionActivityFormMutation() {
  const queryClient = useQueryClient()
  const { entityId } = useLoaderData()

  return useMutation({
    mutationKey: [UPDATE_EXPECTED_TRANSACTION_ACTIVITY_MUTATION_KEY, entityId],
    mutationFn: ({ form }: IOnboardingExpectedTransactionActivityMutation) => {
      toast.dismiss()

      return updateTransactionActivity(entityId, {
        expectedTransactionCurrencies: form.expectedTransactionCurrencies,
        expectedTransactionJurisdictions: form.expectedTransactionJurisdictions,
        yearlyNumberOfPaymentsInbound: form.yearlyNumberOfPaymentsInbound * 12,
        yearlyNumberOfPaymentsOutbound:
          form.yearlyNumberOfPaymentsOutbound * 12,
        yearlyValueOfPaymentsInbound: form.yearlyValueOfPaymentsInbound * 12,
        yearlyValueOfPaymentsOutbound: form.yearlyValueOfPaymentsOutbound * 12,
        maximumTransactionSize: form.maximumTransactionSize,
        activityTypes: form.activityTypes.map((activityType) => {
          return {
            code: parseInt(activityType),
          }
        }) satisfies SetExpectedTransactionActivityTypeDto[],
      })
    },
    onError: (error) => {
      toast.error("Failed to update activity", {
        description: error.message,
      })
    },
    onSuccess: (_) => {
      toast.success("Transaction activity updated successfully", {
        duration: 5000,
      })

      queryClient.invalidateQueries({
        queryKey: [GET_EXPECTED_TRANSACTION_ACTIVITY_QRY_KEY],
      })
    },
  })
}
