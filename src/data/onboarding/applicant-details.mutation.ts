import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import {
  CreateAddressRequestDto,
  UpdateAddressDto,
  UpdateEntityRequestDto,
} from "@/client/onboarding/types.gen"

import { updateAddress, updateEntity } from "./entity-details.api"
import {
  GET_NATURAL_ENTITY_QRY_KEY,
  GET_NATURAL_ENTITY_ADDRESSES_QRY_KEY,
} from "./applicant-details.query"
import { useLoaderData } from "./$entityId.loader"

export const UPDATE_NATURAL_ENTITY_ADDRESS_MUTATION_KEY =
  "update-natural-entity-address"

interface IUpdateNaturalEntityAddressMutation {
  residentialAddress: CreateAddressRequestDto | UpdateAddressDto
}

export function useUpdateNaturalEntityAddressMutation() {
  const queryClient = useQueryClient()

  const { user } = useLoaderData()

  return useMutation({
    mutationKey: [
      "applicant-details",
      UPDATE_NATURAL_ENTITY_ADDRESS_MUTATION_KEY,
      user.ownerEntityId,
    ],
    mutationFn: (payload: IUpdateNaturalEntityAddressMutation) =>
      updateAddress(user.ownerEntityId!, payload),
    onError: (error) => {
      toast.error("Failed to update address", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Address updated successfully", {
        duration: 5000,
      })
    },
    onSettled: async () => {
      queryClient.invalidateQueries({
        queryKey: [
          "applicant-details",
          GET_NATURAL_ENTITY_ADDRESSES_QRY_KEY,
          user.ownerEntityId,
        ],
      })
    },
  })
}

export const UPDATE_APPLICANT_DETAILS_MUTATION_KEY = "update-applicant-details"

export function useUpdateApplicantDetailsMutation() {
  const queryClient = useQueryClient()

  const { user } = useLoaderData()

  return useMutation({
    mutationKey: [
      "applicant-details",
      UPDATE_APPLICANT_DETAILS_MUTATION_KEY,
      user.ownerEntityId,
    ],
    mutationFn: (payload: UpdateEntityRequestDto) =>
      updateEntity(user.ownerEntityId!, { entity: payload }),
    onError: (error) => {
      toast.error("Failed to update applicant details", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("Applicant details updated successfully", {
        duration: 5000,
      })
    },
    onSettled: async () => {
      queryClient.invalidateQueries({
        queryKey: [
          "applicant-details",
          GET_NATURAL_ENTITY_QRY_KEY,
          user.ownerEntityId,
        ],
      })
    },
  })
}
