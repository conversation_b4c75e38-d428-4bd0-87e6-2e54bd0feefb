import { queryOptions, useSuspenseQuery } from "@tanstack/react-query"

import { useLazyQuery } from "@/hooks/use-lazy-query"

import { QryOpts } from "./types"
import {
  downloadTermsAndConditions,
  fetchTermsAndConditions,
} from "./review.api"
import { useLoaderData } from "./$entityId.loader"

export const GET_TERMS_AND_CONDITIONS_QRY_KEY = "get-terms-and-conditions"

export const getTermsAndConditionsQryOpts = (
  entityId: string,
  opts: QryOpts = {},
) =>
  queryOptions({
    queryKey: ["review", GET_TERMS_AND_CONDITIONS_QRY_KEY, entityId],
    queryFn: () => fetchTermsAndConditions(entityId),
    ...opts,
  })

export function useGetTermsAndConditionsSuspenseQry() {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(getTermsAndConditionsQryOpts(entityId))
}

export const DOWNLOAD_TERMS_AND_CONDITIONS_QRY_KEY = "get-terms-and-conditions"

export const downloadTermsAndConditionsQryKey = (key: string[]) => [
  "review",
  DOWNLOAD_TERMS_AND_CONDITIONS_QRY_KEY,
  ...key,
]

export function downloadTermsAndConditionsQryOpts({
  queryKey,
}: {
  queryKey: string[]
}) {
  const [termsAndConditionsId] = queryKey
  return downloadTermsAndConditions(termsAndConditionsId)
}

export function useDownloadTermsAndConditionsQry() {
  return useLazyQuery(downloadTermsAndConditionsQryOpts, { retry: 0 })
}
