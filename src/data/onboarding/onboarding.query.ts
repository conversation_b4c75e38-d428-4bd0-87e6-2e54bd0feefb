import { queryOptions, useQuery, useSuspenseQuery } from "@tanstack/react-query"

import { QryOpts } from "./types"
import {
  fetchDocuments,
  fetchLoggedInUser,
  fetchFinancialInfoSources,
} from "./onboarding.api"
import { fetchEntityAccess } from "./entity-access.api"
import { useLoaderData } from "./$entityId.loader"
import { STALE_TIME } from "../global/global"

export const GET_LOGGED_IN_USER_QRY_KEY = "user"

export const loggedInUserQryOpts = () =>
  queryOptions({
    queryKey: [GET_LOGGED_IN_USER_QRY_KEY],
    queryFn: () => fetchLoggedInUser(),
  })

export function useGetLoggedInUserSuspenseQry() {
  return useSuspenseQuery(loggedInUserQryOpts())
}

export const GET_ENTITY_ACCESS_QRY_KEY = "entity-access"

export const getEntityAccessQryOpts = () =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [GET_ENTITY_ACCESS_QRY_KEY],
    queryFn: () => fetchEntityAccess(),
  })

export function useEntityAccessQry() {
  return useQuery(getEntityAccessQryOpts())
}

export const GET_DOCUMENT_REGISTER_OF_DIRECTORS_QRY_KEY =
  "document-register-of-directors"
export const GET_DOCUMENT_STRUCTURE_CHART_QRY_KEY = "document-structure-chart"

export const getOwnershipDocumentsQryOpts = (
  entityId: string,
  opts: QryOpts = {},
) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [
      GET_DOCUMENT_REGISTER_OF_DIRECTORS_QRY_KEY,
      GET_DOCUMENT_STRUCTURE_CHART_QRY_KEY,
      entityId,
    ],
    queryFn: () =>
      fetchDocuments(entityId, ["RegisterOfDirectors", "StructureChart"]),
    ...opts,
  })

export const useGetOwnershipDocumentsSuspenseQry = () => {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(getOwnershipDocumentsQryOpts(entityId))
}

export const GET_DOCUMENT_FINANCIAL_STATEMENT_QRY_KEY =
  "document-financial-statement"

export const getFinancialDocumentsQryOpts = (
  entityId: string,
  opts: QryOpts = {},
) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [GET_DOCUMENT_FINANCIAL_STATEMENT_QRY_KEY, entityId],
    queryFn: () => fetchDocuments(entityId, ["FinancialStatements"]),
    ...opts,
  })

export const useGetFinancialDocumentsSuspenseQry = () => {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(getFinancialDocumentsQryOpts(entityId))
}

export const GET_FINANCIAL_INFO_SOURCES_QRY_KEY =
  "financial-information-sources"

export const getFinancialInfoSourcesQryOpts = (entityId: string) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [GET_FINANCIAL_INFO_SOURCES_QRY_KEY, entityId],
    queryFn: () => fetchFinancialInfoSources(entityId),
  })

export const useGetFinancialInfoSourcesSuspenseQry = () => {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(getFinancialInfoSourcesQryOpts(entityId))
}

export const GET_DOCUMENT_PROOF_OF_EXISTENCE_QRY_KEY =
  "document-proof-of-existence"

export const getProofOfExistenceDocumentsQryOpts = (
  entityId: string,
  opts: QryOpts = {},
) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [GET_DOCUMENT_PROOF_OF_EXISTENCE_QRY_KEY, entityId],
    queryFn: () => fetchDocuments(entityId, ["ProofOfExistence"]),
    ...opts,
  })

export const useGetProofOfExistenceDocumentsSuspenseQry = () => {
  const { entityId } = useLoaderData()

  return useSuspenseQuery(getProofOfExistenceDocumentsQryOpts(entityId))
}

export const GET_DOCUMENT_PASSPORT_OR_DRIVING_LICENSE_QRY_KEY =
  "document-passport-or-driving-license"
export const GET_DOCUMENT_PROOF_OF_ADDRESS_QRY_KEY = "document-proof-of-address"

export const getApplicantDetailsDocumentsQryOpts = (
  entityId: string,
  opts: QryOpts = {},
) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [
      GET_DOCUMENT_PASSPORT_OR_DRIVING_LICENSE_QRY_KEY,
      GET_DOCUMENT_PROOF_OF_ADDRESS_QRY_KEY,
      entityId,
    ],
    queryFn: () =>
      fetchDocuments(entityId, ["ProofOfIdentity", "ProofOfAddress"]),
    ...opts,
  })

export const useGetApplicantDetailsDocumentsSuspenseQry = () => {
  const { user } = useLoaderData()

  return useSuspenseQuery(
    getApplicantDetailsDocumentsQryOpts(user.ownerEntityId!),
  )
}
