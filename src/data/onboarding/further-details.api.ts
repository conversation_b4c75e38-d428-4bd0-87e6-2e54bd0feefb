import { UpdateEntityRequestDto } from "@/client/onboarding/types.gen"

import { uploadDocuments } from "./onboarding.api"
import { updateEntity } from "./entity-details.api"

interface UpdateFurtherDetailsPayload {
  entity: UpdateEntityRequestDto
  documents: File[]
}

export async function updateFurtherDetails(
  entityId: string,
  { entity, documents }: UpdateFurtherDetailsPayload,
) {
  await Promise.all([
    updateEntity(entityId, { entity }),
    uploadDocuments(entityId, "ProofOfExistence", documents),
  ])
}
