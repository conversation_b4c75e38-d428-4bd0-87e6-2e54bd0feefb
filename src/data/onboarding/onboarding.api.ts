import { HttpStatusCode } from "axios"

import type {
  DocumentCategory,
  UpdateFinancialSourcesRequest,
} from "@/client/onboarding/types.gen"

import {
  documentsGetEntityDocuments,
  documentsPostDocumentFile,
  documentsPostDocumentMetadata,
  documentsUnlinkDocumentEntity,
  usersGetCurrentUser,
  financialInformationGetSources,
  financialInformationPutSources,
} from "@/client/onboarding/sdk.gen"

import { assertResponse } from "./_exception-handler"
import { BadRequestError } from "../global/global.exceptions"

export async function fetchLoggedInUser() {
  const response = await usersGetCurrentUser()

  assertResponse(response, {
    [HttpStatusCode.NotFound]: new BadRequestError("User not found"),
  })

  return response.data!
}

/** To hold onboarding generic api */
export async function fetchDocuments(
  entityId: string,
  documentCategories: DocumentCategory[],
) {
  const response = await documentsGetEntityDocuments({
    path: { entityId },
    query: { documentCategory: documentCategories },
  })

  assertResponse(response)

  return response.data
}

const createDocument = async (
  entityId: string,
  documentCategory: DocumentCategory,
  file: File,
) => {
  const response = await documentsPostDocumentMetadata({
    path: { entityId },
    body: {
      category: documentCategory,
      name: file.name,
    },
  })

  assertResponse(response)

  return response.data?.id
}

const uploadFile = async (file: File, documentId: string) => {
  const response = await documentsPostDocumentFile({
    path: { documentId },
    body: { file },
  })

  assertResponse(response)
}

export async function uploadDocuments(
  entityId: string,
  documentCategory: DocumentCategory,
  payload: File[],
) {
  if (!entityId) throw new Error("Entity id not found")

  for (const file of payload) {
    if (!(file instanceof File)) {
      throw new Error("Invalid file type in documents array")
    }
  }

  const uploadPromises = payload.map(async (file) => {
    const documentId = await createDocument(entityId, documentCategory, file)

    if (documentId) await uploadFile(file, documentId)
  })

  await Promise.all(uploadPromises)
}

export async function deleteDocument(entityId: string, documentId: string) {
  const response = await documentsUnlinkDocumentEntity({
    path: { entityId, documentId },
  })

  assertResponse(response)
}

export async function fetchFinancialInfoSources(entityId: string) {
  const response = await financialInformationGetSources({
    path: { entityId },
  })

  assertResponse(response)

  return response.data
}

export async function putFinancialInfoSources(
  entityId: string,
  payload: UpdateFinancialSourcesRequest,
) {
  const response = await financialInformationPutSources({
    path: { entityId },
    body: payload,
  })

  assertResponse(response)

  return response.data
}
