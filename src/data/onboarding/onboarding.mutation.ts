import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import {
  DocumentCategory,
  UpdateFinancialSourcesRequest,
} from "@/client/onboarding/types.gen"

import {
  deleteDocument,
  uploadDocuments,
  putFinancialInfoSources,
} from "./onboarding.api"
import { useLoaderData } from "./$entityId.loader"

export const UPLOAD_DOCUMENT_MUTATE_QRY_KEY = "upload-documents"

export function useUploadDocumentMutation(
  qryKeys: string[],
  coreEntityId?: string,
  disableToast = false,
) {
  const data = useLoaderData()
  const queryClient = useQueryClient()
  const entityId = coreEntityId ?? data.entityId

  return useMutation({
    mutationKey: [UPLOAD_DOCUMENT_MUTATE_QRY_KEY, entityId],
    mutationFn: (props: {
      documentCategory: DocumentCategory
      payload: File[]
    }) => uploadDocuments(entityId, props.documentCategory, props.payload),
    onSuccess: () => {
      if (disableToast) return

      toast.success("Document uploaded successfully", {
        duration: 5000,
      })
    },
    onSettled: (_, error) => {
      if (error) {
        if (disableToast) return

        toast.error("Failed to upload document", {
          description: error?.message,
          duration: 5000,
        })
      }

      queryClient.invalidateQueries({
        queryKey: qryKeys,
      })
    },
  })
}

export const DELETE_DOCUMENT_MUTATE_QRY_KEY = "delete-document"

export function useDeleteDocumentMutation(
  qryKeys: string[],
  coreEntityId?: string,
  disableToast = false,
) {
  const data = useLoaderData()
  const queryClient = useQueryClient()
  const entityId = coreEntityId ?? data.entityId

  return useMutation({
    mutationKey: [DELETE_DOCUMENT_MUTATE_QRY_KEY, entityId],
    mutationFn: (props: { documentId: string }) =>
      deleteDocument(entityId, props.documentId),
    onSuccess: () => {
      if (disableToast) return

      toast.success("Document deleted successfully", {
        duration: 5000,
      })
    },
    onSettled: async (_, error) => {
      if (error) {
        if (disableToast) return

        toast.error("Failed to delete document", {
          description: error?.message,
          duration: 5000,
        })
      }

      queryClient.invalidateQueries({
        queryKey: qryKeys,
      })
    },
  })
}

export function useUpdateFinancialInfoSourceFundsMutation(qryKeys: string[]) {
  const queryClient = useQueryClient()
  const { entityId } = useLoaderData()

  return useMutation({
    mutationFn: (payload: UpdateFinancialSourcesRequest) =>
      putFinancialInfoSources(entityId, payload),
    onError: (error) => {
      toast.error("Failed to update source funds", {
        description: error.message,
      })
    },
    onSettled: async () => {
      toast.success("Source Funds updated successfully", {
        duration: 5000,
      })
      queryClient.invalidateQueries({
        queryKey: [...qryKeys],
      })
    },
  })
}
