import { queryOptions, useSuspenseQuery } from "@tanstack/react-query"

import { QryOpts } from "./types"
import { fetchAllAddressById, fetchEntityById } from "./entity-details.api"
import { useLoaderData } from "./$entityId.loader"
import { STALE_TIME } from "../global/global"

export const GET_NATURAL_ENTITY_ADDRESSES_QRY_KEY =
  "get-natural-entity-addresses"

export const getNaturalEntityAddressesQryOpts = (
  entityId: string,
  opts: QryOpts = {},
) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: [
      "applicant-details",
      GET_NATURAL_ENTITY_ADDRESSES_QRY_KEY,
      entityId,
    ],
    queryFn: () => fetchAllAddressById(entityId),
    ...opts,
  })

export function useGetNaturalEntityAddressesSuspenseQry() {
  const { user } = useLoaderData()

  return useSuspenseQuery(getNaturalEntityAddressesQryOpts(user.ownerEntityId!))
}

export const GET_NATURAL_ENTITY_QRY_KEY = "get-natural-entity"

export const entityQryOpts = (entityId: string, opts: QryOpts = {}) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: ["applicant-details", GET_NATURAL_ENTITY_QRY_KEY, entityId],
    queryFn: () => fetchEntityById(entityId),
    ...opts,
  })

export function useGetNaturalEntitySuspenseQry() {
  const { user } = useLoaderData()

  const entityId = user.ownerEntityId!

  return useSuspenseQuery(entityQryOpts(entityId))
}
