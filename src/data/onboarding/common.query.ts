import { useQueries } from "@tanstack/react-query"

import { expectedTransactionActivityQryOpts } from "./transaction-activity.query"
import {
  getApplicantDetailsDocumentsQryOpts,
  getFinancialDocumentsQryOpts,
  getOwnershipDocumentsQryOpts,
  getProofOfExistenceDocumentsQryOpts,
  getFinancialInfoSourcesQryOpts,
} from "./onboarding.query"
import { entityQryOpts, getAddressesQryOpts } from "./entity-details.query"
import {
  entityQryOpts as applicantEntityQryOpts,
  getNaturalEntityAddressesQryOpts,
} from "./applicant-details.query"
import { useLoaderData } from "./$entityId.loader"
import { getAllUsersQryOpts } from "../entity/entity.query"

export function useGetAllQueries() {
  const { entityId, user, ...staticData } = useLoaderData()

  const queries = useQueries({
    queries: [
      entityQryOpts(entityId),
      getAddressesQryOpts(entityId),
      getProofOfExistenceDocumentsQryOpts(entityId),
      getOwnershipDocumentsQryOpts(entityId),
      getFinancialDocumentsQryOpts(entityId),
      getAllUsersQryOpts(entityId),
      expectedTransactionActivityQryOpts(entityId),
      applicantEntityQryOpts(user.ownerEntityId!),
      getNaturalEntityAddressesQryOpts(user.ownerEntityId!),
      getApplicantDetailsDocumentsQryOpts(user.ownerEntityId!),
      getFinancialInfoSourcesQryOpts(entityId),
    ],
    combine: (queries) => ({
      staticData,
      entity: queries[0].data,
      addresses: queries[1].data,
      proofOfExistenceDocs: queries[2].data,
      ownershipDocs: queries[3].data,
      financialDocs: queries[4].data,
      users: queries[5].data,
      expectedTransactions: queries[6].data,
      applicantDetailsUser: queries[7].data?.naturalEntity,
      applicantDetailsUserAddresses: queries[8].data,
      applicantDetailsUserDocs: queries[9].data,
      financialInfoSourcesOfFunds: queries[10].data,
      isLoading: queries.every((result) => result.isLoading),
      isFetching: queries.every((result) => result.isFetching),
      isFetched: queries.every((result) => result.isFetched),
      isSuccess: queries.every((result) => result.isSuccess),
      isError: queries.every((result) => result.isError),
      refetch: () => queries.forEach((result) => result.refetch()),
    }),
  })

  return queries
}
