import { EnumValueDto } from "@/client/onboarding/types.gen"
import {
  staticDataEntityRegulationGet,
  staticDataGetCountryList,
  staticDataGetCurrencyList,
  staticDataGetEntityTypes,
  staticDataGetGicsIndustriesList,
  staticDataGetTransactionActivityTypesList,
  staticDataPurposeOfEntityGet,
  staticDataStockExchangeGet,
  staticDataUserRoleGet,
  staticDataSourceOfFundsGet,
} from "@/client/onboarding/sdk.gen"

import { assertResponse } from "./_exception-handler"

export async function fetchCountries() {
  const response = await staticDataGetCountryList()

  assertResponse(response)

  return response.data!
}

export async function fetchCurrencies() {
  const response = await staticDataGetCurrencyList()

  assertResponse(response)

  return response.data!
}

export async function fetchTransactionActivityTypes() {
  const response = await staticDataGetTransactionActivityTypesList()

  assertResponse(response)

  return response.data!
}

export async function fetchEntityTypes() {
  const response = await staticDataGetEntityTypes()

  assertResponse(response)

  return response.data!
}

export async function fetchPurposeOfEntities() {
  const response = await staticDataPurposeOfEntityGet()

  assertResponse(response)

  return response.data!
}

export async function fetchEntityRegulation() {
  const response = await staticDataEntityRegulationGet()

  assertResponse(response)

  return response.data!
}

export async function fetchStockExchange() {
  const response = await staticDataStockExchangeGet()

  assertResponse(response)

  return response.data!
}

export async function fetchGicsClassificationIndustries() {
  const response = await staticDataGetGicsIndustriesList()

  assertResponse(response)

  return response.data!.map((e) => ({
    key: e.industryCode?.toString(),
    display: e.industryName!,
  })) as EnumValueDto[]
}

export async function fetchUserRole() {
  const response = await staticDataUserRoleGet()

  assertResponse(response)

  return response.data!
}

export async function fetchSourceOfFunds() {
  const response = await staticDataSourceOfFundsGet()

  assertResponse(response)

  return response.data!
}
