import { HttpStatusCode, isAxiosError } from "axios"

import { ProblemDetails } from "@/client/onboarding/types.gen"

import { BadRequestError, NotFoundError } from "../global/global.exceptions"

export function assertResponse<T>(
  response: T,
  error?: Partial<Record<HttpStatusCode, Error>>,
) {
  if (isAxiosError(response)) {
    const { detail } = response?.response?.data as ProblemDetails

    switch (response.status) {
      case HttpStatusCode.Forbidden:
        throw new NotFoundError("Failed to retrieve entity")
      case HttpStatusCode.BadRequest:
        throw (
          error?.[HttpStatusCode.BadRequest] ??
          new BadRequestError(detail ?? "Bad request")
        )
      case HttpStatusCode.NotFound:
        throw (
          error?.[HttpStatusCode.NotFound] ??
          new NotFoundError(detail ?? "Not found")
        )
      case HttpStatusCode.InternalServerError:
        throw new Error(detail ?? "Internal server error")
      default:
        throw new Error("[Onboarding]: Internal server error")
    }
  }
}
