import { HttpStatusCode } from "axios"

import { CreateEntityAccessRequestDto } from "@/client/onboarding/types.gen"
import {
  entityAccessCreateEntityAccess,
  entityAccessDeleteEntityAccess,
  entityAccessGetEntityAccess,
} from "@/client/onboarding/sdk.gen"

import { assertResponse } from "./_exception-handler"
import { BadRequestError } from "../global/global.exceptions"

export async function fetchEntityAccess() {
  const response = await entityAccessGetEntityAccess()

  assertResponse(response, {
    [HttpStatusCode.NotFound]: new BadRequestError(
      "[Onboarding]: Entity access not found",
    ),
  })

  const filteredData = response.data?.map(({ entityId, ...entity }) => {
    return { ...entity, entityId: entityId?.toLowerCase() }
  })

  return filteredData
}

export async function createEntityAccess(
  payload: CreateEntityAccessRequestDto,
) {
  const response = await entityAccessCreateEntityAccess({
    body: payload,
  })

  assertResponse(response, {
    [HttpStatusCode.BadRequest]: new BadRequestError(
      "Unable to create entity access",
    ),
  })
}

export async function deleteEntityAccess(entityAccessId: string) {
  const response = await entityAccessDeleteEntityAccess({
    path: { entityAccessId },
  })

  assertResponse(response)
}
