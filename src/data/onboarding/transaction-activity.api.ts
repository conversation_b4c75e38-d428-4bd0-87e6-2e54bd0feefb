import { SetExpectedTransactionActivityDto } from "@/client/onboarding/types.gen"
import {
  expectedTransactionActivityGet,
  expectedTransactionActivityPut,
} from "@/client/onboarding/sdk.gen"

import { assertResponse } from "./_exception-handler"

/** /api/v1/Entities/{entityId}/ExpectedPaymentActivity */
export async function fetchExpectedTransactionActivity(entityId: string) {
  const response = await expectedTransactionActivityGet({
    path: { entityId },
  })

  assertResponse(response)

  return response.data!
}

export async function updateTransactionActivity(
  entityId: string,
  payload: SetExpectedTransactionActivityDto,
) {
  const response = await expectedTransactionActivityPut({
    path: { entityId },
    body: payload,
  })

  assertResponse(response)
}
