import { queryOptions, useQuery } from "@tanstack/react-query"

import {
  fetchAllUsersWithEntities,
  fetchCurrentUser,
  fetchByUserId,
} from "./user.api"
import { useLazyParameterizedQuery } from "@/hooks/use-lazy-query"
import { QryOpts } from "../onboarding/types"

export const CURRENT_USER_QUERY_KEY = "current-user"

/**
 * Hook to fetch and provide details of the currently logged-in user.
 * Designed for banking tribe to access user data for payment approvals (phone numbers etc).
 * No expiry on the data
 *
 * @returns The query result containing the user data including phone number and other details
 */
export function useCurrentUser() {
  return useQuery({
    queryKey: [CURRENT_USER_QUERY_KEY],
    queryFn: fetchCurrentUser,
    staleTime: Infinity, // Keep data fresh indefinitely as per requirements
  })
}

export const GET_ALL_USERS_WITH_ENTITIES_QRY_KEY = "get-all-users-with-entities"

export const getAllUsersWithEntitiesQryOpts = (
  opts: QryOpts = { enabled: false },
) =>
  queryOptions({
    queryKey: ["user", GET_ALL_USERS_WITH_ENTITIES_QRY_KEY],
    queryFn: fetchAllUsersWithEntities,
    // only fetch when user submits the form
    ...opts,
  })

export function useGetAllUsersWithEntitiesQry(opts: QryOpts = {}) {
  return useQuery(getAllUsersWithEntitiesQryOpts(opts))
}

export const GET_USER_BY_ID_QRY_KEY = "get-user-by-id"

export function useGetUserQry() {
  return useLazyParameterizedQuery(
    fetchByUserId,
    (userId: string) => ["user", GET_USER_BY_ID_QRY_KEY, userId],
    {
      retry: 0,
    },
  )
}
