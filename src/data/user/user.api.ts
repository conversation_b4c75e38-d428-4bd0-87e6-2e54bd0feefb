import * as client from "@/client/onboarding/sdk.gen"
import { assertResponse } from "@/data/onboarding/_exception-handler"

export async function fetchCurrentUser() {
  const response = await client.usersGetCurrentUser()

  assertResponse(response)

  return response.data!
}

/** /api/v1/Users */
export async function fetchAllUsersWithEntities() {
  const response =
    await client.usersGetAllUsersWithAccessToEntitiesForAdminUser()

  assertResponse(response)

  return response.data!
}

/** /api/v1/Users/<USER>/
export async function fetchByUserId(userId: string) {
  const response = await client.usersGetUser({ path: { userId } })

  assertResponse(response)

  return response.data!
}
