import { queryOptions, useQuery } from "@tanstack/react-query"

import { useLazyQuery } from "@/hooks/use-lazy-query"

import {
  fetchAllTermsAndConditions,
  downloadTermsAndConditionsDocument,
} from "./terms-and-conditions.api"

export const GET_ALL_TERMS_AND_CONDITIONS_QRY_KEY =
  "get-all-terms-and-conditions"

export const getAllTermsAndConditionsQryOpts = () =>
  queryOptions({
    queryKey: [GET_ALL_TERMS_AND_CONDITIONS_QRY_KEY],
    queryFn: () => fetchAllTermsAndConditions(),
  })

export function useGetAllTermsAndConditionsQry() {
  return useQuery(getAllTermsAndConditionsQryOpts())
}

export const DOWNLOAD_TERMS_AND_CONDITIONS_DOCUMENT_QRY_KEY =
  "download-terms-and-conditions-document"

export const downloadTermsAndConditionsDocumentQryKey = (key: string[]) => [
  DOWNLOAD_TERMS_AND_CONDITIONS_DOCUMENT_QRY_KEY,
  ...key,
]

export function downloadTermsAndConditionsDocumentQryOpts({
  queryKey,
}: {
  queryKey: string[]
}) {
  const [termsAndConditionsId] = queryKey
  return downloadTermsAndConditionsDocument(termsAndConditionsId)
}

export function useDownloadTermsAndConditionsDocumentQry() {
  return useLazyQuery(downloadTermsAndConditionsDocumentQryOpts, { retry: 0 })
}
