import { describe, it, expect, vi, beforeEach } from "vitest"

import {
  fetchAllTermsAndConditions,
  downloadTermsAndConditionsDocument,
} from "../terms-and-conditions.api"

// Mock the SDK functions
vi.mock("@/client/onboarding/sdk.gen", () => ({
  termsAndConditionsTermsAndConditions: vi.fn(),
  termsAndConditionsDownloadTermsAndConditions: vi.fn(),
}))

// Mock the exception handler
vi.mock("@/data/onboarding/_exception-handler", () => ({
  assertResponse: vi.fn((response) => {
    if (!response.data) {
      throw new Error("API Error")
    }
  }),
}))

import {
  termsAndConditionsTermsAndConditions,
  termsAndConditionsDownloadTermsAndConditions,
} from "@/client/onboarding/sdk.gen"
import { assertResponse } from "@/data/onboarding/_exception-handler"

const mockTermsResponse = {
  data: {
    allTermsAndConditions: [
      {
        termsAndConditionsId: "1",
        documentId: "doc1",
        documentName: "Terms of Service",
        versionNumber: 1,
        type: "TermsAndConditions",
        requiresAgreement: true,
      },
      {
        termsAndConditionsId: "2",
        documentId: "doc2",
        documentName: "Privacy Policy",
        versionNumber: 1,
        type: "CountryAddendum",
        requiresAgreement: true,
      },
    ],
  },
}

const mockPdfResponse = {
  data: new Blob(["mock pdf content"], { type: "application/pdf" }),
}

describe("Terms and Conditions API", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("fetchAllTermsAndConditions", () => {
    it("successfully fetches all terms and conditions", async () => {
      vi.mocked(termsAndConditionsTermsAndConditions).mockResolvedValue(
        mockTermsResponse as any,
      )

      const result = await fetchAllTermsAndConditions()

      expect(termsAndConditionsTermsAndConditions).toHaveBeenCalledWith()
      expect(assertResponse).toHaveBeenCalledWith(mockTermsResponse)
      expect(result).toEqual(mockTermsResponse.data)
    })

    it("handles API errors", async () => {
      const errorResponse = { data: null, error: "API Error" }
      vi.mocked(termsAndConditionsTermsAndConditions).mockResolvedValue(
        errorResponse as any,
      )

      // The mock already throws for null data, so we just need to test the behavior
      await expect(fetchAllTermsAndConditions()).rejects.toThrow("API Error")
    })

    it("handles network errors", async () => {
      vi.mocked(termsAndConditionsTermsAndConditions).mockRejectedValue(
        new Error("Network Error"),
      )

      await expect(fetchAllTermsAndConditions()).rejects.toThrow(
        "Network Error",
      )
    })
  })

  describe("downloadTermsAndConditionsDocument", () => {
    it("successfully downloads a document", async () => {
      vi.mocked(termsAndConditionsDownloadTermsAndConditions).mockResolvedValue(
        mockPdfResponse as any,
      )

      const result = await downloadTermsAndConditionsDocument("test-id")

      expect(termsAndConditionsDownloadTermsAndConditions).toHaveBeenCalledWith(
        {
          path: { termsAndConditionsId: "test-id" },
        },
      )
      expect(assertResponse).toHaveBeenCalledWith(mockPdfResponse)
      expect(result).toEqual(mockPdfResponse.data)
    })

    it("handles download errors", async () => {
      const errorResponse = { data: null, error: "Document not found" }
      vi.mocked(termsAndConditionsDownloadTermsAndConditions).mockResolvedValue(
        errorResponse as any,
      )

      // The mock already throws for null data, so we just need to test the behavior
      await expect(
        downloadTermsAndConditionsDocument("invalid-id"),
      ).rejects.toThrow("API Error")
    })

    it("handles network errors during download", async () => {
      vi.mocked(termsAndConditionsDownloadTermsAndConditions).mockRejectedValue(
        new Error("Network Error"),
      )

      await expect(
        downloadTermsAndConditionsDocument("test-id"),
      ).rejects.toThrow("Network Error")
    })

    it("passes correct parameters", async () => {
      vi.mocked(termsAndConditionsDownloadTermsAndConditions).mockResolvedValue(
        mockPdfResponse as any,
      )

      await downloadTermsAndConditionsDocument("specific-document-id")

      expect(termsAndConditionsDownloadTermsAndConditions).toHaveBeenCalledWith(
        {
          path: { termsAndConditionsId: "specific-document-id" },
        },
      )
    })
  })

  describe("Error Handling", () => {
    it("properly handles empty response data", async () => {
      const emptyResponse = { data: null }
      vi.mocked(termsAndConditionsTermsAndConditions).mockResolvedValue(
        emptyResponse as any,
      )

      // The mock already throws for null data, so we just need to test the behavior
      await expect(fetchAllTermsAndConditions()).rejects.toThrow("API Error")
    })

    it("handles malformed response data", async () => {
      const malformedResponse = { data: "invalid data" }
      vi.mocked(termsAndConditionsTermsAndConditions).mockResolvedValue(
        malformedResponse as any,
      )

      const result = await fetchAllTermsAndConditions()
      expect(result).toBe("invalid data")
    })

    it("handles timeout errors", async () => {
      vi.mocked(termsAndConditionsTermsAndConditions).mockRejectedValue(
        new Error("Request timeout"),
      )

      await expect(fetchAllTermsAndConditions()).rejects.toThrow(
        "Request timeout",
      )
    })
  })

  describe("Response Validation", () => {
    it("validates successful response structure", async () => {
      const validResponse = {
        data: {
          allTermsAndConditions: [
            {
              termsAndConditionsId: "1",
              documentName: "Test Document",
              versionNumber: 1,
              type: "TermsAndConditions",
            },
          ],
        },
      }
      vi.mocked(termsAndConditionsTermsAndConditions).mockResolvedValue(
        validResponse as any,
      )

      const result = await fetchAllTermsAndConditions()

      expect(result).toEqual(validResponse.data)
      expect(result.allTermsAndConditions).toBeDefined()
      expect(result.allTermsAndConditions!).toHaveLength(1)
      expect(result.allTermsAndConditions![0]).toHaveProperty(
        "termsAndConditionsId",
      )
      expect(result.allTermsAndConditions![0]).toHaveProperty("documentName")
    })

    it("validates PDF download response", async () => {
      const pdfBlob = new Blob(["PDF content"], { type: "application/pdf" })
      const pdfResponse = { data: pdfBlob }
      vi.mocked(termsAndConditionsDownloadTermsAndConditions).mockResolvedValue(
        pdfResponse as any,
      )

      const result = await downloadTermsAndConditionsDocument("test-id")

      expect(result).toBeInstanceOf(Blob)
      expect(result.type).toBe("application/pdf")
    })
  })

  describe("Edge Cases", () => {
    it("handles empty terms list", async () => {
      const emptyResponse = {
        data: {
          allTermsAndConditions: [],
        },
      }
      vi.mocked(termsAndConditionsTermsAndConditions).mockResolvedValue(
        emptyResponse as any,
      )

      const result = await fetchAllTermsAndConditions()

      expect(result.allTermsAndConditions).toHaveLength(0)
    })

    it("handles very large PDF files", async () => {
      const largePdfContent = new Array(1024 * 1024 + 1).fill("a").join("") // 1MB + 1 byte of 'a'
      const largePdfBlob = new Blob([largePdfContent], {
        type: "application/pdf",
      })
      const largePdfResponse = { data: largePdfBlob }

      vi.mocked(termsAndConditionsDownloadTermsAndConditions).mockResolvedValue(
        largePdfResponse as any,
      )

      const result = await downloadTermsAndConditionsDocument("large-doc-id")

      expect(result).toBeInstanceOf(Blob)
      expect(result.size).toBeGreaterThan(1024 * 1024)
    })

    it("handles special characters in document IDs", async () => {
      const specialId = "doc-with-special-chars-!@#$%^&*()"
      vi.mocked(termsAndConditionsDownloadTermsAndConditions).mockResolvedValue(
        mockPdfResponse as any,
      )

      await downloadTermsAndConditionsDocument(specialId)

      expect(termsAndConditionsDownloadTermsAndConditions).toHaveBeenCalledWith(
        {
          path: { termsAndConditionsId: specialId },
        },
      )
    })
  })
})
