import { renderHook, waitFor } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { describe, it, expect, vi, beforeEach } from "vitest"

import {
  useGetAllTermsAndConditionsQry,
  useDownloadTermsAndConditionsDocumentQry,
  getAllTermsAndConditionsQryOpts,
  downloadTermsAndConditionsDocumentQryKey,
  downloadTermsAndConditionsDocumentQryOpts,
} from "../terms-and-conditions.query"
import * as api from "../terms-and-conditions.api"
import { useLazyQuery } from "@/hooks/use-lazy-query"

// Mock the API functions
vi.mock("../terms-and-conditions.api", () => ({
  fetchAllTermsAndConditions: vi.fn(),
  downloadTermsAndConditionsDocument: vi.fn(),
}))

// Mock the lazy query hook
vi.mock("@/hooks/use-lazy-query", () => ({
  useLazyQuery: vi.fn((_queryFn, _options) => ({
    trigger: vi.fn(),
    data: null,
    isLoading: false,
    error: null,
    reset: vi.fn(),
  })),
}))

const mockTermsData = {
  allTermsAndConditions: [
    {
      termsAndConditionsId: "1",
      documentId: "doc1",
      documentName: "Terms of Service",
      versionNumber: 1,
      type: "TermsAndConditions" as const,
      requiresAgreement: true,
    },
    {
      termsAndConditionsId: "2",
      documentId: "doc2",
      documentName: "Privacy Policy",
      versionNumber: 1,
      type: "CountryAddendum" as const,
      requiresAgreement: true,
    },
  ],
}

const mockPdfBlob = new Blob(["mock pdf content"], { type: "application/pdf" })

describe("Terms and Conditions Query Hooks", () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    vi.clearAllMocks()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  describe("getAllTermsAndConditionsQryOpts", () => {
    it("creates correct query options", () => {
      const options = getAllTermsAndConditionsQryOpts()

      expect(options.queryKey).toEqual(["get-all-terms-and-conditions"])
      expect(options.queryFn).toBeDefined()
    })

    it("calls fetchAllTermsAndConditions when query function is executed", async () => {
      vi.mocked(api.fetchAllTermsAndConditions).mockResolvedValue(mockTermsData)

      const options = getAllTermsAndConditionsQryOpts()
      const queryClient = new QueryClient()

      // Type assertion to handle the possibly undefined queryFn
      const queryFn = options.queryFn!
      const result = await queryFn({
        queryKey: options.queryKey,
        signal: new AbortController().signal,
        meta: undefined,
        client: queryClient,
      })

      expect(api.fetchAllTermsAndConditions).toHaveBeenCalled()
      expect(result).toEqual(mockTermsData)
    })
  })

  describe("useGetAllTermsAndConditionsQry", () => {
    it("successfully fetches terms and conditions", async () => {
      vi.mocked(api.fetchAllTermsAndConditions).mockResolvedValue(mockTermsData)

      const { result } = renderHook(() => useGetAllTermsAndConditionsQry(), {
        wrapper,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockTermsData)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
    })

    it("handles loading state", () => {
      vi.mocked(api.fetchAllTermsAndConditions).mockImplementation(
        () => new Promise(() => {}), // Never resolves
      )

      const { result } = renderHook(() => useGetAllTermsAndConditionsQry(), {
        wrapper,
      })

      expect(result.current.isLoading).toBe(true)
      expect(result.current.data).toBeUndefined()
      expect(result.current.error).toBeNull()
    })

    it("handles error state", async () => {
      const mockError = new Error("Failed to fetch terms")
      vi.mocked(api.fetchAllTermsAndConditions).mockRejectedValue(mockError)

      const { result } = renderHook(() => useGetAllTermsAndConditionsQry(), {
        wrapper,
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(mockError)
      expect(result.current.data).toBeUndefined()
      expect(result.current.isLoading).toBe(false)
    })

    it("uses correct query key", () => {
      renderHook(() => useGetAllTermsAndConditionsQry(), { wrapper })

      // Check that the query is registered with the correct key
      const queryData = queryClient.getQueryData([
        "get-all-terms-and-conditions",
      ])
      expect(queryData).toBeUndefined() // Initially undefined before fetch
    })

    it("caches data correctly", async () => {
      vi.mocked(api.fetchAllTermsAndConditions).mockResolvedValue(mockTermsData)

      const { result: result1 } = renderHook(
        () => useGetAllTermsAndConditionsQry(),
        { wrapper },
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
      })

      // Second hook should use cached data
      const { result: result2 } = renderHook(
        () => useGetAllTermsAndConditionsQry(),
        { wrapper },
      )

      expect(result2.current.data).toEqual(mockTermsData)
      // Note: In test environment, React Query may call the function multiple times
      // The important thing is that caching works, not the exact call count
      expect(api.fetchAllTermsAndConditions).toHaveBeenCalled()
    })
  })

  describe("useDownloadTermsAndConditionsDocumentQry", () => {
    it("returns correct hook structure", () => {
      const { result } = renderHook(
        () => useDownloadTermsAndConditionsDocumentQry(),
        { wrapper },
      )

      expect(result.current).toHaveProperty("trigger")
      expect(result.current).toHaveProperty("data")
      expect(result.current).toHaveProperty("isLoading")
      expect(result.current).toHaveProperty("error")
      expect(result.current).toHaveProperty("reset")
    })

    it("has retry disabled", () => {
      // This test verifies that the useLazyQuery is called with retry: 0
      renderHook(() => useDownloadTermsAndConditionsDocumentQry(), { wrapper })

      expect(vi.mocked(useLazyQuery)).toHaveBeenCalledWith(
        expect.any(Function),
        { retry: 0 },
      )
    })
  })

  describe("Query Key Generation", () => {
    it("generates correct download query key", () => {
      const key = downloadTermsAndConditionsDocumentQryKey(["test-id"])
      expect(key).toEqual(["download-terms-and-conditions-document", "test-id"])
    })

    it("handles multiple parameters in query key", () => {
      const key = downloadTermsAndConditionsDocumentQryKey([
        "id1",
        "param2",
        "param3",
      ])
      expect(key).toEqual([
        "download-terms-and-conditions-document",
        "id1",
        "param2",
        "param3",
      ])
    })
  })

  describe("Query Function Options", () => {
    it("downloadTermsAndConditionsDocumentQryOpts extracts correct ID", async () => {
      vi.mocked(api.downloadTermsAndConditionsDocument).mockResolvedValue(
        mockPdfBlob,
      )

      await downloadTermsAndConditionsDocumentQryOpts({
        queryKey: ["test-document-id"],
      })

      expect(api.downloadTermsAndConditionsDocument).toHaveBeenCalledWith(
        "test-document-id",
      )
    })

    it("handles empty query key array", async () => {
      vi.mocked(api.downloadTermsAndConditionsDocument).mockResolvedValue(
        mockPdfBlob,
      )

      await downloadTermsAndConditionsDocumentQryOpts({
        queryKey: [],
      })

      expect(api.downloadTermsAndConditionsDocument).toHaveBeenCalledWith(
        undefined,
      )
    })
  })

  describe("Error Handling", () => {
    it("handles network errors gracefully", async () => {
      const networkError = new Error("Network error")
      vi.mocked(api.fetchAllTermsAndConditions).mockRejectedValue(networkError)

      const { result } = renderHook(() => useGetAllTermsAndConditionsQry(), {
        wrapper,
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(networkError)
    })

    it("handles API errors with proper error structure", async () => {
      const apiError = new Error("API returned 500")
      apiError.name = "APIError"
      vi.mocked(api.fetchAllTermsAndConditions).mockRejectedValue(apiError)

      const { result } = renderHook(() => useGetAllTermsAndConditionsQry(), {
        wrapper,
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.name).toBe("APIError")
      expect(result.current.error?.message).toBe("API returned 500")
    })
  })

  describe("Refetch Functionality", () => {
    it("allows manual refetch", async () => {
      vi.mocked(api.fetchAllTermsAndConditions)
        .mockResolvedValueOnce(mockTermsData)
        .mockResolvedValueOnce({
          ...mockTermsData,
          allTermsAndConditions: [
            ...mockTermsData.allTermsAndConditions,
            {
              termsAndConditionsId: "3",
              documentId: "doc3",
              documentName: "New Document",
              versionNumber: 1,
              type: "TermsAndConditions" as const,
              requiresAgreement: true,
            },
          ],
        })

      const { result } = renderHook(() => useGetAllTermsAndConditionsQry(), {
        wrapper,
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data?.allTermsAndConditions).toHaveLength(2)

      // Trigger refetch
      await result.current.refetch()

      await waitFor(() => {
        expect(result.current.data?.allTermsAndConditions).toHaveLength(3)
      })

      expect(api.fetchAllTermsAndConditions).toHaveBeenCalledTimes(2)
    })
  })
})
