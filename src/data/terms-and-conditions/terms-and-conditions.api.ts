import {
  termsAndConditionsTermsAndConditions,
  termsAndConditionsDownloadTermsAndConditions,
} from "@/client/onboarding/sdk.gen"

import { assertResponse } from "../onboarding/_exception-handler"

/**
 * Fetch all terms and conditions documents
 * Uses the /api/v1/TermsAndConditions endpoint
 */
export async function fetchAllTermsAndConditions() {
  const response = await termsAndConditionsTermsAndConditions()

  assertResponse(response)

  return response.data!
}

/**
 * Download a specific terms and conditions document
 * @param termsAndConditionsId - The ID of the terms and conditions document to download
 */
export async function downloadTermsAndConditionsDocument(
  termsAndConditionsId: string,
) {
  const response = await termsAndConditionsDownloadTermsAndConditions({
    path: { termsAndConditionsId },
  })

  assertResponse(response)

  return response.data!
}
