import { useQuery } from "@tanstack/react-query"

import { getPayeesList, fetchPayeeById } from "./payees.api"
import { queryKeys } from "@/lib/constants/query.constants"
import { STALE_TIME } from "../global/global"
export function usePayeesListQuery({
  entityId,
  pageNumber = 1,
  pageSize = 10,
  createdAtStart,
  createdAtEnd,
  accountName,
  type,
  orderByField = "createdAt",
  orderByDirection = "desc",
}: {
  entityId?: string
  pageNumber?: number
  pageSize?: number
  createdAtStart?: string
  createdAtEnd?: string
  accountName?: string
  type?: string
  orderByField?: string
  orderByDirection?: string
}) {
  return useQuery({
    queryKey: [
      queryKeys.payee.list,
      entityId,
      pageNumber,
      pageSize,
      createdAtStart,
      createdAtEnd,
      accountName,
      type,
      orderByField,
      orderByDirection,
    ],
    queryFn: () =>
      getPayeesList({
        entityId,
        pageNumber,
        pageSize,
        createdAtStart,
        createdAtEnd,
        accountName,
        type,
        orderByField,
        orderByDirection,
      }),
    enabled: !!entityId,
    staleTime: STALE_TIME,
  })
}

export function usePayeeByIdQuery(payeeId: string | null) {
  return useQuery({
    queryKey: [queryKeys.payee.byId, payeeId],
    queryFn: () => fetchPayeeById(payeeId!),
    enabled: !!payeeId,
    staleTime: STALE_TIME,
  })
}
