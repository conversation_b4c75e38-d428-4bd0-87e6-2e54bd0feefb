export interface IPayeeForm {
  bankDetails: IPayeeBankDetails
  payeeDetails: IPayeeDetails
}

export interface IBankData {
  bankCode: string
  payeeBankId: string
  name: string
  nationalId: string
  nationalIdType: string
  swiftBic: string
  postalCode: string
  streetName: string
  buildingNumber: string
  regionState: string
  city: string
  country?: string
  address?: Address
}

interface Address {
  buildingName: string | null
  buildingNumber: string | null
  streetName: string | null
  addressLine1: string
  addressLine2: string | null
  city: string
  regionState: string | null
  country: {
    name: string
    value: string
  }
  postalCode: string
}

export interface IPayeeBankDetails {
  isBusiness: boolean
  country?: string
  countryDetails?: ICountry
  bankCodeValue?: string // this should be nationalId
  accountNumberValue?: string // iban, acountNumber other things accountNumberType -> IBAN, AccountNumber
  bankCodeDetails?: IBankData
  accountName: string
}

export interface IPayeeDetails {
  displayName: string
  nationality: string
  email?: string
  searchTextAddress: string
  selectedAddress: string
  address: IPayeeAddress
  countryDetails?: ICountry
  nationalityDetails?: ICountry
}

export interface IPayeeAddress {
  buildingNumber: string
  street: string
  state: string
  city: string
  postalCode: string
  country?: string
}

export interface ILoqateAddress {
  Id: string
  Text: string
  Type: string
  Description: string
  Highlight: string
}

export interface ILoqateAddressDetail {
  Id: string
  DomesticId: string
  Language: string
  LanguageAlternatives: string
  Department: string
  Company: string
  SubBuilding: string
  BuildingNumber: string
  BuildingName: string
  Street: string
  SubStreet: string
  District: string
  City: string
  Line1: string
  Line2: string
  Line3: string
  Line4: string
  Line5: string
  AdminAreaName: string
  AdminAreaCode: string
  Province: string
  ProvinceName: string
  ProvinceCode: string
  PostalCode: string
  CountryName: string
  CountryIso2: string
  CountryIso3: string
  CountryIsoNumber: string
  SortingNumber1: string
  SortingNumber2: string
  Barcode: string
  POBoxNumber: string
  Label: string
  Type: string
  DataLevel: string
}

export interface IBankDetails {
  name: string // Name of the bank
  nationalId: string // National ID of the bank
  nationalIdType: string // Type of the national ID (e.g., SC)
  swiftBic: string // SWIFT/BIC code of the bank
  country: ICountry
}

export interface IPayeeData {
  id: string // Unique identifier for the payee
  accountName: string // Name of the account holder
  accountNumber?: string // Account number (e.g., last 4 digits visible)
  iban?: string // IBAN (International Bank Account Number)
  bank: IBankDetails // Bank details
  accountDetailsConfirmed: boolean
  accountDetailsConfirmedAt: string

  status: "Active" | "Pending" | "Inactive" // Status of the payee
  type: "Individual" | "Business" // Type of the payee
  displayName: string
  currentStatus: string
  createdAt: string
  createdBy: {
    id: string
    email: string
    displayName: string
  }
  client?: {
    id: string
    name: string
    registrationNumber: string
    address: string | null
  }
  isScaCompleted: boolean
}

export interface IPostPayee {
  displayName: string
  accountName: string
  accountNumber?: string
  iban?: string
  nationalId?: string
  swiftBic?: string
  postalCode: string
  streetName: string
  buildingNumber: string
  regionState: string
  city: string
  email?: string
  country?: string
  payeeType: "Business" | "Individual"
}

interface ICreatedBy {
  name: string | null
  email: string
}

interface ICountryShort {
  name: string
  code: string
}

interface IPayeeBank {
  payeeBankId: string
  name: string
  nationalId: string
  nationalIdType: string
  swiftBic: string
  postalCode: string
  streetName: string | null
  buildingNumber: string | null
  regionState: string | null
  city: string
  country: ICountryShort
}

export interface IPayeeDetailsResponse {
  id: string
  displayName: string
  accountName: string
  accountNumber: string
  iban: string | null
  postalCode: string
  streetName: string
  buildingNumber: string
  regionState: string
  city: string
  email: string
  country: ICountryShort
  type: string
  currentStatus: string
  createdAt: string
  createdBy: ICreatedBy
  approvedAt: string | null
  approvedBy: string | null
  rejectedAt: string | null
  rejectedBy: string | null
  payeeBank: IPayeeBank
  lastSentAmount: string
  lastSentValueDate: string
  isScaCompleted: boolean
}

export interface IUpdatePayee {
  displayName?: string | null
  accountName?: string | null
  accountNumber?: string | null
  buildingNumber?: string | null
  streetName?: string | null
  city?: string | null
  regionState?: string | null
  postalCode?: string | null
  country?: string | null
  email?: string | null
  nationality?: string | null
  accountDetailsConfirmed?: boolean
  iban?: string | null
  swiftBic?: string | null
}

export interface IUpdatePayeeResponse {
  aggregateId: string
  correlationId: string
}

export interface IPayeeResponse {
  id: string
  accountName: string
  accountNumber: string
  displayName: string
  iban: string | null
  type: "Individual" | "Business" | string
  currentStatus: string
  createdAt: string
  createdBy: ICreatedBy
  client: IClient
  bank: IBank
}
export interface IClient {
  id: string
  name: string
  registrationNumber: string
  address: string | null
}

export interface IBank {
  name: string
  nationalId: string
  nationalIdType: string
  swiftBic: string
  country: ICountry
}

export interface ICountry {
  id: string
  name: string
  formalName: string
  codeIso2: string
  codeIso3: string
  codeIso3Numeric: string
  phoneCode: string
  ibanLength: number
  ibanRegex: string
  createdAt: string
  createdBy: string
  ibanSupported: boolean
  accountNumberType: string
  nationalIdType: string
  isSepaCountry: boolean
  paymentPurposeCodeRequired: boolean
}
