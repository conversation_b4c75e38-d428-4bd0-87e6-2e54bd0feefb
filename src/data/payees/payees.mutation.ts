import { toast } from "sonner"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import type { IPayeeForm, IUpdatePayee } from "./payees.interface"
import { queryKeys } from "@/lib/constants/query.constants"
import {
  postPayee,
  deletePayee,
  updatePayee,
  startPayeeVerification,
  verifyPayee,
} from "./payees.api"

export function addPayeeMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payee: IPayeeForm) => postPayee(payee),
    onSuccess: (data) => {
      toast.success("Payee added successfully", {
        duration: 5000,
      })
      queryClient.invalidateQueries({
        predicate: (query) => query.queryKey[0] === queryKeys.payee.list,
      })
    },
    onError: (error: Error) => {
      toast.error("Failed to add payee", {
        description: error?.message,
        duration: 5000,
      })
    },
  })
}

export function useDeletePayeeMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payeeId: string) => deletePayee(payeeId),
    onSuccess: () => {
      // Invalidate and refetch the payees list query
      queryClient.invalidateQueries({ queryKey: [queryKeys.payee.list] })
      toast.success("Payee has been deleted successfully.", {
        duration: 5000,
      })
    },
    onError: (error: Error) => {
      toast.error("Cannot delete payee", {
        description:
          error?.message || "Failed to delete payee. Please try again.",
        duration: 5000,
      })
    },
  })
}

export function useUpdatePayeeMutation() {
  return useMutation({
    mutationFn: ({ id, payeeData }: { id: string; payeeData: IUpdatePayee }) =>
      updatePayee(id, payeeData),
    onSuccess: () => {
      toast.success("Payee updated successfully", {
        duration: 5000,
      })
    },
    onError: (error: Error) => {
      toast.error("Failed to update payee", {
        description: error?.message,
        duration: 5000,
      })
    },
  })
}

export function useStartPayeeVerificationMutation() {
  return useMutation({
    mutationFn: ({
      payeeId,
      type = "Sms",
    }: {
      payeeId: string
      type?: string
    }) => startPayeeVerification(payeeId, type),
    onSuccess: () => {
      toast.success(
        "Verification started. Please check your SMS for the code.",
        {
          duration: 5000,
        },
      )
    },
    onError: (error: Error) => {
      toast.error("Failed to start verification", {
        description: error?.message,
        duration: 5000,
      })
    },
  })
}

export function useVerifyPayeeMutation() {
  return useMutation({
    mutationFn: ({
      payeeId,
      verificationCode,
    }: {
      payeeId: string
      verificationCode: string
    }) => verifyPayee(payeeId, verificationCode),
    onSuccess: () => {
      toast.success("Payee verified successfully.", {
        duration: 5000,
      })
    },
    onError: (error: Error) => {
      toast.error("Failed to verify payee", {
        description: error?.message,
        duration: 5000,
      })
    },
  })
}
