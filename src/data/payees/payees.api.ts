import { bankingClient as client } from "@/client/banking"

import {
  IPayeeForm,
  IPostPayee,
  IPayeeData,
  IBankData,
  IPayeeDetailsResponse,
  IUpdatePayee,
  IUpdatePayeeResponse,
} from "./payees.interface"
import { ICountry } from "../global/global.interface"

export const getPayeesList = async ({
  entityId,
  pageNumber = 1,
  pageSize = 10,
  createdAtStart,
  createdAtEnd,
  accountName,
  type,
  orderByField = "createdAt",
  orderByDirection = "desc",
}: {
  entityId?: string
  pageNumber?: number
  pageSize?: number
  createdAtStart?: string
  createdAtEnd?: string
  accountName?: string
  type?: string
  orderByField?: string
  orderByDirection?: string
}): Promise<{ items: IPayeeData[]; totalItems: number }> => {
  try {
    const response = await client.get("/api/v1/payees", {
      params: {
        entityId,
        pageNumber,
        pageSize,
        createdAtStart,
        createdAtEnd,
        accountName,
        type,
        orderByField,
        orderByDirection,
      },
    })

    return {
      items: response.data?.data || [],
      totalItems: response.data?.totalCount || 0,
    }
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "An error occurred while fetching payees.",
    )
  }
}

export const postPayee = async (payee: IPayeeForm) => {
  const payeeData: IPostPayee = {
    displayName: payee.payeeDetails?.displayName,
    accountName: payee.bankDetails?.accountName,
    city: payee.payeeDetails?.address?.city,
    postalCode: payee.payeeDetails?.address?.postalCode,
    regionState: payee.payeeDetails?.address?.state,
    streetName: payee.payeeDetails?.address?.street,
    buildingNumber: payee.payeeDetails?.address?.buildingNumber,
    payeeType: payee.bankDetails?.isBusiness ? "Business" : "Individual",
    email: payee.payeeDetails?.email,
  }
  if (payee.bankDetails.countryDetails) {
    if (payee.bankDetails.countryDetails.nationalIdType === "SwiftBic") {
      payeeData.swiftBic = payee.bankDetails.bankCodeValue
    } else {
      payeeData.nationalId = payee.bankDetails.bankCodeValue
    }

    if (payee.bankDetails.countryDetails.accountNumberType === "IBAN") {
      payeeData.iban = payee.bankDetails.accountNumberValue
    } else {
      payeeData.accountNumber = payee.bankDetails.accountNumberValue
    }
  }
  if (payee.bankDetails.countryDetails) {
    payeeData.country = payee.bankDetails.countryDetails.codeIso2
  }
  const response = await client.post("/api/v1/payees", payeeData)

  return response.data
}

export const fetchPayeeById = async (id: string) => {
  if (!id) {
    throw new Error("Payee ID is required.")
  }

  try {
    const { data } = await client.get<IPayeeDetailsResponse>(
      `/api/v1/payees/${id}`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to fetch payee details. Please try again.",
    )
  }
}

export const fetchShortCode = async (sortCode: string, country: ICountry) => {
  const params: any = {
    type: country.nationalIdType == "SwiftBic" ? "Bic" : "NationalId",
  }
  if (country.codeIso2) {
    params.CountryCode = country.codeIso2
  }

  try {
    const { data } = await client.get<IBankData>(
      `/api/v1/payees/banks/${sortCode}`,
      {
        params,
      },
    )
    if (data) {
      return data
    }
    throw new Error("Invalid sort code or branch not found")
  } catch (error) {
    throw new Error("Invalid sort code or branch not found")
  }
}

export const deletePayee = async (id: string) => {
  if (!id) {
    throw new Error("Payee ID is required.")
  }

  try {
    const response = await client.delete(`/api/v1/payees/${id}`)
    return response.data
  } catch (error: any) {
    // Handle complex error structure with errors array
    if (
      error.response?.data?.errors &&
      Array.isArray(error.response.data.errors)
    ) {
      const errorMessages = error.response.data.errors
        .map((err: any) => err.reason)
        .filter(Boolean)
        .join(", ")

      if (errorMessages) {
        throw new Error(errorMessages)
      }
    }

    // Fallback to message or default error
    throw new Error(
      error.response?.data?.message ||
        "Failed to delete payee. Please try again.",
    )
  }
}

export const updatePayee = async (
  id: string,
  payeeData: IUpdatePayee,
): Promise<IUpdatePayeeResponse> => {
  if (!id) {
    throw new Error("Payee ID is required.")
  }

  try {
    const response = await client.patch(`/api/v1/payees/${id}`, payeeData)
    return response.data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to update payee. Please try again.",
    )
  }
}

/**
 * Initiate SCA verification for a payee before it can be used in payments.
 * @param payeeId The payee's ID
 * @param type The verification type (default: 'Sms')
 * @returns The response from the verification endpoint
 */
export const startPayeeVerification = async (
  payeeId: string,
  type: string = "Sms",
): Promise<{ aggregateId: string; correlationId: string }> => {
  if (!payeeId) throw new Error("Payee ID is required for verification.")
  try {
    const response = await client.post(
      `/api/v1/payees/${payeeId}/start-verification`,
      {
        type,
      },
      {
        headers: { "Content-Type": "application/json" },
      },
    )
    return response.data
  } catch (error: any) {
    // Handle validation error (400) with detail message
    if (error.response?.status === 400 && error.response?.data?.detail) {
      throw new Error(error.response.data.detail)
    }
    throw new Error(
      error.response?.data?.message ||
        "Failed to start payee verification. Please try again.",
    )
  }
}

/**
 * Complete SCA verification for a payee by validating the verification code.
 * @param payeeId The payee's ID
 * @param verificationCode The verification code to validate
 * @returns The response from the verification endpoint
 */
export const verifyPayee = async (
  payeeId: string,
  verificationCode: string,
): Promise<{ aggregateId: string; correlationId: string }> => {
  if (!payeeId) throw new Error("Payee ID is required for verification.")
  if (!verificationCode) throw new Error("Verification code is required.")
  try {
    const response = await client.post(
      `/api/v1/payees/${payeeId}/verify`,
      { verificationCode },
      { headers: { "Content-Type": "application/json" } },
    )
    return response.data
  } catch (error: any) {
    // Handle validation error (400) with detail message
    if (error.response?.status === 400 && error.response?.data?.detail) {
      throw new Error(error.response.data.detail)
    }
    throw new Error(
      error.response?.data?.message ||
        "Failed to verify payee. Please try again.",
    )
  }
}
