import { toast } from "sonner"
import { useMutation } from "@tanstack/react-query"
import { queryClient } from "@/main"

import {
  IPaymentForm,
  IPaymentRejectionReason,
  IPendingPayment,
  IApproveFxEmbeddedRequest,
  UpdatePaymentRequest,
  VerificationType,
  VerificationOperationType,
} from "./payments.interface"
import {
  postPayment,
  approvePayment,
  revertPaymentToDraft,
  cancelPaymentWithReason,
  bulkPaymentProcess,
  bulkPaymentSubmit,
  bulkPaymentCancel,
  bulkPaymentDeletePayments,
  bulkPaymentPatchRow,
  approveBulkPayment,
  approvePaymentWithFxEmbedded,
  submitPaymentWithVerification,
  updatePayment,
  startBulkPaymentVerification,
  verifyBulkPaymentCode,
} from "./payments.api"
import { mutationKeys, queryKeys } from "@/lib/constants/query.constants"
import { getHttpErrorMessage } from "@/lib/http.error.utils"
interface IAddPaymentMutation {
  payment: IPaymentForm
  isDraft: boolean
}
export function addPaymentMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.add],
    mutationFn: ({ payment, isDraft }: IAddPaymentMutation) =>
      postPayment(payment, isDraft),
    onSettled: (data, error, variables) => {
      if (error) {
        toast.error("Failed to submit payment", {
          description: error?.message,
          duration: 5000,
        })
      }
      if (data && !variables.isDraft) {
        toast.success("Payment submitted for approval successfully", {
          duration: 5000,
        })
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        predicate(query) {
          return query.queryKey.at(0) === queryKeys.payment.list
        },
      })
    },
  })
}

export function approvePaymentMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.approve],
    mutationFn: ({ paymentId }: { paymentId: string }) =>
      approvePayment(paymentId),
    onSettled: (data, error) => {
      if (error) {
        const message = getHttpErrorMessage(error)
        toast.error("Failed to approve payment", {
          description: message,
          duration: 5000,
        })
      }
      if (data) {
        toast.success("Payment approved successfully", {
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          if (query.queryKey.at(0) === queryKeys.payment.list) return true
          if (
            query.queryKey.at(0) === queryKeys.payment.byId &&
            query.queryKey.at(1) === variables.paymentId
          )
            return true
          if (
            query.queryKey.at(0) === queryKeys.payment.byId &&
            query.queryKey.at(1) === variables.paymentId
          )
            return true
          if (
            query.queryKey.at(0) === queryKeys.payment.approvalDetails &&
            query.queryKey.at(1) === variables.paymentId
          )
            return true
          return false
        },
      })
    },
  })
}

export function revertPaymentToDraftMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.revert],
    mutationFn: (paymentId: string) => revertPaymentToDraft(paymentId),
    onSettled: (data, error) => {
      if (error) {
        toast.error("Failed to revert payment", {
          description: error?.message,
          duration: 5000,
        })
      }
      if (data) {
        toast.success("Payment reverted to draft status", {
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          return (
            query.queryKey.at(0) === queryKeys.payment.list ||
            (query.queryKey.at(0) === queryKeys.payment.byId &&
              query.queryKey.at(1) === variables)
          )
        },
      })
    },
  })
}

interface ICancelWithReasonParams {
  paymentId: string
  reason: IPaymentRejectionReason
}

export function cancelWithReasonMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.cancelWithReason],
    mutationFn: ({ paymentId, reason }: ICancelWithReasonParams) =>
      cancelPaymentWithReason(paymentId, reason),
    onSettled: (_data, error) => {
      if (error) {
        toast.error("Failed to cancel payment", {
          duration: 5000,
        })
      } else {
        toast.success("Payment cancelled successfully", {
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          return (
            query.queryKey.at(0) === queryKeys.payment.list ||
            (query.queryKey.at(0) === queryKeys.payment.byId &&
              query.queryKey.at(1) === variables.paymentId)
          )
        },
      })
    },
  })
}

function invalidateBulkPaymentQueries(
  bulkUploadId: string,
  invalidatePayments = false,
) {
  queryClient.invalidateQueries({
    predicate(query) {
      if (
        query.queryKey.at(0) === queryKeys.payment.pending &&
        query.queryKey.at(1) === bulkUploadId
      ) {
        return true
      }
      if (
        query.queryKey.at(0) === queryKeys.payment.bulkSummary &&
        query.queryKey.at(1) === bulkUploadId
      ) {
        return true
      }
      if (
        invalidatePayments &&
        query.queryKey.at(0) === queryKeys.payment.list
      ) {
        return true
      }
      return false
    },
  })
}

export function bulkPaymentProcessMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.bulkProcess],
    mutationFn: (bulkUploadId: string) => bulkPaymentProcess(bulkUploadId),
    onSettled: (data, error, variables) => {
      if (data) {
        toast.success("Bulk payment processed successfully", {
          duration: 5000,
        })
        invalidateBulkPaymentQueries(variables, false)
      }

      if (error) {
        toast.error("Failed to process bulk payment", {
          duration: 5000,
        })
      }
    },
  })
}

export function bulkPaymentSubmitMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.bulkSubmit],
    mutationFn: (bulkUploadId: string) => bulkPaymentSubmit(bulkUploadId),
    onSettled: (data, error, variables) => {
      if (data) {
        toast.success("Bulk payment submitted successfully", {
          duration: 5000,
        })
        invalidateBulkPaymentQueries(variables, true)
      }

      if (error) {
        toast.error("Failed to submit bulk payment", {
          duration: 5000,
        })
      }
    },
  })
}

export function bulkPaymentCancelMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.bulkCancel],
    mutationFn: (bulkUploadId: string) => bulkPaymentCancel(bulkUploadId),
    onSettled: (data, error, variables) => {
      if (data) {
        toast.success("Bulk payment cancelled successfully", {
          duration: 5000,
        })
        invalidateBulkPaymentQueries(variables, true)
      }

      if (error) {
        toast.error("Failed to cancel bulk payment", {
          duration: 5000,
        })
      }
    },
  })
}

export function approveBulkPaymentMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.approveBulk],
    mutationFn: (bulkUploadId: string) => approveBulkPayment(bulkUploadId),
    onSettled: (data, error, variables) => {
      if (data) {
        toast.success("Bulk payment approved successfully", {
          duration: 5000,
        })
        invalidateBulkPaymentQueries(variables, true)
      }

      if (error) {
        toast.error("Failed to approve bulk payment", {
          duration: 5000,
        })
      }
    },
  })
}

export function bulkPaymentDeletePaymentsMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.bulkDeletePayments],
    mutationFn: ({
      bulkUploadId,
      rowNumbers,
    }: {
      bulkUploadId: string
      rowNumbers: string[]
    }) => bulkPaymentDeletePayments(bulkUploadId, rowNumbers),
    onSettled: (_data, error, variables) => {
      if (!error) {
        toast.success("Payment rows deleted successfully", {
          duration: 5000,
        })
        invalidateBulkPaymentQueries(variables.bulkUploadId)
      } else {
        toast.error("Failed to delete payments", {
          duration: 5000,
        })
      }
    },
  })
}

export function bulkPaymentPatchRowMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.bulkPatchRow],
    mutationFn: ({
      bulkUploadId,
      payload,
    }: {
      bulkUploadId: string
      payload: IPendingPayment
    }) => bulkPaymentPatchRow(bulkUploadId, payload),
    onSettled: (_data, error, variables) => {
      if (error) {
        toast.error("Failed to update payment", {
          duration: 5000,
        })
      } else {
        toast.success("Payment updated successfully", {
          duration: 5000,
        })
        invalidateBulkPaymentQueries(variables.bulkUploadId)
      }
    },
  })
}

interface IApproveWithFxEmbeddedParams {
  paymentId: string
  request: IApproveFxEmbeddedRequest
}

export function approvePaymentWithFxEmbeddedMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.approveWithFx],
    mutationFn: ({ paymentId, request }: IApproveWithFxEmbeddedParams) =>
      approvePaymentWithFxEmbedded(paymentId, request),
    onSettled: (data, error) => {
      if (error) {
        toast.error("Failed to approve payment with FX", {
          description: error?.message,
          duration: 5000,
        })
      }
      if (data) {
        toast.success("Payment with FX approved successfully", {
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          return (
            query.queryKey.at(0) === queryKeys.payment.list ||
            (query.queryKey.at(0) === queryKeys.payment.byId &&
              query.queryKey.at(1) === variables.paymentId)
          )
        },
      })
    },
  })
}

export function updatePaymentMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.update],
    mutationFn: ({
      paymentId,
      payment,
    }: {
      paymentId: string
      payment: UpdatePaymentRequest
    }) => updatePayment(paymentId, payment),
    onSettled: (data, error) => {
      if (error) {
        toast.error("Failed to update payment", {
          description: error?.message,
          duration: 5000,
        })
      }
      if (data) {
        toast.success("Payment updated successfully", {
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          return (
            query.queryKey.at(0) === queryKeys.payment.list ||
            (query.queryKey.at(0) === queryKeys.payment.byId &&
              query.queryKey.at(1) === variables.paymentId)
          )
        },
      })
    },
  })
}

export function submitPaymentWithVerificationMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.submitWithVerification],
    mutationFn: ({ paymentId }: { paymentId: string }) =>
      submitPaymentWithVerification(paymentId),
    onSettled: (data, error) => {
      if (error) {
        toast.error("Failed to submit payment", {
          description: error?.message,
          duration: 5000,
        })
      }
      if (data) {
        toast.success("Payment submitted successfully", {
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          return (
            query.queryKey.at(0) === queryKeys.payment.list ||
            (query.queryKey.at(0) === queryKeys.payment.byId &&
              query.queryKey.at(1) === variables.paymentId)
          )
        },
      })
    },
  })
}

export function startBulkPaymentVerificationMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.startBulkVerification],
    mutationFn: ({
      bulkPaymentId,
      type,
      operationType,
    }: {
      bulkPaymentId: string
      type: VerificationType
      operationType: VerificationOperationType
    }) => startBulkPaymentVerification(bulkPaymentId, type, operationType),
    onSettled: (data, error) => {
      if (error) {
        toast.error("Failed to start bulk payment verification", {
          description: error?.message,
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          return (
            query.queryKey.at(0) === queryKeys.payment.bulkSummary &&
            query.queryKey.at(1) === variables.bulkPaymentId
          )
        },
      })
    },
  })
}

export function verifyBulkPaymentCodeMutation() {
  return useMutation({
    mutationKey: [mutationKeys.payment.verifyBulkCode],
    mutationFn: ({
      bulkPaymentId,
      verificationCode,
      operationType,
    }: {
      bulkPaymentId: string
      verificationCode: string
      operationType: VerificationOperationType
    }) => verifyBulkPaymentCode(bulkPaymentId, verificationCode, operationType),
    onSettled: (data, error) => {
      if (error) {
        toast.error("Failed to verify bulk payment code", {
          description: error?.message,
          duration: 5000,
        })
      }
      if (data) {
        toast.success("Bulk payment verification successful", {
          duration: 5000,
        })
      }
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        predicate(query) {
          return (
            query.queryKey.at(0) === queryKeys.payment.bulkSummary &&
            query.queryKey.at(1) === variables.bulkPaymentId
          )
        },
      })
    },
  })
}
