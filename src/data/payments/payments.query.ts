import { keepPreviousData, useMutation, useQuery } from "@tanstack/react-query"

import {
  fetchBulkPaymentSummary,
  fetchPaymentById,
  fetchPaymentPurpose,
  fetchPaymentRejectionReasons,
  fetchPayments,
  fetchPendingPayments,
  fetchPaymentApprovalDetails,
  fetchBulkPaymentById,
  fetchFxRateById,
  fetchFxValueDates,
  fetchFxQuote,
  deletePayment,
} from "@/data/payments/payments.api"

import { IFxQuoteRequest } from "./payments.interface"
import { validateSignatoryAmount } from "./payments.api"
import { queryKeys } from "@/lib/constants/query.constants"
import { queryClient } from "@/main"
import { toast } from "sonner"
interface IUsePaymentsListQuery {
  entityId: string
  variant: "bulk" | "default"
  pageNumber?: number
  pageSize?: number
  orderByField?: string
  orderByDirection?: "asc" | "desc"
  currentStatus?: string
  payeeName?: string
  bulkUploadId?: string
}

export function usePaymentsListQuery({
  entityId,
  variant,
  pageNumber = 1,
  pageSize = 12,
  orderByField = "created",
  orderByDirection = "desc",
  currentStatus = "All",
  payeeName = "",
  bulkUploadId,
}: IUsePaymentsListQuery) {
  return useQuery({
    queryKey: [
      queryKeys.payment.list,
      entityId,
      variant,
      pageNumber,
      pageSize,
      orderByField,
      orderByDirection,
      currentStatus,
      payeeName,
      bulkUploadId,
    ],
    queryFn: () =>
      fetchPayments({
        pageNumber,
        pageSize,
        orderByField,
        orderByDirection,
        currentStatus,
        payeeName,
        variant,
        bulkUploadId,
      }),
    enabled: !!entityId,
    placeholderData: keepPreviousData,
  })
}

export function usePaymentQuery(paymentId?: string) {
  return useQuery({
    queryKey: [queryKeys.payment.byId, paymentId],
    queryFn: () => {
      if (!paymentId) {
        throw new Error("Payment ID is required.")
      }
      return fetchPaymentById(paymentId)
    },
    enabled: !!paymentId,
  })
}

export function usePaymentRejectionReasonsQuery() {
  return useQuery({
    queryKey: [queryKeys.payment.rejectionReasons],
    queryFn: () => fetchPaymentRejectionReasons(),
    staleTime: Infinity,
  })
}

export function usePaymentPurposeQuery(countryIdentifier?: string) {
  return useQuery({
    queryKey: [queryKeys.payment.purpose, countryIdentifier],
    queryFn: () => fetchPaymentPurpose(countryIdentifier),
    enabled: !!countryIdentifier,
    staleTime: Infinity,
  })
}

export function usePendingPaymentsQuery(bulkUploadId: string) {
  return useQuery({
    queryKey: [queryKeys.payment.pending, bulkUploadId],
    queryFn: () => fetchPendingPayments(bulkUploadId),
    enabled: !!bulkUploadId,
  })
}

export function useBulkPaymentSummaryQuery({
  bulkUploadId,
  enabled,
}: {
  bulkUploadId?: string
  enabled?: boolean
}) {
  return useQuery({
    queryKey: [queryKeys.payment.bulkSummary, bulkUploadId],
    queryFn: () => {
      if (!bulkUploadId) {
        throw new Error("Bulk payment ID is required.")
      }
      return fetchBulkPaymentSummary(bulkUploadId)
    },
    enabled: !!bulkUploadId && enabled,
  })
}

export const useValidateSignatoryAmount = (
  amount: number,
  currency: string,
) => {
  return useQuery({
    queryKey: [queryKeys.payment.validateSignatory, amount, currency],
    queryFn: () => validateSignatoryAmount(amount, currency),
    enabled: !!amount && !!currency,
  })
}
export function usePaymentApprovalDetailsQuery(paymentId: string) {
  return useQuery({
    queryKey: [queryKeys.payment.approvalDetails, paymentId],
    queryFn: () => fetchPaymentApprovalDetails(paymentId),
    enabled: !!paymentId,
  })
}

export function useBulkPaymentByIdQuery(bulkPaymentId?: string) {
  return useQuery({
    queryKey: [queryKeys.payment.bulkById, bulkPaymentId],
    queryFn: () => {
      if (!bulkPaymentId) {
        throw new Error("Bulk payment ID is required.")
      }
      return fetchBulkPaymentById(bulkPaymentId)
    },
    enabled: !!bulkPaymentId,
  })
}

export function useFxRateQuery(currencyPair?: string) {
  return useQuery({
    queryKey: [queryKeys.payment.fxRate, currencyPair],
    queryFn: () => {
      if (!currencyPair) {
        throw new Error("Currency pair is required.")
      }
      return fetchFxRateById(currencyPair)
    },
  })
}

export function useFxValueDatesQuery(
  lhsCurrency?: string,
  rhsCurrency?: string,
) {
  return useQuery({
    queryKey: [queryKeys.payment.fxValueDates, lhsCurrency, rhsCurrency],
    queryFn: () => {
      if (!lhsCurrency) {
        throw new Error("Left-hand side currency is required.")
      }
      if (!rhsCurrency) {
        throw new Error("Right-hand side currency is required.")
      }

      return fetchFxValueDates(lhsCurrency, rhsCurrency)
    },
    enabled: !!lhsCurrency && !!rhsCurrency,
  })
}

export function useFxQuoteQuery(
  quoteRequest: IFxQuoteRequest | undefined,
  enabled = true,
) {
  return useQuery({
    queryKey: [queryKeys.payment.fxQuote, quoteRequest],
    queryFn: () => {
      if (!quoteRequest) {
        throw new Error("Quote request data is required.")
      }

      try {
        return fetchFxQuote(quoteRequest)
      } catch (error: any) {
        throw new Error(
          error.response?.data?.message ||
            "Failed to fetch FX quote. Please try again.",
        )
      }
    },
    enabled: !!quoteRequest && enabled,
  })
}

export function useDeleteDraftPaymentQuery(paymentId: string) {
  return useMutation({
    mutationFn: () => deletePayment(paymentId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.payment.list],
      })
      toast.success("Payment deleted successfully.")
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to delete payment.")
    },
  })
}
