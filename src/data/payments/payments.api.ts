import { v1 } from "uuid"
import { toast } from "sonner"
import { bankingClient as client } from "@/client/banking"
import { BulkPaymentErrorType } from "@/components/pages/payments/components/BulkPaymentAlert"
import {
  convertBulkPaymentBackendToBulkPayment,
  convertOutboundPaymentBackendToOutboundPayment,
} from "./payments.utils"
import {
  IAddPaymentDTO,
  IBulkPaymentPendingPayments,
  IBulkPaymentSummary,
  IPaymentPurpose,
  IPaymentRejectionReason,
  IPendingPayment,
  UpdatePaymentRequest,
  ISignatoryValidation,
  IPaymentApprovalDetails,
  IBulkPaymentDetails,
  IUploadBulkPaymentDTO,
  IUploadOptions,
  IOutboundPaymentBackend,
  IBulkPaymentBackend,
  IPaymentDetailsBackend,
  IPaymentResponse,
  IFxRateResponse,
  IFxQuoteRequest,
  IFxQuoteResponse,
  IApproveFxEmbeddedRequest,
  VerificationType,
  VerificationOperationType,
  IVerificationResponse,
  IEmbeddedPaymentRequest,
  IEmbeddedPaymentResponse,
  PaymentType,
  FeeOption,
} from "./payments.interface"
import { IPaymentForm } from "./payments.interface"
import { IPaginationResponse } from "../global/global.interface"

export async function fetchPayments({
  pageNumber,
  pageSize,
  orderByField,
  orderByDirection,
  currentStatus,
  payeeName,
  variant = "default",
  bulkUploadId,
}: {
  pageNumber: number
  variant: "bulk" | "default"
  pageSize: number
  orderByField: string
  orderByDirection: string
  currentStatus: string
  payeeName: string
  bulkUploadId?: string
}) {
  if (variant === "default") {
    const params: any = {
      PageNumber: pageNumber,
      PageSize: pageSize,
      OrderByField: orderByField,
      OrderByDirection: orderByDirection,
      PayeeName: payeeName,
      CurrentStatus: currentStatus,
    }
    if (bulkUploadId) {
      params.BulkPaymentUploadId = bulkUploadId
    }
    const { data } = await client.get<
      IPaginationResponse<IOutboundPaymentBackend>
    >("/api/v1/outbound-payments/search", {
      params,
    })
    const payments = data.data.map(
      convertOutboundPaymentBackendToOutboundPayment,
    )
    return {
      ...data,
      data: payments,
    }
  } else {
    const params: any = {
      PageNumber: pageNumber,
      PageSize: pageSize,
      CurrentStatus: currentStatus,
    }

    const { data } = await client.get<IPaginationResponse<IBulkPaymentBackend>>(
      "/api/v1/bulk-payment-uploads",
      {
        params,
      },
    )
    const payments = data.data.map(convertBulkPaymentBackendToBulkPayment)
    return {
      ...data,
      data: payments,
    }
  }
}

export async function fetchPaymentById(paymentId: string) {
  try {
    const { data } = await client.get<IPaymentDetailsBackend>(
      `/api/v1/outbound-payments/${paymentId}`,
    )
    return data
  } catch (error) {
    throw new Error("Failed to fetch the payment. Please try again later.")
  }
}

export async function postPayment(paymentData: IPaymentForm, isDraft = false) {
  const amount = Number(paymentData.details.sendAmount)

  let purpose = paymentData.additionalDetails.purpose

  if (purpose?.toLowerCase() === "other") {
    purpose = paymentData.additionalDetails.purposeOtherText
  }

  // Explicitly generate a new UUID for each request
  const newIdempotencyKey = v1()

  // Check if currencies are different - if so, use the new embedded payment endpoint
  const isDifferentCurrencies =
    paymentData.details.currencySend !== paymentData.details.currencyReceive

  if (isDifferentCurrencies) {
    return await postEmbeddedPayment(paymentData, isDraft, newIdempotencyKey)
  }

  const dto: IAddPaymentDTO = {
    amount: amount,
    currency: paymentData.details?.currencySend,
    clientAccountId: paymentData.details.fromDetails?.clientAccountId ?? "",
    payeeId: paymentData.details.toDetails?.id ?? "",
    reference: paymentData.additionalDetails.paymentReference ?? "",
    purpose: purpose ?? null,
    valueDate: paymentData.details.paymentDate?.split("T")[0] ?? "",
    idempotencyKey: newIdempotencyKey,
    copyNumber: 1,
    saveAsDraft: isDraft,
    fxQuoteRequestId: paymentData.details.fxQuoteRequestId ?? null,
    fxBrokeredQuoteId: paymentData.details.fxBrokeredQuoteId ?? null,
  }

  try {
    const { data } = await client.post("/api/v1/outbound-payments", dto)
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "An error occurred while submitting the payment.",
    )
  }
}

/**
 * Posts a payment using the new embedded payments endpoint for FX transactions
 * @param paymentData The payment form data
 * @param isDraft Whether to save as draft
 * @param idempotencyKey The idempotency key for the request
 * @returns A promise with the response data
 */
export async function postEmbeddedPayment(
  paymentData: IPaymentForm,
  isDraft = false,
  idempotencyKey?: string,
): Promise<IEmbeddedPaymentResponse> {
  let purpose = paymentData.additionalDetails.purpose

  if (purpose?.toLowerCase() === "other") {
    purpose = paymentData.additionalDetails.purposeOtherText
  }

  // Generate idempotency key if not provided
  const newIdempotencyKey = idempotencyKey || v1()

  // Helper function to validate GUID format
  const isValidGuid = (guid: string): boolean => {
    const guidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    return guidRegex.test(guid)
  }

  // Only include FX quote IDs if they are valid GUIDs
  const fxQuoteRequestId = paymentData.details.fxQuoteRequestId
  const fxBrokeredQuoteId = paymentData.details.fxBrokeredQuoteId

  const embeddedPaymentRequest: IEmbeddedPaymentRequest = {
    type: PaymentType.Swift,
    payeeId: paymentData.details.toDetails?.id ?? "",
    clientAccountId: paymentData.details.fromDetails?.clientAccountId ?? "",
    reference: paymentData.additionalDetails.paymentReference ?? "",
    purpose: purpose ?? "",
    feeOption: FeeOption.Shared,
    idempotencyKey: newIdempotencyKey,
    saveAsDraft: isDraft,
  }

  // Only add FX quote IDs if they are valid GUIDs
  if (fxQuoteRequestId && isValidGuid(fxQuoteRequestId)) {
    embeddedPaymentRequest.fxQuoteRequestId = fxQuoteRequestId
  }

  if (fxBrokeredQuoteId && isValidGuid(fxBrokeredQuoteId)) {
    embeddedPaymentRequest.fxBrokeredQuoteId = fxBrokeredQuoteId
  }

  try {
    const { data } = await client.post<IEmbeddedPaymentResponse>(
      "/api/v1/outbound-payments/fx-embedded",
      embeddedPaymentRequest,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "An error occurred while submitting the embedded payment.",
    )
  }
}

export async function updatePayment(
  paymentId: string,
  payload: UpdatePaymentRequest,
) {
  const amount = Number(payload.amount)
  payload.amount = amount
  try {
    const { data } = await client.patch(
      `/api/v1/outbound-payments/${paymentId}`,
      payload,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "An error occurred while updating the payment.",
    )
  }
}

export async function deletePayment(paymentId: string) {
  try {
    const { data } = await client.delete(
      `/api/v1/outbound-payments/${paymentId}`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "An error occurred while deleting the payment.",
    )
  }
}

export async function submitPayment(paymentId: string) {
  try {
    const { data } = await client.post(
      `/api/v1/outbound-payments/${paymentId}/submit`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "An error occurred while submitting the payment.",
    )
  }
}

export async function cancelPaymentWithReason(
  paymentId: string,
  reason: IPaymentRejectionReason,
): Promise<void> {
  const response = await client.post(
    `/api/v1/outbound-payments/${paymentId}/cancel-with-reason`,
    {
      rejectionReason: reason.key,
    },
  )
  return response.data
}

export async function approvePayment(paymentId: string) {
  const { data } = await client.post(
    `/api/v1/outbound-payments/${paymentId}/approve`,
  )
  return data
}

export async function revertPaymentToDraft(paymentId: string) {
  try {
    const { data } = await client.post(
      `/api/v1/outbound-payments/${paymentId}/revert`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "An error occurred while reverting the payment to draft.",
    )
  }
}

export async function fetchPaymentRejectionReasons() {
  const { data } = await client.get<IPaymentRejectionReason[]>(
    "/api/v1/outbound-payments/rejection-reasons",
  )
  return data
}

export async function fetchPaymentPurpose(countryIdentifier?: string) {
  if (!countryIdentifier) {
    return [] as IPaymentPurpose[]
  }
  const { data } = await client.get<IPaymentPurpose[]>(
    `/api/v1/countries/${countryIdentifier}/payment-purpose-codes`,
  )
  return data
}

export async function uploadBulkPayment(
  payload: IUploadBulkPaymentDTO,
  options?: IUploadOptions,
): Promise<boolean> {
  const formData = new FormData()
  formData.append("file", payload.file)
  try {
    return await client.post("/api/v1/bulk-payment-uploads", formData, {
      signal: options?.signal,
      onUploadProgress(progressEvent) {
        if (options?.onUploadProgress) {
          options.onUploadProgress(progressEvent)
        }
      },
    })
  } catch (err: any) {
    let errorMessage: BulkPaymentErrorType
    if (err.response?.data?.title == "Unprocessable Entity") {
      errorMessage = "data-header-columns"
    } else {
      errorMessage = "file-format"
    }
    throw new Error(errorMessage)
  }
}

export async function downloadBulkPaymentTemplate(): Promise<void> {
  try {
    const response = await client.get("/api/v1/bulk-payment-uploads/template", {
      responseType: "blob",
    })

    const blob = response.data
    const contentType = response.headers["content-type"]
    const contentDisposition = response.headers["content-disposition"]

    let filename = "payment-template"
    let originalExtension: string | undefined

    if (contentDisposition) {
      const matches = contentDisposition.match(/filename\*=UTF-8''([^;]+)/i)
      if (matches && matches[1]) {
        const originalFilename = decodeURIComponent(matches[1])
        const extMatch = originalFilename.match(/\.[^.]+$/)
        if (extMatch) {
          originalExtension = extMatch[0]
        }
        filename = originalFilename
      } else {
        const filenameMatch = contentDisposition.match(
          /filename="?([^";\n]*)"?/i,
        )
        if (filenameMatch && filenameMatch[1]) {
          const originalFilename = filenameMatch[1]
          const extMatch = originalFilename.match(/\.[^.]+$/)
          if (extMatch) {
            originalExtension = extMatch[0]
          }
          filename = originalFilename
        }
      }
    }

    if (!originalExtension) {
      if (contentType?.includes("spreadsheetml")) {
        filename += ".xlsx"
      } else if (contentType?.includes("ms-excel")) {
        filename += ".xls"
      } else {
        filename += ".csv"
      }
    }

    const url = window.URL.createObjectURL(
      new Blob([blob], { type: contentType || "application/octet-stream" }),
    )
    const link = document.createElement("a")
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error("Error downloading template:", error)
    toast.error("Error downloading template")
  }
}

export async function fetchPendingPayments(bulkUploadId: string) {
  const { data } = await client.get<IBulkPaymentPendingPayments>(
    `/api/v1/bulk-payment-uploads/${bulkUploadId}/payments`,
  )
  return data
}
// /api/v1/outbound-payments/{id}
export async function bulkPaymentProcess(bulkUploadId: string): Promise<void> {
  const { data } = await client.post(
    `/api/v1/bulk-payment-uploads/${bulkUploadId}/process`,
  )
  return data
}

export async function bulkPaymentSubmit(bulkUploadId: string): Promise<void> {
  const { data } = await client.post(
    `/api/v1/bulk-payment-uploads/${bulkUploadId}/submit`,
  )
  return data
}
export async function fetchBulkPaymentSummary(bulkUploadId: string) {
  const { data } = await client.get<IBulkPaymentSummary>(
    `/api/v1/bulk-payment-uploads/${bulkUploadId}/summary`,
  )
  return data
}

export async function bulkPaymentCancel(bulkUploadId: string): Promise<void> {
  const { data } = await client.post(
    `/api/v1/bulk-payment-uploads/${bulkUploadId}/cancel`,
  )
  return data
}

export async function bulkPaymentDeletePayments(
  bulkUploadId: string,
  rowNumbers: string[],
) {
  const { data } = await client.delete(
    `/api/v1/bulk-payment-uploads/${bulkUploadId}/payments`,
    {
      params: {
        BulkUploadPaymentIds: rowNumbers,
      },
    },
  )
  return data
}

export async function bulkPaymentPatchRow(
  bulkUploadId: string,
  payload: IPendingPayment,
) {
  const sanitizedPayload: any = {
    ...payload,
  }
  delete sanitizedPayload.id
  delete sanitizedPayload.createdAt
  delete sanitizedPayload.rowNumber
  delete sanitizedPayload.validationErrors

  const { data } = await client.put(
    `/api/v1/bulk-payment-uploads/${bulkUploadId}/payments/${payload.id}`,
    sanitizedPayload,
  )
  return data
}

export async function validateSignatoryAmount(
  amount: number,
  currency: string,
) {
  if (isNaN(amount)) {
    throw new Error("Amount is not a number")
  }

  try {
    const { data } = await client.get<ISignatoryValidation>(
      `/api/v1/signatory-matrix/validate-signatory`,
      {
        params: {
          amount: amount,
          currency: currency,
        },
      },
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to validate signatory amount. Please try again.",
    )
  }
}

export async function fetchPaymentApprovalDetails(paymentId: string) {
  const { data } = await client.get<IPaymentApprovalDetails>(
    `/api/v1/outbound-payments/${paymentId}/approval-details`,
  )
  return data
}

export async function fetchBulkPaymentById(bulkPaymentId: string) {
  const { data } = await client.get<IBulkPaymentDetails>(
    `/api/v1/bulk-payment-uploads/${bulkPaymentId}`,
  )
  return data
}

export async function approveBulkPayment(bulkPaymentId: string) {
  const { data } = await client.post(
    `/api/v1/bulk-payment-uploads/${bulkPaymentId}/approve`,
  )
  return data
}

export async function validatePaymentSignatory(
  paymentId: string,
): Promise<ISignatoryValidation> {
  if (!paymentId) {
    throw new Error("Payment ID is required.")
  }

  try {
    const { data } = await client.get<ISignatoryValidation>(
      `/api/v1/outbound-payments/${paymentId}/validate-signatory`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to validate payment signatory. Please try again.",
    )
  }
}

export async function cancelPayment(
  paymentId: string,
): Promise<IPaymentResponse> {
  if (!paymentId) {
    throw new Error("Payment ID is required.")
  }

  try {
    const { data } = await client.post<IPaymentResponse>(
      `/api/v1/outbound-payments/${paymentId}/cancel`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to cancel payment. Please try again.",
    )
  }
}

export async function fetchFxRateById(
  currencyPair: string,
): Promise<IFxRateResponse[]> {
  if (!currencyPair) {
    throw new Error("Currency pair is required.")
  }

  try {
    const { data } = await client.get<IFxRateResponse[]>(
      `/api/v1/fx/rate/history/${currencyPair}`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to fetch FX rate. Please try again.",
    )
  }
}

export async function fetchFxValueDates(
  lhsCurrency: string,
  rhsCurrency: string,
): Promise<{ valueDateOptions: string[] }> {
  if (!lhsCurrency) {
    throw new Error("Left-hand side currency is required.")
  }

  if (!rhsCurrency) {
    throw new Error("Right-hand side currency is required.")
  }
  try {
    const { data } = await client.post<{ valueDateOptions: string[] }>(
      `/api/v1/fx/valuedates`,
      {
        lhsCurrency,
        rhsCurrency,
      },
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to fetch value dates. Please try again.",
    )
  }
}

/**
 * Fetches a list of FX quotes from configured brokers
 * @param requestData The quote request data
 * @param entityId The entity ID for the request
 * @returns Promise with the quote response
 */
export async function fetchFxQuote(
  requestData: IFxQuoteRequest,
): Promise<IFxQuoteResponse> {
  try {
    const { data } = await client.post<IFxQuoteResponse>(
      "/api/v1/fx/quote",
      requestData,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to fetch FX quote. Please try again.",
    )
  }
}

/**
 * Approves a payment with an embedded FX quote
 * @param paymentId The ID of the payment to approve
 * @param request The FX approval details containing broker ID and quote ID
 * @returns A promise with the response data
 */
export async function approvePaymentWithFxEmbedded(
  paymentId: string,
  request: IApproveFxEmbeddedRequest,
): Promise<IPaymentResponse> {
  if (!paymentId) {
    throw new Error("Payment ID is required.")
  }

  try {
    const { data } = await client.post<IPaymentResponse>(
      `/api/v1/outbound-payments/${paymentId}/approve-fx-embedded-final`,
      request,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to approve payment with FX. Please try again.",
    )
  }
}

/**
 * Submits a payment with a verification code (single payment)
 * @param paymentId The ID of the payment to submit
 * @returns The payment response with aggregateId and correlationId
 */
export async function submitPaymentWithVerification(
  paymentId: string,
): Promise<IPaymentResponse> {
  if (!paymentId) {
    throw new Error("Payment ID is required.")
  }
  try {
    const { data } = await client.post<IPaymentResponse>(
      `/api/v1/outbound-payments/${paymentId}/submit`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to submit payment. Please try again.",
    )
  }
}

/**
 * Starts the verification process for a bulk payment
 * @param bulkPaymentId The ID of the bulk payment
 * @param type The verification method (Sms, Call, App)
 * @param operationType The operation being verified (Submit, Approve)
 * @returns A promise with the verification response
 */
export async function startBulkPaymentVerification(
  bulkPaymentId: string,
  type: VerificationType,
  operationType: VerificationOperationType,
): Promise<IVerificationResponse> {
  if (!bulkPaymentId) {
    throw new Error("Bulk payment ID is required.")
  }

  try {
    const { data } = await client.post<IVerificationResponse>(
      `/api/v1/bulk-payment-uploads/${bulkPaymentId}/start-verification`,
      {
        type,
        operationType,
      },
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to start bulk payment verification. Please try again.",
    )
  }
}

/**
 * Verifies a code for a bulk payment operation
 * @param bulkPaymentId The ID of the bulk payment
 * @param verificationCode The code received by the user
 * @param operationType The operation being verified (Submit, Approve)
 * @returns A promise with the verification response
 */
export async function verifyBulkPaymentCode(
  bulkPaymentId: string,
  verificationCode: string,
  operationType: VerificationOperationType,
): Promise<IVerificationResponse> {
  if (!bulkPaymentId) {
    throw new Error("Bulk payment ID is required.")
  }

  if (!verificationCode) {
    throw new Error("Verification code is required.")
  }

  try {
    const { data } = await client.post<IVerificationResponse>(
      `/api/v1/bulk-payment-uploads/${bulkPaymentId}/verify`,
      {
        verificationCode,
        operationType,
      },
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to verify bulk payment code. Please try again.",
    )
  }
}
