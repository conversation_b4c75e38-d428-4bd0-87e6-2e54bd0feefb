import { AxiosProgressEvent, GenericAbortSignal } from "axios"

import { IPayeeData } from "../payees/payees.interface"
import { Account } from "../account/account.interface"

export type BulkPaymentStatusType =
  | "Uploaded"
  | "Validating"
  | "ReadyForReview"
  | "ReadyForSubmission"
  | "PendingApproval"
  | "PendingArgentexApproval"
  | "Approved"
  | "Processed"
  | "Cancelled"

export const BULK_PAYMENT_STATUS_MAP: Record<BulkPaymentStatusType, string> = {
  Uploaded: "Uploaded",
  Validating: "Validating",
  ReadyForReview: "Ready For Review",
  ReadyForSubmission: "Ready For Submission",
  PendingApproval: "Pending Approval",
  PendingArgentexApproval: "Pending",
  Approved: "Approved",
  Processed: "Processed",
  Cancelled: "Cancelled",
}

export interface IBulkPaymentStatus {
  status: string
  count: number
}

export const BULK_PAYMENT_PAYMENT_STATUS = [
  "Sent",
  "Processing",
  "Pending",
  "Rejected",
] as const

export type TBulkPaymentPaymentStatus =
  (typeof BULK_PAYMENT_PAYMENT_STATUS)[number]

export interface IBulkPaymentPaymentStatus {
  status: TBulkPaymentPaymentStatus
  count: number
}

export type IBulkPaymentBackend = Omit<IBulkPayment, "paymentType">

export interface IBulkPayment {
  id: string
  paymentType: "BulkPayment"
  fileName: string
  totalRows: number
  completedAt: string | null
  failedAt: string | null
  failedReason: string | null
  bulkPaymentStatuses: IBulkPaymentStatus[]
  paymentStatuses: IBulkPaymentPaymentStatus[]
  currencyAccounts: {
    accountName: string
    virtualIban: string
    accountNumber: string | null
  }[]
  currentStatus: BulkPaymentStatusType
  createdAt: string
}

export interface IOutboundPaymentBackend {
  id: string
  code: string
  type: string
  amount: number
  currency: string
  payeeCurrency: string
  valueDate: string
  payee: Payee
  createdAt: string
  currentStatus: string
  client: {
    id: string
    name: string
    registrationNumber: string
    address: string | null
  }
  clientAccountId: string
  currencyAccount: {
    accountName: string
    virtualIban: string
    currency: string
    accountNumber: string | null
  }
  bulkPaymentUploadId: string | null
}

export interface IOutboundPayment {
  id: string
  bulkPaymentUploadId?: string
  code: string
  paymentType: "OutboundPayment"
  feeCurrency?: string
  fileName: string
  type: string
  valueDate: string
  payee: Payee
  currencyAccount: CurrencyAccount
  clientAccountId: string
  currentStatus: string
  createdAt: string
  fee?: number
  amount: number
  currency: string
  purpose?: string
  reference?: string
  clientAccount?: {
    accountName: string
    accountNumber: string
    id: string
    virtualIban: string
  }
  payeeCurrency?: string
}

export type Payment = IBulkPayment | IOutboundPayment

export interface IBulkPaymentResponse {
  data: IBulkPayment[]
  totalCount: number
  pageNumber: number
  pageSize: number
}

export interface CurrencyAccount {
  virtualIban: string
  currency: string
  clientAccount?: {
    accountName: string
    accountNumber: string
    id: string
    virtualIban: string
  }
}

export interface Payee {
  id: string
  accountName: string
  accountNumber: string | null
  iban: string
  type: string
  bank: Bank
}

export interface Bank {
  name: string
  nationalId: string | null
  nationalIdType: string | null
  swiftBic: string
  country: Country
}

export interface Country {
  name: string
  formalName: string
  codeIso2: string
  codeIso3: string
  codeIso3Numeric: string
  phoneCode: string
  ibanLength: number
  ibanRegex: string | null
  ibanSupported: boolean
  accountNumberType: string
  nationalIdType: string
  isSepaCountry: boolean
  paymentPurposeCodeRequired: boolean
}

export interface IPaymentDetailsBackend {
  id: string
  code: string
  type: string
  amount: number
  currency: string
  reference: string
  purpose: string
  feeOption: string | null
  currentStatus: string
  relatedFxTradeId: string | null
  valueDate: string
  isCancellable: boolean
  toAccount: IPayeeData | null
  fromAccount: {
    id: string
    currency: string
    clientAccount: {
      id: string
      accountName: string
      virtualIban: string
      accountNumber: string
    }
  }
  clientAccountId: string
  createdAt: string
  createdBy: {
    id: string
    email: string
    displayName: string
  }
  fxQuoteRequestId: string | null
  fxBrokeredQuoteId: string | null
  copyNumber: number
  fxTrade?: IFxTrade | null
  remitterCurrency: string
  remitterSentAmount: number
}

export interface IFxTrade {
  id: string
  code: string
  executedFxRate: number
}

export interface IPaymentDetails {
  fromAccount?: string
  currencySend: string
  sendAmount: number | string
  currencyReceive: string
  receiveAmount: number | string
  payee?: string
  toDetails?: IPayeeData
  fromDetails?: Account
  fxQuoteRequestId?: string
  fxBrokeredQuoteId?: string
  verificationCode?: string
  lastEditedField?: "send" | "receive" | null
  paymentDate?: string
  confirmPayeeDetails?: boolean
  payeeCurrency?: string
}

export interface IAdditionalDetails {
  paymentReference?: string
  purpose?: string
  purposeOtherText?: string
}

export interface IPaymentForm {
  details: IPaymentDetails
  additionalDetails: IAdditionalDetails
  clientAccountId: string
}

export interface UpdatePaymentRequest {
  amount: number // uint64
  currency: string
  clientAccountId: string // GUID
  payeeId: string // GUID
  reference?: string | null
  purpose?: string | null
  feeOption?: string | null
  valueDate: string // RFC 3339 full-date (e.g., "2017-07-21")
  idempotencyKey: string
  copyNumber: number // int32
  type?: string | null
  saveAsDraft?: boolean | null
}

export interface IAddPaymentDTO {
  type?: string
  amount: number // uint64
  currency: string
  clientAccountId: string // guid
  payeeId: string // guid
  reference: string | null
  purpose: string | null
  feeOption?: string | null
  valueDate: string // date format RFC 3339
  idempotencyKey: string
  copyNumber: number // int32
  saveAsDraft?: boolean
  fxQuoteRequestId: string | null
  fxBrokeredQuoteId: string | null
}

export interface IPaymentRejectionReason {
  key: string
  value: string
}

export interface IPaymentPurpose {
  id: string
  code: string
  description: string
}

export interface IBulkPaymentFile {
  file: File
  fileName: string
  fileType: string
  fileSize: number
}

export type TPendingPaymentKeys = keyof IPendingPayment

export interface IPendingPaymentValidationErrors {
  type: TPendingPaymentKeys
  message: string
}

export interface IPendingPayment {
  id: string
  rowNumber: number
  status: string
  paymentType: string
  clientId: string
  originatingAccountIban: string
  payeeAccountName: string
  payeeAccountNumber: string
  paymentAmount: string
  paymentCurrency: string
  paymentReference: string
  paymentPurpose: string
  paymentDate: string
  payeeIBAN: string
  payeeSWIFTBIC: string
  payeeCountry: string
  payeeCity: string
  payeePostalCode: string
  payeeStreetName: string
  payeeBuildingNumber: string
  payeeType: string
  payeeLocalBankCode: string
  payeeRegionState: string
  payeeNationality: string
  validationErrors: IPendingPaymentValidationErrors[]
  createdAt: string
}

export interface IBulkPaymentPendingPayments {
  pendingPayments: IPendingPayment[]
}

export interface ICurrencyAccountSummary {
  currency: string
  paymentsCount: number
  paymentsAmount: number
  fees: number
  feesCurrency: string
  availableBalance: number
}

export interface IAccountSummary {
  virtualIban: string
  displayName: string
  currencyAccounts: ICurrencyAccountSummary[]
}

export interface IBulkPaymentSummary {
  id: string
  fileName: string
  createdAt: string
  createdBy: ISubmittedBy
  totalPayments: number
  totalFees: number
  totalAmount: number
  totalCurrency: string
  isScaRequired: boolean
  signatoryAmountBand: ISignatoryAmountBand
  approvers: IApprover[]
  accountSummaries: IAccountSummary[]
}

export interface ISignatoryValidation {
  isValid: boolean
  isSelfApproved: boolean

  errorMessage: string
  signatoryMatrixEnabled: boolean
  canApproveOwnSubmission: boolean
  signatoryAmountBand: {
    id: string
    maximumAmount: number
    signatoryRules: Array<{
      id: string
      approverLevel: string
      requiredCount: number
    }>
  }
}

export interface ISubmittedBy {
  id: string
  email: string
  displayName: string
}

export interface ISignatoryRule {
  id: string
  approverLevel: string
  requiredCount: number
}

export interface ISignatoryAmountBand {
  id: string
  maximumAmount: number
  signatoryRules: ISignatoryRule[]
}

export interface IApprover {
  approverLevel: string
  approverDisplayName: string
  approverEmail: string
  approverId: string
  approvedAt: string
  approverRoles: string[]
}

export interface IPaymentApprovalDetails {
  id: string
  clientId: string
  submittedBy: ISubmittedBy
  status: string
  amount: number
  currency: string
  clientApprovalRequired: boolean
  signatoryAmountBand: ISignatoryAmountBand
  approvals: IApprover[]
  isFinalApprover: boolean
  canApprove: boolean
}

export interface IBulkPaymentDetails {
  id: string
  fileName: string
  currentStatus: BulkPaymentStatusType
  totalRows: number
  createdAt: string
  completedAt: string | null
  failedAt: string | null
  failedReason: string | null
  bulkPaymentStatuses: IBulkPaymentStatus[]
  outboundPaymentStatuses: {
    status: string
    count: number
  }[]
  signatoryAmountBand: ISignatoryAmountBand | null
  approvals: IApprover[]
  createdBy: ISubmittedBy
  totalAmount: number | null
  totalAmountCurrency: string
  clientAccountId?: string
  totalPayments?: number
}

export interface IUploadBulkPaymentDTO {
  file: File
}

export interface IUploadOptions {
  signal?: GenericAbortSignal
  onUploadProgress?: (progress: AxiosProgressEvent) => void
}

export interface IPaymentResponse {
  aggregateId: string
  correlationId: string
}

export interface IFxRateResponse {
  rate: number
  date: string
}

export interface IFxQuoteRequest {
  buyCurrency: string
  sellCurrency: string
  fixedSide: string
  amount: number
  valueDate: string
}

export interface IFxQuoteResponse {
  sell: {
    amount: number
    currency: string
  }
  buy: {
    amount: number
    currency: string
  }
  fixedSide: string
  valueDate: string
  sellRate: number
  buyRate: number
  brokerId: string
  brokerName: string
  fxQuoteRequestId: string
  fxBrokeredQuoteId: string
}

export interface IApproveFxEmbeddedRequest {
  fxBrokeredQuoteId: string
  fxQuoteRequestId: string
}

export const CustomerPaymentStatus = {
  Draft: "Draft",
  PendingSignatoryApproval: "Pending Signatory Approval",
  InsufficientBalance: "Insufficient Balance",
  Cancelled: "Cancelled",
  Held: "Held",
  Sent: "Sent",
  Processing: "Processing",
  Rejected: "Rejected",
  Pending: "Pending",
} as const

export type CustomerPaymentStatus =
  (typeof CustomerPaymentStatus)[keyof typeof CustomerPaymentStatus]

export type VerificationType = "Sms" | "Call" | "App"
export type VerificationOperationType = "Submit" | "Approve"

export interface IVerificationResponse {
  aggregateId: string
  correlationId: string
}

// New interfaces for embedded payments
export enum PaymentType {
  Swift = "Swift",
  Sepa = "Sepa",
  FasterPayments = "FasterPayments",
  Chaps = "Chaps",
  Bacs = "Bacs",
}

export enum FeeOption {
  Shared = "Shared",
  Originator = "Originator",
  Beneficiary = "Beneficiary",
}

export interface IEmbeddedPaymentRequest {
  type: PaymentType
  payeeId: string
  clientAccountId: string
  reference: string
  purpose: string
  feeOption: FeeOption
  idempotencyKey: string
  saveAsDraft: boolean
  fxQuoteRequestId?: string
  fxBrokeredQuoteId?: string
}

export interface IEmbeddedPaymentResponse {
  aggregateId: string
  correlationId: string
}
