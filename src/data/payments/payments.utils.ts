import {
  CurrencyAccount,
  IBulkPayment,
  IBulkPaymentBackend,
  IOutboundPayment,
  IOutboundPaymentBackend,
  Payee,
} from "./payments.interface"

export function convertOutboundPaymentBackendToOutboundPayment(
  payment: IOutboundPaymentBackend,
): IOutboundPayment {
  return {
    ...payment,
    paymentType: "OutboundPayment",
    fileName: "",
    bulkPaymentUploadId: payment.bulkPaymentUploadId || undefined,
    payee: payment.payee as Payee,
    currencyAccount: payment.currencyAccount as CurrencyAccount,
  }
}

export function convertBulkPaymentBackendToBulkPayment(
  payment: IBulkPaymentBackend,
): IBulkPayment {
  return {
    ...payment,
    paymentType: "BulkPayment",
  }
}
