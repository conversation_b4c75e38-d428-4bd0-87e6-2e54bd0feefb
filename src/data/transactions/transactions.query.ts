import { useQuery, useMutation } from "@tanstack/react-query"

import {
  ITransactionListQuery,
  IDownloadTransactionsParams,
  ITransaction,
} from "./transactions.interface"
import {
  fetchTransactionById,
  fetchTransactions,
  downloadTransactions,
  fetchAccountTransactions,
} from "./transactions.api"
import { queryKeys } from "@/lib/constants/query.constants"

export function useTransactionsListQuery(
  entityId: string,
  query: ITransactionListQuery,
  options?: { enabled: boolean },
) {
  return useQuery({
    queryKey: [queryKeys.transaction.list, entityId, query],
    queryFn: () => fetchTransactions(query),
    enabled: !!query.accountId,
  })
}

export function useAccountTransactionsQuery(
  entityId: string,
  query: ITransactionListQuery & {
    orderByField?: string
    orderByDirection?: string
  },
  options?: { enabled?: boolean },
) {
  return useQuery({
    queryKey: [queryKeys.transaction.list, entityId, query],
    queryFn: () => fetchAccountTransactions(query),
    enabled: !!query.accountId && (options?.enabled ?? true),
  })
}

export function useTransactionQuery(transaction?: ITransaction) {
  return useQuery({
    queryKey: [
      queryKeys.transaction.byId,
      transaction?.sourceId,
      transaction?.transactionType,
    ],
    queryFn: () =>
      transaction?.sourceType
        ? fetchTransactionById(transaction.sourceId, transaction.sourceType)
        : undefined,
    enabled: !!transaction?.sourceId,
  })
}

export function useDownloadTransactionsMutation() {
  return useMutation({
    mutationFn: (params: IDownloadTransactionsParams) =>
      downloadTransactions(params),
    onError: (error: Error) => {
      console.error("Failed to download transactions:", error)
    },
  })
}
