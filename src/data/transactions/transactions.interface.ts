import { IPaginationResponse } from "../global/global.interface"

export interface ITransactionListQuery {
  accountId: string
  currency: string
  fromDate: string
  toDate: string
  pageNumber?: number
  pageSize?: number
}

export interface ITransactionDetailsQuery {
  transactionId: string
}

export const TRANSACTION_TYPES = [
  "OutboundPaymentInstructed",
  "OutboundPaymentRejected",
  "InboundPaymentReceived",
  "InboundPaymentAllocated",
  "InboundPaymentAllocationReversed",
  "FxTradeBuyBrokered",
  "FxTradeSellBrokered",
  "FxTradeBuySettledBroker",
  "FxTradeSellSettledBroker",
  "FxTradeBuySettledClient",
  "FxTradeSellSettledClient",
  "FxTradeSellRevertToBrokered",
  "FxTradeBuyRevertToBrokered",
  "FeeTransaction",
] as const

export type TTransactionType = (typeof TRANSACTION_TYPES)[number]
export type TTransactionMap = "OUT" | "IN" | "FX" | "FEES"

export const TRANSACTION_MAP: Record<TTransactionType, TTransactionMap> = {
  OutboundPaymentInstructed: "OUT",
  OutboundPaymentRejected: "OUT",
  InboundPaymentReceived: "IN",
  InboundPaymentAllocated: "IN",
  InboundPaymentAllocationReversed: "IN",
  FxTradeBuyBrokered: "FX",
  FxTradeSellBrokered: "FX",
  FxTradeBuySettledBroker: "FX",
  FxTradeSellSettledBroker: "FX",
  FxTradeBuySettledClient: "FX",
  FxTradeSellSettledClient: "FX",
  FxTradeSellRevertToBrokered: "FX",
  FxTradeBuyRevertToBrokered: "FX",
  FeeTransaction: "FEES",
} as const

interface ITransactionAccount {
  id: string
  name: string
  accountNumber: string
  currency: string
  balance?: number
}
/*
  {
      "id": "2484caca-a648-4dcb-a247-304dc416b67f",
      "date": "2025-03-31T11:30:14.703795+00:00",
      "description": "Alistair Pitts",
      "reference": "Swift Test",
      "transactionType": "OutboundPaymentInstructed",
      "amount": 855,
      "currency": "EUR",
      "direction": "Debit",
      "runningBalance": 916174,
      "sourceType": "Payment",
      "sourceId": "f32a838a-c612-4395-91e2-abe830964a38"
  },
*/
export interface ITransaction {
  id: string
  date: string
  amount?: number
  currency?: string
  direction?: string // new
  description: string
  reference?: string
  runningBalance: number
  sourceId: string
  sourceType: string
  transactionType: TTransactionType
}

export type ITransactionListBackend = IPaginationResponse<ITransaction>

export interface ITransactionListResponse {
  data: ITransaction[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFirstPage: boolean
  isLastPage: boolean
  pageStartIndex: number
  pageEndIndex: number
}

export interface IFee {
  type: string
  amount: number
  currency: string
}

export interface ITransactionCreatedBy {
  name: string
  email: string
}

export interface ITransactionApprovedBy {
  email: string
  grade: string
}

export interface IDebitDetailsResponse {
  type: "DEBIT"
  id: string
  date: string
  fromAccount: ITransactionAccount
  toAccount: ITransactionAccount
  amount: number
  fxRate?: number
  fee?: IFee
  reference?: string
  purpose?: string
  createdBy?: ITransactionCreatedBy
  approvedBy?: ITransactionApprovedBy[]
}

export interface ICreditDetailsResponse {
  type: "CREDIT"
  id: string
  date: string
  fromAccount: ITransactionAccount
  toAccount: ITransactionAccount
  amount: number
  reference?: string
}

export interface IFXDetailsResponse {
  type: "FX_TRADE"
  id: string
  date: string
  fromAccount: ITransactionAccount
  toAccount: ITransactionAccount
  amount: number
  fxRate?: number
  fee?: IFee
  purpose?: string
  createdBy?: ITransactionCreatedBy
  approvedBy?: ITransactionApprovedBy[]
}

export interface IFeeDetailsResponse {
  type: "FEE"
  id: string
  date: string
  fromAccount: ITransactionAccount
  toAccount: ITransactionAccount
  amount: number
  fee: IFee
  fxRate?: number
  invoiceId?: string
}

export interface ISwiftDetailsResponse {
  type: string
  id: string
  code: string
  date: string
  fromAccount: ITransactionAccount
  toAccount: ITransactionAccount
  amount: number
  currency: string
  fxRate?: number | null
  fee?: IFee | null
  reference?: string
  purpose?: string
  createdBy?: ITransactionCreatedBy
  approvedBy?: ITransactionApprovedBy[] | null
  invoiceId?: string | null
}

export type ITransactionDetailsResponse =
  | IInboundPaymentResponse
  | IOutboundPaymentResponse

export interface IDownloadTransactionsParams {
  accountId: string
  fileType: "csv" | "pdf" | "excel"
  currency: string
  fromDate: string
  toDate: string
  pageNumber?: number
  pageSize?: number
  orderByField?: string
  orderByDirection?: string
}

export interface IInboundPaymentClient {
  id: string
  name: string
  cId: number
  registrationNumber: string
  status: string
  subStatus: string
}

export interface IInboundPaymentRemitter {
  id: string
  accountNumber: string
  holderName: string
}

export interface IInboundPaymentClientAccount {
  id: string
  accountName: string
  virtualIban: string
  accountNumber: string
}

export interface IInboundPaymentComplianceCase {
  paymentId: string
  externalCaseId: string
  assignedTo: string | null
  transactionStatus: string
  investigationStatus: string
  processingStatus: string
  decisionType: string
  decidedAt: string
  createdAt: string
  createdBy: string
  code: string
}

export interface IInboundPaymentCurrencyAccount {
  id: string
  currency: string
  clientAccount: IInboundPaymentClientAccount
  balance: number
}

export interface IInboundPaymentCreatedBy {
  id: string
  email: string
  displayName: string
}

/*
{
    "id": "18f1bf99-3c66-473d-8532-0642ce3d60f4",
    "code": "PC-9B6X-156J",
    "amount": 70500,
    "currency": "EUR",
    "valueDate": "2025-03-28",
    "reference": "gift",
    "processorPaymentId": "84e6b6ce-62f0-4627-9735-6fe639924947",
    "currentStatus": "Allocated",
    "rejectionReason": null,
    "isBlocked": false,
    "client": {
        "id": "0e0b33c4-9758-460e-b0f0-08dd778ccba4",
        "name": "FIRST TESTING LIMITED",
        "cId": 100001,
        "registrationNumber": "**********",
        "status": "Client",
        "subStatus": "Active"
    },
    "remitter": {
        "id": "45ed9074-1f73-4d2a-bf62-08dd8d54cb5a",
        "accountNumber": "********",
        "holderName": "PartnerCo"
    },
    "complianceCase": {
        "paymentId": "18f1bf99-3c66-473d-8532-0642ce3d60f4",
        "externalCaseId": "1139f55f-821c-4f64-894b-0022b9fff8a7",
        "assignedTo": null,
        "transactionStatus": "proceeded",
        "investigationStatus": "new",
        "processingStatus": "new",
        "decisionType": "complete",
        "decidedAt": "2025-05-29T08:48:23.9480067+00:00",
        "createdAt": "2025-05-29T08:48:22.119083+00:00",
        "createdBy": "eaf6035a-22c6-40e9-a215-770a5342761d",
        "code": "CCS-NPDP-PJ2W"
    },
    "currencyAccount": {
        "id": "8033dac9-0000-41da-bd51-ddb74051ebdc",
        "currency": "EUR",
        "clientAccount": {
            "id": "5801670c-f217-41e9-9e32-c7d793f28a61",
            "accountName": "Inbound Tests",
            "virtualIban": "GBTEST0001TEST",
            "accountNumber": "INTEST01"
        },
        "balance": 86600
    },
    "createdAt": "2025-05-07T10:49:07.4954439+00:00",
    "createdBy": {
        "id": "********-1111-1111-1111-************",
        "email": "<EMAIL>",
        "displayName": "System User"
    }
}
*/

export interface IInboundPaymentResponse {
  id: string
  code: string
  amount: number
  currency: string
  valueDate: string
  reference: string
  processorPaymentId: string
  currentStatus: string
  rejectionReason: string | null
  isBlocked: boolean
  client: IInboundPaymentClient
  remitter: IInboundPaymentRemitter
  complianceCase: IInboundPaymentComplianceCase
  currencyAccount: IInboundPaymentCurrencyAccount
  createdAt: string
  createdBy: IInboundPaymentCreatedBy
}

export interface IOutboundPaymentToAccount {
  id: string
  accountName: string
  accountNumber: string
  displayName: string
  iban: string
  type: string
  currentStatus: string
  createdAt: string
  createdBy: {
    id: string
    email: string
    displayName: string
  }
  bank: {
    nationalId: string
    name: string
    country: string
  }
  country: string
}

export interface IOutboundPaymentFromAccount {
  id: string
  currency: string
  clientAccount: {
    id: string
    accountName: string
    virtualIban: string
    accountNumber: string
  }
}

export interface IOutboundPaymentResponse {
  id: string
  code: string
  type: string
  amount: number
  currency: string
  payeeCurrency: string | null
  reference: string
  purpose: string
  feeOption: string | null
  currentStatus: string
  relatedFxTradeId: string | null
  valueDate: string
  isCancellable: boolean
  toAccount: IOutboundPaymentToAccount
  fromAccount: IOutboundPaymentFromAccount
  createdAt: string
  createdBy: IInboundPaymentCreatedBy
  fxQuoteRequestId: string | null
  fxBrokeredQuoteId: string | null
  copyNumber: number
}
