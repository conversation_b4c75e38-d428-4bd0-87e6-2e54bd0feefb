import { bankingClient as client } from "@/client/banking"

import {
  IDownloadTransactionsParams,
  ITransactionDetailsResponse,
  ITransactionListQuery,
  ITransactionListResponse,
  IInboundPaymentResponse,
  IOutboundPaymentResponse,
} from "./transactions.interface"

// export const downloadTransactions = async ({
//   accountId,
//   fileType,
//   currency,
//   fromDate,
//   toDate,
// }: IDownloadTransactionsParams) => {
//   try {
//     const { data } = await client.get<Blob>("/api/v1/transactions/download", {
//       params: {
//         accountId,
//         fileType,
//         currency,
//         fromDate,
//         toDate,
//       },
//       responseType: "blob",
//     });

//     return data;
//   } catch (error: any) {
//     throw new Error(
//       error.response?.data?.message || "Failed to download transactions",
//     );
//   }
// };

export const downloadTransactions = async ({
  accountId,
  fileType,
  currency,
  fromDate,
  toDate,
  pageNumber = 1,
  pageSize = 10,
  orderByField = "createdAt",
  orderByDirection = "desc",
}: IDownloadTransactionsParams): Promise<Blob> => {
  if (!accountId) {
    throw new Error("Account ID is required.")
  }

  try {
    const { data } = await client.get<Blob>(
      `/api/v1/currency-accounts/${accountId}/transactions`,
      {
        params: {
          fileType,
          fromDate,
          toDate,
          currency,
          pageNumber,
          pageSize,
          orderByField,
          orderByDirection,
        },
        responseType: "blob",
      },
    )

    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to download account transactions. Please try again.",
    )
  }
}

export const fetchTransactions = async ({
  accountId,
  fromDate,
  toDate,
  pageNumber = 1,
  pageSize = 10,
}: ITransactionListQuery) => {
  try {
    const { data } = await client.get<ITransactionListResponse>(
      `/api/v1/currency-accounts/${accountId}`,
      {
        params: {
          fromDate,
          toDate,
          pageNumber,
          pageSize,
        },
      },
    )

    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch transactions",
    )
  }
}

export const fetchAccountTransactions = async ({
  accountId,
  fromDate,
  toDate,
  pageNumber = 1,
  pageSize = 10,
  orderByField = "createdAt",
  orderByDirection = "desc",
  currency,
}: ITransactionListQuery & {
  orderByField?: string
  orderByDirection?: string
}) => {
  try {
    const { data } = await client.get<ITransactionListResponse>(
      `/api/v1/currency-accounts/${accountId}/transactions`,
      {
        params: {
          fromDate,
          toDate,
          pageNumber,
          pageSize,
          orderByField,
          orderByDirection,
          currency,
        },
      },
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch account transactions",
    )
  }
}

/**
 * Fetches a transaction by its ID based on the source type
 * @param id The ID of the transaction to fetch
 * @param sourceType The type of the transaction source ("InboundPayment" or "OutboundPayment")
 * @returns The transaction details mapped to ITransactionDetailsResponse
 */
export const fetchTransactionById = async (
  id: string,
  sourceType?: string | null,
): Promise<ITransactionDetailsResponse | undefined> => {
  if (!id) {
    throw new Error("Transaction ID is required.")
  }

  try {
    // Route to the appropriate API endpoint based on sourceType
    if (sourceType === "InboundPayment") {
      const inboundPayment = await fetchInboundPaymentById(id)
      return inboundPayment
    }
    if (sourceType === "OutboundPayment") {
      // Default to OutboundPayment if sourceType is not specified or is "OutboundPayment"
      const outboundPayment = await fetchOutboundPaymentById(id)
      return outboundPayment
    }
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch transaction details",
    )
  }
}

/**
 * Fetches an inbound payment by its ID
 * @param paymentIdentifier The ID of the inbound payment to fetch
 * @returns The inbound payment details
 */
export const fetchInboundPaymentById = async (
  paymentIdentifier: string,
): Promise<IInboundPaymentResponse> => {
  if (!paymentIdentifier) {
    throw new Error("Payment identifier is required.")
  }

  try {
    const { data } = await client.get<IInboundPaymentResponse>(
      `/api/v1/inbound-payments/${paymentIdentifier}`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to fetch inbound payment details. Please try again.",
    )
  }
}

/**
 * Fetches an outbound payment by its ID
 * @param paymentIdentifier The ID of the outbound payment to fetch
 * @returns The outbound payment details
 */
export const fetchOutboundPaymentById = async (
  paymentIdentifier: string,
): Promise<IOutboundPaymentResponse> => {
  if (!paymentIdentifier) {
    throw new Error("Payment identifier is required.")
  }

  try {
    const { data } = await client.get<IOutboundPaymentResponse>(
      `/api/v1/outbound-payments/${paymentIdentifier}`,
    )
    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to fetch outbound payment details. Please try again.",
    )
  }
}
