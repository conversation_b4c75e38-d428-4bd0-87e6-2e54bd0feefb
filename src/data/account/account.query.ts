import { useQuery } from "@tanstack/react-query"

import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

import { getAccountsList } from "./accounts.api"
import { queryKeys } from "@/lib/constants/query.constants"
export function useAccountsListQuery() {
  const { entity } = useLoaderData()
  const entityId = entity?.id

  return useQuery({
    queryKey: [queryKeys.global.accounts, entityId],
    queryFn: () => getAccountsList(),
    staleTime: 0, // Always consider data stale to encourage refetching
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  })
}
