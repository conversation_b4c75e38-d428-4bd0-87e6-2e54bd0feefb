import { bankingClient as client } from "@/client/banking"

import { Account } from "./account.interface"

export async function getAccountsList() {
  try {
    const { data } = await client.get<Account[]>("/api/v1/currency-accounts")
    return data
  } catch (error) {
    console.error("Error while fetching accounts list:", error)
    throw new Error("Failed to fetch accounts list. Please try again later.")
  }
}
