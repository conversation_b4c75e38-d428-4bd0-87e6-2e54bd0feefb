import { Currency } from "@/lib/constants/currency.constants"

export interface Account {
  accountName: string
  clientAccountId: string
  clientId: string
  balances: AccountBalance[]
  virtualIban: string
  totalBalance: number
  totalCurrency: Currency
  category: string
  id: string
  name: string
  iban: string
  entityId: string
  bankName: string
  bankAddress: string
}

export interface AccountBalance {
  id: string
  currency: Currency
  balance: number
}
