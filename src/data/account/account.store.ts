import { persist, createJSONStorage } from "zustand/middleware"
import { create } from "zustand"

import { Currency } from "@/lib/constants/currency.constants"

interface AccountStore {
  displayCurrency: Currency
  setDisplayCurrency: (currency: Currency) => void
}

export const useAccountStore = create<AccountStore>()(
  persist(
    (set) => ({
      displayCurrency: "GBP",
      setDisplayCurrency: (currency) => set({ displayCurrency: currency }),
    }),
    {
      name: "account-storage",
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        displayCurrency: state.displayCurrency,
      }),
    },
  ),
)
