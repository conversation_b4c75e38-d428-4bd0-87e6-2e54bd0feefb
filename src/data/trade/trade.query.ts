import { toast } from "sonner"
import { useMutation, useQuery } from "@tanstack/react-query"

import {
  ITradeExecuteBodyDTO,
  ITradeRateRequestDTO,
  IValueDateRequestDTO,
  ITradeListingRequestQueryDTO,
  IRateForCurrencyPairRequestDTO,
} from "./trade.interface"
import {
  executeTrade,
  fetchRateForCurrencyPair,
  fetchTradeRate,
  fetchValueDates,
  getTradeById,
  downloadTradeConfirmationById,
  getTrades,
} from "./trade.api"
import { queryClient } from "@/main"
import { queryKeys } from "@/lib/constants/query.constants"

export const useValueDatesQuery = ({
  lhsCurrency,
  rhsCurrency,
}: IValueDateRequestDTO) => {
  return useQuery({
    queryKey: [queryKeys.trade.valueDates, lhsCurrency, rhsCurrency],
    queryFn: () => fetchValueDates({ lhsCurrency, rhsCurrency }),
    enabled: !!lhsCurrency && !!rhsCurrency,
  })
}

export const useTradeRateQuery = (payload: ITradeRateRequestDTO) => {
  return useQuery({
    queryKey: [queryKeys.trade.tradeRate, ...Object.values(payload)],
    queryFn: () => fetchTradeRate(payload),
    enabled:
      !!payload.amount &&
      !!payload.buyCurrency &&
      !!payload.sellCurrency &&
      !!payload.fixedSide &&
      !!payload.valueDate &&
      payload.buyCurrency !== payload.sellCurrency,

    staleTime: 10000, // Cache for 10 seconds
    retry: 1, // Only retry once on failure
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
  })
}

export const useTradeExecuteMutation = () => {
  return useMutation({
    mutationFn: (payload: ITradeExecuteBodyDTO) => executeTrade(payload),
    onSuccess: () => {
      toast.success("Trade executed successfully")
      queryClient.invalidateQueries({ queryKey: [queryKeys.trade.trades] })
    },
    onError: () => {
      toast.error("Trade execution failed")
    },
  })
}

export const useTradesQuery = (params: ITradeListingRequestQueryDTO) => {
  return useQuery({
    queryKey: [queryKeys.trade.trades, params],
    queryFn: () => getTrades(params),
  })
}

export const useRateForCurrencyPairQuery = (
  payload: IRateForCurrencyPairRequestDTO,
) => {
  return useQuery({
    queryKey: [queryKeys.trade.rateForCurrencyPair, payload],
    queryFn: () => fetchRateForCurrencyPair(payload),
    enabled:
      !!payload.displayCurrency &&
      !!payload.originalCurrency &&
      payload.displayCurrency !== payload.originalCurrency,
  })
}

export const useTradeByIdQuery = (id?: string) => {
  return useQuery({
    queryKey: [queryKeys.trade.tradeById, id],
    queryFn: () => (id ? getTradeById(id) : null),
    retry: 1,
    enabled: !!id,
  })
}

export function useDownloadTradeConfirmationMutation() {
  return useMutation({
    mutationFn: (tradeId: string) => downloadTradeConfirmationById(tradeId),
    onError: (error: Error) => {
      console.error("Failed to download trade confirmation:", error)
    },
  })
}
