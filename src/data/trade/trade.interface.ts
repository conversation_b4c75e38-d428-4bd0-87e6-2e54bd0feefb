import { Currency } from "@/lib/constants/currency.constants"

import { IPaginationResponse } from "../global/global.interface"

export interface IValueDateResponseDTO {
  valueDateOptions: string[]
}

export interface IValueDateRequestDTO {
  lhsCurrency: string
  rhsCurrency: string
}

export interface ITradeRateRequestDTO {
  buyCurrency: Currency
  sellCurrency: Currency
  fixedSide?: "Buy" | "Sell"
  amount?: number
  valueDate: string
}

export interface Money {
  amount: number
  currency: Currency
}

export interface ITradeRateResponseDTO {
  fxQuoteRequestId: string
  fxBrokeredQuoteId: string
  buyRate: number
  sellRate: number
  buy: Money
  sell: Money
  fixedSide: "Buy" | "Sell"
  valueDate: string
  code?: string
  brokerName: string
}

export interface ITradeExecuteBodyDTO {
  fxQuoteRequestId: string
  fxBrokeredQuoteId: string
  clientAccountId: string
}

/*

{
    "commandResponse": {
        "aggregateId": "39b059bc-0329-45ac-bc96-bd4ea340b26f",
        "correlationId": "44d6110f-258d-4954-b220-b6f3017809e1"
    },
    "settlementDetails": {
        "fxTradeId": "39b059bc-0329-45ac-bc96-bd4ea340b26f",
        "money": {
            "amount": 100000,
            "currency": "GBP"
        },
        "valueDate": "2025-05-02",
        "bankName": "TODO",
        "iban": "TODO",
        "reference": "TODO"
    }
}
*/

export interface ITradeExecuteResponseDTO {
  commandResponse: {
    aggregateId: string
    correlationId: string
  }
  settlementDetails: {
    fxTradeId: string
    money: Money
    valueDate: string
    bankName?: string
    iban?: string
    reference?: string
    accountName?: string
  }
}

export interface ITradeListingRequestQueryDTO {
  pageNumber?: number
  pageSize?: number
  orderByField?: string
  orderByDirection?: string
  status: "All" | "Brokered" | "Settled" | ""
  statusToIgnore?:
    | "Created"
    | "Initiated"
    | "Brokered"
    | "Settled"
    | "Failed"
    | "ClosedOut"
  freeText?: string
}

export type ITradeStatus = "Brokered" | "Settled" | "Failed"

export interface ITradeListingItemDTO {
  id: string
  valueDate: string
  code: string
  buy: Money
  sell: Money
  buyRate: number
  sellRate: number
  fixedSide: "Buy" | "Sell"
  status: ITradeStatus
}

export type ITradeListingResponseDTO = IPaginationResponse<ITradeListingItemDTO>

export interface IRateForCurrencyPairResponseDTO {
  rate: number
  date: string
}

export interface IRateForCurrencyPairRequestDTO {
  displayCurrency: Currency
  originalCurrency: Currency
}

export interface ITradeByIdResponseDTO {
  id: string
  valueDate: string
  code: string
  sell: Money
  buy: Money
  fixedSide: "Buy" | "Sell"
  sellRate: number
  buyRate: number
  status: "Brokered" | "Settled"
  createdBy: {
    id: string
    email: string
    displayName: string
  }
  settlementDetails: {
    fxTradeId: string
    money: Money
    valueDate: string
    bankName: string
    accountName: string
    iban: string
    reference: string
  }
  createdAt: string
  outboundPaymentId: string | null
  outboundPaymentCode: string | null
}
