import { bankingClient as client } from "@/client/banking"

import {
  ITradeRateRequestDTO,
  ITradeRateResponseDTO,
  IValueDateRequestDTO,
  IValueDateResponseDTO,
  ITradeExecuteBodyDTO,
  ITradeExecuteResponseDTO,
  ITradeListingResponseDTO,
  ITradeListingRequestQueryDTO,
  ITradeListingItemDTO,
  IRateForCurrencyPairResponseDTO,
  IRateForCurrencyPairRequestDTO,
  ITradeByIdResponseDTO,
} from "./trade.interface"

export const fetchValueDates = async ({
  lhsCurrency,
  rhsCurrency,
}: IValueDateRequestDTO) => {
  const { data } = await client.post<IValueDateResponseDTO>(
    "/api/v1/fx/valuedates",
    {
      lhsCurrency,
      rhsCurrency,
    },
  )

  return data
}

export const fetchTradeRate = async (payload: ITradeRateRequestDTO) => {
  const { data } = await client.post<ITradeRateResponseDTO>(
    "/api/v1/fx/quote",
    { ...payload, amount: Math.round(payload.amount ?? 0) },
  )

  return data
}

export const executeTrade = async (payload: ITradeExecuteBodyDTO) => {
  const { data } = await client.post<ITradeExecuteResponseDTO>(
    "/api/v1/fx/trade",
    payload,
  )

  return data
}

export const getTrades = async (
  params: ITradeListingRequestQueryDTO,
): Promise<ITradeListingResponseDTO> => {
  if (params.status === "All") {
    params.status = ""
  }

  if (!params.orderByField) {
    params.orderByField = "valueDate"
  }

  if (!params.orderByDirection) {
    params.orderByDirection = "desc"
  }

  if (!params.statusToIgnore) {
    params.statusToIgnore = "Failed"
  }
  const { data } = await client.get<ITradeListingResponseDTO>(
    `/api/v1/fx/trade`,
    { params },
  )

  return data
}

export const fetchRateForCurrencyPair = async ({
  displayCurrency,
  originalCurrency,
}: IRateForCurrencyPairRequestDTO) => {
  const { data } = await client.get<IRateForCurrencyPairResponseDTO>(
    `/api/v1/fx/rate/${originalCurrency}${displayCurrency}`,
  )

  return data
}

export const getTradeById = async (id: string) => {
  const { data } = await client.get<ITradeByIdResponseDTO>(
    `/api/v1/fx/trade/${id}`,
  )
  return data
}

export const downloadTradeConfirmationById = async (
  tradeId: string,
): Promise<Blob> => {
  if (!tradeId) {
    throw new Error("Trade ID is required.")
  }

  try {
    const { data } = await client.get<Blob>(
      `/api/v1/fx/trade/${tradeId}/confirmation`,
      {
        responseType: "blob",
      },
    )

    return data
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        "Failed to download account trade confirmation. Please try again.",
    )
  }
}
