import { ICurrency } from "@/lib/constants/currency.constants"
import { bankingClient as client } from "@/client/banking"

import { ICountry } from "./global.interface"

export const fetchCountries = async () => {
  const { data } = await client.get<ICountry[]>("/api/v1/countries")
  data.sort((a, b) => a.name.localeCompare(b.name))
  return data
}

export const fetchCurrencies = async () => {
  const { data } = await client.get<ICurrency[]>("/api/v1/currencies")
  return data
}

export const fetchTradableCurrencies = async (clientAccountId: string) => {
  // add clientAccountId as query param
  const { data } = await client.get<ICurrency[]>(
    "/api/v1/tradable-currencies",
    {
      params: {
        clientAccountId,
      },
    },
  )
  return data
}
