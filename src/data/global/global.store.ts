import { devtools } from "zustand/middleware"
import { create } from "zustand"
import { LucideIcon } from "lucide-react"

import type { GetEntityResponseDto } from "@/client/onboarding/types.gen"

import { IBreadcrumbItem } from "./global.interface"
import { Account } from "../account/account.interface"

export interface ValidationState {
  entityDetailsForm: boolean
  furtherDetailsForm: boolean
  ownershipAndControlForm: boolean
  financialInformationForm: boolean
  transactionActivityForm: boolean
  usersAndSignatoriesForm: boolean
  applicantDetailsForm: boolean
  reviewForm: boolean
}
export interface SeenState extends ValidationState {}

export interface GlobalState {
  entityId: string | undefined
  setEntityId: (entityId?: string) => void
  entity?: GetEntityResponseDto
  setEntity: (entity?: GetEntityResponseDto) => void
  account?: Account
  setAccount: (account?: Account) => void
  title?: string
  setTitle: (title?: string) => void
  icon?: LucideIcon
  setIcon: (icon?: LucideIcon) => void
  breadcrumbs: IBreadcrumbItem[]
  setBreadcrumbs: (
    breadcrumbs:
      | IBreadcrumbItem[]
      | ((prev: IBreadcrumbItem[]) => IBreadcrumbItem[]),
  ) => void

  onboardingHasUnsavedChanges: boolean
  setOnboardingHasUnsavedChanges: (unsavedChanges: boolean) => void

  onboardingStepperActiveStep: number
  setOnboardingStepperActiveStep: (step: number) => void

  onboardingSeenState: SeenState
  setOnboardingSeenState: (state: Partial<SeenState>) => void
}

export const useGlobalStore = create<GlobalState>()(
  devtools(
    (set) => ({
      entityId: undefined,
      setEntityId: (entityId) => set({ entityId }),
      entity: undefined,
      setEntity: (entity) => set({ entity }, undefined, "entity/setEntity"),
      account: undefined,
      setAccount: (account) => set({ account }),
      title: undefined,
      setTitle: (title) => set({ title }),
      icon: undefined,
      setIcon: (icon) => set({ icon }),
      breadcrumbs: [],

      setBreadcrumbs: (breadcrumbs) =>
        set((state) => ({
          breadcrumbs:
            typeof breadcrumbs === "function"
              ? breadcrumbs(state.breadcrumbs)
              : breadcrumbs,
        })),

      // Onboarding state
      onboardingHasUnsavedChanges: false,
      setOnboardingHasUnsavedChanges: (unsavedChanges) =>
        set({ onboardingHasUnsavedChanges: unsavedChanges }),

      onboardingStepperActiveStep: 0,
      setOnboardingStepperActiveStep: (step) =>
        set({ onboardingStepperActiveStep: step }),

      onboardingSeenState: {
        entityDetailsForm: false,
        furtherDetailsForm: false,
        ownershipAndControlForm: false,
        financialInformationForm: false,
        transactionActivityForm: false,
        usersAndSignatoriesForm: false,
        applicantDetailsForm: false,
        reviewForm: false,
      },
      setOnboardingSeenState: (state) =>
        set({
          onboardingSeenState: {
            entityDetailsForm: state.entityDetailsForm ?? false,
            furtherDetailsForm: state.furtherDetailsForm ?? false,
            ownershipAndControlForm: state.ownershipAndControlForm ?? false,
            financialInformationForm: state.financialInformationForm ?? false,
            transactionActivityForm: state.transactionActivityForm ?? false,
            usersAndSignatoriesForm: state.usersAndSignatoriesForm ?? false,
            applicantDetailsForm: state.applicantDetailsForm ?? false,
            reviewForm: state.reviewForm ?? false,
          },
        }),
    }),

    {
      trace: true,
    },
  ),
)
