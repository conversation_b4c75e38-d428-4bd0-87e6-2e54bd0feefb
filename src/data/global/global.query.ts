import { useQuery } from "@tanstack/react-query"

import { queryClient } from "@/main"
import { queryKeys } from "@/lib/constants/query.constants"

import {
  fetchCountries,
  fetchCurrencies,
  fetchTradableCurrencies,
} from "./global.api"

export const useCountriesQuery = () => {
  return useQuery({
    queryKey: [queryKeys.global.countries],
    queryFn: () => fetchCountries(),
    staleTime: Infinity,
  })
}

export const useCurrenciesQuery = () => {
  return useQuery({
    queryKey: [queryKeys.global.currencies],
    queryFn: () => fetchCurrencies(),
    staleTime: Infinity,
  })
}

export const fetchCurrencyWithQuery = async () => {
  const currencies = await queryClient.fetchQuery({
    queryKey: [queryKeys.global.currencies],
    queryFn: () => fetchCurrencies(),
    staleTime: Infinity,
  })
  return currencies
}

export const useTradableCurrenciesQuery = (
  clientAccountId: string,
  options?: {
    enabled?: boolean
  },
) => {
  return useQuery({
    queryKey: [queryKeys.global.tradableCurrencies, clientAccountId],
    queryFn: () => fetchTradableCurrencies(clientAccountId),
    enabled: options?.enabled ?? !!clientAccountId,
  })
}
