export interface IBreadcrumbItem {
  key: string
  path: string
  label: string
  timestamp?: number
}

export interface ICountry {
  id: string
  name: string
  formalName: string
  codeIso2: string
  codeIso3: string
  codeIso3Numeric: string
  phoneCode: string
  ibanLength: number
  ibanRegex: string
  ibanSupported: boolean
  accountNumberType: string
  nationalIdType: string
  isSepaCountry: boolean
  paymentPurposeCodeRequired: boolean
  createdAt: string
  createdBy: string
}

export interface IPaginationResponse<T> {
  data: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFirstPage: boolean
  isLastPage: boolean
  pageStartIndex: number
  pageEndIndex: number
}

export interface QryOpts {
  enabled?: boolean
}
