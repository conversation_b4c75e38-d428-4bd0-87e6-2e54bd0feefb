import { queryOptions, useQuery, useSuspenseQuery } from "@tanstack/react-query"
import { QryOpts } from "../onboarding/types"
import { fetchAllUsersWith } from "./entity.api"
import { STALE_TIME } from "../global/global"
import { useRolePermissions } from "@/hooks/useRolePermissions"
import { useAuth } from "@/hooks/use-auth"
import { GetAllUsersWithAccessToEntityResponseDto } from "@/client/onboarding/types.gen"

export const GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY = "get-all-users-by-entityid"

export const getAllUsersQryOpts = (entityId: string, opts: QryOpts = {}) => {
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")
  const { user: authUser } = useAuth()

  if (permission) {
    // If user has permission, return the actual query to fetch all users
    return queryOptions({
      staleTime: STALE_TIME,
      queryKey: ["entity", GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY, entityId],
      queryFn: () => fetchAllUsersWith(entityId),
      ...opts,
    })
  } else {
    // If user doesn't have permission, return a fake promise with current user data from Auth0
    return queryOptions({
      staleTime: STALE_TIME,
      queryKey: ["entity", GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY, entityId],
      queryFn: async (): Promise<GetAllUsersWithAccessToEntityResponseDto[]> => {
        // Use Auth0 user data
        if (!authUser) {
          return []
        }
        // Return current user in the same format as fetchAllUsersWith (GetAllUsersWithAccessToEntityResponseDto[])
        return [
          {
            id: authUser.user_id, // Auth0 user_id (modified in useAuth hook)
            email: authUser.email,
            status: undefined, // Auth0 doesn't provide status
            displayName: authUser.name || authUser.nickname,
            entityAccess: {
              entityAccessId: undefined,
              roles: [] // Empty roles array since we don't have access to see roles
            }
          }
        ] as GetAllUsersWithAccessToEntityResponseDto[]
      },
      ...opts,
    })
  }
}
  

export function useGetAllUsersQry(entityId: string, opts: QryOpts = {}) {
  return useSuspenseQuery(getAllUsersQryOpts(entityId, opts))
}
