import { queryOptions, useQuery, useSuspenseQuery } from "@tanstack/react-query"
import { QryOpts } from "../onboarding/types"
import { fetchAllUsersWith } from "./entity.api"
import { STALE_TIME } from "../global/global"

export const GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY = "get-all-users-by-entityid"

export const getAllUsersQryOpts = (entityId: string, opts: QryOpts = {}) =>
  queryOptions({
    staleTime: STALE_TIME,
    queryKey: ["entity", GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY, entityId],
    queryFn: () => fetchAllUsersWith(entityId),
    ...opts,
  })

export function useGetAllUsersQry(entityId: string, opts: QryOpts = {}) {
  return useSuspenseQuery(getAllUsersQryOpts(entityId, opts))
}
