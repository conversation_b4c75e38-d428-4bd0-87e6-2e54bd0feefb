import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { addNewUserToExistingEntity } from "./entity.api"
import { LoginEntityApproverLevel } from "@/client/onboarding/types.gen"
import { GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY } from "./entity.query"

export interface AddNewUserToExistingEntityPayload {
  firstName: string
  lastName: string
  email: string
  phone: string
  dialCode: string
  role: string
  signatory: string
}

export const ADD_NEW_USER_TO_EXISTING_ENTITY_MUTATION_KEY =
  "add-new-user-to-existing-entity"

export function useAddNewUserToExistingEntityMutation(entityId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [
      "entity",
      ADD_NEW_USER_TO_EXISTING_ENTITY_MUTATION_KEY,
      entityId,
    ],
    mutationFn: (payload: AddNewUserToExistingEntityPayload) =>
      addNewUserToExistingEntity(entityId, {
        email: payload.email,
        phoneNumber: payload.dialCode + payload.phone,
        givenName: payload.firstName,
        familyName: payload.lastName,
        approverLevel: (payload.signatory || null) as LoginEntityApproverLevel,
        roles: payload.role.split(","),
      }),
    onError: (error) => {
      toast.error("Unable to add user to entity", {
        description: error.message,
      })
    },
    onSuccess: () => {
      toast.success("User added successfully")
    },
    onSettled: () =>
      queryClient.invalidateQueries({
        queryKey: ["entity", GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY, entityId],
      }),
  })
}
