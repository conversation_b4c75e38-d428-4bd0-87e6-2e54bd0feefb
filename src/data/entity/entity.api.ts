import {
  usersAddNewLoginWithRolesToExistingEntity,
  usersGetAllUsersWithAccessToEntity,
} from "@/client/onboarding/sdk.gen"
import { assertResponse } from "../onboarding/_exception-handler"
import { AddNewLoginWithRolesToExistingEntityRequestDto } from "@/client/onboarding/types.gen"

/** /api/v1/Entities/{entityId}/Users */
export async function fetchAllUsersWith(entityId: string) {
  const response = await usersGetAllUsersWithAccessToEntity({
    path: { entityId },
  })

  assertResponse(response)

  return response.data!
}

/** /api/v1/Entities/{entityId}/Users */
export async function addNewUserToExistingEntity(
  entityId: string,
  payload: AddNewLoginWithRolesToExistingEntityRequestDto,
) {
  const response = await usersAddNewLoginWithRolesToExistingEntity({
    path: { entityId },
    body: payload,
  })

  assertResponse(response)
}
