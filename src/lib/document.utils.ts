import { MouseEvent } from "react"

import { DocumentsMetadata } from "@/data/onboarding/onboarding.interface"

/**
 * Creates a badge click handler for document download
 *
 * @param form The form object containing the documents
 * @param fieldName The name of the field containing the documents to upload
 * @param handleDownload The function to handle document downloads
 * @returns A function that handles badge clicks for document downloads
 */
export const createDocumentBadgeClickHandler = (
  form: any,
  fieldName: string,
  handleDownload: (
    document: DocumentsMetadata,
    localFiles?: File[],
  ) => Promise<void>,
) => {
  return (e: MouseEvent, document: DocumentsMetadata) => {
    // Check if the click is on or bubbled from the X button
    const target = e.target as HTMLElement
    if (target.closest(".delete-button")) {
      return
    }

    // Get the local files to check if this is a newly uploaded document
    const localFiles = form.getFieldValue(fieldName) || []

    // Pass the local files to the download function
    handleDownload(document, localFiles)
  }
}
