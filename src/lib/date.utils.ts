import { format, parse, isValid } from "date-fns"

export const format2Date = (date: Date) => {
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")
  const year = date.getFullYear().toString()
  return `${year}-${month}-${day}`
}

export function formatAmount(amount?: number): string {
  return amount ? amount.toFixed(2) : " "
}

export function formatDateTime(dateString: string): string {
  const date = parseDate(dateString)
  if (!date) return ""
  return format(date, "dd MMM yyyy HH:mm (z)")
}

export function formatDate(dateString: string): string {
  const date = parseDate(dateString)
  if (!date) return ""
  return format(date, "dd MMM yyyy")
}

export function parseDate(dateString: string): Date | undefined {
  if (!dateString) return undefined

  let parsedDate: Date | undefined

  if (/^\d{4}-\d{2}-\d{2}/.test(dateString)) {
    parsedDate = new Date(dateString)
  } else if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
    const [day, month, year] = dateString.split("/").map(Number)
    parsedDate = new Date(year, month - 1, day)
  } else if (/^\d{1,2}\.\d{1,2}\.\d{4}$/.test(dateString)) {
    const [day, month, year] = dateString.split(".").map(Number)
    parsedDate = new Date(year, month - 1, day)
  } else if (/^\d{1,2}\s[a-zA-Z]{3,}\s\d{4}$/.test(dateString)) {
    try {
      parsedDate = parse(dateString, "dd MMM yyyy", new Date())
    } catch (error) {
      console.error("Error parsing date with format 'dd MMM yyyy':", error)
    }
  }

  if (parsedDate && isValid(parsedDate)) {
    return parsedDate
  }

  const formats = [
    "dd/MM/yyyy",
    "dd.MM.yyyy",
    "dd MMM yyyy",
    "yyyy-MM-dd",
    "MMM dd, yyyy",
  ]

  for (const formatString of formats) {
    try {
      const result = parse(dateString, formatString, new Date())
      if (isValid(result)) {
        return result
      }
    } catch {
      // Continue to the next format if parsing fails
    }
  }

  console.warn(`Could not parse date string: ${dateString}`)
  return undefined
}

export function formatDateTimeUTC(dateString: string): string {
  const date = parseDate(dateString)
  if (!date) return ""
  return (
    date
      .toLocaleString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
        timeZone: "UTC",
      })
      .replace(",", "") + " (UTC)"
  )
}
