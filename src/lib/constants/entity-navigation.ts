import {
  LayoutDashboard,
  LineChart,
  Send,
  Users2,
  List,
  CommandIcon,
} from "lucide-react"
import { LoopIcon } from "@radix-ui/react-icons"

import { GetEntityAccessDto } from "@/client/onboarding/types.gen"

import { ROLES } from "./roles.constants"

interface EntityNavItem {
  icon?: any
  label: string
  path: string
  children?: EntityNavItem[]
  exact?: boolean
}

export const entityNavItems: EntityNavItem[] = [
  {
    icon: LayoutDashboard,
    label: "Dashboard",
    path: "",
    exact: true,
  },
  {
    icon: LineChart,
    label: "Accounts",
    path: "/accounts",
    exact: false,
    children: [
      {
        label: "Expenses",
        path: "/expenses",
      },
    ],
  },
  {
    icon: Send,
    label: "Payments",
    path: "/payments",
    exact: false,
  },
  {
    icon: LoopIcon,
    label: "Trades",
    path: "/trades",
    exact: false,
  },
  {
    icon: List,
    label: "Transactions",
    path: "/transactions",
    exact: false,
  },
  {
    icon: Users2,
    label: "Payees",
    path: "/payees",
    exact: false,
  },
  {
    icon: CommandIcon,
    label: "User Admin",
    path: "/user-admin",
    exact: false,
  },
] as const

export function getEntityNavItems(
  entityId?: string,
  entityAccess?: GetEntityAccessDto[],
) {
  const isAdmin = entityAccess?.some(
    (access) =>
      access.entityId?.toLowerCase() === entityId?.toLowerCase() &&
      access.roles?.includes(ROLES.ADMINISTRATOR),
  )

  return entityNavItems
    .filter((item) => item.path !== "/user-admin" || isAdmin)
    .map((item) => ({
      ...item,
      to: `/${entityId}${item.path}`,
    }))
}

export function getNavItemIcon(path: string) {
  return entityNavItems.find((item) => item.path === path)?.icon
}
