export const currencyFlags = {
  AED: "circle-flags:ae",
  MAD: "circle-flags:ma",
  MUR: "circle-flags:mu",
  XCD: "circle-flags:ag", // Using Antigua and Barbuda flag as representative
  CLP: "circle-flags:cl",
  ZAR: "circle-flags:za",
  SEK: "circle-flags:se",
  KES: "circle-flags:ke",
  CAD: "circle-flags:ca",
  GBP: "circle-flags:gb",
  OMR: "circle-flags:om",
  RON: "circle-flags:ro",
  NOK: "circle-flags:no",
  SAR: "circle-flags:sa",
  JPY: "circle-flags:jp",
  DKK: "circle-flags:dk",
  HUF: "circle-flags:hu",
  IDR: "circle-flags:id",
  KWD: "circle-flags:kw",
  TWD: "circle-flags:tw",
  TTD: "circle-flags:tt",
  QAR: "circle-flags:qa",
  MYR: "circle-flags:my",
  HKD: "circle-flags:hk",
  USD: "circle-flags:us",
  CNY: "circle-flags:cn",
  BBD: "circle-flags:bb",
  ZMW: "circle-flags:zm",
  PLN: "circle-flags:pl",
  CHF: "circle-flags:ch",
  XOF: "circle-flags:bj", // Using Benin flag as representative
  BWP: "circle-flags:bw",
  BHD: "circle-flags:bh",
  KZT: "circle-flags:kz",
  EGP: "circle-flags:eg",
  ISK: "circle-flags:is",
  MWK: "circle-flags:mw",
  HRK: "circle-flags:hr",
  NGN: "circle-flags:ng",
  AUD: "circle-flags:au",
  RUB: "circle-flags:ru",
  JOD: "circle-flags:jo",
  FJD: "circle-flags:fj",
  THB: "circle-flags:th",
  MXN: "circle-flags:mx",
  KRW: "circle-flags:kr",
  NZD: "circle-flags:nz",
  LKR: "circle-flags:lk",
  EUR: "circle-flags:european-union",
  CZK: "circle-flags:cz",
  BGN: "circle-flags:bg",
  UGX: "circle-flags:ug",
  SGD: "circle-flags:sg",
  INR: "circle-flags:in",
  GHS: "circle-flags:gh",
  PHP: "circle-flags:ph",
  ILS: "circle-flags:il",
  TRY: "circle-flags:tr",
  BRL: "circle-flags:br",
  CNH: "circle-flags:cn", // Using China flag for CNH (offshore yuan)
}

function orderCurrencies(_currencies: Record<string, string>) {
  const priorityCurrencies = ["GBP", "USD", "EUR"]
  // priories should be at the top of the list with given order
  // rest should be alphebetical
  const restCurrencies = Object.keys(_currencies)
  const currencies: string[] = []

  priorityCurrencies.forEach((priorityCurrency) => {
    const currency = restCurrencies.find((c) => c === priorityCurrency)
    if (currency) {
      currencies.push(currency)
      restCurrencies.splice(restCurrencies.indexOf(currency), 1)
    }
  })

  restCurrencies.sort((a, b) => a.localeCompare(b))
  currencies.push(...restCurrencies)

  return currencies
}

export const currencies = orderCurrencies(currencyFlags)

export type Currency = keyof typeof currencyFlags

/*
{
    "code": "AUD",
    "description": "Australian Dollar",
    "amountDisplayFormat": "###,###.##",
    "isTradingRestricted": false,
    "canOnlyBeBought": false,
    "htmlEncodedSymbol": "&#36;",
    "isForPayerId": true,
    "factor": 100
  },
*/

export interface ICurrency {
  code: string
  description: string
  amountDisplayFormat: string
  isTradingRestricted: boolean
  canOnlyBeBought: boolean
  htmlEncodedSymbol?: string
  isForPayerId: boolean
  factor: number
}
