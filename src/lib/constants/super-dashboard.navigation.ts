import { type LucideIcon } from "lucide-react"

interface SuperDashboardNavItem {
  icon?: LucideIcon
  label: string
  path: string
  children?: SuperDashboardNavItem[]
}

export const SuperDashboardNavItems: SuperDashboardNavItem[] = [] as const

export function getSuperDashboardNavItems() {
  return SuperDashboardNavItems.map((item) => ({
    ...item,
    to: `/${item.path}`,
  }))
}

export function getSuperDashboardNavItemIcon(path: string) {
  return SuperDashboardNavItems.find((item) => item.path === path)?.icon
}
