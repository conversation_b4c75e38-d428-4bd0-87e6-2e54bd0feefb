export const countryFlags = {
  AE: "circle-flags:ae",
  MA: "circle-flags:ma",
  MU: "circle-flags:mu",
  XC: "circle-flags:ag", // Using Antigua and Barbuda flag as representative
  CL: "circle-flags:cl",
  ZA: "circle-flags:za",
  SE: "circle-flags:se",
  KE: "circle-flags:ke",
  CA: "circle-flags:ca",
  GB: "circle-flags:gb",
  OM: "circle-flags:om",
  RO: "circle-flags:ro",
  NO: "circle-flags:no",
  SA: "circle-flags:sa",
  JP: "circle-flags:jp",
  DK: "circle-flags:dk",
  HU: "circle-flags:hu",
  ID: "circle-flags:id",
  KW: "circle-flags:kw",
  TW: "circle-flags:tw",
  TT: "circle-flags:tt",
  QA: "circle-flags:qa",
  MY: "circle-flags:my",
  HK: "circle-flags:hk",
  US: "circle-flags:us",
  BB: "circle-flags:bb",
  ZM: "circle-flags:zm",
  PL: "circle-flags:pl",
  CH: "circle-flags:ch",
  XO: "circle-flags:bj", // Using Benin flag as representative
  BW: "circle-flags:bw",
  BH: "circle-flags:bh",
  KZ: "circle-flags:kz",
  EG: "circle-flags:eg",
  IS: "circle-flags:is",
  MW: "circle-flags:mw",
  HR: "circle-flags:hr",
  NG: "circle-flags:ng",
  AU: "circle-flags:au",
  RU: "circle-flags:ru",
  JO: "circle-flags:jo",
  FJ: "circle-flags:fj",
  TH: "circle-flags:th",
  MX: "circle-flags:mx",
  KR: "circle-flags:kr",
  NZ: "circle-flags:nz",
  LK: "circle-flags:lk",
  EU: "circle-flags:european-union",
  CZ: "circle-flags:cz",
  BG: "circle-flags:bg",
  UG: "circle-flags:ug",
  SG: "circle-flags:sg",
  IN: "circle-flags:in",
  GH: "circle-flags:gh",
  PH: "circle-flags:ph",
  IL: "circle-flags:il",
  TR: "circle-flags:tr",
  BR: "circle-flags:br",
  CN: "circle-flags:cn", // Using China flag for CNH (offshore yuan)
  AD: "circle-flags:ad",
  AF: "circle-flags:af",
  AG: "circle-flags:ag",
  AI: "circle-flags:ai",
  AL: "circle-flags:al",
  AM: "circle-flags:am",
  AO: "circle-flags:ao",
  AQ: "circle-flags:aq",
  AR: "circle-flags:ar",
  AS: "circle-flags:as",
  AT: "circle-flags:at",
  AW: "circle-flags:aw",
  AX: "circle-flags:ax",
  AZ: "circle-flags:az",
  BA: "circle-flags:ba",
  BD: "circle-flags:bd",
  BE: "circle-flags:be",
  BF: "circle-flags:bf",
  BI: "circle-flags:bi",
  BJ: "circle-flags:bj",
  BL: "circle-flags:bl",
  BM: "circle-flags:bm",
  BN: "circle-flags:bn",
  BO: "circle-flags:bo",
  BQ: "circle-flags:bq",
  BS: "circle-flags:bs",
  BT: "circle-flags:bt",
  BV: "circle-flags:bv",
  BY: "circle-flags:by",
  BZ: "circle-flags:bz",
  CC: "circle-flags:cc",
  CD: "circle-flags:cd",
  CF: "circle-flags:cf",
  CG: "circle-flags:cg",
  CI: "circle-flags:ci",
  CK: "circle-flags:ck",
  CM: "circle-flags:cm",
  CO: "circle-flags:co",
  CR: "circle-flags:cr",
  CU: "circle-flags:cu",
  CV: "circle-flags:cv",
  CW: "circle-flags:cw",
  CX: "circle-flags:cx",
  CY: "circle-flags:cy",
  DE: "circle-flags:de",
  DJ: "circle-flags:dj",
  DM: "circle-flags:dm",
  DO: "circle-flags:do",
  DZ: "circle-flags:dz",
  EC: "circle-flags:ec",
  EE: "circle-flags:ee",
  EH: "circle-flags:eh",
  ER: "circle-flags:er",
  ES: "circle-flags:es",
  ET: "circle-flags:et",
  FI: "circle-flags:fi",
  FK: "circle-flags:fk",
  FM: "circle-flags:fm",
  FO: "circle-flags:fo",
  FR: "circle-flags:fr",
  GA: "circle-flags:ga",
  GD: "circle-flags:gd",
  GE: "circle-flags:ge",
  GF: "circle-flags:gf",
  GG: "circle-flags:gg",
  GI: "circle-flags:gi",
  GL: "circle-flags:gl",
  GM: "circle-flags:gm",
  GN: "circle-flags:gn",
  GP: "circle-flags:gp",
  GQ: "circle-flags:gq",
  GR: "circle-flags:gr",
  GS: "circle-flags:gs",
  GT: "circle-flags:gt",
  GU: "circle-flags:gu",
  GW: "circle-flags:gw",
  GY: "circle-flags:gy",
  HM: "circle-flags:hm",
  HN: "circle-flags:hn",
  HT: "circle-flags:ht",
  IE: "circle-flags:ie",
  IM: "circle-flags:im",
  IO: "circle-flags:io",
  IQ: "circle-flags:iq",
  IR: "circle-flags:ir",
  IT: "circle-flags:it",
  JE: "circle-flags:je",
  JM: "circle-flags:jm",
  KG: "circle-flags:kg",
  KH: "circle-flags:kh",
  KI: "circle-flags:ki",
  KM: "circle-flags:km",
  KN: "circle-flags:kn",
  KP: "circle-flags:kp",
  KY: "circle-flags:ky",
  LA: "circle-flags:la",
  LB: "circle-flags:lb",
  LC: "circle-flags:lc",
  LI: "circle-flags:li",
  LR: "circle-flags:lr",
  LS: "circle-flags:ls",
  LT: "circle-flags:lt",
  LU: "circle-flags:lu",
  LV: "circle-flags:lv",
  LY: "circle-flags:ly",
  MC: "circle-flags:mc",
  MD: "circle-flags:md",
  ME: "circle-flags:me",
  MF: "circle-flags:mf",
  MG: "circle-flags:mg",
  MH: "circle-flags:mh",
  MK: "circle-flags:mk",
  ML: "circle-flags:ml",
  MM: "circle-flags:mm",
  MN: "circle-flags:mn",
  MO: "circle-flags:mo",
  MP: "circle-flags:mp",
  MQ: "circle-flags:mq",
  MR: "circle-flags:mr",
  MS: "circle-flags:ms",
  MT: "circle-flags:mt",
  MV: "circle-flags:mv",
  MZ: "circle-flags:mz",
  NA: "circle-flags:na",
  NC: "circle-flags:nc",
  NE: "circle-flags:ne",
  NF: "circle-flags:nf",
  NI: "circle-flags:ni",
  NL: "circle-flags:nl",
  NP: "circle-flags:np",
  NR: "circle-flags:nr",
  NU: "circle-flags:nu",
  PA: "circle-flags:pa",
  PE: "circle-flags:pe",
  PF: "circle-flags:pf",
  PG: "circle-flags:pg",
  PK: "circle-flags:pk",
  PM: "circle-flags:pm",
  PN: "circle-flags:pn",
  PR: "circle-flags:pr",
  PS: "circle-flags:ps",
  PT: "circle-flags:pt",
  PW: "circle-flags:pw",
  PY: "circle-flags:py",
  RE: "circle-flags:re",
  RS: "circle-flags:rs",
  RW: "circle-flags:rw",
  SB: "circle-flags:sb",
  SC: "circle-flags:sc",
  SD: "circle-flags:sd",
  SH: "circle-flags:sh",
  SI: "circle-flags:si",
  SJ: "circle-flags:sj",
  SK: "circle-flags:sk",
  SL: "circle-flags:sl",
  SM: "circle-flags:sm",
  SN: "circle-flags:sn",
  SO: "circle-flags:so",
  SR: "circle-flags:sr",
  SS: "circle-flags:ss",
  ST: "circle-flags:st",
  SV: "circle-flags:sv",
  SX: "circle-flags:sx",
  SY: "circle-flags:sy",
  SZ: "circle-flags:sz",
  TC: "circle-flags:tc",
  TD: "circle-flags:td",
  TF: "circle-flags:tf",
  TG: "circle-flags:tg",
  TJ: "circle-flags:tj",
  TK: "circle-flags:tk",
  TL: "circle-flags:tl",
  TM: "circle-flags:tm",
  TN: "circle-flags:tn",
  TO: "circle-flags:to",
  TV: "circle-flags:tv",
  TZ: "circle-flags:tz",
  UA: "circle-flags:ua",
  UM: "circle-flags:um",
  UY: "circle-flags:uy",
  UZ: "circle-flags:uz",
  VA: "circle-flags:va",
  VC: "circle-flags:vc",
  VE: "circle-flags:ve",
  VG: "circle-flags:vg",
  VI: "circle-flags:vi",
  VN: "circle-flags:vn",
  VU: "circle-flags:vu",
  WF: "circle-flags:wf",
  WS: "circle-flags:ws",
  YE: "circle-flags:ye",
  YT: "circle-flags:yt",
  ZW: "circle-flags:zw",
}

export type Country = keyof typeof countryFlags
