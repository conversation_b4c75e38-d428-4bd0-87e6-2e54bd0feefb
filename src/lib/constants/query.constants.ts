export const queryKeys = {
  global: {
    accounts: "accounts",
    currencies: "currencies",
    countries: "countries",
    tradableCurrencies: "tradable-currencies",
  },
  payee: {
    list: "payee-list",
    byId: "payee-details",
  },
  payment: {
    list: "payments",
    byId: "payment",
    rejectionReasons: "payment-rejection-reasons",
    purpose: "payment-purpose",
    pending: "pending-payments",
    bulkSummary: "bulk-payment-summary",
    validateSignatory: "validate-signatory",
    approvalDetails: "payment-approval-details",
    bulkById: "bulk-payment",
    fxRate: "fx-rate",
    fxValueDates: "fx-value-dates",
    fxQuote: "fx-quote",
  },
  trade: {
    valueDates: "value-dates",
    tradeRate: "trade-rate",
    tradeExecute: "trade-execute",
    trades: "trades",
    rateForCurrencyPair: "rate-for-currency-pair",
    tradeById: "trade-by-id",
  },
  transaction: {
    list: "transactions",
    byId: "transaction",
  },
} as const

export const mutationKeys = {
  payment: {
    add: "add-payment",
    approve: "approve-payment",
    revert: "revert-payment",
    verify: "verify-payment",
    update: "update-payment",
    cancelWithReason: "cancel-payment-with-reason",
    bulkProcess: "bulk-payment-process",
    bulkSubmit: "bulk-payment-submit",
    bulkCancel: "bulk-payment-cancel",
    approveBulk: "approve-bulk-payment",
    bulkDeletePayments: "bulk-payment-delete-payments",
    bulkPatchRow: "bulk-payment-patch-row",
    approveWithFx: "approve-payment-with-fx",
    startVerification: "start-payment-verification",
    submitWithVerification: "submit-payment-with-verification",
    startBulkVerification: "start-bulk-payment-verification",
    verifyBulkCode: "verify-bulk-payment-code",
  },
} as const
