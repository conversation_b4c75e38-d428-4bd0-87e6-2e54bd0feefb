import { DeepKeys, ReactFormExtendedApi } from "@tanstack/react-form"

import { ICountry } from "@/data/global/global.interface"

const EmailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

export const required = (value?: string, message?: string) =>
  value?.trim().length === 0 ? (message ?? "This field is required") : undefined

export const validateIBan = (value: string, countryData?: ICountry) => {
  if (!countryData) return "Country is required"
  const v = value.replace(/[\s-]+/g, "")
  const r = required(v, "IBAN is required")
  if (r) return r
  /*  if (countryData.ibanRegex) {
    if (!v.match(countryData.ibanRegex)) {
      return "IBAN is not valid";
    }
  } */
  if (countryData.ibanLength) {
    if (v.length !== countryData.ibanLength) {
      return `IBAN must be ${countryData.ibanLength} characters long`
    }
  }
  return undefined
}

export const resetFieldMeta = <T>(
  form: ReactFormExtendedApi<T, undefined>,
  field: DeepKeys<T>,
) => {
  form.setFieldMeta(field, (meta) => {
    meta.errors = []
    meta.errorMap = {
      onMount: undefined,
      onChange: undefined,
    }
    meta.isBlurred = true
    meta.isDirty = true
    meta.isPristine = false
    meta.isTouched = true
    meta.isValidating = false
    return meta
  })
}

export const mergeDeep = <T extends object>(
  defaults: Partial<T>,
  source: Partial<T> | undefined,
): T => {
  const isObject = (obj: any) => obj && typeof obj === "object"

  const result: any = {}

  for (const key in defaults) {
    if (isObject(defaults[key]) && source && isObject((source as any)[key])) {
      result[key] = mergeDeep(defaults[key] as any, (source as any)[key])
    } else {
      result[key] = source && key in source ? source[key] : defaults[key]
    }
  }

  if (source) {
    for (const key in source) {
      if (!(key in result)) {
        result[key] = source[key]
      }
    }
  }

  return result as T
}

export const validateEmail = (value?: string, required = false) => {
  const trimmedValue = value?.trim()

  if (!trimmedValue && required) return "Email is required"
  if (!trimmedValue) return undefined
  if (trimmedValue.length > 254) return "Email is too long"

  return EmailRegex.test(trimmedValue) ? undefined : "Invalid email"
}
