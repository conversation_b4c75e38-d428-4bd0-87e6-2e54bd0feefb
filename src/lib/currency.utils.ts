import { useCurrenciesQuery } from "@/data/global/global.query"

export function parseCurrencyDigits(
  value: number | string,
  decimals: number,
): string {
  if (value === null || value === undefined) {
    return "N/A"
  }
  const amountNumber = typeof value === "string" ? parseFloat(value) : value

  const normalizedValue = amountNumber / 10 ** decimals

  const formattedNumber = new Intl.NumberFormat("en-US", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(normalizedValue)

  return formattedNumber
}

export function parseCurrencyDigitsWithoutDivision(
  value: number | string | undefined,
  decimals: number,
): string {
  if (value === undefined) return "N/A"

  const amountNumber = typeof value === "string" ? parseFloat(value) : value

  const formattedNumber = new Intl.NumberFormat("en-US", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amountNumber)

  return formattedNumber
}

/**
 * Converts a payment amount to a clean numeric value suitable for form inputs.
 * Handles both numeric values (assuming they're in cents) and formatted strings.
 *
 * @param amount - The payment amount to convert
 * @param inCents - Whether the amount is stored in cents (default: true)
 * @returns A numeric value suitable for form inputs
 */
export function parsePaymentAmount(
  amount: number | string | undefined | null,
  inCents: boolean = true,
): number | undefined {
  if (amount === undefined || amount === null) {
    return undefined
  }

  if (typeof amount === "number") {
    return inCents ? amount / 100 : amount
  }

  // Handle string values by removing commas and other formatting characters
  return parseFloat(String(amount).replace(/,/g, ""))
}

export function compareCurrencyDigits(
  currentBalance: number | string,
  currency1: string,
  amountToSend: number | string,
): boolean {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { data: currencies } = useCurrenciesQuery()
  const _currency = currencies?.find((c) => c.code === currency1)

  let numericValue1: number

  if (_currency?.amountDisplayFormat) {
    const decimals = _currency.amountDisplayFormat.split(".")[1]?.length ?? 0
    numericValue1 =
      typeof currentBalance === "string"
        ? parseFloat(currentBalance)
        : currentBalance
    numericValue1 = numericValue1 / 10 ** decimals
  } else {
    numericValue1 =
      typeof currentBalance === "string"
        ? parseFloat(currentBalance)
        : currentBalance
  }

  const numericValue2 =
    typeof amountToSend === "string" ? parseFloat(amountToSend) : amountToSend

  return numericValue1 < numericValue2
}

export function parseCurrencyDigitsWithNoDecimals(
  value: number | string,
): string {
  if (value === undefined) return "N/A"

  const amountNumber = typeof value === "string" ? parseInt(value) : value

  const normalizedValue = amountNumber / 10

  const formattedNumber = new Intl.NumberFormat("en-US").format(normalizedValue)

  return formattedNumber
}
