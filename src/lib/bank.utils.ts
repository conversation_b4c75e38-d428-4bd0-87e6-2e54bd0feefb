import { IBankData } from "@/data/payees/payees.interface"
import { Account } from "@/data/account/account.interface"

export function formatBankDetails(bankData: IBankData) {
  const addressParts = [
    bankData.address?.buildingNumber,
    bankData.address?.streetName,
    bankData.address?.city,
    bankData.address?.regionState,
    bankData.address?.postalCode,
    bankData.address?.country.value,
  ]

  const address = bankData.address?.buildingNumber
    ? addressParts.filter((part) => !!part).join(", ")
    : bankData.address?.addressLine1

  return `${bankData.name} - ${address}`
}

export function formatAccountName({
  name,
  iban,
}: {
  name?: string
  iban?: string
}) {
  const _name = name ?? "-"
  const _iban = iban && iban.length > 4 ? `...${iban.slice(-4)}` : "-"
  return `${_name} (${_iban})`
}

export function getAccountName(account?: Account) {
  if (!account) return "Select account"
  return formatAccountName({
    name: account.accountName,
    iban: account.virtualIban,
  })
}

export function formatAccountNumber(accountNumber?: string) {
  if (!accountNumber) return "-"
  if (accountNumber.length <= 4) return accountNumber
  return `(...${accountNumber.slice(-4)})`
}

export function formatWithMask(
  value?: string,
  options?: { mask?: string; length?: number; maxLength?: number },
) {
  if (!value) return "-"
  const mask = options?.mask ?? "*"
  const length = options?.length ?? 4
  if (options?.maxLength) {
    // slice from the end
    value = value.slice(-options.maxLength)
  }
  return mask.repeat(value.length - length) + value.slice(-length)
}
