/*
Bulk Uploads:
Uploaded,
Validating,
Failed,
ReadyToProcess,
Processing,
Completed
Payments:
Draft,
PendingClientApproval,
PendingArgentexApproval,
PendingComplianceApproval,
PendingBalanceCheck,
ProcessingSentToPaymentConnector,
ProcessingSentToBank,
Sent,
Cancelled,
Deleted,
RejectedOperations,
RejectedCompliance,
RejectedInsufficientBalance,
RejectedBank,
ScreeningInitiationFailed,
PaymentConnectorInitiationFailed,
Held,
Returned,
PendingReturn
*/

type Variant = "pending" | "success" | "error" | "info"

export const getPaymentStatusVariant = (status: string): Variant => {
  const pendingStatuses = [
    "Validating",
    "ReadyToProcess",
    "Processing",
    "Draft",
    "PendingClientApproval",
    "PendingArgentexApproval",
    "PendingComplianceApproval",
    "PendingBalanceCheck",
    "ProcessingSentToPaymentConnector",
    "ProcessingSentToBank",
    "Held",
    "PendingReturn",
  ]

  const successStatuses = ["Uploaded", "Completed", "Sent"]

  const errorStatuses = [
    "Failed",
    "Cancelled",
    "Deleted",
    "RejectedOperations",
    "RejectedCompliance",
    "RejectedInsufficientBalance",
    "RejectedBank",
    "ScreeningInitiationFailed",
    "PaymentConnectorInitiationFailed",
    "Returned",
  ]

  const normalizedStatus = status.toLowerCase()

  if (pendingStatuses.some((s) => normalizedStatus.includes(s.toLowerCase()))) {
    return "pending"
  }
  if (successStatuses.some((s) => normalizedStatus.includes(s.toLowerCase()))) {
    return "success"
  }
  if (errorStatuses.some((s) => normalizedStatus.includes(s.toLowerCase()))) {
    return "error"
  }
  return "info"
}
