import { format2Date } from "./date.utils"

describe("date.utils", () => {
  describe("format2Date", () => {
    it("should format date with single digit month and day", () => {
      const date = new Date("2024-1-5")
      expect(format2Date(date)).toBe("2024-01-05")
    })

    it("should format date with double digit month and day", () => {
      const date = new Date("2024-12-25")
      expect(format2Date(date)).toBe("2024-12-25")
    })

    it("should format date with mixed single and double digits", () => {
      const date = new Date("2024-12-05")
      expect(format2Date(date)).toBe("2024-12-05")
    })

    it("should handle year rollover", () => {
      const date = new Date("2023-12-31")
      expect(format2Date(date)).toBe("2023-12-31")
    })
  })
})
