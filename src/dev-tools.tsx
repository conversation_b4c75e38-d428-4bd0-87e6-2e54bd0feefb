import ReactDOM from "react-dom/client"

import { UseAuth } from "@/hooks/use-auth"
import DevTools from "@/components/base/dev-tools"

export default function loadDevTools(auth: UseAuth, cb?: () => void) {
  // this allows you to explicitly disable it in development for example
  const explicitlyDisabled =
    window.location.search.includes("dev-tools=false") ||
    window.localStorage.getItem("dev-tools") === "false"

  const explicitlyEnabled =
    window.location.search.includes("dev-tools=true") ||
    window.localStorage.getItem("dev-tools") === "true"

  const devToolsRoot = document.getElementById("dev-tools-root")!

  // we want it enabled by default everywhere but production and we also want
  // to support the dev tools in production (to make us more productive triaging production issues).
  // you can enable the DevTools via localStorage or the query string.
  if (
    !explicitlyDisabled &&
    (import.meta.env.VITE_ENV === "development" || explicitlyEnabled)
  ) {
    if (!devToolsRoot?.innerHTML) {
      const root = ReactDOM.createRoot(devToolsRoot)

      root.render(<DevTools auth={auth} />)
    }
  } else {
    // if we don't need the DevTools, call the callback immediately.
    cb?.()
  }
}
