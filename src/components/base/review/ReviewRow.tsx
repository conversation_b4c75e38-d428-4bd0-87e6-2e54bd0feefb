import { cn } from "@/lib/utils"

export function ReviewRow({
  label,
  value,
  noBorder,
  className,
  labelClassName,
}: {
  label: string
  value: string | string[] | React.ReactNode | undefined
  noBorder?: boolean
  className?: string
  labelClassName?: string
}) {
  return (
    <div
      className={cn(
        "flex items-start justify-between py-2",
        noBorder ? "" : "border-b border-dashed",
        className,
      )}
    >
      <div className={cn("text-sm text-foreground", labelClassName)}>
        {label}
      </div>

      <div className="text-sm text-muted-foreground">
        {Array.isArray(value)
          ? value.map((item, index) => (
              <span key={index}>
                {item}
                <br />
              </span>
            ))
          : value}
      </div>
    </div>
  )
}
