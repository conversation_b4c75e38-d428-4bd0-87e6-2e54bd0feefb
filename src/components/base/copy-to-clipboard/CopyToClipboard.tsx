import { MouseEvent } from "react"

import { Check, Co<PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { useToggle } from "@/hooks/use-toggle"
import { cn } from "@/lib/utils"

interface CopyToClipboardProps {
  text?: string
  html?: string
  htmlRef?: React.RefObject<HTMLElement | null>
  className?: string
  toastSuccessMessage?: string
}

export default function CopyToClipboard({
  text,
  html,
  htmlRef,
  className,
  toastSuccessMessage,
}: CopyToClipboardProps) {
  const [dismiss, { on, off }] = useToggle()

  const copyToClipboard = async (e: MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    try {
      if (!text && !html && !htmlRef?.current) {
        toast.info("No content")
        return
      }

      const htmlContent = html || htmlRef?.current?.innerHTML || ""
      const isHTMLContent = !!htmlContent

      let textContent = text

      // TODO: If we need to support other types
      if (!textContent && isHTMLContent) {
        // its not text, but markdown
        textContent = "TODO: HTML Content"
      }

      if (htmlContent) {
        const clipboardData = new ClipboardItem({
          "text/plain": new Blob([textContent ?? ""], { type: "text/plain" }),
          "text/html": new Blob([htmlContent], { type: "text/html" }),
        })

        await navigator.clipboard.write([clipboardData])
      } else {
        await navigator.clipboard.writeText(textContent!)
        on()
      }

      toast.success(toastSuccessMessage ?? `${textContent} copied`, {
        onAutoClose: off,
      })
    } catch (err) {
      console.error("Failed to copy text:", err)
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      className={cn(
        "relative flex items-center justify-center overflow-hidden",
        dismiss && "border-weak-border",
        className,
      )}
      onClick={copyToClipboard}
    >
      <Check
        className={cn(
          "absolute size-4 -translate-y-10 text-primary opacity-0 transition-all",
          dismiss && "translate-y-0 opacity-100",
        )}
      />
      <Copy
        className={cn(
          "absolute size-4 translate-y-0 text-weak-foreground opacity-100 transition-all",
          dismiss && "translate-y-10 opacity-0",
        )}
      />
    </Button>
  )
}
