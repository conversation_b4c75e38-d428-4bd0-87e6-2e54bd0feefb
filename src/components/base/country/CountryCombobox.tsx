import { useCountriesQuery } from "@/data/global/global.query"
import { ComboboxDropdown } from "../form/ComboboxDropdown"
import { cn } from "@/lib/utils"
import { CountryFlag } from "./CountryFlag"

interface CountryComboboxProps {
  value?: string
  onChange: (value: string) => void
  className?: string
  hasErrors?: boolean
  matchValue?: "name" | "codeIso2"
  disabled?: boolean
}

export function CountryCombobox({
  value,
  onChange,
  className,
  hasErrors,
  disabled,
  matchValue = "name",
}: CountryComboboxProps) {
  const { data: countries, isLoading: isCountriesLoading } = useCountriesQuery()

  return (
    <ComboboxDropdown
      className={cn(
        hasErrors && "border-destructive ring-destructive",
        className,
      )}
      disabled={disabled || isCountriesLoading}
      emptyMessage="No country found"
      items={
        countries?.map((country) => ({
          id: matchValue === "name" ? country.name : country.codeIso2,
          label: `${country.name} (${country.codeIso2})`,
          value: matchValue === "name" ? country.name : country.codeIso2,
          icon: <CountryFlag country={country.codeIso2 as any} />,
        })) ?? []
      }
      onChange={onChange}
      placeholder="Select a country"
      searchPlaceholder="Search country..."
      loading={isCountriesLoading}
      value={value}
    />
  )
}
