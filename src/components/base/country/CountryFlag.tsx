import { Icon } from "@iconify/react"

import { cn } from "@/lib/utils"
import { countryFlags, type Country } from "@/lib/constants/country.constants"

export function CountryFlagSkeleton({ className }: { className?: string }) {
  return <div className={cn("size-4 rounded-full bg-slate-100", className)} />
}

interface CurrencyFlagProps {
  country?: Country
  className?: string
  size?: "sm" | "md" | "lg"
}

export function CountryFlag({
  country,
  className,
  size = "sm",
}: CurrencyFlagProps) {
  const icon = country && countryFlags[country]

  if (!icon) {
    return (
      <CountryFlagSkeleton
        className={cn(
          size === "sm" ? "size-4" : size === "md" ? "size-6" : "size-8",
          className,
        )}
      />
    )
  }

  return (
    <Icon
      className={cn(
        size === "sm" ? "size-4" : size === "md" ? "size-6" : "size-8",
        className,
      )}
      icon={icon}
    />
  )
}
