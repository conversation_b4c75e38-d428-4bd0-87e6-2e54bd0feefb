import { But<PERSON> } from "@/components/ui/button"
import { CopyIcon } from "lucide-react"
import { Card } from "@/components/ui/card"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface DisplayCardProps {
  children: React.ReactNode
  title?: string | React.ReactNode
  description?: string | React.ReactNode
}

export function DisplayCard({
  children,
  title,
  description,
}: DisplayCardProps) {
  return (
    <Card className="border-none bg-muted/80 p-4">
      {title && <div className="mb-4 text-base font-semibold">{title}</div>}
      {description && (
        <div className="text-sm text-muted-foreground">{description}</div>
      )}
      {children}
    </Card>
  )
}

const MAX_LENGTH = 24

export function DisplayCardRow({
  label,
  value,
  copyable,
  className = "",
}: {
  label: string
  value: React.ReactNode
  copyable?: boolean
  className?: string
}) {
  const handleCopy = () => {
    if (typeof value === "string") {
      navigator.clipboard.writeText(value)
      toast.success(`${label} copied to clipboard`, {
        description:
          value.length > MAX_LENGTH
            ? value.slice(0, MAX_LENGTH) + "..."
            : value,
        icon: "📋",
        duration: 1000,
      })
    }
  }

  return (
    <div
      className={cn(
        "mt-2 flex items-start justify-between py-1 first:pt-0",
        className,
      )}
    >
      <span className="text-sm font-normal text-foreground">{label}</span>
      <div className="flex items-center gap-2">
        <span className="text-right text-sm text-muted-foreground">
          {value}
        </span>
        {copyable && (
          <Button className="h-4 w-4 p-0" onClick={handleCopy} variant="ghost">
            <CopyIcon className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  )
}
