import { PropsWithChildren } from "react"
import {
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  Info,
  CircleX,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Alert as CoreAlert } from "@/components/ui/alert"

type VariantKey = "success" | "info" | "warning" | "error"

export interface AlertBoxProps extends PropsWithChildren {
  severity?: VariantKey
  className?: string
}

const alertStyles = {
  success: {
    styles: cn(
      "border-success/20 border-l-success bg-success/[0.05] [&>svg]:text-success",
    ),
    icon: (
      <CheckCircle
        className="text-alert-success h-4 w-4"
        data-testid="check-circle-icon"
      />
    ),
  },
  info: {
    styles: cn("border-info/20 border-l-info bg-info/[0.05] [&>svg]:text-info"),
    icon: <Info className="h-4 w-4" data-testid="info-icon" />,
  },
  warning: {
    styles: "border-warning/20 border-l-warning bg-warning-light",
    icon: (
      <AlertTriangle
        className="h-4 w-4 stroke-warning"
        data-testid="alert-triangle-icon"
      />
    ),
  },
  error: {
    styles: "border-destructive border-l-destructive bg-destructive/10",
    icon: (
      <CircleX
        className="h-4 w-4 stroke-destructive"
        data-testid="alert-circle-icon"
      />
    ),
  },
}

export default function AlertBox({
  severity,
  className,
  children,
}: AlertBoxProps) {
  const { icon, styles } = alertStyles[severity ?? "success"]

  const rootStyles = cn(
    "rounded-none border border-l-4 py-4",
    styles,
    className,
  )

  return (
    <CoreAlert className={rootStyles}>
      {icon}
      {children}
    </CoreAlert>
  )
}

export { AlertTitle, AlertDescription } from "@/components/ui/alert"
