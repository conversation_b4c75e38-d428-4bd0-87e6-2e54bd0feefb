import React from "react"
import {
  ErrorComponent as <PERSON>Error<PERSON>omponent,
  ErrorComponentProps,
  useRouter,
} from "@tanstack/react-router"
import { useQueryErrorResetBoundary } from "@tanstack/react-query"

import { BadRequestError, NotFoundError } from "@/data/global/global.exceptions"

export function ErrorComponent({ error }: ErrorComponentProps) {
  const router = useRouter()

  if (error instanceof (NotFoundError || BadRequestError)) {
    return <div aria-label="Error">{error.message}</div>
  }

  const queryErrorResetBoundary = useQueryErrorResetBoundary()

  React.useEffect(() => {
    queryErrorResetBoundary.reset()
  }, [queryErrorResetBoundary])

  return (
    <div>
      <button
        onClick={() => {
          router.invalidate()
        }}
      >
        retry
      </button>
      <CoreErrorComponent error={error} />
    </div>
  )
}
