import { AriaAttributes } from "react"

import { PaginationConfig } from "@/hooks/usePagination"

export type SortDirection = "asc" | "desc" | "none"

export interface SortState<T = string> {
  field: T | null
  direction: SortDirection
}

export interface TableColumn<TData = any> {
  key: keyof TData | null | undefined
  header: string | React.ReactNode
  width?: string
  sortable?: boolean
  tooltip?: string
  render?: (value: any, row: TData, index: number) => React.ReactNode
  accessor?: string | ((row: TData) => any)
  className?: string
  headerClassName?: string
  meta?: {
    name: string
    description?: string
  }
}

interface IdentificationProps<TData> {
  getRowKey?: (row: TData, index: number) => string
}

interface InteractionProps<TData> {
  onRowClick?: (row: TData, index: number) => void
  onSort?: (field: string, direction: SortDirection) => void
}

interface StylingProps<TData> {
  className?: string
  containerClassName?: string
  rowClassName?: string | ((row: TData, index: number) => string)
  headerClassName?: string
  headerRowClassName?: string
  maxHeight?: "sm" | "md" | "lg" | "screen"
}

export interface FeaturesProps {
  sortable?: boolean
  clickable?: boolean
}

export interface EmptyState {
  title: string
  description: string
  action?: React.ReactNode
}

export interface LoadingProps {
  loading?: boolean
  loadingRows?: number
}

export interface PaginationProps extends PaginationConfig {
  className?: string
}

export interface DataTableProps<TData>
  extends IdentificationProps<TData>,
    InteractionProps<TData>,
    StylingProps<TData>,
    FeaturesProps,
    LoadingProps,
    AriaAttributes {
  // Core
  data: TData[]
  columns: TableColumn<TData>[]
  stickyHeader?: boolean

  // Empty State
  emptyState?: EmptyState

  // Pagination
  pagination?: PaginationProps | boolean
  totalItems?: number
  currentPage?: number
  onPageChange?: (page: number) => void
  onPrevious?: () => void
  onNext?: () => void
  onPageSizeChange?: (pageSize: number) => void
}
