import type { TableColumn } from "../DataTable.types"

interface CellProps<TData> {
  index: number
  row: TData
  column: TableColumn<TData>
  getCellValue: (row: TData, column: TableColumn<TData>) => any
}

export function Cell<TData>(props: CellProps<TData>) {
  if (props.column.render) {
    const value = props.getCellValue(props.row, props.column)
    return props.column.render(value, props.row, props.index)
  }

  const value = props.getCellValue(props.row, props.column)

  // Handle different data types with sensible defaults
  if (value == null) return

  if (value instanceof Date) return value.toLocaleDateString()
  if (typeof value === "boolean") return value ? "Yes" : "No"
  if (typeof value === "object") return JSON.stringify(value)

  return String(value)
}
