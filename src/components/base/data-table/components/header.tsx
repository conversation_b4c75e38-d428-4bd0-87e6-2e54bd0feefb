import { ChevronsUpDown, Info } from "lucide-react"

import { cn } from "@/lib/utils"
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { <PERSON><PERSON> } from "@/components/ui/button"

import type { FeaturesProps, SortState, TableColumn } from "../DataTable.types"

interface HeaderProps<TData> extends FeaturesProps {
  column: TableColumn<TData>
  sort: SortState
  className?: string
  onClick: (column: TableColumn<TData>) => void
}

export function Header<TData>(props: HeaderProps<TData>) {
  const canSort = props.sortable && props.column.sortable !== false
  const isActive = props.sort.field === props.column.key

  const headerContent = (
    <>
      {typeof props.column.header === "string" ? (
        <span
          aria-label={props.column.header}
          className={cn(
            isActive && ["asc", "desc"].includes(props.sort.direction)
              ? "text-primary"
              : "text-muted-foreground",
          )}
        >
          {props.column.header}
        </span>
      ) : (
        props.column.header
      )}

      {canSort && (
        <div className="flex items-center justify-center">
          <ChevronsUpDown
            className={cn(
              isActive && ["asc", "desc"].includes(props.sort.direction)
                ? "text-primary"
                : "text-muted-foreground",
            )}
          />
        </div>
      )}

      {props.column.tooltip && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Info className="size-4 text-muted-foreground" />
          </TooltipTrigger>
          <TooltipContent>{props.column.tooltip}</TooltipContent>
        </Tooltip>
      )}
    </>
  )

  if (canSort) {
    return (
      <Button
        className="flex size-full items-center justify-start p-0 text-xs hover:bg-transparent [&_svg]:size-3 [&_svg]:w-3"
        onClick={() => props.onClick(props.column)}
        variant="ghost"
      >
        {headerContent}
      </Button>
    )
  }

  return (
    <div className={cn("flex items-center gap-1", props.className)}>
      {headerContent}
    </div>
  )
}
