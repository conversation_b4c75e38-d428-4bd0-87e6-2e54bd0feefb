import * as Core from "@/components/ui/table"

import type {
  TableColumn,
  LoadingProps as CoreLoadingProps,
} from "../DataTable.types"

interface LoadingProps<TData> extends CoreLoadingProps {
  columns: TableColumn<TData>[]
}

export function Loading<TData>(props: LoadingProps<TData>) {
  return (
    <Core.TableHeader aria-label="Loading">
      {Array.from({ length: props.loadingRows! }).map((_, index) => (
        <Core.TableRow key={index}>
          {props.columns.map((column) => (
            <Core.TableCell
              className={column.className}
              key={column.key as string}
            >
              <div className="h-6 animate-pulse rounded bg-muted" />
            </Core.TableCell>
          ))}
        </Core.TableRow>
      ))}
    </Core.TableHeader>
  )
}
