import { cn } from "@/lib/utils"

import { TableColumn } from "./DataTable.types"

type ColKey<T> = keyof T | null | undefined

function useText<T>(
  key: ColKey<T>,
  header: string,
  options?: Partial<TableColumn<T>>,
): TableColumn<T> {
  return {
    key,
    header,
    sortable: true,
    ...options,
  }
}

function useNumber<T>(
  key: ColKey<T>,
  header: string,
  options?: Partial<TableColumn<T>>,
): TableColumn<T> {
  return {
    key,
    header,
    sortable: true,
    className: "text-right",
    headerClassName: "text-right",
    ...options,
  }
}

function useDate<T>(
  key: ColKey<T>,
  header: string,
  options?: Partial<TableColumn<T>>,
): TableColumn<T> {
  return {
    key,
    header,
    sortable: true,
    render: (value) =>
      value instanceof Date
        ? value.toLocaleDateString()
        : value
          ? new Date(value).toLocaleDateString()
          : "-",
    ...options,
  }
}

function useBoolean<T>(
  key: Col<PERSON>ey<T>,
  header: string,
  options?: Partial<TableColumn<T>>,
): TableColumn<T> {
  return {
    key,
    header,
    sortable: true,
    render: (value: any) => (
      <span
        className={cn(
          "rounded-full px-2 py-1 text-xs font-medium",
          value ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800",
        )}
      >
        {value ? "Yes" : "No"}
      </span>
    ),
    ...options,
  }
}

function useCustom<T>(
  key: ColKey<T>,
  header: string,
  render: (value: any, row: T, index: number) => React.ReactNode,
  options?: Partial<TableColumn<T>>,
): TableColumn<T> {
  return {
    key: key ?? options?.key,
    header,
    render,
    sortable: false,
    ...options,
  }
}

function useEmpty<T>(
  header?: string | undefined,
  options?: Partial<TableColumn<T>>,
): TableColumn<T> {
  return {
    key: options?.key ?? ("empty" as ColKey<T>),
    header,
    sortable: false,
    ...options,
  }
}

// Utility functions for common column types
export const column = {
  text: useText,
  number: useNumber,
  date: useDate,
  boolean: useBoolean,
  custom: useCustom,
  empty: useEmpty,
}
