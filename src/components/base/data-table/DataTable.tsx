import { cn } from "@/lib/utils"
import * as Core from "@/components/ui/table"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"

import type { DataTableProps } from "./DataTable.types"

import { useDataTable } from "./DataTable.hook"
import { Loading } from "./components/loading"
import { Header } from "./components/header"
import { Cell } from "./components/cell"
import { ShowChildren } from "../show-children"
import Pagination from "../pagination"

export default function DataTable<TData>(props: DataTableProps<TData>) {
  const {
    data,
    columns,
    loading,
    emptyState,
    sort,
    className,
    headerClassName,
    headerRowClassName,
    containerClassName,
    loadingRows,
    sortedData,
    sortable,
    pagination,
    stickyHeader,
    maxHeight,
    getRowClassNames,
    getRowKey,
    handleRowClick,
    handleSort,
    getCellValue,
    "aria-label": ariaLabel,
  } = useDataTable(props)

  if (!loading && data.length === 0) {
    return (
      <div
        aria-label="Empty data"
        className="flex h-full min-h-80 w-full items-center justify-center"
      >
        <div className="flex flex-col items-center justify-center gap-4">
          <h2 className="text-lg font-medium">{emptyState.title}</h2>
          <p className="max-w-md text-center text-sm text-muted-foreground">
            {emptyState.description}
          </p>
          {emptyState.action}
        </div>
      </div>
    )
  }

  function paginationOptions() {
    if (typeof props.pagination === "boolean") return

    return {
      pageSizeOptions: props.pagination?.pageSizeOptions,
      showFirstLast: props.pagination?.showFirstLast,
      showPageInfo: props.pagination?.showPageInfo,
      showSizeSelector: props.pagination?.showSizeSelector,
    }
  }

  return (
    <>
      <ScrollArea
        className={cn(
          "flex flex-col overflow-auto",
          stickyHeader && "h-fit",
          {
            "max-h-[250px]": maxHeight === "sm",
            "max-h-[700px]": maxHeight === "md",
            "max-h-[1000px]": maxHeight === "lg",
            "max-h-screen": maxHeight === "screen",
          },
          containerClassName,
        )}
      >
        <Core.Table
          aria-label={ariaLabel ?? "Data table"}
          className={cn(className)}
        >
          <ShowChildren when={!loading}>
            <Core.TableBody>
              {sortedData.map((row, index) => (
                <Core.TableRow
                  aria-label={getRowKey(row, index)}
                  className={getRowClassNames(row, index)}
                  key={getRowKey(row, index)}
                  onClick={() => handleRowClick(row, index)}
                >
                  {columns.map((column) => (
                    <Core.TableCell
                      aria-label={
                        typeof getCellValue(row, column) === "string"
                          ? getCellValue(row, column)
                          : "Table cell"
                      }
                      className={cn("py-2 last:pr-2", column.className)}
                      key={column.key as string}
                    >
                      <Cell
                        column={column}
                        getCellValue={getCellValue}
                        index={index}
                        row={row}
                      />
                    </Core.TableCell>
                  ))}
                </Core.TableRow>
              ))}
            </Core.TableBody>
          </ShowChildren>

          <Core.TableHeader
            className={cn(
              {
                "sticky top-0 bg-white shadow-[inset_0px_-1px_#e2e8f0]":
                  stickyHeader,
              },
              headerClassName,
            )}
          >
            <Core.TableRow
              aria-label="Table row"
              className={cn(
                "select-none border-none hover:bg-transparent",
                headerRowClassName,
              )}
            >
              {columns.map((column) => (
                <Core.TableHead
                  className={cn(
                    "whitespace-nowrap text-xs",
                    column.width,
                    column.headerClassName,
                  )}
                  key={column.key as string}
                >
                  <Header
                    column={column}
                    onClick={handleSort}
                    sort={sort}
                    sortable={sortable}
                  />
                </Core.TableHead>
              ))}
            </Core.TableRow>
          </Core.TableHeader>

          <ShowChildren when={loading}>
            <Loading columns={columns} loadingRows={loadingRows} />
          </ShowChildren>
        </Core.Table>

        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      <ShowChildren when={!!pagination}>
        <Pagination {...pagination!} {...paginationOptions()} />
      </ShowChildren>
    </>
  )
}
