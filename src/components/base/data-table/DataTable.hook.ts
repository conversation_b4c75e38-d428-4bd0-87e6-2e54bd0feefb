import { useCallback, useMemo, useState } from "react"

import { cn } from "@/lib/utils"
import { usePagination } from "@/hooks/usePagination"

import type {
  DataTableProps,
  EmptyState,
  SortDirection,
  SortState,
  TableColumn,
} from "./DataTable.types"

const DEFAULT_EMPTY_STATE: EmptyState = {
  title: "No data found",
  description: "There are no items to display.",
}

export function useDataTable<TData>({
  data,
  columns,
  emptyState = DEFAULT_EMPTY_STATE,
  className = "",
  rowClassName = "",
  headerRowClassName = "",
  headerClassName = "",
  sortable = true,
  clickable = true,
  loading = false,
  stickyHeader = false,
  loadingRows = 5,
  maxHeight,
  pagination,
  onPageChange,
  onPageSizeChange,
  onPrevious,
  onNext,
  totalItems,
  currentPage: controlledCurrentPage,
  getRowKey = (_, index) => index.toString(),
  onRowClick,
  onSort,
  ...props
}: DataTableProps<TData>) {
  const isServerSidePagination = totalItems !== undefined
  const dataLength = isServerSidePagination ? totalItems : data.length
  const paginationOptions = typeof pagination === "boolean" ? {} : pagination

  const paginationResult = usePagination({
    totalItems: dataLength,
    initialPageSize: paginationOptions?.pageSize || 10,
    onPageChange,
    onPageSizeChange,
    onPrevious,
    onNext,
    currentPage: controlledCurrentPage,
  })

  const [sort, setSort] = useState<SortState>({
    field: null,
    direction: "none",
  })

  const getCellValue = useCallback((row: TData, column: TableColumn<TData>) => {
    if (typeof column.accessor === "function") {
      return column.accessor(row)
    }

    if (typeof column.accessor === "string") {
      return row[column.accessor as keyof TData]
    }

    return row[column.key as keyof TData]
  }, [])

  const sortData = useCallback(
    (data: TData[]) => {
      if (!sort.field || sort.direction === "none") return data

      const column = columns.find((col) => col.key === sort.field)
      if (!column) return data

      return [...data].sort((a, b) => {
        const aValue = getCellValue(a, column)
        const bValue = getCellValue(b, column)

        if (aValue == null && bValue == null) return 0
        if (aValue == null) return sort.direction === "asc" ? -1 : 1
        if (bValue == null) return sort.direction === "asc" ? 1 : -1

        if (typeof aValue === "string" && typeof bValue === "string") {
          const result = aValue.localeCompare(bValue)
          return sort.direction === "asc" ? result : -result
        }

        if (typeof aValue === "number" && typeof bValue === "number") {
          return sort.direction === "asc" ? aValue - bValue : bValue - aValue
        }

        if (aValue instanceof Date && bValue instanceof Date) {
          return sort.direction === "asc"
            ? aValue.getTime() - bValue.getTime()
            : bValue.getTime() - aValue.getTime()
        }

        const aStr = String(aValue)
        const bStr = String(bValue)
        const result = aStr.localeCompare(bStr)
        return sort.direction === "asc" ? result : -result
      })
    },
    [columns, getCellValue, sort.direction, sort.field],
  )

  const sortedData = useMemo(() => sortData(data), [sortData, data])

  const paginatedData = useMemo(() => {
    if (!pagination) {
      return sortedData
    }

    if (isServerSidePagination) {
      return sortedData
    }

    return paginationResult.getPageItems(sortedData)
  }, [sortedData, pagination, isServerSidePagination, paginationResult])

  function handleSort(column: TableColumn<TData>) {
    if (!column.sortable && sortable) return

    const newDirection: SortDirection =
      sort.field === column.key
        ? sort.direction === "asc"
          ? "desc"
          : sort.direction === "desc"
            ? "none"
            : "asc"
        : "asc"

    setSort({ field: column.key as string | null, direction: newDirection })
    onSort?.(column.key as string, newDirection)
  }

  function handleRowClick(row: TData, index: number) {
    if (clickable && onRowClick) {
      onRowClick(row, index)
    }
  }

  function getRowClassNames(row: TData, index: number) {
    const baseClass = clickable ? cn("cursor-pointer") : ""

    if (typeof rowClassName === "function") {
      return `${baseClass} ${rowClassName(row, index)}`.trim()
    }

    return `${baseClass} ${rowClassName}`.trim()
  }

  return {
    data,
    columns,
    className,
    rowClassName,
    headerRowClassName,
    headerClassName,
    sortable,
    clickable,
    emptyState,
    loading,
    stickyHeader,
    loadingRows,
    sort,
    sortedData: paginatedData,
    pagination: pagination
      ? {
          ...paginationOptions,
          ...paginationResult,
        }
      : undefined,
    maxHeight: stickyHeader ? "md" : maxHeight,
    setSort,
    getRowKey,
    onRowClick,
    onSort,
    getCellValue,
    sortData,
    handleSort,
    handleRowClick,
    getRowClassNames,
    ...props,
  }
}
