import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import DataTable, {
  column,
  type DataTableProps,
} from "@/components/base/data-table"

type TableProps<T = any> = Partial<DataTableProps<T>>

const setup = (props: TableProps = {}) => {
  const user = userEvent.setup()
  const component = render(<DataTable columns={[]} data={[]} {...props} />)

  // table
  const asyncEmpty = () => screen.getByLabelText("Empty data")
  const asyncTable = () => screen.getByLabelText("Data table")
  const getPagination = () => screen.getByLabelText("Pagination")

  return {
    component,
    user,
    empty: asyncEmpty,
    table: asyncTable,
    pagination: getPagination,
  }
}

describe("DataTable", () => {
  it("should render", () => {
    const { component } = setup()

    expect(component.container).toBeInTheDocument()
  })

  it("should render empty container", () => {
    const { empty } = setup()

    expect(empty()).toBeInTheDocument()
  })

  it("should render empty table with name column", () => {
    setup({
      data: [{}],
      columns: [column.text("name", "Name")],
      loading: true,
    })

    const nameCol = screen.getByLabelText("Name")

    expect(nameCol).toBeInTheDocument()
  })

  it("Should render single row", () => {
    const name = "John doe"

    setup({
      data: [{ name }],
      columns: [column.text("name", "Name")],
    })

    const nameColHeader = screen.getByLabelText("Name")
    const nameCol = screen.getByLabelText(name)

    expect(nameColHeader).toBeInTheDocument()
    expect(nameCol).toBeInTheDocument()
  })

  it("Should render table with pagination", () => {
    const { pagination } = setup({
      data: [{ name: "John Doe" }],
      columns: [column.text("name", "Name")],
      pagination: true,
    })

    expect(pagination()).toBeInTheDocument()
  })
})
