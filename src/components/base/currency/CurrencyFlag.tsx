import { useMemo } from "react"
import { Icon } from "@iconify/react"

import { cn } from "@/lib/utils"
import { currencyFlags } from "@/lib/constants/currency.constants"

interface CurrencyFlagProps {
  currency: string
  className?: string
  size?: "sm" | "md" | "lg"
}

export function CurrencyFlag({
  currency,
  className,
  size = "sm",
}: CurrencyFlagProps) {
  if (!currency) return null

  const icon = useMemo(() => {
    return Object.entries(currencyFlags).find(
      ([key, _]) => key === currency,
    )?.[1]
  }, [currency])

  if (!icon) return null

  return (
    <Icon
      className={cn(
        size === "sm" ? "h-4 w-4" : size === "md" ? "h-6 w-6" : "h-8 w-8",
        className,
      )}
      icon={icon}
    />
  )
}
