import { useState, useRef, useEffect, useMemo } from "react"

import { cn } from "@/lib/utils"
import { ICurrency } from "@/lib/constants/currency.constants"
import { useCurrenciesQuery } from "@/data/global/global.query"
import { Input } from "@/components/ui/input"

type CurrencyInputProps = Omit<
  React.ComponentProps<"input">,
  "value" | "onChange"
> & {
  value?: number | null
  onChange?: (value: number) => void
  className?: string
  currency: string
  placeholder?: string
}

interface CoreCurrencyInputProps {
  id?: string
  value?: number | null
  currencies?: ICurrency[]
  currency: string
  className?: string
  disabled?: boolean
  onChange: (value: number) => void
  placeholder?: string
}

export const CoreCurrencyInput = ({
  id,
  value,
  currencies,
  currency,
  className,
  disabled,
  onChange,
  placeholder,
  ...props
}: CoreCurrencyInputProps) => {
  const inputRef = useRef<HTMLInputElement>(null)

  const _currency = useMemo(
    () => currencies?.find((c) => c.code === currency),
    [currencies, currency],
  )

  const _decimals = useMemo(
    () => _currency?.amountDisplayFormat.split(".")[1]?.length || 0,
    [_currency],
  )

  const getFormattedValue = (value?: number | null) =>
    value?.toLocaleString("en-US", {
      minimumFractionDigits: _decimals,
      maximumFractionDigits: _decimals,
    }) ?? ""

  const [formattedValue, setFormattedValue] = useState(() =>
    getFormattedValue(value),
  )

  useEffect(() => {
    const decimalsCurrent = formattedValue.split(".")[1]?.length || 0
    if (decimalsCurrent !== _decimals) {
      setFormattedValue(getFormattedValue(value))
    }
  }, [_currency])

  useEffect(() => {
    setFormattedValue(getFormattedValue(value))
  }, [value])

  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    const decimalPosition = event.target.value.indexOf(".")
    if (decimalPosition !== -1) {
      event.target.setSelectionRange(0, decimalPosition)
    }
  }

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const input = event.target.value
    const cursorPosition = event.target.selectionStart || 0
    const previousValue = formattedValue

    const filteredInput = input.replace(/[^0-9\-+\.]/g, "")

    if (filteredInput === "") {
      setFormattedValue("")
      onChange?.(0)
      return
    }

    const noLeadingZeros = filteredInput.replace(/^0+([^0.]|$)/, "$1")

    const number = parseFloat(noLeadingZeros)
    if (isNaN(number)) {
      return
    }

    const formatted = getFormattedValue(number)

    const commasBeforeCursor = (
      previousValue.slice(0, cursorPosition).match(/,/g) || []
    ).length
    const newCommasBeforeCursor = (
      formatted.slice(0, cursorPosition).match(/,/g) || []
    ).length
    const cursorOffset = newCommasBeforeCursor - commasBeforeCursor

    if (onChange) {
      setFormattedValue(formatted)
      onChange(number)
    }

    requestAnimationFrame(() => {
      if (inputRef.current) {
        const newPosition = cursorPosition + cursorOffset
        inputRef.current.setSelectionRange(newPosition, newPosition)
      }
    })
  }

  return (
    <Input
      className={cn("", className)}
      disabled={!!disabled}
      id={id}
      onChange={handleChange}
      onFocus={handleFocus}
      placeholder={placeholder}
      ref={inputRef}
      maxLength={27}
      type="text"
      value={formattedValue}
      {...props}
    />
  )
}

export const CurrencyInput = ({
  id,
  value,
  onChange,
  className,
  currency,
  ...props
}: CurrencyInputProps) => {
  const {
    data: currencies,
    isLoading: isCurrencyLoading,
    isError: isCurrencyError,
  } = useCurrenciesQuery()

  return (
    <CoreCurrencyInput
      className={cn("", className)}
      currencies={currencies}
      currency={currency}
      disabled={isCurrencyLoading || isCurrencyError || props.disabled}
      id={id}
      onChange={(v) => onChange?.(v)}
      value={value}
      {...props}
    />
  )
}
