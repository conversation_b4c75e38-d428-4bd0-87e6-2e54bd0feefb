import { useMemo } from "react"

import { useCurrenciesQuery } from "@/data/global/global.query"

export interface CurrencyNameProps {
  currency: string
  className?: string
}

export function CurrencyName({ currency, className }: CurrencyNameProps) {
  const { data: currencies, isError, isLoading } = useCurrenciesQuery()

  const currencyName = useMemo(() => {
    return currencies?.find((c) => c.code === currency)?.description
  }, [currencies, currency])

  if (isLoading) return <span>Loading...</span>
  if (isError) return <span>Error</span>
  if (!currencyName) return <span>N/A</span>

  return <span className={className}>{currencyName}</span>
}
