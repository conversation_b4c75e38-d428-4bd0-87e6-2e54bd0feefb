import { useEffect, useState } from "react"
import { Check, ChevronDown, Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"
import { Currency } from "@/lib/constants/currency.constants"
import { useCurrenciesQuery } from "@/data/global/global.query"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { SimpleAutoComplete } from "@/components/base/form/autocomplete"

import { CurrencyFlag } from "./CurrencyFlag"

interface CurrencySelectorProps {
  currency: Currency
  onChange: (currency: Currency) => void
  buttonClassName?: string
  triggerClassName?: string
  itemClassName?: string
  className?: string
  disabled?: boolean
  availableCurrencies?: Currency[]
  variant?: "default" | "autocomplete" | "select"
}

export function CurrencySelector({
  currency,
  onChange,
  triggerClassName,
  buttonClassName,
  itemClassName,
  availableCurrencies,
  disabled = false,
  variant = "default",
}: CurrencySelectorProps) {
  const [open, setOpen] = useState(false)
  const [searchValue, setSearchValue] = useState("")
  const { data: currencies, isLoading, isError } = useCurrenciesQuery()

  // Reset search when popover closes
  useEffect(() => {
    if (!open) {
      setSearchValue("")
    }
  }, [open])

  if (isError) return <div>Error loading currencies</div>
  // Filter currencies if availableCurrencies is provided
  const _currencies =
    availableCurrencies && availableCurrencies.length > 0
      ? currencies?.filter((curr) =>
          availableCurrencies.includes(curr.code as Currency),
        ) || []
      : currencies || []

  if (variant === "autocomplete") {
    return (
      <SimpleAutoComplete<Currency>
        emptyMessage="No currency found"
        items={
          _currencies?.map((curr) => ({
            value: curr.code as Currency,
            label: `${curr.code} - ${curr.description}`,
            leftIcon: <CurrencyFlag currency={curr.code} />,
          })) ?? []
        }
        onSelectedValueChange={(value) => value && onChange(value)}
        placeholder="Select currency"
        selectedValue={currency}
        triggerClassName={cn("w-24 bg-background py-5", triggerClassName)}
      />
    )
  }

  if (variant === "select") {
    return (
      <Select onValueChange={onChange} value={currency}>
        <SelectTrigger
          className={cn("w-[120px] bg-background", triggerClassName)}
        >
          <SelectValue>
            <div className={cn("flex items-center gap-2", buttonClassName)}>
              <CurrencyFlag currency={currency} />
              {currency}
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {_currencies.map((_c) => (
              <SelectItem key={_c.code} value={_c.code}>
                <div className={cn("flex items-center gap-2", itemClassName)}>
                  <CurrencyFlag currency={_c.code} />
                  {_c.code}
                </div>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    )
  }

  return (
    <Popover onOpenChange={setOpen} open={open}>
      <PopoverTrigger asChild>
        <button
          disabled={disabled}
          className={cn(
            "flex h-full w-fit items-center gap-2 px-2",
            buttonClassName,
            `${disabled ? "" : "bg-background"}`,
          )}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <>
              <CurrencyFlag currency={currency} />
              {currency}
              <ChevronDown className="ml-4 h-4 w-4" />
            </>
          )}
        </button>
      </PopoverTrigger>
      <PopoverContent className="p-0">
        <Command className="w-full" shouldFilter={false}>
          <CommandInput
            placeholder="Search currency..."
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            <CommandEmpty>No currency found</CommandEmpty>
            <CommandGroup>
              {[
                // Selected currency first
                ..._currencies.filter(
                  (_c) =>
                    _c.code === currency &&
                    (!searchValue ||
                      _c.code
                        .toLowerCase()
                        .includes(searchValue.toLowerCase()) ||
                      _c.description
                        .toLowerCase()
                        .includes(searchValue.toLowerCase())),
                ),
                // All other currencies
                ..._currencies.filter(
                  (_c) =>
                    _c.code !== currency &&
                    (!searchValue ||
                      _c.code
                        .toLowerCase()
                        .includes(searchValue.toLowerCase()) ||
                      _c.description
                        .toLowerCase()
                        .includes(searchValue.toLowerCase())),
                ),
              ].map((_c) => (
                <CommandItem
                  key={_c.code}
                  keywords={[_c.code, _c.description]}
                  onSelect={(currentValue) => {
                    onChange?.(currentValue as Currency)
                    setOpen(false)
                  }}
                  value={_c.code}
                >
                  <CurrencyFlag currency={_c.code} />
                  <span className="flex flex-1 flex-col">
                    <span className="text-sm font-medium">{_c.code}</span>
                    <span className="text-xs text-muted-foreground">
                      {_c.description}
                    </span>
                  </span>
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      currency === _c.code ? "opacity-100" : "opacity-0",
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
