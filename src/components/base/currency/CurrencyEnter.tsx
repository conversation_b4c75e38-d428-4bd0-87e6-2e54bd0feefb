import { Input } from "@/components/ui/input"
import { useCurrenciesQuery } from "@/data/global/global.query"
import { Currency } from "@/lib/constants/currency.constants"
import { cn } from "@/lib/utils"
import { useMemo, useState, useRef, useEffect } from "react"

export interface CurrencyEnterProps {
  value?: number
  onChange: (value: number) => void
  currency: Currency
  placeholder?: string
  className?: string
  inputClassName?: string
  disabled?: boolean
}

export function CurrencyEnter({
  value,
  onChange,
  currency,
  placeholder,
  className,
  inputClassName,
  disabled = false,
}: CurrencyEnterProps) {
  const { data: currencies } = useCurrenciesQuery()
  const [isOverlayOpen, setIsOverlayOpen] = useState(false)
  const [overlayValue, setOverlayValue] = useState("")
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const _currency = useMemo(
    () => currencies?.find((c) => c.code === currency),
    [currencies, currency],
  )

  const _factor = useMemo(() => {
    return _currency?.factor || 100
  }, [_currency])

  const _decimals = useMemo(() => Math.log10(_factor), [_factor])

  // Calculate display value
  const displayValue = useMemo(() => {
    if (value === undefined) return ""
    const numberValue = value / _factor
    // Always show decimal places when not in edit mode, unless user has specified decimals
    const hasDecimals = numberValue % 1 !== 0
    return numberValue.toLocaleString("en-US", {
      minimumFractionDigits: isOverlayOpen
        ? hasDecimals
          ? _decimals
          : 0
        : _decimals,
      maximumFractionDigits: _decimals,
    })
  }, [value, _decimals, _factor, isOverlayOpen])

  // Handle click outside to close overlay
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        saveAndClose()
      }
    }

    if (isOverlayOpen) {
      document.addEventListener("mousedown", handleClickOutside)
      return () => document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOverlayOpen])

  // Focus input when overlay opens
  useEffect(() => {
    if (isOverlayOpen && inputRef.current) {
      inputRef.current.focus()
      // Remove the select() call to allow normal cursor positioning
    }
  }, [isOverlayOpen])

  const openOverlay = () => {
    setIsOverlayOpen(true)
    // Set overlay value based on current value
    if (value !== undefined) {
      const numberValue = value / _factor
      const hasDecimals = numberValue % 1 !== 0
      const displayValue = numberValue.toLocaleString("en-US", {
        minimumFractionDigits: hasDecimals ? _decimals : 0,
        maximumFractionDigits: _decimals,
      })
      setOverlayValue(displayValue.replace(/,/g, ""))
    } else {
      setOverlayValue("")
    }
  }

  const closeOverlay = () => {
    setIsOverlayOpen(false)
    setOverlayValue("")
  }

  function handleOverlayChange(event: React.ChangeEvent<HTMLInputElement>) {
    // only allow numbers, commas and 1 dot
    const value = event.target.value
    if (value.match(/^[0-9,.]*$/)) {
      setOverlayValue(value)
    }
  }

  function handleOverlayKeyDown(event: React.KeyboardEvent<HTMLInputElement>) {
    if (
      event.key === "Enter" ||
      event.key === "Tab" ||
      event.key === "Escape"
    ) {
      event.preventDefault()
      saveAndClose()
    }
  }

  function saveAndClose() {
    const currentInputValue = inputRef.current?.value ?? overlayValue
    const cleanValue = currentInputValue.replace(/,/g, "")

    // Allow empty values to clear the input
    if (cleanValue === "" || cleanValue === ".") {
      onChange(0)
      closeOverlay()
      return
    }

    const parsedValue = parseFloat(cleanValue)

    if (!isNaN(parsedValue)) {
      // Convert to small units if needed
      const finalValue = parsedValue * _factor
      onChange(finalValue)
    }
    closeOverlay()
  }

  return (
    <div
      ref={containerRef}
      className={cn("relative", className)}
      onClick={openOverlay}
    >
      <Input
        disabled={disabled}
        value={displayValue}
        placeholder={placeholder}
        readOnly={true}
        className={cn("w-full bg-background text-foreground", inputClassName)}
      />

      {/* Overlay input */}
      {isOverlayOpen && (
        <div className="absolute inset-0 z-10">
          <Input
            disabled={disabled}
            ref={inputRef}
            className={cn(
              "h-full bg-background text-foreground",
              inputClassName,
            )}
            value={overlayValue}
            onChange={handleOverlayChange}
            onKeyDown={handleOverlayKeyDown}
            onBlur={saveAndClose}
            placeholder={placeholder}
          />
        </div>
      )}
    </div>
  )
}
