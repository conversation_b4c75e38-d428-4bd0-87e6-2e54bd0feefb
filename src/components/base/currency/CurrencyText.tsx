import { useMemo } from "react"

import {
  parseCurrencyDigits,
  parseCurrencyDigitsWithoutDivision,
} from "@/lib/currency.utils"
import { useCurrenciesQuery } from "@/data/global/global.query"

type DisplayMode = "symbol" | "code" | "amount"

export interface CurrencyTextProps {
  amount: number | string
  currency: string
  className?: string
  dontDivide?: boolean
  displayModes?: DisplayMode[]
}

export function CurrencyText({
  amount,
  currency,
  className,
  dontDivide = false,
  displayModes = ["code", "amount"],
}: CurrencyTextProps) {
  const { data: currencies, isLoading, isError } = useCurrenciesQuery()

  const _currency = currencies?.find((c) => c.code === currency)

  function htmlDecode(input: string) {
    const doc = new DOMParser().parseFromString(input, "text/html")
    return doc.documentElement.textContent
  }

  const _symbol = useMemo(() => {
    if (_currency?.htmlEncodedSymbol) {
      return htmlDecode(_currency.htmlEncodedSymbol)
    }
    return _currency?.code
  }, [_currency])

  const _amount = useMemo(() => {
    if (_currency) {
      const decimals = Math.round(Math.log10(_currency.factor ?? 100))
      return dontDivide
        ? parseCurrencyDigitsWithoutDivision(amount, decimals)
        : parseCurrencyDigits(amount, decimals)
    }
    return amount?.toString()
  }, [amount, _currency])

  const displayText = useMemo(() => {
    const parts = []
    if (displayModes.includes("symbol") && _symbol) {
      parts.push(_symbol)
    }
    if (displayModes.includes("amount") && _amount) {
      parts.push(_amount)
    }
    if (displayModes.includes("code") && _currency?.code) {
      parts.push(` ${_currency.code}`)
    }
    return parts.join("")
  }, [_symbol, _amount, _currency?.code, displayModes])

  if (isLoading) return <span>...</span>
  if (isError) return <span>N/A</span>

  return <span className={className}>{displayText}</span>
}
