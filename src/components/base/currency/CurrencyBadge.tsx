import { cn } from "@/lib/utils"

import { CurrencyFlag } from "./CurrencyFlag"

interface CurrencyBadgeProps {
  currency: string
  className?: string
}

export function CurrencyBadge({ currency, className }: CurrencyBadgeProps) {
  return (
    <div
      className={cn(
        "flex items-center gap-1 rounded-full bg-muted px-2 py-1 text-xs",
        className,
      )}
    >
      <CurrencyFlag currency={currency} />
      {currency}
    </div>
  )
}
