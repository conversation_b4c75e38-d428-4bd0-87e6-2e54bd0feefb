import { DayPickerProps } from "react-day-picker"
import { useState } from "react"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"

import { cn } from "@/lib/utils"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"
interface DatePickerProps {
  value?: Date
  onChange: (date: Date | undefined) => void
  placeholder?: string
  className?: string
  calendarProps?: Omit<DayPickerProps, "mode" | "selected" | "onSelect">
}

export function DatePicker({
  value,
  onChange,
  placeholder = "Pick a date",
  className,
  calendarProps,
}: DatePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  function handleDateSelect(date: Date | undefined) {
    setIsOpen(false)
    onChange(date)
  }

  return (
    <Popover onOpenChange={setIsOpen} open={isOpen}>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            "flex w-full justify-between text-left font-normal",
            !value && "text-muted-foreground",
            className,
          )}
          variant={"outline"}
        >
          {value ? format(value, "PPP") : <span>{placeholder}</span>}
          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-auto p-0">
        <Calendar
          autoFocus
          mode="single"
          selected={value}
          {...calendarProps}
          onSelect={handleDateSelect}
        />
      </PopoverContent>
    </Popover>
  )
}
