import { useState } from "react"
import { Check, ChevronDown, Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Button } from "@/components/ui/button"

export interface ComboboxDropdownItem {
  id: string
  label: string
  subLabel?: string
  value: string
  icon?: React.ReactNode
  searchFields?: string[]
  [key: string]: any // Allow additional fields for searching
}

interface ComboboxDropdownProps {
  items: ComboboxDropdownItem[]
  value?: string
  onChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  loading?: boolean
  className?: string
  buttonClassName?: string
  disabled?: boolean
  getItemLabel?: (item: ComboboxDropdownItem) => string
  getItemSubLabel?: (item: ComboboxDropdownItem) => string | undefined
  searchFields?: string[]
}

export function ComboboxDropdown({
  items,
  value,
  onChange,
  placeholder = "Select an option",
  searchPlaceholder = "Search...",
  emptyMessage = "No results found",
  loading = false,
  className,
  buttonClassName,
  disabled = false,
  getItemLabel = (item) => item.label,
  getItemSubLabel = (item) => item.subLabel,
  searchFields = ["label"],
}: ComboboxDropdownProps) {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")
  const selectedItem = items.find((item) => item.value === value)

  const filteredItems = items.filter((item) => {
    if (!search) return true

    const searchLower = search.toLowerCase()
    return searchFields.some((field) => {
      const fieldValue = item[field]
      return (
        typeof fieldValue === "string" &&
        fieldValue.toLowerCase().includes(searchLower)
      )
    })
  })

  return (
    <div className={cn("relative w-full")}>
      <Popover onOpenChange={setOpen} open={open}>
        <PopoverTrigger asChild>
          <Button
            className={cn(
              "w-full justify-between rounded-lg py-5",
              "focus-visible:border-ring/20 focus-visible:outline-none focus-visible:ring-[0.5px] focus-visible:ring-ring/30",
              buttonClassName,
              className,
            )}
            disabled={disabled || loading}
            variant="outline"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <>
                <span className="flex-1 text-left">
                  {selectedItem ? getItemLabel(selectedItem) : placeholder}
                </span>
                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
          <Command className="w-full" shouldFilter={false}>
            <CommandInput
              onValueChange={setSearch}
              placeholder={searchPlaceholder}
              value={search}
            />
            <CommandList>
              <CommandEmpty>{emptyMessage}</CommandEmpty>
              <CommandGroup>
                {filteredItems.map((item) => (
                  <CommandItem
                    key={item.id}
                    onSelect={() => {
                      onChange(item.value)
                      setOpen(false)
                      setSearch("")
                    }}
                    value={item.value}
                  >
                    <div className="flex flex-col">
                      <span className="flex flex-1 items-center gap-2 text-sm font-medium">
                        {item.icon}
                        {getItemLabel(item)}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {getItemSubLabel(item)}
                      </span>
                    </div>
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === item.value ? "opacity-100" : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
