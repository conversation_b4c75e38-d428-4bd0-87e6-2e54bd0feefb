import { v4 } from "uuid"
import { useEffect, useMemo, useRef, useState } from "react"
import { Check, X } from "lucide-react"
import { Command as CommandPrimitive } from "cmdk"
import { Icon } from "@iconify/react"

import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Popover, PopoverAnchor, PopoverContent } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command"

type Props<T extends string> = {
  id?: string
  inputClassName?: string
  selectedValue: T | undefined
  onSelectedValueChange: (value: T | undefined) => void
  searchValue: string
  onSearchValueChange: (value: string) => void
  items: { value: T; label: string; leftIcon?: React.ReactNode }[]
  isLoading?: boolean
  emptyMessage?: string
  placeholder?: string
  disabled?: boolean
  "data-testid"?: string
  triggerClassName?: string
  showChevron?: boolean
  className?: string
}

function highlightMatch(text: string, query: string) {
  if (!query) return text
  const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
  const parts = text.split(new RegExp(`(${escapedQuery})`, "gi"))
  return parts.map((part, i) =>
    part.toLowerCase() === query.toLowerCase() ? (
      <span className="font-semibold" key={i}>
        {part}
      </span>
    ) : (
      part
    ),
  )
}

export function AutoComplete<T extends string>({
  id,
  inputClassName,
  selectedValue,
  onSelectedValueChange,
  searchValue,
  onSearchValueChange,
  items,
  isLoading,
  emptyMessage = "No items.",
  placeholder = "Search...",
  disabled = false,
  "data-testid": dataTestId,
  triggerClassName,
  showChevron = false,
  className,
}: Props<T>) {
  const [open, setOpen] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const labels = useMemo(
    () =>
      items.reduce(
        (acc, item) => {
          acc[item.value] = item.label
          return acc
        },
        {} as Record<string, string>,
      ),
    [items],
  )

  const openTime = useRef<number>(0)
  const uuid = useRef<string>(v4())
  const [isClearing, setIsClearing] = useState(false)

  useEffect(() => {
    if (open) {
      openTime.current = Date.now()
      setTimeout(() => {
        inputRef.current?.select()
      }, 300)
    }
  }, [open])

  useEffect(() => {
    onSearchValueChange(selectedValue ?? "")
  }, [selectedValue])

  const reset = () => {
    onSelectedValueChange(undefined as unknown as T)
    onSearchValueChange("")
  }

  const onInputBlur = (_e: React.FocusEvent<HTMLInputElement>) => {
    /* if (
      !e.relatedTarget?.hasAttribute("cmdk-list") &&
      labels[selectedValue ?? ""] !== searchValue
    ) {
      reset();
    }  */
    if (open) {
      setOpen(false)
    }
    if (selectedValue !== searchValue) {
      onSearchValueChange(selectedValue ?? "")
    }
    if (searchValue === "") {
      reset()
    }
  }

  const onSelectItem = (inputValue: string) => {
    if (inputValue === selectedValue) {
      reset()
    } else {
      onSelectedValueChange(inputValue as T)
      onSearchValueChange(labels[inputValue] ?? "")
    }
    setOpen(false)
  }

  function handleMouseDown(_e: React.MouseEvent<HTMLDivElement>) {
    if (!open) {
      setOpen(true)
    }
  }

  function handlePopoverOpenChange(o: boolean) {
    const now = Date.now()
    if (!o && now - openTime.current < 2000) return
    setOpen(o)
  }

  useEffect(() => {
    if (isClearing) {
      setIsClearing(false)
    }
  }, [selectedValue])

  return (
    <Popover onOpenChange={handlePopoverOpenChange} open={open}>
      <Command shouldFilter={false}>
        <PopoverAnchor asChild>
          <div
            className={cn(
              "relative flex items-center justify-between",
              className,
            )}
          >
            {selectedValue && selectedValue !== "" && !isClearing && (
              <div className="absolute inset-y-0 left-2 flex items-center">
                {items.find((i) => i.value === selectedValue)?.leftIcon &&
                  items.find((i) => i.value === selectedValue)?.leftIcon}
              </div>
            )}
            <div className="absolute inset-y-0 right-2 flex items-center py-5">
              {isLoading ? (
                <Icon className="animate-spin" icon="tabler:loader" />
              ) : (
                <>
                  {selectedValue && selectedValue == searchValue && (
                    <X
                      aria-label="Clear search"
                      className="h-4 w-4 cursor-pointer hover:text-destructive"
                      data-testid="clear-search"
                      onClick={(e) => {
                        e.stopPropagation()
                        setIsClearing(true)
                        reset()
                        setOpen(false)
                      }}
                    />
                  )}
                  {showChevron && (
                    <Icon
                      className={cn("ml-1 h-4 w-4", selectedValue && "ml-2")}
                      data-testid="dropdown-chevron"
                      icon="tabler:chevron-down"
                    />
                  )}
                </>
              )}
            </div>
            <Input
              autoComplete={`autocomplete-${uuid.current}`}
              className={cn(
                "py-5 pl-3 focus-visible:border-ring/15 focus-visible:outline-none focus-visible:ring-[0.3px] focus-visible:ring-ring/20",
                triggerClassName,
                inputClassName,
                className,
              )}
              data-testid={dataTestId}
              disabled={disabled}
              id={id}
              onBlur={onInputBlur}
              onChange={(e) => onSearchValueChange(e.target.value)}
              onKeyDown={(e) => setOpen(e.key !== "Escape")}
              onMouseDown={handleMouseDown}
              placeholder={placeholder}
              ref={inputRef}
              value={searchValue}
            />
          </div>
        </PopoverAnchor>
        {!open && <CommandList aria-hidden="true" className="hidden" />}
        <PopoverContent
          asChild
          className="w-auto min-w-[--radix-popover-trigger-width] p-0"
          onInteractOutside={(e) => {
            if (
              e.target instanceof Element &&
              e.target.hasAttribute("cmdk-input")
            ) {
              e.preventDefault()
            }
          }}
          onOpenAutoFocus={(e) => e.preventDefault()}
          onWheel={(e) => e.stopPropagation()}
        >
          <CommandList
            onWheel={(e) => {
              e.stopPropagation()
            }}
          >
            {isLoading && (
              <CommandPrimitive.Loading>
                <div className="p-1">
                  <Skeleton className="h-6 w-full" />
                </div>
              </CommandPrimitive.Loading>
            )}
            {items.length > 0 && !isLoading ? (
              <CommandGroup>
                {items.map((option) => (
                  <CommandItem
                    className={cn(
                      selectedValue === option.value &&
                        "bg-muted !text-primary",
                    )}
                    key={option.value}
                    onMouseDown={(e) => e.preventDefault()}
                    onSelect={onSelectItem}
                    value={option.value}
                  >
                    <div className="relative flex w-full items-center gap-2">
                      {option.leftIcon}
                      <div className="flex-grow">
                        {highlightMatch(option.label, searchValue)}
                      </div>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-0">
                        {selectedValue === option.value && (
                          <Check className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            ) : null}
            {!isLoading ? (
              <CommandEmpty>{emptyMessage ?? "No items."}</CommandEmpty>
            ) : null}
          </CommandList>
        </PopoverContent>
      </Command>
    </Popover>
  )
}

interface SimpleAutoCompleteProps<T extends string> {
  id?: string
  className?: string
  inputClassName?: string
  items: { value: T; label: string; leftIcon?: React.ReactNode }[]
  selectedValue: T | undefined
  onSelectedValueChange: (value: T | undefined) => void
  emptyMessage?: string
  placeholder?: string
  disabled?: boolean
  "data-testid"?: string
  triggerClassName?: string
  showChevron?: boolean
}

export function SimpleAutoComplete<T extends string>({
  id,
  className,
  inputClassName,
  items,
  onSelectedValueChange,
  selectedValue,
  emptyMessage,
  placeholder,
  disabled,
  "data-testid": dataTestId,
  triggerClassName,
  showChevron = false,
}: SimpleAutoCompleteProps<T>) {
  const [searchValue, setSearchValue] = useState<string>("")

  useEffect(() => {
    if (!selectedValue || selectedValue === "") {
      setSearchValue("")
    }
  }, [selectedValue])

  const handleSelectedValueChange = (value: T | undefined) => {
    if (!value || value === ("" as any)) {
      onSelectedValueChange(undefined)
      setSearchValue("")
    } else {
      onSelectedValueChange(value)
    }
  }

  const filteredItems = useMemo(() => {
    if (searchValue === "") return items

    if (searchValue === selectedValue) {
      const item = items.find((item) => item.value === selectedValue)
      const rest = items.filter((item) => item.value !== selectedValue)
      if (item) {
        return [item, ...rest]
      }
    }
    return items.filter((item) =>
      item.label.toLowerCase().includes(searchValue.toLowerCase()),
    )
  }, [items, searchValue])

  return (
    <AutoComplete
      className={className}
      data-testid={dataTestId}
      disabled={disabled}
      emptyMessage={emptyMessage}
      id={id}
      inputClassName={cn(inputClassName)}
      items={filteredItems}
      onSearchValueChange={setSearchValue}
      onSelectedValueChange={handleSelectedValueChange}
      placeholder={placeholder}
      searchValue={searchValue}
      selectedValue={selectedValue}
      showChevron={showChevron}
      triggerClassName={triggerClassName}
    />
  )
}
