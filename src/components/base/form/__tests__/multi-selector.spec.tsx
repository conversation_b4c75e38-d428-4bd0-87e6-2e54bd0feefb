import { vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, within } from "@testing-library/react"

import {
  MultiSelector,
  MultiSelectorProps,
} from "@/components/base/form/searchable-selector"

vi.mock("@iconify/react/dist/iconify.js", () => ({
  Icon: ({ className }: { className: string }) => (
    <span className={className} data-testid="mock-icon" />
  ),
}))

class ResizeObserverMock {
  observe() {}
  unobserve() {}
  disconnect() {}
}

global.ResizeObserver = ResizeObserverMock

const mockItems = [
  {
    value: "next.js",
    label: "Next.js",
  },
  {
    value: "sveltekit",
    label: "SvelteKit",
  },
  {
    value: "nuxt.js",
    label: "Nuxt.js",
  },
  {
    value: "remix",
    label: "Remix",
  },
  {
    value: "astro",
    label: "Astro",
  },
]

const setup = (componentProps?: Partial<MultiSelectorProps>) => {
  const user = userEvent.setup()
  const component = render(
    <MultiSelector items={mockItems} {...componentProps} />,
  )

  window.HTMLElement.prototype.scrollIntoView = vi.fn()
  window.HTMLElement.prototype.hasPointerCapture = vi.fn()

  const [trigger, onClickThenGetListbox] = [
    screen.getByRole("combobox", {
      name:
        componentProps?.Trigger?.["aria-label"] ??
        componentProps?.Trigger?.placeholder ??
        "Select item",
    }),
    () => screen.getByRole("listbox"),
  ]

  return {
    ...component,
    trigger,
    onClickThenGetListbox,
    user,
  }
}

describe("Multi-selector component", () => {
  beforeEach(() => {
    vi.spyOn(console, "log").mockImplementation(() => {})
  })

  it("should render multiselect component", () => {
    const { trigger } = setup()

    expect(trigger).toBeInTheDocument()
  })

  it("should render multiselect component with placeholder", () => {
    const { trigger } = setup({ Trigger: { placeholder: "This is a test" } })

    expect(trigger).toBeInTheDocument()
  })

  it("should open popover on input click", async () => {
    const { trigger, user, onClickThenGetListbox } = setup()

    await user.click(trigger)
    expect(onClickThenGetListbox()).toBeInTheDocument()
  })

  it("should handle item selection", async () => {
    const onSelectedItemsChange = vi.fn()

    const {
      trigger,
      user,
      onClickThenGetListbox: listBox,
    } = setup({ onSelectedItemsChange: onSelectedItemsChange })

    await user.click(trigger)

    const nextJsOption = within(listBox()).getByRole("option", {
      name: "Next.js",
    })

    expect(nextJsOption).toBeInTheDocument()

    await user.click(nextJsOption)

    expect(onSelectedItemsChange).toHaveBeenCalledWith(["next.js"])
  })
})
