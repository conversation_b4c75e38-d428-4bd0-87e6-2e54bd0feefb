import { vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import {
  FormLayout,
  FormLabel,
  FormButtons,
  FormFieldError,
  FormField,
} from "@/components/base/form/form"

vi.mock("@iconify/react/dist/iconify.js", () => ({
  Icon: ({ className }: { className: string }) => (
    <span className={className} data-testid="mock-icon" />
  ),
}))

vi.mock("@tanstack/react-store", () => ({
  useStore: (store: any, selector: any) => selector(store.get()),
}))

describe("Form Components", () => {
  beforeEach(() => {
    vi.spyOn(console, "log").mockImplementation(() => {})
  })

  describe("FormLayout", () => {
    it("should render with title and description", () => {
      render(
        <FormLayout className="custom-class" title="Test Form">
          <div>Content</div>
        </FormLayout>,
      )

      expect(screen.getByText("Test Form")).toBeInTheDocument()
      expect(screen.getByText("Content")).toBeInTheDocument()
    })
  })

  describe("FormLabel", () => {
    it("should render required label", () => {
      render(<FormLabel required>Required Field</FormLabel>)
      expect(screen.getByText("Required Field")).toBeInTheDocument()
    })

    it("should render optional label", () => {
      render(<FormLabel>Optional Field</FormLabel>)
      expect(screen.getByText("Optional Field")).toBeInTheDocument()
      expect(screen.getByText("(optional)")).toBeInTheDocument()
    })

    it("should render with loading state", () => {
      render(<FormLabel loading>Loading Field</FormLabel>)
      expect(screen.getByText("Loading Field")).toBeInTheDocument()
      expect(screen.getByTestId("mock-icon")).toHaveClass("animate-spin")
    })

    it("should render with header button", () => {
      render(
        <FormLabel headerButton={<button>Header Button</button>}>
          Field with Button
        </FormLabel>,
      )
      expect(screen.getByText("Header Button")).toBeInTheDocument()
    })
  })

  describe("FormButtons", () => {
    it("should render without form", () => {
      const onNext = vi.fn()
      const onCancel = vi.fn()
      const onSaveDraft = vi.fn()

      render(
        <FormButtons
          cancelButtonText="Go Back"
          isValid={true}
          nextButtonText="Continue"
          onCancel={onCancel}
          onNext={onNext}
          onSaveDraft={onSaveDraft}
          saveDraftButtonText="Save draft"
        />,
      )

      fireEvent.click(screen.getByText("Continue"))
      expect(onNext).toHaveBeenCalled()

      fireEvent.click(screen.getByText("Go Back"))
      expect(onCancel).toHaveBeenCalled()

      fireEvent.click(screen.getByText("Save draft"))
      expect(onSaveDraft).toHaveBeenCalled()
    })

    it("should render with form", () => {
      const mockForm = {
        Subscribe: ({ children }: { children: any }) =>
          children({
            isFieldsValidating: false,
            isFieldsValid: true,
            isValid: true,
          }),
        handleSubmit: vi.fn(),
      }

      render(
        <FormButtons
          form={mockForm as any}
          nextButtonText="Submit"
          onCancel={vi.fn()}
        />,
      )

      expect(screen.getByText("Submit")).toBeInTheDocument()
    })

    it("should handle form submission with validation", () => {
      const mockHandleSubmit = vi.fn()
      const mockForm = {
        Subscribe: ({ children }: { children: any }) =>
          children({
            isFieldsValidating: true, // Test validation state
            isFieldsValid: false,
            isValid: false,
          }),
        handleSubmit: mockHandleSubmit,
      }

      render(
        <FormButtons
          form={mockForm as any}
          nextButtonText="Submit"
          onCancel={vi.fn()}
        />,
      )

      const submitButton = screen.getByText("Submit")
      expect(submitButton).toBeDisabled()
    })

    it("should handle save draft with custom text", () => {
      const onSaveDraft = vi.fn()
      const mockForm = {
        Subscribe: ({ children }: { children: any }) =>
          children({
            isFieldsValidating: false,
            isFieldsValid: true,
            isValid: true,
          }),
        handleSubmit: vi.fn(),
      }

      render(
        <FormButtons
          form={mockForm as any}
          onSaveDraft={onSaveDraft}
          saveDraftButtonText="Custom Draft"
        />,
      )

      fireEvent.click(screen.getByText("Custom Draft"))
      expect(onSaveDraft).toHaveBeenCalled()
    })
  })

  describe("FormFieldError", () => {
    it("should render direct error", () => {
      render(<FormFieldError errors={["Direct error message"]} />)
      expect(screen.getByText("Direct error message")).toBeInTheDocument()
    })

    it("should render field error", () => {
      render(<FormFieldError errors={["Field error message"]} />)
      expect(screen.getByText("Field error message")).toBeInTheDocument()
    })

    it("should handle null field", () => {
      render(<FormFieldError errors={[]} />)
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument()
    })

    it("should handle empty errors array", () => {
      render(<FormFieldError errors={[]} />)
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument()
    })
  })

  describe("FormField", () => {
    it("should render with all props", () => {
      const mockField = {
        store: {
          get: () => ({
            meta: { errors: [] },
          }),
        },
      }

      render(
        <FormField
          field={mockField as any}
          headerButton={<button>Action</button>}
          label="Test Field"
          loading
          required
        >
          <input type="text" />
        </FormField>,
      )

      expect(screen.getByText("Test Field")).toBeInTheDocument()
      expect(screen.getByText("Action")).toBeInTheDocument()
      expect(screen.getByRole("textbox")).toBeInTheDocument()
    })

    it("should render without label or header button", () => {
      const mockField = {
        store: {
          get: () => ({
            meta: { errors: [] },
          }),
        },
      }

      render(
        <FormField field={mockField as any}>
          <input type="text" />
        </FormField>,
      )

      expect(screen.queryByRole("label")).not.toBeInTheDocument()
      expect(screen.getByRole("textbox")).toBeInTheDocument()
    })
  })
})
