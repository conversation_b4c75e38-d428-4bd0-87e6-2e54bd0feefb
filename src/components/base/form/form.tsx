import React from "react"
import { useStore } from "@tanstack/react-store"
import { <PERSON><PERSON><PERSON>, ReactFormExtendedApi } from "@tanstack/react-form"
import { QuestionMarkCircledIcon } from "@radix-ui/react-icons"
import { Icon } from "@iconify/react/dist/iconify.js"

import { cn } from "@/lib/utils"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

import { ShowChildren } from "../show-children"

interface FormLayoutProps {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
}

export function FormLayout({
  title,
  description,
  children,
  className,
}: FormLayoutProps) {
  return (
    <div
      className={cn(
        "mx-auto flex max-w-lg flex-col justify-center gap-2 pb-4",
        className,
      )}
    >
      <ShowChildren when={!!title}>
        <FormHeader description={description} title={title} />
      </ShowChildren>
      {children}
    </div>
  )
}

interface FormLabelProps {
  children: React.ReactNode
  required?: boolean
  headerButton?: React.ReactNode
  trailing?: React.ReactNode
  info?: React.ReactNode | string
  loading?: boolean
  "aria-label"?: string
  headerButtonClassName?: string
  labelClassName?: string
}

export function FormLabel({
  children,
  required,
  headerButton,
  trailing,
  loading,
  info,
  "aria-label": ariaLabel,
  headerButtonClassName,
  labelClassName,
}: FormLabelProps) {
  return (
    <div
      className={cn(
        "field-wrapper flex items-center",
        trailing && "justify-between",
      )}
    >
      <div className="flex items-center gap-2">
        <Label
          className={cn(
            "field-label mt-2 inline-flex items-center",
            labelClassName,
            // required && "after:ml-0.5 after:text-destructive after:content-['*']",
          )}
          htmlFor={ariaLabel}
        >
          {loading && (
            <Icon
              alignmentBaseline="baseline"
              className="me-2 animate-spin"
              icon="tabler:loader"
            />
          )}
          {children}
          {info && (
            <Tooltip>
              <TooltipTrigger asChild>
                <QuestionMarkCircledIcon className="ml-1 h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <span className="text-sm font-medium text-foreground">
                  {info}
                </span>
              </TooltipContent>
            </Tooltip>
          )}
          {!required && (
            <span className="font-normal text-muted-foreground">
              &nbsp;(optional)
            </span>
          )}
        </Label>
        <span
          className={cn("mt-2 flex items-center gap-2", headerButtonClassName)}
        >
          {headerButton}
        </span>
      </div>
      {trailing}
    </div>
  )
}

interface FormHeaderProps {
  title?: string
  description?: string
}

export function FormHeader({ title, description }: FormHeaderProps) {
  return (
    <div className="flex flex-col gap-2">
      {title && (
        <h3 className="text-2xl font-semibold text-foreground">{title}</h3>
      )}
      {description && (
        <p className="text-xs font-light text-muted-foreground">
          {description}
        </p>
      )}
    </div>
  )
}

interface FormButtonsProps {
  form?: ReactFormExtendedApi<any, any>
  isValid?: boolean
  onNext?: () => void
  onCancel?: () => void
  onSaveDraft?: () => void // Add onSaveDraft prop
  nextButtonText?: string
  cancelButtonText?: string
  saveDraftButtonText?: string // Add save draft button text
  isSubmitting?: boolean
  invertBtns?: boolean
  nextButtonProps?: React.ComponentProps<typeof Button>
  modal?: boolean // NEW PROP
}

export function FormButtons({
  form,
  isValid,
  onNext,
  onCancel,
  onSaveDraft,
  nextButtonText,
  cancelButtonText,
  saveDraftButtonText,
  nextButtonProps,
  invertBtns,
  modal = false, // NEW DEFAULT
}: FormButtonsProps) {
  if (!form)
    return (
      <div
        className={cn(
          "mt-4 flex gap-4",
          modal ? "justify-end" : "justify-start",
        )}
      >
        {" "}
        {/* align right if modal */}
        <Button
          disabled={!isValid}
          onClick={onNext}
          type="submit"
          {...nextButtonProps}
        >
          {nextButtonText ?? "Next"}
        </Button>
        {!modal && (
          <>
            {onSaveDraft && (
              <Button onClick={onSaveDraft} variant="outline">
                {saveDraftButtonText ?? "Save draft"}
              </Button>
            )}
            <Button
              className="underline"
              onClick={onCancel}
              type="button"
              variant="link-muted"
            >
              {cancelButtonText ?? "Cancel"}
            </Button>
          </>
        )}
      </div>
    )

  return (
    <form.Subscribe>
      {({ isFieldsValidating, isFieldsValid, isValid }) => (
        <div
          className={cn(
            "mt-4 flex gap-4",
            modal ? "justify-end" : "justify-start",
          )}
        >
          {" "}
          {/* align right if modal */}
          <Button
            className={cn(invertBtns && "order-2")}
            disabled={!isFieldsValid || !isValid || isFieldsValidating}
            onClick={onNext ?? form.handleSubmit}
            type="submit"
          >
            {nextButtonText ?? "Next"}
          </Button>
          {!modal && (
            <>
              {onSaveDraft && (
                <Button
                  className="w-1/3"
                  onClick={onSaveDraft}
                  type="button"
                  variant="outline"
                >
                  {saveDraftButtonText ?? "Save draft"}
                </Button>
              )}
              <Button
                className={cn("underline", invertBtns && "order-1")}
                onClick={onCancel}
                type="button"
                variant="link-muted"
              >
                {cancelButtonText ?? "Cancel"}
              </Button>
            </>
          )}
        </div>
      )}
    </form.Subscribe>
  )
}

interface FormFieldErrorProps {
  errors?: string[]
  "aria-label"?: string
}

export function FormFieldError({ errors, ...props }: FormFieldErrorProps) {
  function getError(error: string) {
    if (error.length > 90) return error.slice(0, 90) + "..."
    return error
  }
  if (errors && errors.length > 0)
    return (
      <span className="flex flex-col gap-1 text-xs text-destructive" {...props}>
        {errors.map((error, i) =>
          error.length > 90 ? (
            <Tooltip key={i}>
              <TooltipTrigger asChild>
                <span key={error}>{getError(error)}</span>
              </TooltipTrigger>
              <TooltipContent className="border-1 max-w-md border-border bg-background p-2 text-foreground shadow-md">
                <span className="text-sm font-medium text-destructive">
                  (Error)
                </span>
                <span className="ms-2 font-medium text-foreground">
                  {error}
                </span>
              </TooltipContent>
            </Tooltip>
          ) : (
            <span key={error}>{error}</span>
          ),
        )}
      </span>
    )

  return null
}

export interface FormFieldProps<T> {
  field: FieldApi<T, any, any, any>
  required?: boolean
  label?: string | React.ReactNode
  children: React.ReactNode
  headerButton?: React.ReactNode
  headerButtonClassName?: string
  labelClassName?: string
  loading?: boolean
  trailing?: React.ReactNode
  info?: React.ReactNode | string
  "aria-label"?: string
}

export function FormField<T>({
  field,
  required,
  children,
  label,
  trailing,
  info,
  headerButton,
  headerButtonClassName,
  labelClassName,
  loading,
  "aria-label": arialLabel,
}: FormFieldProps<T>) {
  const errors = useStore(
    field?.store,
    (state) => (state.meta.errors ?? []) as string[],
  )

  const styledChildren = React.cloneElement(children as React.ReactElement, {
    className: cn(
      (children as React.ReactElement).props.className,
      errors.length > 0 && "border-destructive ring-destructive",
    ),
  })

  return (
    <>
      {(label || headerButton) && (
        <FormLabel
          aria-label={arialLabel}
          headerButton={headerButton}
          headerButtonClassName={headerButtonClassName}
          info={info}
          labelClassName={labelClassName}
          loading={loading}
          required={required}
          trailing={trailing}
        >
          {label}
        </FormLabel>
      )}

      {styledChildren}

      <FormFieldError aria-label={`${label} error`} errors={errors} />
    </>
  )
}
