import { PopoverContent } from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandList,
} from "@/components/ui/command"

import type * as Types from "../types"

interface ContentProps
  extends Pick<Types.CoreContentProps, "Input" | "notFoundText"> {
  children?: React.ReactNode
}

export function Content(props: ContentProps) {
  return (
    <PopoverContent className="w-[--radix-popper-anchor-width] overflow-hidden rounded-xl p-0">
      <Command>
        <CommandInput placeholder={props?.Input?.placeholder ?? "Search..."} />
        <CommandEmpty>{props?.notFoundText ?? "Not found"}</CommandEmpty>
        <CommandGroup>
          <CommandList>{props.children}</CommandList>
        </CommandGroup>
      </Command>
    </PopoverContent>
  )
}
