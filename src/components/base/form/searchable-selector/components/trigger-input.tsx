import { ChevronsUpDown } from "lucide-react"

import { plural } from "@/lib/pluralize"
import { PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "@/components/ui/button"

import type * as Types from "../types"

interface TriggerInputProps
  extends Omit<Types.CoreTriggerInputProps, "items" | "onChange"> {
  children?: React.ReactNode
}

export function TriggerInput({
  id,
  open,
  icon,
  selectedItems,
  placeholder,
  itemName,
  "aria-label": ariaLabel,
  children,
  disabled,
}: TriggerInputProps) {
  const pluralized = plural(itemName || "item", selectedItems?.length)

  return (
    <PopoverTrigger asChild>
      <div
        className={`relative flex-col ${disabled ? "cursor-not-allowed" : ""}`}
      >
        <Button
          aria-expanded={open}
          aria-label={ariaLabel ?? placeholder ?? "Select item"}
          className="z-[1] h-11 w-full items-center justify-between rounded-xl py-6"
          disabled={disabled}
          id={id}
          role="combobox"
          variant="outline"
        >
          {selectedItems?.length > 0 ? (
            <span className="pointer-events-none z-[1]">{`${selectedItems?.length} ${pluralized}`}</span>
          ) : (
            <span className="pointer-events-none z-[1] text-slate-700/50">
              {placeholder ?? "Select item..."}
            </span>
          )}
          {icon ?? (
            <ChevronsUpDown className="order-2 ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>

        {children}
      </div>
    </PopoverTrigger>
  )
}
