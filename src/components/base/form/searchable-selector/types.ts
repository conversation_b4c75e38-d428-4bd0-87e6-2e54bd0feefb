export interface CoreItem {
  value: string
  label: string
}

export interface CoreBase {
  items: CoreItem[]
  selectedItems: string[]
  onChange: (selectedItem: string) => void
}

export interface CoreTooltipProps {
  triggerClassName?: string
  contentClassName?: string
}

export interface CoreTriggerInputProps extends CoreBase {
  id?: string
  open?: boolean
  icon?: React.ReactNode
  placeholder?: string
  itemName?: string
  Tooltip?: CoreTooltipProps
  "aria-label"?: string
  disabled?: boolean
}

export interface InputProps {
  placeholder?: string
}

export interface CoreContentProps extends CoreBase {
  Input?: InputProps
  notFoundText?: string
}
