import { useEffect } from "react"
import { Check, X } from "lucide-react"

import { cn } from "@/lib/utils"
import { useMultiSelector } from "@/hooks/use-multi-selector"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Popover } from "@/components/ui/popover"
import { CommandItem } from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"

import type * as Types from "./types"

import { TriggerInput as CoreTriggerInput } from "./components/trigger-input"
import { Content as CoreContent } from "./components/content"
import { ShowChildren } from "../../show-children"

function TriggerInput(props: Types.CoreTriggerInputProps) {
  return (
    <CoreTriggerInput {...props}>
      <div className="relative flex-col">
        <div
          aria-label="Selected items"
          className="mt-3 flex w-[calc(100%-50px)] flex-wrap items-center justify-start gap-x-2"
        >
          <ShowChildren when={props.selectedItems?.length > 0}>
            {props.selectedItems.map((coreItem) => (
              <Badge
                className="z-[2] mb-2 ml-1 flex items-center justify-center rounded-full border border-slate-400/50 px-1 py-0.5 pl-2"
                key={coreItem as string}
                variant="secondary"
              >
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          "inline-flex items-center",
                          props.Tooltip?.triggerClassName,
                        )}
                      >
                        <span className="overflow-hidden text-ellipsis whitespace-nowrap font-normal text-slate-500">
                          {
                            props.items.find((item) => item.value === coreItem)
                              ?.label
                          }
                        </span>
                        <X
                          className={cn(
                            "relative -mr-0 ml-1 size-3 min-w-3 cursor-pointer text-slate-400 hover:text-primary",
                            props?.disabled ? "cursor-not-allowed" : "",
                          )}
                          onClick={(e) => {
                            e.preventDefault()
                            if (!props.disabled) props.onChange(coreItem)
                          }}
                          strokeWidth={3}
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      align="start"
                      className={cn(props.Tooltip?.contentClassName)}
                      data-side="bottom"
                      side="top"
                    >
                      {
                        props.items.find((item) => item.value === coreItem)
                          ?.label
                      }
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Badge>
            ))}
          </ShowChildren>
        </div>
      </div>
    </CoreTriggerInput>
  )
}

function Content(props: Types.CoreContentProps) {
  return (
    <CoreContent {...props}>
      {props.items.map((item) => (
        <CommandItem
          className="rounded-lg"
          key={item.value}
          onSelect={() => props.onChange(item.value)}
          value={item.label}
        >
          <Check
            className={cn(
              "mr-2 h-4 w-4 opacity-0",
              props.selectedItems.includes(item.value) && "opacity-100",
            )}
          />
          {item.label}
        </CommandItem>
      ))}
    </CoreContent>
  )
}

export interface MultiSelectorProps {
  items: Types.CoreItem[]
  selectedItems?: string[]
  Trigger?: Partial<Types.CoreTriggerInputProps>
  Content?: Partial<Types.CoreContentProps>
  onSelectedItemsChange?: (selectItems: string[]) => void
  disabled?: boolean
}

export function MultiSelector({
  Trigger: TriggerProps,
  Content: ContentProps,
  disabled,
  ...props
}: MultiSelectorProps) {
  const {
    isOpen,
    toggle: { on, off },
    selectItems,
    handleSelectedValue,
    setSelectItems,
  } = useMultiSelector(props.selectedItems)

  useEffect(() => {
    setSelectItems(props.selectedItems ?? [])
  }, [props.selectedItems, setSelectItems])

  const handleChange = (selectedItem: string) => {
    handleSelectedValue((prevItems) => {
      const newItems = prevItems.includes(selectedItem)
        ? prevItems.filter((item) => item !== selectedItem) // Remove item
        : [...prevItems, selectedItem] // Add item

      props.onSelectedItemsChange?.(newItems) // Send updated state to parent immediately

      return newItems
    })
  }

  return (
    <Popover
      onOpenChange={(isOpen) => (isOpen ? (!disabled ? on() : off()) : off())}
      open={isOpen}
    >
      <TriggerInput
        disabled={disabled}
        items={props.items}
        onChange={handleChange}
        open={isOpen}
        selectedItems={selectItems as string[]}
        {...TriggerProps}
      />

      <Content
        items={props.items}
        onChange={handleChange}
        selectedItems={selectItems}
        {...ContentProps}
      />
    </Popover>
  )
}
