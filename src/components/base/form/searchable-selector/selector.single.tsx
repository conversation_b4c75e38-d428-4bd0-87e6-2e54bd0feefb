import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { useToggle } from "@/hooks/use-toggle"
import { Popover, PopoverTrigger } from "@/components/ui/popover"
import { CommandItem } from "@/components/ui/command"
import { Button } from "@/components/ui/button"

import type * as Types from "./types"

import { Content as CoreContent } from "./components/content"

interface Base
  extends Omit<Types.CoreBase, "selectedItem" | "selectedItems" | "onChange"> {
  selectedItem: Types.CoreItem | undefined
}

interface TriggerInputProps
  extends Omit<
      Types.CoreTriggerInputProps,
      "selectedItems" | "onChange" | "items"
    >,
    Omit<Base, "items" | "onChange" | "selectedItem"> {
  selectedItem?: Types.CoreItem
  disabled?: boolean
}

function TriggerInput({
  id,
  open,
  icon,
  selectedItem,
  placeholder,
  disabled,
  "aria-label": ariaLabel,
}: TriggerInputProps) {
  return (
    <PopoverTrigger asChild>
      <div
        className={`relative flex-col ${disabled ? "cursor-not-allowed" : ""}`}
      >
        <Button
          aria-expanded={open}
          aria-label={ariaLabel ?? placeholder ?? "Select item"}
          className="z-[1] h-11 w-full items-center justify-between rounded-xl py-6"
          disabled={disabled}
          id={id}
          role="combobox"
          variant="outline"
        >
          {selectedItem?.label ? (
            <span className="pointer-events-none z-[1]">{`${selectedItem?.label}`}</span>
          ) : (
            <span className="pointer-events-none z-[1] text-slate-700/50">
              {placeholder ?? "Select item..."}
            </span>
          )}
          {icon ?? (
            <ChevronsUpDown className="order-2 ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </div>
    </PopoverTrigger>
  )
}

interface ContentProps
  extends Omit<Types.CoreContentProps, "selectedItems" | "onChange">,
    Omit<Base, "selectedItems"> {
  onChange: (selectedItem: Types.CoreItem) => void
}

function Content(props: ContentProps) {
  return (
    <CoreContent {...props}>
      {props.items.map((item) => (
        <CommandItem
          className={cn(
            "flex justify-between rounded-lg",
            props.selectedItem?.value == item.value && "bg-muted",
          )}
          key={item.value}
          onSelect={() => props.onChange(item)}
          value={item.label}
        >
          {item.label}
          <Check
            className={cn(
              "mr-2 h-4 w-4 opacity-0",
              props.selectedItem?.value == item.value && "opacity-100",
            )}
          />
        </CommandItem>
      ))}
    </CoreContent>
  )
}

export interface SingleSelectorProps extends Base {
  Trigger?: TriggerInputProps
  Content?: ContentProps
  onSelectedItemsChange?: (selectItems: Types.CoreItem) => void
  disabled?: boolean
}

export function SingleSelector({
  Trigger: TriggerProps,
  Content: ContentProps,
  disabled,
  ...props
}: SingleSelectorProps) {
  const [isOpen, toggle] = useToggle()
  const { on, off } = toggle

  const handleChange = (selectedItem: Types.CoreItem) => {
    props.onSelectedItemsChange?.(selectedItem)
    off()
  }

  return (
    <Popover
      onOpenChange={(isOpen) => (isOpen ? (!disabled ? on() : off()) : off())}
      open={isOpen}
    >
      <TriggerInput
        disabled={disabled}
        open={isOpen}
        selectedItem={props.selectedItem}
        {...TriggerProps}
      />

      <Content
        items={props.items}
        onChange={handleChange}
        selectedItem={props.selectedItem}
        {...ContentProps}
      />
    </Popover>
  )
}
