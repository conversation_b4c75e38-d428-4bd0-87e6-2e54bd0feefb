import React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { useToggle } from "@/hooks/use-toggle"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Button } from "@/components/ui/button"

interface Item {
  value: string
  label: string
}

interface Base {
  items: Item[]
  selectedItem: Item | undefined
  onChange: (selectedItem: Item) => void
}

interface TriggerInputProps {
  id?: string
  open?: boolean
  icon?: React.ReactNode
  placeholder?: string
  "aria-label"?: string
  selectedItem?: Item
}

interface InputProps {
  placeholder?: string
}

interface ContentProps extends Base {
  Input?: InputProps
  notFoundText?: string
}

function TriggerInput({
  id,
  open,
  icon,
  selectedItem,
  placeholder,
  "aria-label": ariaLabel,
}: TriggerInputProps) {
  return (
    <PopoverTrigger asChild>
      <div className="relative flex-col">
        <Button
          aria-expanded={open}
          aria-label={ariaLabel ?? placeholder ?? "Select item"}
          className="z-[1] h-11 w-full items-center justify-between rounded-xl py-6"
          id={id}
          role="combobox"
          variant="outline"
        >
          {selectedItem?.label ? (
            <span className="pointer-events-none z-[1]">{`${selectedItem?.label}`}</span>
          ) : (
            <span className="pointer-events-none z-[1] text-slate-700/50">
              {placeholder ?? "Select item..."}
            </span>
          )}
          {icon ?? (
            <ChevronsUpDown className="order-2 ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </div>
    </PopoverTrigger>
  )
}

function Content(props: ContentProps) {
  return (
    <PopoverContent className="w-[--radix-popper-anchor-width] overflow-hidden rounded-xl p-0">
      <Command>
        <CommandInput placeholder={props?.Input?.placeholder ?? "Search..."} />
        <CommandEmpty>{props?.notFoundText ?? "Not found"}</CommandEmpty>
        <CommandGroup>
          <CommandList>
            {props.items.map((item) => (
              <CommandItem
                className={cn(
                  "flex justify-between rounded-lg",
                  props.selectedItem?.value == item.value && "bg-muted",
                )}
                key={item.value}
                onSelect={() => props.onChange(item)}
                value={item.label}
              >
                {item.label}
                <Check
                  className={cn(
                    "mr-2 h-4 w-4 opacity-0",
                    props.selectedItem?.value == item.value && "opacity-100",
                  )}
                />
              </CommandItem>
            ))}
          </CommandList>
        </CommandGroup>
      </Command>
    </PopoverContent>
  )
}

export interface SingleSelectorProps<T> {
  items: T[]
  selectedItem?: T | undefined
  Trigger?: TriggerInputProps
  Content?: ContentProps
  onSelectedItemsChange?: (selectItems: Item) => void
}

export function SingleSelector<T extends Item>({
  Trigger: TriggerProps,
  Content: ContentProps,
  ...props
}: SingleSelectorProps<T>) {
  const [isOpen, toggle] = useToggle()
  const { on, off } = toggle

  const handleChange = (selectedItem: Item) => {
    props.onSelectedItemsChange?.(selectedItem)
    off()
  }

  return (
    <Popover onOpenChange={(isOpen) => (isOpen ? on() : off())} open={isOpen}>
      <TriggerInput
        open={isOpen}
        selectedItem={props.selectedItem}
        {...TriggerProps}
      />

      <Content
        items={props.items}
        onChange={handleChange}
        selectedItem={props.selectedItem}
        {...ContentProps}
      />
    </Popover>
  )
}
