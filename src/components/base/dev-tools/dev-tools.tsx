import { CogIcon } from "lucide-react"

import { UseAuth } from "@/hooks/use-auth"
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>itle,
  SheetHeader,
  SheetDescription,
  SheetTrigger,
} from "@/components/ui/sheet"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"

import { copyToClipboard } from "./dev-tools.utils"
import { useGetTokenClaims } from "./dev-tools.hooks"

const TIME_EPOCH = 1000

function ExpiryDate({ exp }: { exp?: number }) {
  const expTime = exp ? new Date(exp * TIME_EPOCH).toDateString() : null

  return <>{expTime}</>
}

export default function DevTools({ auth }: { auth: UseAuth }) {
  const { claims, token } = useGetTokenClaims(auth)

  return (
    <Sheet>
      <SheetTrigger className="fixed bottom-4 left-4 z-10 flex size-12 items-center justify-center rounded-full bg-white text-slate-800 shadow-xl ring ring-slate-300/20 hover:bg-slate-950/50 hover:text-white hover:ring-slate-800 [&_svg]:size-6">
        <CogIcon />
      </SheetTrigger>

      <SheetContent side="bottom">
        <SheetHeader>
          <SheetTitle className="font-black">Control panel</SheetTitle>
          <SheetDescription>
            This is the developer tool that is solely used to make developers
            life easy. It provides current user details, API mocking, feature
            flagging...
          </SheetDescription>

          <Card>
            <CardHeader>
              <CardTitle>User</CardTitle>
              <CardDescription className="flex flex-col">
                <div className="">
                  <b>id:</b> {auth.user?.sub}
                  <br />
                  <b>Name:</b> {auth.user?.name}
                  <br />
                  <b>Email:</b> {auth.user?.email}
                  <br />
                  <b>Expires:</b> {<ExpiryDate exp={claims?.exp} />}
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                className="bg-foreground text-primary-foreground hover:bg-foreground/90"
                onClick={() =>
                  copyToClipboard(token ?? "", "Token copied to clipboard")
                }
              >
                Copy bearer token
              </Button>
            </CardContent>
          </Card>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  )
}
