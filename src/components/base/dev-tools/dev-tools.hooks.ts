import { useEffect, useState } from "react"
import { IdToken } from "@auth0/auth0-react"

import { UseAuth } from "@/hooks/use-auth"

export function useGetTokenClaims({
  getIdTokenClaims,
  getAccessTokenSilently,
}: UseAuth) {
  const [claims, setClaims] = useState<IdToken>()
  const [token, setToken] = useState<string>()

  useEffect(() => {
    async function getClaimsAndToken() {
      const claims = await getIdTokenClaims()
      const token = await getAccessTokenSilently()

      if (claims) setClaims(claims)
      if (token) setToken(token as unknown as string)
    }

    getClaimsAndToken()
  }, [])

  return { claims, token }
}
