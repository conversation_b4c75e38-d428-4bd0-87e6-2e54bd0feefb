import React, { forwardRef, useImperativeHandle } from "react"
import { UploadIcon } from "@radix-ui/react-icons"

import { cn } from "@/lib/utils"

interface FileUploadAreaProps extends React.HTMLAttributes<HTMLDivElement> {
  onFileSelect: (file: File) => void
  acceptedFileTypes?: string[] | "*"
  maxFileSize?: number // in MB
  label?: string
  description?: string
  ref?: React.RefObject<any>
}

export const FileUploadArea = forwardRef<any, FileUploadAreaProps>(
  (
    {
      onFileSelect,
      acceptedFileTypes = "*",
      maxFileSize = 5,
      className,
      label = "Drag and drop file or",
      description,
      ...props
    },
    ref,
  ) => {
    const [isDragging, setIsDragging] = React.useState(false)
    const fileInputRef = React.useRef<HTMLInputElement>(null)

    useImperativeHandle(ref, () => ({
      reset: () => {
        if (fileInputRef.current) {
          fileInputRef.current.value = ""
        }
      },
    }))

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      setIsDragging(true)
    }

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      setIsDragging(false)
    }

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      setIsDragging(false)

      const files = e.dataTransfer.files
      if (files.length) {
        validateAndProcessFile(files[0])
      }
    }

    const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files

      if (files?.length) {
        validateAndProcessFile(files[0])
      }
    }

    const validateAndProcessFile = (file: File) => {
      // Check file type
      if (acceptedFileTypes !== "*" && !acceptedFileTypes.includes(file.type)) {
        alert("Please upload a valid file type")
        return
      }

      // Check file size
      if (file.size > maxFileSize * 1024 * 1024) {
        alert(`File size must be less than ${maxFileSize}MB`)
        return
      }

      onFileSelect(file)
    }

    return (
      <div
        className={cn(
          "relative rounded-lg border-2 border-dashed bg-background p-4 shadow-sm",
          "transition-colors hover:border-primary/50",
          isDragging ? "border-primary bg-primary/5" : "border-border",
          className,
        )}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        {...props}
      >
        <input
          accept={acceptedFileTypes === "*" ? "*" : acceptedFileTypes.join(",")}
          className="hidden"
          onChange={handleFileInput}
          ref={fileInputRef}
          type="file"
        />

        <div className="flex flex-col items-center justify-center gap-2 text-center">
          <UploadIcon className="h-10 w-10 text-muted-foreground" />
          <div className="text-lg">
            {label}
            &nbsp;
            <button
              className="text-primary hover:underline"
              onClick={() => fileInputRef.current?.click()}
            >
              browse
            </button>
          </div>
          <div className="text-sm text-muted-foreground">{description}</div>
          <div className="text-sm text-muted-foreground">
            Max file size: {maxFileSize}MB
          </div>
        </div>
      </div>
    )
  },
)
