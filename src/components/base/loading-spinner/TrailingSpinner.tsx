import { Icon } from "@iconify/react"

import { cn } from "@/lib/utils"

interface TrailingSpinnerProps {
  className?: string
  children: React.ReactNode
  isLoading?: boolean
}

export function TrailingSpinner({
  className,
  children,
  isLoading,
}: TrailingSpinnerProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          <Icon className="animate-spin" icon="tabler:loader" />
        </div>
      )}
    </div>
  )
}
