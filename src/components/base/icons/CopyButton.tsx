import { Copy } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { toast } from "sonner"

const MAX_COPY_SHORTEN = 20

export function CopyButton({ text, label }: { text: string; label: string }) {
  function copyToClipboard(text: string, label: string) {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard`, {
      description:
        text.length > MAX_COPY_SHORTEN
          ? text.slice(0, MAX_COPY_SHORTEN) + "..."
          : text,
      icon: "📋",
      duration: 1000,
    })
  }
  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-4 w-5"
      onClick={() => copyToClipboard(text, label)}
    >
      <Copy className="h-3 w-3" />
    </Button>
  )
}
