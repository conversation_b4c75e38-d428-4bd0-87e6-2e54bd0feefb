import { createElement, Fragment } from "react"
import { LucideIcon, MoreVertical } from "lucide-react"
import { Link, LinkProps } from "@tanstack/react-router"

import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

type MenuItemType = "link" | "button" | "label"

export interface MenuDotsDropdownItem {
  type: MenuItemType
  label: string
  icon: React.ReactNode | LucideIcon
  onClick?: () => void
  linkProps?: LinkProps
}

export interface MenuDotsDropdownProps {
  className?: string
  items: MenuDotsDropdownItem[]
}

export function MenuDotsDropdown({ items, className }: MenuDotsDropdownProps) {
  return (
    <div
      className={cn("flex flex-none items-center justify-center", className)}
    >
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            className="text-muted-foreground transition-colors hover:text-primary"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreVertical className="h-5 w-5" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {items.map((item, i) => (
            <Fragment key={i}>
              {item.type === "link" && (
                <Link
                  params={item.linkProps?.params}
                  search={item.linkProps?.search}
                  to={item.linkProps?.to}
                >
                  <DropdownMenuItem className="cursor-pointer">
                    {item.icon &&
                      createElement(item.icon as any, {
                        className: "h-4 w-4 mr-2",
                      })}
                    {item.label}
                  </DropdownMenuItem>
                </Link>
              )}
              {item.type === "button" && (
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={item.onClick}
                >
                  {item.icon &&
                    createElement(item.icon as any, {
                      className: "h-4 w-4 mr-2",
                    })}
                  {item.label}
                </DropdownMenuItem>
              )}
              {item.type === "label" && (
                <DropdownMenuItem>{item.label}</DropdownMenuItem>
              )}
            </Fragment>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
