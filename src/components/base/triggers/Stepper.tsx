import React, { Fragment, ReactNode, ReactElement } from "react"
import { ArrowLeftIcon, CheckIcon, ChevronRightIcon } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

import { cn } from "@/lib/utils"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button, ButtonProps } from "@/components/ui/button"
import { ShowChildren } from "../show-children"

interface StepProps extends ButtonProps {
  title?: string
  children?: ReactNode
  status?: "current" | "completed" | "upcoming" | "incomplete"
}

export const Step = ({ children }: StepProps) => {
  return <>{children}</>
}

interface StepperProps {
  children: ReactNode[]
  onStepChange?: (step: number) => void
  value: number
  variant?: "horizontal" | "vertical"
  modal?: boolean
  className?: string
  buttonClassName?: string
  showHeader?: boolean
  buttonTextSize?: string
}

export const Stepper = ({
  children,
  onStepChange,
  value,
  variant = "horizontal",
  modal = false,
  className,
  buttonClassName,
  showHeader = true,
  buttonTextSize,
}: StepperProps) => {
  const steps = React.useMemo(
    () =>
      React.Children.toArray(children).filter(
        (child) => React.isValidElement(child) && child.type === Step,
      ) as ReactElement<StepProps>[],
    [children],
  )

  const prevValue = React.useRef(value)

  React.useEffect(() => {
    prevValue.current = value
  }, [value])

  if (variant === "horizontal") {
    return (
      <div className={cn("space-y-4", className)}>
        <ShowChildren when={showHeader}>
          <StepperHeaderHorizontal
            currentStep={value}
            onStepChange={onStepChange}
            steps={steps.map((step) => step.props)}
            buttonTextSize={buttonTextSize}
          />
        </ShowChildren>

        <StepperContentHorizontal
          from={prevValue.current}
          modal={modal}
          onStepChange={onStepChange}
          to={value}
          buttonTextSize={buttonTextSize}
        >
          {steps}
        </StepperContentHorizontal>
      </div>
    )
  }

  return (
    <>
      <ShowChildren when={showHeader}>
        <div
          aria-label="Stepper steps"
          className="fixed flex w-80 flex-col items-start gap-y-4"
        >
          {steps.map((step, index) => (
            <StepperButton
              className={cn("h-fit px-3 py-3", buttonClassName)}
              index={index}
              key={index}
              onStepChange={onStepChange}
              status={
                step.props.status ??
                (index < value
                  ? "completed"
                  : index === value
                    ? "current"
                    : "upcoming")
              }
              step={step.props}
            />
          ))}
        </div>
      </ShowChildren>

      <div aria-label="Stepper content" className="ml-80 w-[40rem]">
        {steps.map((step, index) => (
          <AnimatePresence key={index} mode="wait">
            {index === value && (
              <motion.div
                animate={{ opacity: 1, height: "auto" }}
                className="flex-1"
                exit={{ opacity: 0, height: 0 }}
                initial={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                {step}
              </motion.div>
            )}
          </AnimatePresence>
        ))}
      </div>
    </>
  )
}

interface StepperButtonProps {
  index: number
  status: "current" | "completed" | "upcoming" | "incomplete"
  step: StepProps
  className?: string
  onStepChange?: (step: number) => void
  buttonTextSize?: string
}

const StepperButton = ({
  index,
  status,
  step,
  onStepChange,
  className,
  buttonTextSize,
}: StepperButtonProps) => {
  return (
    <Button
      className={cn(
        "flex-none text-center",
        status === "current" && "bg-accent font-semibold",
        status === "completed" && "font-normal text-muted-foreground",
        status === "upcoming" && "font-normal",
        className,
      )}
      data-current={status === "current" ? true : undefined}
      data-status={status}
      onClick={() => onStepChange?.(index)}
      variant="ghost"
      {...step}
    >
      {status === "upcoming" && (
        <div className="flex h-6 w-6 flex-none items-center justify-center rounded-full bg-accent text-foreground">
          {index + 1}
        </div>
      )}
      {status === "current" && (
        <div
          className={cn(
            "flex h-6 w-6 flex-none items-center justify-center rounded-full bg-foreground text-background",
          )}
        >
          {index + 1}
        </div>
      )}
      {status === "completed" && (
        <div
          className={cn(
            "flex h-6 w-6 flex-none items-center justify-center rounded-full border-2 border-muted-foreground bg-background text-muted-foreground",
          )}
        >
          <CheckIcon className="h-4 w-4" />
        </div>
      )}
      {status === "incomplete" && (
        <div
          className={cn(
            "flex h-6 w-6 flex-none items-center justify-center rounded-full border-0 bg-orange-700 text-background",
          )}
        >
          !
        </div>
      )}
      <span className={cn("text-base", buttonTextSize)}>{step.title}</span>
    </Button>
  )
}

interface StepperHeaderHorizontalProps {
  steps: StepProps[]
  currentStep: number
  onStepChange?: (step: number) => void
  buttonTextSize?: string
}

const StepperHeaderHorizontal = ({
  steps,
  currentStep,
  onStepChange,
  buttonTextSize,
}: StepperHeaderHorizontalProps) => {
  return (
    <div className="flex items-center justify-center space-x-2">
      {steps.map((step, index) => (
        <Fragment key={index}>
          <div className="flex items-center justify-center">
            <StepperButton
              index={index}
              onStepChange={onStepChange}
              buttonTextSize={buttonTextSize}
              status={
                step.status ??
                (index < currentStep
                  ? "completed"
                  : index === currentStep
                    ? "current"
                    : "upcoming")
              }
              step={step}
            />
          </div>
          {index < steps.length - 1 && (
            <ChevronRightIcon className="h-6 w-6 font-bold text-muted-foreground" />
          )}
        </Fragment>
      ))}
    </div>
  )
}

interface StepperContentHorizontalProps {
  children: ReactNode[]
  from?: number
  to: number
  onStepChange?: (step: number) => void
  modal?: boolean
  buttonTextSize?: string
}

const StepperContentHorizontal = ({
  children,
  from = 0,
  to,
  onStepChange,
  modal = false,
  buttonTextSize,
}: StepperContentHorizontalProps) => {
  const containerRef = React.useRef<HTMLDivElement>(null)
  const [containerWidth, setContainerWidth] = React.useState(0)

  React.useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const observer = new ResizeObserver((entries) => {
      setContainerWidth(entries[0].contentRect.width)
    })

    observer.observe(container)
    return () => observer.disconnect()
  }, [])

  const variants = {
    initial: (dir: "forward" | "backward") => ({
      x: from === to ? 0 : dir === "forward" ? "100%" : "-100%",
      opacity: from === to ? 1 : 0,
    }),
    animate: {
      x: 0,
      opacity: 1,
    },
    exit: (dir: "forward" | "backward") => ({
      x: from === to ? 0 : dir === "forward" ? "-100%" : "100%",
      opacity: from === to ? 1 : 0,
    }),
  }

  return (
    <div className="relative" ref={containerRef}>
      {to > 0 && containerWidth >= 600 && (
        <div className="absolute -top-4 left-0">
          <Button onClick={() => onStepChange?.(to - 1)} variant="link">
            <ArrowLeftIcon className="h-4 w-4" />
            <span className="text-sm font-medium">Back</span>
          </Button>
        </div>
      )}

      <AnimatePresence custom={from <= to ? "forward" : "backward"} mode="wait">
        <motion.div
          animate="animate"
          custom={from <= to ? "forward" : "backward"}
          exit="exit"
          initial="initial"
          key={to}
          transition={{
            duration: from === to ? 0 : 0.2,
            ease: "easeOut",
          }}
          variants={variants}
        >
          {modal ? (
            <ScrollArea>
              <div className="h-full max-h-[80vh]">{children[to]}</div>
            </ScrollArea>
          ) : (
            children[to]
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
