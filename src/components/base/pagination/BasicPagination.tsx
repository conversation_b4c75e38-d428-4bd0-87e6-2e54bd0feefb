import React from "react"
import { ArrowLeftIcon, ArrowRightIcon } from "@radix-ui/react-icons"

import { IPaginationResponse } from "@/data/global/global.interface"
import { Button } from "@/components/ui/button"

export interface BasicPaginationProps {
  data: Omit<IPaginationResponse<any>, "data">
  onPrevious?: () => void
  onNext?: () => void
  totalItems: number
  pageSize: number
}

/**
 * @deprecated This component is soon to be deprecated. Use `Pagination` instead.
 */
export function BasicPagination({
  data,
  onPrevious,
  onNext,
  totalItems,
  pageSize,
}: BasicPaginationProps) {
  // Calculate entry range
  const startEntry = (data.pageNumber - 1) * pageSize + 1
  const endEntry = Math.min(data.pageNumber * pageSize, totalItems)

  return (
    <div className="mt-4 flex w-full items-center gap-12">
      <div className="flex items-center gap-4">
        <Button
          className="no-underline"
          disabled={data.isFirstPage}
          onClick={onPrevious}
          style={{ background: "none", border: "none", padding: 0 }}
          variant="link-muted"
        >
          <span aria-hidden>
            <ArrowLeftIcon className="h-4 w-4" />
          </span>
          Previous
        </Button>
        <span className="text-sm text-foreground">
          Page {data.pageNumber} of {data.totalPages}
        </span>
        <Button
          className="no-underline"
          disabled={data.isLastPage}
          onClick={onNext}
          style={{ background: "none", border: "none", padding: 0 }}
          variant="link-muted"
        >
          Next
          <span aria-hidden>
            <ArrowRightIcon className="h-4 w-4" />
          </span>
        </Button>
      </div>
      <div className="text-sm text-foreground">
        Showing entries {startEntry} - {endEntry} of {totalItems}
      </div>
    </div>
  )
}
