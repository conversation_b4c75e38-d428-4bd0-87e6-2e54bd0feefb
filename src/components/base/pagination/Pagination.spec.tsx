import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import Pagination, {
  type PaginationProps as CorePaginationProps,
} from "./Pagination"

type PaginationProps = Partial<CorePaginationProps>

const defaultProps: PaginationProps = {
  pageSize: 0,
  getPaginationInfo: () => ({
    startItem: 0,
    endItem: 0,
    hasItems: false,
  }),
  getVisiblePages: () => [0],
}

const setup = (props = defaultProps) => {
  const user = userEvent.setup()
  const component = render(
    <Pagination {...{ ...defaultProps, ...(props as CorePaginationProps) }} />,
  )

  const getPagination = () => screen.getByLabelText("Pagination")
  const getPageInfo = () => screen.getByLabelText("Page info")
  const getPageControls = () => screen.getByLabelText("Page controls")
  const getPageSizeSelector = () => screen.getByLabelText("Page size selector")

  return {
    component,
    user,
    pagination: getPagination,
    pageInfo: getPageInfo,
    pageControls: getPageControls,
    pageSizeSelector: getPageSizeSelector,
  }
}

describe("Pagination", () => {
  it("should render", () => {
    const { component } = setup()

    expect(component.container).toBeInTheDocument()
  })

  describe("Page info", () => {
    it("should render page info", () => {
      const { pageInfo } = setup()

      expect(pageInfo()).toBeInTheDocument()
    })

    it("should not render page info", () => {
      setup({
        showPageInfo: false,
      })

      const pageInfo = screen.queryByLabelText("Page info")

      expect(pageInfo).toBeNull()
    })
  })

  describe("Page controls", () => {
    it("should render page controls", () => {
      const { pageControls } = setup()

      expect(pageControls()).toBeInTheDocument()
    })
  })

  describe("Page size selector", () => {
    it("should render page size selector", () => {
      const { pageSizeSelector } = setup()

      expect(pageSizeSelector()).toBeInTheDocument()
    })

    it("should not render page size selector", () => {
      setup({
        showSizeSelector: false,
      })

      const pageSizeSelector = screen.queryByLabelText("Page size selector")

      expect(pageSizeSelector).toBeNull()
    })
  })
})
