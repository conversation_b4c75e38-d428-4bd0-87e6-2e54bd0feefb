import {
  Chevron<PERSON>eft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { type PaginationReturnType } from "@/hooks/usePagination"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"

import { ShowChildren } from "../show-children"

export interface PaginationProps extends PaginationReturnType {
  showSizeSelector?: boolean
  pageSizeOptions?: number[]
  showPageInfo?: boolean
  showFirstLast?: boolean
  className?: string
  onPrevious?: () => void
  onNext?: () => void
}

const PAGE_SIZE_OPTIONS = [5, 10, 20, 50, 100]

export default function Pagination({
  currentPage,
  totalPages: _totalPages,
  pageSize,
  totalItems,
  canPreviousPage,
  canNextPage,
  goToPage,
  nextPage,
  previousPage,
  setPageSize,
  goToFirstPage,
  goToLastPage,
  getPaginationInfo,
  getVisiblePages,
  onNext: _onNext,
  onPrevious: _onPrevious,
  showSizeSelector = true,
  pageSizeOptions = PAGE_SIZE_OPTIONS,
  showPageInfo = true,
  showFirstLast = true,
  className,
}: PaginationProps) {
  const { startItem, endItem, hasItems } = getPaginationInfo()

  const allOptionsDisabled = !showPageInfo && !showSizeSelector

  return (
    <div
      aria-label="Pagination"
      className={cn(
        "flex items-center justify-between px-2 py-4",
        allOptionsDisabled && "justify-center",
        className,
      )}
    >
      <ShowChildren when={showPageInfo}>
        <div
          aria-label="Page info"
          className="flex w-[150px] items-center text-xs text-gray-700/70"
        >
          <span>Showing</span>&nbsp;
          <span>
            {hasItems ? `${startItem} - ${endItem} of ${totalItems}` : "0 of 0"}
          </span>
        </div>
      </ShowChildren>

      <div aria-label="Page controls" className="flex items-center space-x-2">
        <ShowChildren when={showFirstLast}>
          <Button
            className="hidden size-8 p-0 text-gray-700/70 hover:bg-muted/50 lg:flex"
            disabled={!canPreviousPage}
            onClick={goToFirstPage}
            variant="ghost"
          >
            <ChevronsLeft className="size-4" />
          </Button>
        </ShowChildren>

        <Button
          aria-label="Previous"
          className="size-8 p-0 text-gray-700/70 hover:bg-muted/50"
          disabled={!canPreviousPage}
          onClick={previousPage}
          variant="ghost"
        >
          <ChevronLeft className="size-4" />
        </Button>

        <div className="flex items-center space-x-1">
          {getVisiblePages().map((page, index) => (
            <Button
              className={cn(
                "size-8 border-gray-700/10 p-0 text-xs text-gray-700/70 hover:bg-muted/50",
                page === "..." &&
                  "cursor-default hover:bg-transparent hover:text-foreground",
              )}
              disabled={page === "..."}
              key={index}
              onClick={() => typeof page === "number" && goToPage(page)}
              variant={page === currentPage ? "outline" : "ghost"}
            >
              {page}
            </Button>
          ))}
        </div>

        <Button
          aria-label="Next"
          className="size-8 p-0 text-gray-700/70 hover:bg-muted/50"
          disabled={!canNextPage}
          onClick={nextPage}
          variant="ghost"
        >
          <ChevronRight className="size-4" />
        </Button>

        <ShowChildren when={showFirstLast}>
          <Button
            className="hidden size-8 p-0 text-gray-700/70 hover:bg-muted/50 lg:flex"
            disabled={!canNextPage}
            onClick={goToLastPage}
            variant="ghost"
          >
            <ChevronsRight className="size-4" />
          </Button>
        </ShowChildren>
      </div>

      <ShowChildren when={showSizeSelector}>
        <div
          aria-label="Page size selector"
          className="flex items-center space-x-6 text-gray-700/70 lg:space-x-8"
        >
          <div className="flex items-center space-x-2">
            <p className="text-xs">Rows per page</p>
            <Select
              onValueChange={(value) => setPageSize(Number(value))}
              value={pageSize.toString()}
            >
              <SelectTrigger className="h-7 w-[60px] pr-1.5 text-xs">
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </ShowChildren>
    </div>
  )
}
