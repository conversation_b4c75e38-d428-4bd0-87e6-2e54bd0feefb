import { vi, describe, it, expect, beforeEach, Mock } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, act } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useGetAllUsersWithEntitiesQry } from "@/data/user/user.query"
import { useCreateEntityAccessMutation } from "@/data/onboarding/entity-access.mutation"
import ExistingUser from "@/components/base/add-user-wizard/components/steps/ExistingUser.form"
import { EnumValueDto } from "@/client/onboarding/types.gen"

// Create a wrapper with QueryClientProvider
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
)

// Mock hooks
vi.mock("@/data/onboarding/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entityId: "entity-123",
    staticDataUserRoles: [
      { display: "Administrator", key: "Administrator" },
      { display: "Approver", key: "Approver" },
      { display: "Viewer", key: "Viewer" },
      { display: "Submitter", key: "Submitter" },
    ],
  })),
}))

vi.mock("@/data/user/user.query", () => ({
  GET_ALL_USERS_WITH_ENTITIES_QRY_KEY: "get-all-users-with-entities",
  useGetAllUsersWithEntitiesQry: vi.fn(),
}))

vi.mock("@/data/onboarding/static-data.query", () => ({
  useGetUserRoleSuspenseQry: vi.fn(() => ({
    data: [
      { display: "Administrator", key: "administrator" },
      { display: "Submitter", key: "submitter" },
    ] as EnumValueDto[],
    isLoading: false,
  })),
}))

vi.mock("@/data/onboarding/entity-access.mutation", () => ({
  useCreateEntityAccessMutation: vi.fn(),
}))

describe("ExistingUser Form", () => {
  const mockUser = {
    id: "user-123",
    email: "<EMAIL>",
    displayName: "John Doe",
    entities: [
      { id: "entity-1", displayName: "Entity 1" },
      { id: "entity-2", displayName: "Entity 2" },
    ],
  }

  const defaultProps = {
    entityId: "entity-1",
    data: {
      email: "<EMAIL>",
      name: "Test User",
      roles: ["Viewer"],
      isSignatory: false,
    },
    onNext: vi.fn(),
    onBack: vi.fn(),
    onReset: vi.fn(),
  }

  const createMockMutation = () => {
    const callbacks: Record<string, () => void> = {}

    return {
      mutate: vi.fn((_data, options) => {
        if (options?.onSuccess) {
          callbacks.onSuccess = options.onSuccess
        }
      }),
      mutateAsync: vi.fn((_data, options) => {
        if (options?.onSuccess) {
          callbacks.onSuccess = options.onSuccess
        }
      }),
      callbacks,
      isLoading: false,
      isError: false,
      isSuccess: false,
      error: null,
    }
  }

  let mockMutation: ReturnType<typeof createMockMutation>

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient.clear()
    mockMutation = createMockMutation()

    // Setup mock implementation
    ;(useGetAllUsersWithEntitiesQry as Mock).mockReturnValue({
      data: [mockUser],
      isLoading: false,
    })
    ;(useCreateEntityAccessMutation as Mock).mockReturnValue(mockMutation)
  })

  it("renders the existing user form with user details", async () => {
    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Check for info alert
    expect(
      screen.getByText("A user with provided email already exists"),
    ).toBeInTheDocument()

    // Check user details are displayed
    expect(screen.getByText("Name:")).toBeInTheDocument()
    expect(screen.getByText("John Doe")).toBeInTheDocument()

    expect(screen.getByText("Email:")).toBeInTheDocument()
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument()

    expect(screen.getByText("Current entities:")).toBeInTheDocument()
    expect(screen.getByText("Entity 1, Entity 2")).toBeInTheDocument()

    // Check for role label
    expect(screen.getByText("Role")).toBeInTheDocument()
  })

  it("calls onBack when back button is clicked", async () => {
    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Click back button
    await userEvent.click(screen.getByText("Back"))

    // Verify onBack was called
    expect(defaultProps.onBack).toHaveBeenCalled()
  })

  it("calls mutate when Add user button is clicked", async () => {
    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Click Add User button
    await userEvent.click(screen.getByText("Add user"))

    // Verify mutation was called
    expect(mockMutation.mutateAsync).toHaveBeenCalled()
  })

  it("calls onReset when mutation is successful", async () => {
    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Click Add User button to trigger mutation
    await userEvent.click(screen.getByText("Add user"))

    // Simulate successful mutation by calling success callback
    act(() => {
      if (mockMutation.callbacks.onSuccess) {
        mockMutation.callbacks.onSuccess()
      }
    })

    // Verify onReset was called
    expect(defaultProps.onReset).toHaveBeenCalled()
  })

  it("handles loading state when users data is loading", async () => {
    ;(useGetAllUsersWithEntitiesQry as Mock).mockReturnValue({
      data: undefined,
      isLoading: true,
    })

    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Basic structure should still be rendered
    expect(
      screen.getByText("A user with provided email already exists"),
    ).toBeInTheDocument()
  })

  it("handles error state in create entity access mutation", async () => {
    const errorMutation = {
      ...mockMutation,
      isError: true,
      error: new Error("Failed to create entity access"),
    }

    ;(useGetAllUsersWithEntitiesQry as Mock).mockReturnValue({
      data: [mockUser],
      isLoading: false,
    })
    ;(useCreateEntityAccessMutation as Mock).mockReturnValue(errorMutation)

    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Check basic rendering works
    expect(
      screen.getByText("A user with provided email already exists"),
    ).toBeInTheDocument()

    // Submit form (shouldn't throw)
    await userEvent.click(screen.getByText("Add user"))

    // Verify mutation was called
    expect(errorMutation.mutateAsync).toHaveBeenCalled()
  })
})
