import { useState, useEffect } from "react"
import { toast } from "sonner"
import { Document, Page, pdfjs } from "react-pdf"
import "react-pdf/dist/Page/TextLayer.css"
import "react-pdf/dist/Page/AnnotationLayer.css"
import workerSrc from "pdfjs-dist/build/pdf.worker.min.mjs?worker&url"
import { FileText, File } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { cn } from "@/lib/utils"
import { useModal } from "@/hooks/use-modal"
import { AllTermsAndConditions } from "@/client/onboarding/types.gen"
import {
  useGetAllTermsAndConditionsQry,
  useDownloadTermsAndConditionsDocumentQry,
} from "@/data/terms-and-conditions/terms-and-conditions.query"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { ScrollArea } from "@/components/ui/scroll-area"
import ArgentexIcon from "@/components/base/icons/ArgentexIcon"
import PdfSvg from "@/components/base/icons/pdf.svg"

pdfjs.GlobalWorkerOptions.workerSrc = workerSrc

interface TermsAndConditionsModalProps {
  children: React.ReactNode
}

export function TermsAndConditionsModal({
  children,
}: TermsAndConditionsModalProps) {
  const [selectedDocument, setSelectedDocument] =
    useState<AllTermsAndConditions | null>(null)
  const [numPages, setNumPages] = useState<number | null>(null)
  const { visible: open, show, close } = useModal()

  const { data: termsData, isLoading: isLoadingTerms } =
    useGetAllTermsAndConditionsQry()
  const {
    trigger: downloadDocument,
    isLoading: isDownloading,
    data: pdfData,
  } = useDownloadTermsAndConditionsDocumentQry()

  const handleOpen = (isOpen: boolean) => {
    if (isOpen) {
      show()
    } else {
      close()
      setSelectedDocument(null)
      setNumPages(null)
    }
  }

  const handleDocumentSelect = (document: AllTermsAndConditions) => {
    setSelectedDocument(document)
    if (document.termsAndConditionsId) {
      downloadDocument([document.termsAndConditionsId], {
        onError: () => {
          toast.error(`Unable to load document: ${document.documentName}`)
        },
      })
    }
  }

  // Auto-select first document when modal opens and data is loaded
  useEffect(() => {
    if (
      open &&
      termsData?.allTermsAndConditions &&
      termsData.allTermsAndConditions.length > 0 &&
      !selectedDocument
    ) {
      const firstDocument = termsData.allTermsAndConditions[0]
      handleDocumentSelect(firstDocument)
    }
  }, [open, termsData, selectedDocument])

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages)
  }

  const handleDownload = () => {
    if (pdfData && selectedDocument) {
      const url = URL.createObjectURL(pdfData)
      const link = document.createElement("a")
      link.href = url
      link.download = selectedDocument.documentName || "document.pdf"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }
  }

  const renderLoading = () => (
    <div className="flex flex-1 items-center justify-center p-8">
      <LoadingSpinner />
    </div>
  )

  const renderDocumentList = () => (
    <div className="">
      <div className="p-5">
        <ArgentexIcon />
      </div>
      <ScrollArea className="h-[600px] w-[350px]">
        <div className="p-2">
          {isLoadingTerms
            ? renderLoading()
            : termsData?.allTermsAndConditions?.map((document) => (
                <Button
                  key={document.termsAndConditionsId}
                  variant={
                    selectedDocument?.termsAndConditionsId ===
                    document.termsAndConditionsId
                      ? "secondary"
                      : "ghost"
                  }
                  className={cn(
                    "mb-2 h-auto w-full justify-start rounded-xl p-2 text-left",
                    selectedDocument?.termsAndConditionsId ===
                      document.termsAndConditionsId && "bg-secondary",
                  )}
                  onClick={() => handleDocumentSelect(document)}
                >
                  <FileText
                    className={cn(
                      "mr-1 h-4 w-4 flex-shrink-0 text-muted-foreground opacity-50",
                      selectedDocument?.termsAndConditionsId ===
                        document.termsAndConditionsId && "opacity-100",
                    )}
                  />
                  <div className="min-w-0 flex-1">
                    <div
                      className={cn(
                        "truncate text-sm font-medium",
                        selectedDocument?.termsAndConditionsId !==
                          document.termsAndConditionsId && "font-light",
                      )}
                    >
                      {document.documentName}
                    </div>
                  </div>
                </Button>
              ))}
        </div>
      </ScrollArea>
    </div>
  )

  const renderPdfViewer = () => (
    <div className="flex flex-1 flex-col">
      {selectedDocument ? (
        <>
          <div className="ml-5 mr-10 mt-10 max-h-[68vh] flex-1 overflow-y-auto border border-gray-200">
            {isDownloading ? (
              renderLoading()
            ) : pdfData ? (
              <div className="p-4">
                <Document
                  file={pdfData}
                  onLoadSuccess={onDocumentLoadSuccess}
                  className="flex flex-col items-center"
                >
                  {Array.from(new Array(numPages), (_, index) => (
                    <div key={`page_${index + 1}`} className="mb-4 shadow-md">
                      <Page
                        pageNumber={index + 1}
                        width={Math.min(800, window.innerWidth - 450)}
                        renderTextLayer={true}
                        renderAnnotationLayer={true}
                      />
                    </div>
                  ))}
                </Document>
              </div>
            ) : (
              <div className="flex h-full items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <FileText className="mx-auto mb-4 h-12 w-12 opacity-50" />
                  <p>Failed to load document</p>
                </div>
              </div>
            )}
          </div>
          {/* Download button positioned at bottom right */}
          {pdfData && (
            <div className="flex justify-end p-4">
              <Button
                onClick={handleDownload}
                variant="ghost"
                size="sm"
                className="h-auto rounded-[99px] border border-[rgba(0,13,77,0.20)] bg-transparent p-2 text-xs"
              >
                <img src={PdfSvg} alt="mySvgImage" width={16} height={16} />
                Download PDF
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="flex flex-1 items-center justify-center text-muted-foreground">
          <div className="text-center">
            <FileText className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Select a document from the list to view</p>
          </div>
        </div>
      )}
    </div>
  )

  return (
    <Dialog modal onOpenChange={handleOpen} open={open}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent className="h-[80vh] w-full max-w-7xl rounded-[20px] p-0">
        <VisuallyHidden>
          <DialogTitle>Legal</DialogTitle>
          <DialogDescription>
            View and download legal documents
          </DialogDescription>
        </VisuallyHidden>

        <div className="flex h-full">
          {renderDocumentList()}
          {renderPdfViewer()}
        </div>
      </DialogContent>
    </Dialog>
  )
}
