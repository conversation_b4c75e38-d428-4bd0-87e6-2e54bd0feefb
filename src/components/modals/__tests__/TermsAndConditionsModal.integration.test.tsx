/**
 * @vitest-environment jsdom
 */
import { render, screen, waitFor, cleanup } from "@testing-library/react"
import {
  vi,
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  beforeAll,
} from "vitest"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import userEvent from "@testing-library/user-event"

import { TermsAndConditionsModal } from "../TermsAndConditionsModal"
import * as termsQuery from "@/data/terms-and-conditions/terms-and-conditions.query"

// Setup global mocks needed for TermsAndConditionsModal integration tests
beforeAll(() => {
  // Mock URL methods for download functionality
  global.URL.createObjectURL = vi.fn(() => "mock-object-url")
  global.URL.revokeObjectURL = vi.fn()
})

// Mock the PDF worker
vi.mock("pdfjs-dist/build/pdf.worker.min.mjs?worker&url", () => ({
  default: "mock-worker-url",
}))

// Mock react-pdf
vi.mock("react-pdf", () => ({
  Document: ({ children, onLoadSuccess }: any) => {
    setTimeout(() => onLoadSuccess?.({ numPages: 3 }), 100)
    return <div data-testid="pdf-document">{children}</div>
  },
  Page: ({ pageNumber }: any) => (
    <div data-testid={`pdf-page-${pageNumber}`}>Page {pageNumber}</div>
  ),
  pdfjs: {
    GlobalWorkerOptions: {},
  },
}))

// Mock icons
vi.mock("@/components/base/icons/ArgentexIcon", () => ({
  default: () => <div data-testid="argentex-icon">Argentex Icon</div>,
}))

vi.mock("lucide-react", () => ({
  FileText: () => <div data-testid="file-text-icon">FileText</div>,
  File: () => <div data-testid="file-icon">File</div>,
  X: () => <div data-testid="x-icon">X</div>,
}))

// Mock toast
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
  },
}))

// Mock PDF SVG
vi.mock("@/components/base/icons/pdf.svg", () => ({
  default: "mock-pdf-icon.svg",
}))

// Mock modal hook to always show modal for integration tests
vi.mock("@/hooks/use-modal", () => ({
  useModal: () => ({
    visible: true,
    show: vi.fn(),
    close: vi.fn(),
    toggle: vi.fn(),
  }),
}))

const mockTermsResponse = {
  allTermsAndConditions: [
    {
      termsAndConditionsId: "terms-1",
      documentId: "doc-1",
      documentName: "General Terms and Conditions",
      versionNumber: 1,
      type: "TermsAndConditions",
      requiresAgreement: true,
    },
    {
      termsAndConditionsId: "terms-2",
      documentId: "doc-2",
      documentName: "Privacy Policy",
      versionNumber: 2,
      type: "CountryAddendum",
      requiresAgreement: true,
    },
    {
      termsAndConditionsId: "terms-3",
      documentId: "doc-3",
      documentName: "Cookie Policy",
      versionNumber: 1,
      type: "Supplementary",
      requiresAgreement: false,
    },
  ],
}

const mockPdfBlob = new Blob(["PDF content"], { type: "application/pdf" })

// Mock the query hooks directly
vi.mock("@/data/terms-and-conditions/terms-and-conditions.query", () => ({
  useGetAllTermsAndConditionsQry: vi.fn(),
  useDownloadTermsAndConditionsDocumentQry: vi.fn(),
}))

describe("TermsAndConditionsModal Integration Tests", () => {
  let queryClient: QueryClient
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    // Clear all mocks first
    vi.clearAllMocks()

    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    user = userEvent.setup()

    // Setup default mock implementations with explicit mock reset
    const mockGetAllTerms = vi.mocked(termsQuery.useGetAllTermsAndConditionsQry)
    mockGetAllTerms.mockClear()
    mockGetAllTerms.mockReturnValue({
      data: mockTermsResponse,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    } as any)

    const mockDownloadTerms = vi.mocked(
      termsQuery.useDownloadTermsAndConditionsDocumentQry,
    )
    mockDownloadTerms.mockClear()
    mockDownloadTerms.mockReturnValue({
      trigger: vi.fn(),
      isLoading: false,
      data: mockPdfBlob,
      error: null,
    } as any)
  })

  afterEach(() => {
    queryClient.clear()
    cleanup()
    // Clean up any DOM elements
    document.body.innerHTML = ""
  })

  const renderModal = (children = <button>Open Legal Documents</button>) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <TermsAndConditionsModal>{children}</TermsAndConditionsModal>
      </QueryClientProvider>,
    )
  }

  describe("Core Functionality", () => {
    it("loads terms list, selects document, views PDF, and downloads", async () => {
      // Verify mocks are set up correctly before rendering
      expect(vi.mocked(termsQuery.useGetAllTermsAndConditionsQry)).toBeDefined()

      renderModal()

      // First ensure the modal dialog is rendered
      await waitFor(() => {
        expect(screen.getByRole("dialog")).toBeInTheDocument()
      })

      // Verify the mock is being called
      expect(termsQuery.useGetAllTermsAndConditionsQry).toHaveBeenCalled()

      // Wait for terms to load - increase timeout and add more specific checks
      await waitFor(
        () => {
          // Debug: log what's actually in the document
          if (process.env.CI) {
            console.log("Document body content:", document.body.innerHTML)
            console.log(
              "Available text content:",
              screen.getByRole("dialog").textContent,
            )
          }

          expect(
            screen.getByText("General Terms and Conditions"),
          ).toBeInTheDocument()
          expect(screen.getByText("Privacy Policy")).toBeInTheDocument()
          expect(screen.getByText("Cookie Policy")).toBeInTheDocument()
        },
        { timeout: 5000 },
      )

      // Verify all documents have file text icons (FileText icons are used in the document list)
      expect(screen.getAllByTestId("file-text-icon")).toHaveLength(3) // 3 in list

      // First document should be auto-selected
      await waitFor(() => {
        expect(screen.getByTestId("pdf-document")).toBeInTheDocument()
      })

      // Click on Privacy Policy
      const privacyPolicyButton = screen.getByText("Privacy Policy")
      await user.click(privacyPolicyButton)

      // Verify Privacy Policy is now selected
      await waitFor(() => {
        expect(privacyPolicyButton.closest("button")).toHaveClass(
          "bg-secondary",
        )
      })

      // Wait a bit more to ensure the selected document state has updated
      await waitFor(() => {
        // Ensure the download button is ready for the correct document
        const downloadBtn = screen.getByRole("button", {
          name: /download pdf/i,
        })
        expect(downloadBtn).toBeInTheDocument()
      })

      // Verify PDF loads for Privacy Policy
      await waitFor(() => {
        expect(screen.getByTestId("pdf-document")).toBeInTheDocument()
      })

      // Test download functionality
      const downloadButton = screen.getByRole("button", {
        name: /download pdf/i,
      })
      expect(downloadButton).toBeInTheDocument()
      expect(downloadButton).toHaveClass("rounded-[99px]")

      // Mock download functionality
      const mockCreateObjectURL = vi.fn(() => "mock-blob-url")
      const mockRevokeObjectURL = vi.fn()
      global.URL.createObjectURL = mockCreateObjectURL
      global.URL.revokeObjectURL = mockRevokeObjectURL

      const mockLink = {
        href: "",
        download: "",
        click: vi.fn(),
        style: {},
        setAttribute: vi.fn(),
        getAttribute: vi.fn(),
        removeAttribute: vi.fn(),
      }

      // Create a more complete mock for createElement
      const originalCreateElement = document.createElement
      vi.spyOn(document, "createElement").mockImplementation(
        (tagName: string) => {
          if (tagName === "a") {
            return mockLink as any
          }
          return originalCreateElement.call(document, tagName)
        },
      )

      vi.spyOn(document.body, "appendChild").mockImplementation((node: any) => {
        // Just return the node without actually appending
        return node
      })
      vi.spyOn(document.body, "removeChild").mockImplementation((node: any) => {
        // Just return the node without actually removing
        return node
      })

      await user.click(downloadButton)

      // Wait for download action to complete
      await waitFor(() => {
        expect(mockLink.click).toHaveBeenCalled()
      })

      // Debug: log the actual download value in CI
      if (process.env.CI) {
        console.log("Actual download filename:", mockLink.download)
        console.log("Expected download filename: Privacy Policy")
      }

      expect(mockLink.download).toBe("Privacy Policy")
    })
  })
})
