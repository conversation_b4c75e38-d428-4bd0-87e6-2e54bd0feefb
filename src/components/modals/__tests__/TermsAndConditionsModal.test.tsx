/**
 * @vitest-environment jsdom
 */
import { render, screen, waitFor } from "@testing-library/react"
import {
  vi,
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  beforeAll,
} from "vitest"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import userEvent from "@testing-library/user-event"

import { TermsAndConditionsModal } from "../TermsAndConditionsModal"
import * as termsQuery from "@/data/terms-and-conditions/terms-and-conditions.query"

// Setup global mocks needed for TermsAndConditionsModal
beforeAll(() => {
  // Setup DOM environment
  if (!document.body) {
    document.body = document.createElement("body")
  }

  // Mock URL methods for download functionality
  global.URL.createObjectURL = vi.fn(() => "mock-object-url")
  global.URL.revokeObjectURL = vi.fn()
})

// Mock the PDF worker
vi.mock("pdfjs-dist/build/pdf.worker.min.mjs?worker&url", () => ({
  default: "mock-worker-url",
}))

// Mock react-pdf
vi.mock("react-pdf", () => ({
  Document: ({ children, onLoadSuccess }: any) => {
    // Simulate successful PDF load
    setTimeout(() => onLoadSuccess?.({ numPages: 2 }), 100)
    return <div data-testid="pdf-document">{children}</div>
  },
  Page: ({ pageNumber }: any) => (
    <div data-testid={`pdf-page-${pageNumber}`}>Page {pageNumber}</div>
  ),
  pdfjs: {
    GlobalWorkerOptions: {},
  },
}))

// Mock the modal hook - start open for testing
vi.mock("@/hooks/use-modal", () => ({
  useModal: () => ({
    visible: true,
    show: vi.fn(),
    close: vi.fn(),
    toggle: vi.fn(),
  }),
}))

// Mock toast
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
  },
}))

// Mock icons
vi.mock("@/components/base/icons/ArgentexIcon", () => ({
  default: () => <div data-testid="argentex-icon">Argentex Icon</div>,
}))

vi.mock("lucide-react", () => ({
  FileText: () => <div data-testid="file-text-icon">FileText</div>,
  File: () => <div data-testid="file-icon">File</div>,
  X: () => <div data-testid="x-icon">X</div>,
}))

const mockTermsData = {
  allTermsAndConditions: [
    {
      termsAndConditionsId: "1",
      documentId: "doc1",
      documentName: "Terms of Service",
      versionNumber: 1,
      type: "TermsAndConditions" as const,
      requiresAgreement: true,
    },
    {
      termsAndConditionsId: "2",
      documentId: "doc2",
      documentName: "Privacy Policy",
      versionNumber: 2,
      type: "CountryAddendum" as const,
      requiresAgreement: false,
    },
  ],
}

const mockPdfBlob = new Blob(["mock pdf content"], { type: "application/pdf" })

describe("TermsAndConditionsModal", () => {
  let queryClient: QueryClient
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    // Ensure DOM is available for each test
    if (!document.body) {
      document.body = document.createElement("body")
    }

    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    user = userEvent.setup()

    // Mock the query hooks
    vi.spyOn(termsQuery, "useGetAllTermsAndConditionsQry").mockReturnValue({
      data: mockTermsData,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    } as any)

    vi.spyOn(
      termsQuery,
      "useDownloadTermsAndConditionsDocumentQry",
    ).mockReturnValue({
      trigger: vi.fn(),
      isLoading: false,
      data: mockPdfBlob,
      error: null,
    } as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  const renderModal = (children = <button>Open Modal</button>) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <TermsAndConditionsModal>{children}</TermsAndConditionsModal>
      </QueryClientProvider>,
    )
  }

  const renderOpenModal = async (children = <button>Open Modal</button>) => {
    const result = renderModal(children)
    const triggerButton = screen.getByRole("button", { name: "Open Modal" })
    await user.click(triggerButton)
    return result
  }

  describe("Modal Rendering", () => {
    it("renders the trigger button", () => {
      renderModal()
      // When modal is open, trigger button is hidden from screen readers but still in DOM
      const triggerButton = screen.getByText("Open Modal")
      expect(triggerButton).toBeInTheDocument()
    })

    it("displays Argentex logo and company name in sidebar header", () => {
      renderModal()
      expect(screen.getByTestId("argentex-icon")).toBeInTheDocument()
      expect(screen.getByText("Argentex Icon")).toBeInTheDocument()
    })

    it("renders document list with file icons", () => {
      renderModal()
      expect(screen.getByText("Terms of Service")).toBeInTheDocument()
      expect(screen.getByText("Privacy Policy")).toBeInTheDocument()
      expect(screen.getAllByTestId("file-text-icon")).toHaveLength(2) // Document list items have file-text-icon
    })

    it("applies correct modal styling with 20px border radius", () => {
      renderModal()
      const dialogContent = screen.getByRole("dialog")
      expect(dialogContent).toHaveClass("rounded-[20px]")
    })
  })

  describe("Document Selection", () => {
    it("auto-selects first document when modal opens", async () => {
      const mockTrigger = vi.fn()
      vi.spyOn(
        termsQuery,
        "useDownloadTermsAndConditionsDocumentQry",
      ).mockReturnValue({
        trigger: mockTrigger,
        isLoading: false,
        data: null,
        error: null,
      } as any)

      renderModal()

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(["1"], expect.any(Object))
      })
    })

    it("selects document when clicked", async () => {
      const mockTrigger = vi.fn()
      vi.spyOn(
        termsQuery,
        "useDownloadTermsAndConditionsDocumentQry",
      ).mockReturnValue({
        trigger: mockTrigger,
        isLoading: false,
        data: null,
        error: null,
      } as any)

      renderModal()

      const privacyPolicyButton = screen.getByText("Privacy Policy")
      await user.click(privacyPolicyButton)

      expect(mockTrigger).toHaveBeenCalledWith(["2"], expect.any(Object))
    })

    it("highlights selected document", async () => {
      renderModal()

      const termsButton = screen.getByText("Terms of Service").closest("button")
      expect(termsButton).toHaveClass("bg-secondary")
    })
  })

  describe("PDF Viewer", () => {
    it("displays loading state while downloading PDF", () => {
      vi.spyOn(
        termsQuery,
        "useDownloadTermsAndConditionsDocumentQry",
      ).mockReturnValue({
        trigger: vi.fn(),
        isLoading: true,
        data: null,
        error: null,
      } as any)

      renderModal()
      expect(screen.getByTestId("loading-spinner")).toBeInTheDocument()
    })

    it("renders PDF document when data is available", async () => {
      renderModal()

      await waitFor(() => {
        expect(screen.getByTestId("pdf-document")).toBeInTheDocument()
      })
    })

    it("displays correct border styling for PDF container", () => {
      renderModal()
      // Look for the PDF content container by its styling classes
      const pdfContainer = document.querySelector(".border.border-gray-200")
      expect(pdfContainer).toBeInTheDocument()
      expect(pdfContainer).toHaveClass("border", "border-gray-200")
    })

    it("shows placeholder when no document is selected", () => {
      vi.spyOn(termsQuery, "useGetAllTermsAndConditionsQry").mockReturnValue({
        data: { allTermsAndConditions: [] },
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as any)

      renderModal()
      expect(
        screen.getByText("Select a document from the list to view"),
      ).toBeInTheDocument()
    })
  })

  describe("Download Functionality", () => {
    it("renders download button with correct styling", () => {
      renderModal()

      const downloadButton = screen.getByRole("button", {
        name: /download pdf/i,
      })
      expect(downloadButton).toHaveClass(
        "bg-transparent",
        "border",
        "border-[rgba(0,13,77,0.20)]",
        "rounded-[99px]",
        "text-xs",
      )
    })
  })
})
