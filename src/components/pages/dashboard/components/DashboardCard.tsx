import { Link, LinkProps } from "@tanstack/react-router"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface DashboardCardProps {
  title: string
  link?: LinkProps
  linkText?: string
  children?: React.ReactNode
  noDataText?: string
}

export function DashboardCard({
  title,
  children,
  link,
  linkText,
  noDataText,
}: DashboardCardProps) {
  return (
    <Card className="m-2 border-none">
      <CardHeader className="flex flex-row items-center justify-between p-2 pb-2">
        <CardTitle className="text-lg font-semibold text-foreground">
          {title}
        </CardTitle>
        {link ? (
          <Link className="text-sm text-primary underline" {...link}>
            {linkText ?? "View all"}
          </Link>
        ) : (
          <div></div>
        )}
      </CardHeader>
      <CardContent className="p-2">
        {children ? (
          children
        ) : (
          <div className="py-4 text-center">
            <p className="text-muted-foreground">{noDataText ?? "No data"}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
