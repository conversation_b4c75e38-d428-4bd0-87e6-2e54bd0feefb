import { Payment } from "@/data/payments/payments.interface"
import { IPayeeData } from "@/data/payees/payees.interface"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

import { PaymentsTile } from "./PaymentsTile"
import { PayeeTile } from "./PayeeTile"
import { DashboardCard } from "./DashboardCard"

interface PayeesAndPaymentsProps {
  payees: IPayeeData[]
  payments: Payment[]
}

export function PayeesAndPayments({
  payees,
  payments,
}: PayeesAndPaymentsProps) {
  const { entity } = useLoaderData()
  const entityId = entity?.id as string

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Payees Section */}
      <DashboardCard
        data-testid="dashboard-card-payees"
        link={{ to: "/$entityId/payees", params: { entityId } }}
        noDataText="No payees found."
        title="Payees"
      >
        <div className="space-y-1">
          {payees?.length > 0 &&
            payees.map((payee) => <PayeeTile key={payee.id} payee={payee} />)}
        </div>
      </DashboardCard>

      {/* Payments Section */}
      <DashboardCard
        data-testid="dashboard-card-payments"
        link={{ to: "/$entityId/payments", params: { entityId } }}
        noDataText="No payments found."
        title="Payments"
      >
        <div className="space-y-1">
          {payments?.length > 0 &&
            payments.map((payment) => (
              <PaymentsTile key={payment.id} payment={payment} />
            ))}
        </div>
      </DashboardCard>
    </div>
  )
}
