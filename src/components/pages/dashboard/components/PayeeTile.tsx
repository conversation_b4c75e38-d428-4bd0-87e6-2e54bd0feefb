import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from "lucide-react"
import { Link, useLocation } from "@tanstack/react-router"

import { formatAccountNumber } from "@/lib/bank.utils"
import { IPayeeData } from "@/data/payees/payees.interface"
import { useGlobalStore } from "@/data/global/global.store"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface PayeeTileProps {
  payee: IPayeeData
}

export function PayeeTile({ payee }: PayeeTileProps) {
  const { entity } = useLoaderData()
  const entityId = entity?.id as string
  const { href } = useLocation()

  return (
    <div
      className="group flex items-center justify-between border-b border-border/60 py-3 last:border-none"
      key={payee.id}
    >
      <div className="flex items-center">
        {/* Avatar or Initials */}
        <Avatar className="mr-4 h-8 w-8 bg-foreground">
          <AvatarFallback
            className="bg-foreground text-sm text-background"
            data-testid="avatar-fallback"
          >
            {payee.accountName?.[0]?.toUpperCase()}
            {payee.accountName?.split(" ")?.[1]?.[0]?.toUpperCase() || ""}
          </AvatarFallback>
        </Avatar>
        {/* Payee Details */}
        <div>
          <p className="font-medium text-foreground">{payee.accountName}</p>
          <p className="text-sm text-muted-foreground">
            {payee.bank?.name +
              " " +
              formatAccountNumber(payee.iban ?? payee.accountNumber)}
          </p>
        </div>
      </div>
      {/* Actions */}
      <div className="flex items-center">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              className="h-8 w-8 text-muted-foreground"
              size="icon"
              variant="ghost"
            >
              <MoreVertical className="h-5 w-5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            align="end"
            className="w-56 rounded-lg border border-primary/20 p-2"
          >
            <Link
              className="w-full"
              params={{ entityId }}
              search={{
                to: payee.id,
                fromRoute: href,
              }}
              to="/$entityId/payments/send"
            >
              <Button
                className="w-full justify-start px-1"
                size="lg"
                variant="ghost"
              >
                <ArrowUpRight className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium text-foreground">Pay</span>
              </Button>
            </Link>
            <Link
              className="w-full"
              params={{ entityId }}
              to="/$entityId/payees"
              search={{
                id: payee.id,
              }}
            >
              <Button
                className="w-full justify-start px-1"
                size="lg"
                variant="ghost"
              >
                <User className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium text-foreground">
                  View details
                </span>
              </Button>
            </Link>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}
