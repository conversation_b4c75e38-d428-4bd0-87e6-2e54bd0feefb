import { ArrowUpRight } from "lucide-react"
import { format } from "date-fns"

import { Payment } from "@/data/payments/payments.interface"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

interface PaymentsTileProps {
  payment: Payment
}

export function PaymentsTile({ payment }: PaymentsTileProps) {
  // Only handle OutboundPayment type for now
  if (payment.paymentType !== "OutboundPayment") return null

  return (
    <div
      className="flex items-center justify-between border-b border-border/60 py-3 last:border-none"
      key={payment.id}
    >
      <div className="flex items-center">
        <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-sidebar-accent">
          <ArrowUpRight className="h-4 w-4 text-sidebar-foreground" />
        </div>
        <div>
          <div className="flex items-center">
            <span className="mr-1 text-sm text-muted-foreground">To</span>
            <p className="font-medium text-foreground">
              {payment.payee.accountName}
            </p>
          </div>
          <p className="text-sm text-muted-foreground">
            {format(new Date(payment.createdAt), "dd/MM/yy HH:mm")}
          </p>
        </div>
      </div>
      <div className="text-right">
        <p className="font-medium text-foreground">
          <CurrencyText
            amount={payment.amount}
            className="font-medium"
            currency={payment.currency}
          />
        </p>
        <p className="text-sm text-muted-foreground">
          {payment.currencyAccount.clientAccount?.accountName || "-"}
        </p>
      </div>
    </div>
  )
}
