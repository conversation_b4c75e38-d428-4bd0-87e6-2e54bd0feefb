import { toast } from "sonner"
import { use<PERSON>emo, useRef } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, Loader } from "lucide-react"
import { <PERSON> } from "@tanstack/react-router"

import { useRateForCurrencyPairQuery } from "@/data/trade/trade.query"
import { useAccountStore } from "@/data/account/account.store"
import { Account } from "@/data/account/account.interface"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencySelector } from "@/components/base/currency/CurrencySelector"

interface AccountsCarouselProps {
  accounts: Account[]
}

export function AccountsCarousel({ accounts }: AccountsCarouselProps) {
  const carouselRef = useRef<HTMLDivElement>(null)
  const { displayCurrency, setDisplayCurrency } = useAccountStore()
  const { entity } = useLoaderData()
  const entityId = entity.id

  // Extract unique currencies from accounts
  const availableCurrencies = accounts
    ? [
        ...new Set(
          accounts.flatMap((account) =>
            account.balances.map((balance) => balance.currency),
          ),
        ),
      ]
    : []

  const scrollLeft = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: -300, behavior: "smooth" })
    }
  }

  const scrollRight = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: 300, behavior: "smooth" })
    }
  }

  function copyToClipboard(value: string) {
    navigator.clipboard.writeText(value)
    toast("Copied to clipboard", {
      description: `${value}`,
      icon: "📋",
      duration: 1000,
    })
  }

  const { data: rateForCurrencyPair, isLoading: isRateForCurrencyPairLoading } =
    useRateForCurrencyPairQuery({
      displayCurrency,
      originalCurrency: "GBP",
    })

  const rate = useMemo(() => {
    return rateForCurrencyPair?.rate ?? 1
  }, [rateForCurrencyPair])

  const isRateLoading = useMemo(() => {
    if (displayCurrency === "GBP") {
      return false
    }
    return isRateForCurrencyPairLoading
  }, [isRateForCurrencyPairLoading, displayCurrency])

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between px-4">
        <h2 className="text-lg font-semibold">Accounts</h2>
        <div className="flex items-center gap-4">
          <span className="text-sm text-muted-foreground">
            Display estimated account balances in
          </span>
          <CurrencySelector
            availableCurrencies={availableCurrencies}
            buttonClassName="flex items-center pe-3 border rounded-md px-2 py-1"
            currency={displayCurrency}
            onChange={setDisplayCurrency}
          />
          {entityId && (
            <Link
              className="text-sm text-primary underline"
              params={{ entityId }}
              search={{
                open: true,
              }}
              to="/$entityId/accounts"
            >
              View all
            </Link>
          )}
        </div>
      </div>

      {/* Carousel Section */}
      {accounts?.length > 0 ? (
        <div
          className="flex items-center space-x-4 overflow-x-auto scroll-smooth px-4 pb-4"
          ref={carouselRef}
        >
          {accounts.map((account: Account) => (
            <Link
              key={account.virtualIban}
              params={{
                entityId: entityId as string,
              }}
              search={{
                open: true,
                accountId: account.virtualIban,
              }}
              to="/$entityId/accounts"
            >
              <Card
                className="w-full flex-shrink-0 border-none bg-sidebar shadow-sm sm:w-80"
                key={account.virtualIban}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg font-semibold text-foreground">
                    {account.accountName || "N/A"}
                  </CardTitle>
                  <CardDescription className="flex items-center text-muted-foreground">
                    {account.virtualIban || "N/A"}
                    <Button
                      className="ml-2 h-4 w-4 text-primary"
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        copyToClipboard(account.virtualIban)
                      }}
                      size="icon"
                      variant="ghost"
                    >
                      <CopyIcon className="h-3 w-3" />
                    </Button>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Balance estimate
                  </p>
                  <p className="text-xl font-semibold text-foreground">
                    {isRateLoading ? (
                      <Loader className="animate-spin" />
                    ) : (
                      <CurrencyText
                        amount={account.totalBalance * rate}
                        currency={displayCurrency}
                      />
                    )}
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      ) : (
        <div className="py-4 text-center text-muted-foreground">
          No accounts available.
        </div>
      )}
    </div>
  )
}
