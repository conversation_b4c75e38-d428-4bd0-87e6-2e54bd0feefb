import { Alert<PERSON>riangle, <PERSON> } from "lucide-react"

interface Notification {
  id: number
  message: string
  timestamp: string
}

interface NotificationCardProps {
  onClose: () => void
  notifications: Notification[]
}

export function NotificationCard({
  onClose,
  notifications,
}: NotificationCardProps) {
  return (
    <div
      className="mb-4 flex flex-col space-y-4"
      data-testid="notifications-container"
    >
      {notifications.map((notification) => (
        <div
          className="flex items-start justify-between rounded-md border-l-4 border-warning bg-warning-light p-4 sm:items-center"
          key={notification.id}
        >
          <div className="flex items-start gap-4 sm:items-center">
            <AlertTriangle className="mt-1 h-5 w-5 text-warning sm:mt-0" />
            <div>
              <p className="text-sm font-medium text-foreground">
                {notification.message}
              </p>
              <p className="mt-1 text-xs text-muted-foreground">
                {notification.timestamp}
              </p>
            </div>
          </div>
          <button
            aria-label="Close notification"
            className="text-muted-foreground transition-colors hover:text-foreground"
            onClick={onClose}
            type="button"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ))}
    </div>
  )
}
