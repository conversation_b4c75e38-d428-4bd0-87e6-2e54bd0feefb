import { toast } from "sonner"
import { useEffect, useState } from "react"

import { usePaymentsListQuery } from "@/data/payments/payments.query"
import { usePayeesListQuery } from "@/data/payees/payees.query"
import { useAccountsListQuery } from "@/data/account/account.query"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

import { PayeesAndPayments } from "./components/PayeesAndPayments"
import { NotificationCard } from "./components/NotificationCard"
import { AccountsCarousel } from "./components/AccountCarousel"

export function DashboardPage() {
  const [isNotificationVisible, setNotificationVisible] = useState(false)
  const handleCloseNotification = () => setNotificationVisible(false)

  const { entity } = useLoaderData()
  const entityId = entity?.id as string

  const {
    data: accounts,
    isLoading: accountsLoading,
    isError: accountsError,
    refetch: refetchAccounts,
    isFetching: accountsFetching,
  } = useAccountsListQuery()

  useEffect(() => {
    refetchAccounts()
  }, [entityId, refetchAccounts])

  const {
    data: payeesData,
    isLoading: payeesLoading,
    isError: payeesError,
  } = usePayeesListQuery({
    entityId,
    pageNumber: 1,
    pageSize: 4,
  })

  const payees = payeesData?.items || []

  // Fetch Payments
  const {
    data: paymentsData,
    isLoading: paymentsLoading,
    isError: paymentsError,
  } = usePaymentsListQuery({
    entityId,
    pageNumber: 1,
    pageSize: 10,
    variant: "default",
  })

  useEffect(() => {
    if (paymentsError) {
      toast.error("Failed to fetch payments")
    }
  }, [paymentsError])

  const payments =
    paymentsData?.data
      .filter((payment) => payment.paymentType === "OutboundPayment")
      .slice(0, 4) ?? []

  // Only show the full page loading spinner on initial load
  if (accountsLoading || payeesLoading || paymentsLoading) {
    return (
      <div className="mt-1 flex h-96 w-full items-center justify-center">
        <LoadingSpinner size="8" />
      </div>
    )
  }

  if (accountsError || payeesError || paymentsError) {
    return <p className="text-center text-destructive">Failed to load data.</p>
  }

  return (
    <div>
      {/* Notification Card */}
      <div className="mb-4 w-full">
        {isNotificationVisible && (
          <NotificationCard
            notifications={[
              {
                id: 1,
                message: "Payment approval request from Sarah Doe",
                timestamp: "12/10/24 16:00",
              },
            ]}
            onClose={handleCloseNotification}
          />
        )}
      </div>

      {/* Accounts Carousel with Loading State */}
      <div className="relative mb-8 w-full">
        <AccountsCarousel accounts={accounts || []} />
      </div>

      {/* Payees and Payments Section */}
      <div className="w-full">
        <PayeesAndPayments payees={payees} payments={payments} />
      </div>
    </div>
  )
}
