import { useState, useEffect } from "react"
import { useRouter, useSearch } from "@tanstack/react-router"
import {
  IAdditionalDetails,
  IPaymentDetails,
  IPaymentForm,
} from "@/data/payments/payments.interface"
import { Step, Stepper } from "@/components/base/triggers/Stepper"
import { usePaymentQuery } from "@/data/payments/payments.query"
import { PaymentDetailsForm } from "./PaymentsDetailsForm"
import { PaymentReviewStep } from "./PaymentReviewStep"
import { AdditionalDetailsForm } from "./AdditionalDetailsForm"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { parsePaymentAmount } from "@/lib/currency.utils"

interface SendPaymentsStepperProps {
  entityId: string
}

export function SendPaymentsStepper({ entityId }: SendPaymentsStepperProps) {
  const [activeStep, setActiveStep] = useState(0)
  const [maxStep, setMaxStep] = useState(0)
  const [payment, setPayment] = useState<Partial<IPaymentForm>>()
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const search: any = useSearch({
    strict: false,
  })

  const paymentId = search.paymentId

  const { data: existingPayment, isLoading: paymentLoading } = usePaymentQuery(
    paymentId || "",
  )

  // Load existing payment data
  useEffect(() => {
    if (existingPayment && paymentId) {
      setIsLoading(true)

      try {
        const numericAmount = parsePaymentAmount(existingPayment.amount) ?? 0

        const paymentData: Partial<IPaymentForm> = {
          details: {
            fromAccount: existingPayment.fromAccount.clientAccount.virtualIban,
            currencySend: existingPayment.currency,
            sendAmount: numericAmount,
            currencyReceive: existingPayment.fromAccount.currency,
            receiveAmount: numericAmount,
            payee: existingPayment.toAccount?.id,
            paymentDate: existingPayment.valueDate,
          },
          additionalDetails: {
            purpose: existingPayment.purpose,
            paymentReference: existingPayment.reference,
          },
        }

        setPayment(paymentData)
        setMaxStep(2)
      } catch (error) {
        console.error("Error setting up payment data:", error)
      } finally {
        setIsLoading(false)
      }
    }
  }, [existingPayment, paymentId])

  function handleEdit(step: number) {
    setActiveStep(step)
  }

  function onDetailsNext(payload: IPaymentDetails) {
    setPayment({ ...payment, details: payload })
    setActiveStep(1)
    if (maxStep < 1) setMaxStep(1)
  }

  function onAdditionalDetailsNext(payload: IAdditionalDetails) {
    setPayment({ ...payment, additionalDetails: payload })
    setActiveStep(2)
    if (maxStep < 2) setMaxStep(2)
  }

  function handleComplete() {
    // TODO: Implement payment submission
  }

  function onCancel() {
    const fromRoute = search.fromRoute
    if (fromRoute) {
      router.navigate({
        to: fromRoute,
      })
    } else {
      router.navigate({
        to: "/$entityId/payments",
        params: {
          entityId,
        },
      })
    }
  }

  function getAvailableBalance() {
    if (!payment?.details?.fromDetails) return 0
    const balance = payment.details.fromDetails.balances?.find(
      (b) => b.currency === payment?.details?.currencySend,
    )
    return balance?.balance || 0
  }

  if ((paymentId && paymentLoading) || isLoading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <LoadingSpinner className="h-8 w-8" />
        <span className="ml-3">Loading payment details...</span>
      </div>
    )
  }

  return (
    <Stepper onStepChange={(step) => setActiveStep(step)} value={activeStep}>
      <Step title="Payee and amount">
        <PaymentDetailsForm
          entityId={entityId}
          getAvailableBalance={getAvailableBalance}
          onCancel={onCancel}
          onInvalid={() => setMaxStep(0)}
          onNext={onDetailsNext}
          payment={payment}
        />
      </Step>
      <Step disabled={maxStep < 1} title="Additional details">
        <AdditionalDetailsForm
          entityId={entityId}
          onCancel={onCancel}
          onEdit={handleEdit}
          onInvalid={() => setMaxStep(1)}
          onNext={onAdditionalDetailsNext}
          payment={payment}
        />
      </Step>
      <Step disabled={maxStep < 2} title="Review">
        {payment && (
          <PaymentReviewStep
            entityId={entityId}
            getAvailableBalance={getAvailableBalance}
            onCancel={onCancel}
            onComplete={handleComplete}
            onEdit={handleEdit}
            payment={payment}
          />
        )}
      </Step>
    </Stepper>
  )
}
