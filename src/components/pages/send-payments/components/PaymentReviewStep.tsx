import { useMemo, useState } from "react"
import {
  Edit2,
  AlertCircle,
  XIcon,
  AlertTriangle,
  InfoIcon,
} from "lucide-react"
import { format } from "date-fns"
import { useNavigate, useSearch } from "@tanstack/react-router"
import { compareCurrencyDigits } from "@/lib/currency.utils"
import { Currency } from "@/lib/constants/currency.constants"
import { getAccountName } from "@/lib/bank.utils"
import {
  usePaymentQuery,
  useValidateSignatoryAmount,
} from "@/data/payments/payments.query"
import {
  addPaymentMutation,
  approvePaymentMutation,
  approvePaymentWithFxEmbeddedMutation,
  submitPaymentWithVerificationMutation,
  updatePaymentMutation,
} from "@/data/payments/payments.mutation"
import { IPaymentForm } from "@/data/payments/payments.interface"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { RateWarningModal } from "@/components/pages/send-payments/components/modals/RateWarningModal"
import { RateQuoteModal } from "@/components/pages/send-payments/components/modals/RateQuoteModal"
import { ReviewRow } from "@/components/base/review/ReviewRow"
import { FormButtons, FormLayout } from "@/components/base/form/form"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyFlag } from "@/components/base/currency/CurrencyFlag"
import { SignatoryApprovalRequirement } from "./SignatoryApprovalRequirement"
import { RateConfirmationModal } from "./modals/rateConfirmationModal"
import { ApprovalModal } from "./modals/ApprovalModal"
import { DottedPaymentFlow } from "./DottedPaymentFlow"
import { PayeeDetailsExpandable } from "../../payees/components/PayeeDetailsExpandable"
import { v1 } from "uuid"
import { ROLES } from "@/lib/constants/roles.constants"
import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"
import { toast } from "sonner"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

interface PaymentReviewStepProps {
  payment: Partial<IPaymentForm>
  onEdit?: (value: number) => void
  onComplete?: (value: any) => void
  onCancel?: () => void
  entityId: string
  getAvailableBalance: () => number
}

export function PaymentReviewStep({
  payment,
  onEdit,
  onComplete,
  onCancel,
  getAvailableBalance,
  entityId,
}: PaymentReviewStepProps) {
  const navigate = useNavigate()
  const { mutate, isPending, isError, error, reset } = addPaymentMutation()
  const { mutate: submitPaymentMutation } =
    submitPaymentWithVerificationMutation()
  const { mutate: updatePayment } = updatePaymentMutation()
  const { mutate: approvePayment } = approvePaymentMutation()
  const [showRateWarning, setShowRateWarning] = useState(false)
  const [showApproval, setShowApproval] = useState(false)
  const [showRateQuote, setShowRateQuote] = useState(false)
  const [doNotShowAgain, setDoNotShowAgain] = useState(false)
  const [paymentDraftSaved, setPaymentDraftSaved] = useState(false)
  const [showRateConfirmation, setShowRateConfirmation] = useState(false)
  const [paymentAggregateId, setPaymentAggregateId] = useState<string | null>(
    null,
  )

  const [operationType, setOperationType] = useState<
    "submit" | "approve" | null
  >(null)
  const search: any = useSearch({
    strict: false,
  })

  const { data: existingPayment, isLoading: paymentLoading } = usePaymentQuery(
    search.paymentId || "",
  )

  const { data: entityAccess } = useEntityAccessQuery(entityId)
  const { entity } = useLoaderData()

  const _entityAccess = useMemo(() => {
    if (!entityAccess) return null
    if (!entity.id) return null
    return entityAccess?.find(
      (access) => access.entityId?.toLowerCase() === entity.id?.toLowerCase(),
    )
  }, [entityAccess, entity])

  const isApproveButtonVisible = useMemo(() => {
    if (!payment) return false

    if (!_entityAccess) return false
    if (
      [ROLES.APPROVER, ROLES.ADMINISTRATOR].some((e) =>
        _entityAccess?.roles?.includes(e),
      )
    )
      return true
    return false
  }, [_entityAccess, payment])

  // Get paymentId from search params for editing existing payments
  const inEditMode = search.paymentId ? true : false

  const currency = useMemo<Currency>(() => {
    return payment.details?.currencySend as Currency
  }, [payment.details?.currencySend])

  const receiveCurrency = useMemo<Currency>(() => {
    return payment.details?.currencyReceive as Currency
  }, [payment.details?.currencyReceive])

  const sendAmount = useMemo<number>(() => {
    try {
      return Number(payment.details?.sendAmount)
    } catch (error) {
      return 0
    }
  }, [payment.details?.sendAmount])

  const receiveAmount = useMemo<number>(() => {
    try {
      return Number(payment.details?.receiveAmount)
    } catch (error) {
      return 0
    }
  }, [payment.details?.receiveAmount])
  const { data: signatoryValidation } = useValidateSignatoryAmount(
    Number(payment.details?.sendAmount),
    currency as string,
  )

  const isSelfApprover = signatoryValidation?.isSelfApproved

  if (signatoryValidation?.errorMessage) {
    toast.error(signatoryValidation?.errorMessage)
  }

  // self approver step 1
  const handleGetRate = () => {
    if (isSelfApprover && currency === receiveCurrency) {
      setShowApproval(true)
      return
    }
    if (isSelfApprover && currency !== receiveCurrency) {
      onSaveDraft(false, "approve")
      // handleVerificationSubmit will be called in onSaveDraft's onSuccess callback
      return
    }
  }

  //not self approver step 1
  const handleApprovalConfirm = () => {
    setPaymentDraftSaved(true)
    onSaveDraft(false, "submit")
    // user is not self approver, call handleVerificationSubmit after draft is saved
    setShowApproval(false)
    // Don't call handleVerificationSubmit here - it will be called in onSaveDraft's onSuccess callback
  }

  const handleRateWarningNext = () => {
    setShowRateWarning(false)
    setShowRateQuote(true)
  }

  const handleRateQuoteCancel = () => {
    setShowRateQuote(false)
  }

  const { mutate: approvePaymentWithFxEmbedded } =
    approvePaymentWithFxEmbeddedMutation()

  const handleRateQuoteAccept = (
    fxQuoteRequestId: string,
    fxBrokeredQuoteId: string,
  ) => {
    setShowRateQuote(false)
    if (payment?.details) {
      payment.details.fxQuoteRequestId = fxQuoteRequestId
      payment.details.fxBrokeredQuoteId = fxBrokeredQuoteId
    }

    approvePaymentWithFxEmbedded(
      {
        paymentId: paymentAggregateId as string,
        request: {
          fxBrokeredQuoteId: fxBrokeredQuoteId,
          fxQuoteRequestId: fxQuoteRequestId,
        },
      },
      {
        onSuccess: (data: any) => {
          navigate({ to: "/$entityId/payments", params: { entityId } })
          onComplete?.(data)
        },
      },
    )
  }

  const handleVerificationSubmit = (code: string, paymentId?: string) => {
    const targetPaymentId = paymentId || paymentAggregateId
    if (!targetPaymentId) {
      return // Don't proceed if no payment ID is available
    }

    if (isSelfApprover && currency !== receiveCurrency) {
      const dontShowAgain = JSON.parse(
        localStorage.getItem("dontShowRateConfirmationModal") || "false",
      )
      if (dontShowAgain) {
        processPayment()
      } else {
        setShowRateConfirmation(true)
      }

      submitPaymentMutation({
        paymentId: targetPaymentId,
      })
      return
    } else if (isSelfApprover && currency === receiveCurrency) {
      submitPaymentMutation(
        {
          paymentId: targetPaymentId,
        },
        {
          onSuccess: (data: { aggregateId: string; correlationId: string }) => {
            onComplete?.(data)
            approvePayment(
              {
                paymentId: targetPaymentId,
              },
              {
                onSuccess: (data: {
                  aggregateId: string
                  correlationId: string
                }) => {
                  navigate({
                    to: "/$entityId/payments",
                    params: { entityId },
                  })
                  onComplete?.(data)
                },
              },
            )
          },
        },
      )
    } else if (!isSelfApprover && currency !== receiveCurrency) {
      // if user is not self approver and same currency, we need to confirm the payment
      submitPaymentMutation(
        {
          paymentId: targetPaymentId,
        },
        {
          onSuccess: (data: { aggregateId: string; correlationId: string }) => {
            navigate({ to: "/$entityId/payments", params: { entityId } })
            onComplete?.(data)
          },
        },
      )
    } else {
      // if user is not self approver and same currency, we need to confirm the payment
      submitPaymentMutation(
        {
          paymentId: targetPaymentId,
        },
        {
          onSuccess: (data: { aggregateId: string; correlationId: string }) => {
            // approve the payment problematical TO-DO FIX IT BACKEND SHOULD FIX THIS
            navigate({ to: "/$entityId/payments", params: { entityId } })
            onComplete?.(data)
          },
        },
      )
    }
  }

  const onSaveDraft = (
    isNavigate: boolean = true,
    operationType: "submit" | "approve" = "submit",
  ) => {
    // this is to prevent the payment from being saved twice
    setOperationType(operationType)
    if (inEditMode) {
      updatePayment(
        {
          paymentId: existingPayment?.id as string,
          payment: {
            type: existingPayment?.type,
            amount: Number(payment.details?.sendAmount),
            currency: payment.details?.currencySend as string,
            clientAccountId:
              payment.details?.fromDetails?.clientAccountId ?? "",
            payeeId: payment.details?.toDetails?.id as string,
            reference: payment.additionalDetails?.paymentReference,
            purpose: payment.additionalDetails?.purpose,
            feeOption: existingPayment?.feeOption,
            valueDate: payment.details?.paymentDate as string,
            idempotencyKey: v1(),
            copyNumber: Math.floor(Math.random() * 1000000),
            saveAsDraft: true,
          },
        },
        {
          onSuccess: (data: { aggregateId: string; correlationId: string }) => {
            if (isNavigate) {
              navigate({ to: "/$entityId/payments", params: { entityId } })
            } else {
              setPaymentAggregateId(data.aggregateId)
              // Call handleVerificationSubmit after draft is saved for non-self approvers
              if (operationType === "submit") {
                handleVerificationSubmit("", data.aggregateId)
              }
              // Also call handleVerificationSubmit for self-approvers with different currencies
              if (operationType === "approve") {
                handleVerificationSubmit("", data.aggregateId)
              }
            }
            setPaymentDraftSaved(true)
          },
        },
      )
    } else {
      if (paymentDraftSaved) {
        // If draft is already saved, call handleVerificationSubmit directly
        if (operationType === "submit") {
          handleVerificationSubmit("", paymentAggregateId || undefined)
        }
        // Also call handleVerificationSubmit for self-approvers with different currencies
        if (operationType === "approve") {
          handleVerificationSubmit("", paymentAggregateId || undefined)
        }
        return
      }
      mutate(
        {
          payment: payment as IPaymentForm,
          isDraft: true,
        },
        {
          onSuccess: (data: { aggregateId: string; correlationId: string }) => {
            if (isNavigate) {
              navigate({ to: "/$entityId/payments", params: { entityId } })
            } else {
              setPaymentAggregateId(data.aggregateId)
              // Call handleVerificationSubmit after draft is saved for non-self approvers
              if (operationType === "submit") {
                handleVerificationSubmit("", data.aggregateId)
              }
              // Also call handleVerificationSubmit for self-approvers with different currencies
              if (operationType === "approve") {
                handleVerificationSubmit("", data.aggregateId)
              }
            }
            setPaymentDraftSaved(true)
            onComplete?.(data)
          },
        },
      )
    }
  }

  const handleSaveDraft = () => {
    if (inEditMode) {
      updatePayment(
        {
          paymentId: existingPayment?.id as string,
          payment: {
            type: existingPayment?.type,
            amount: Number(payment.details?.sendAmount),
            currency: payment.details?.currencySend as string,
            clientAccountId:
              payment.details?.fromDetails?.clientAccountId ?? "",
            payeeId: payment.details?.toDetails?.id as string,
            reference: payment.additionalDetails?.paymentReference,
            purpose: payment.additionalDetails?.purpose,
            feeOption: existingPayment?.feeOption,
            valueDate: payment.details?.paymentDate as string,
            idempotencyKey: v1(),
            copyNumber: Math.floor(Math.random() * 1000000),
            saveAsDraft: true,
          },
        },
        {
          onSuccess: () => {
            navigate({ to: "/$entityId/payments", params: { entityId } })
          },
        },
      )
    } else if (paymentDraftSaved) {
      navigate({ to: "/$entityId/payments", params: { entityId } })
    } else {
      mutate(
        {
          payment: payment as IPaymentForm,
          isDraft: true,
        },
        {
          onSuccess: (data: { aggregateId: string; correlationId: string }) => {
            navigate({ to: "/$entityId/payments", params: { entityId } })
            onComplete?.(data)
          },
        },
      )
    }
  }

  const getApprovalButtonText = () => {
    if (isSelfApprover && isApproveButtonVisible) {
      if (receiveCurrency === currency) {
        return "Approve payment"
      }
      return "Get rate and approve"
    }
    return "Send for approval"
  }

  const processPayment = () => {
    setShowRateConfirmation(false)
    setShowRateQuote(true)
    submitPaymentMutation({
      paymentId: paymentAggregateId as string,
    })
  }

  return (
    <FormLayout>
      {isError && (
        <Alert className="relative bg-background" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error?.message || "Something went wrong."}
          </AlertDescription>
          <div className="absolute right-0 top-0">
            <Button
              className="rounded-full"
              onClick={() => reset()}
              size="icon"
              variant="ghost"
            >
              <XIcon className="h-4 w-4 text-destructive" />
            </Button>
          </div>
        </Alert>
      )}

      {/* Payment Overview */}
      <Card className="rounded-2xl border-none bg-sidebar">
        <CardContent>
          <ReviewRow
            className="flex items-center justify-between p-4"
            label="Additional details"
            labelClassName="font-semibold"
            noBorder
            value={
              <Button
                className="px-1"
                onClick={() => onEdit?.(0)} // Edit Step 1
                size="sm"
                variant="link"
              >
                <Edit2 className="h-4 w-4" />
                Edit
              </Button>
            }
          />
          <div className="flex h-32 items-center justify-between">
            <DottedPaymentFlow
              className="mb-0 mt-4 h-full"
              name={payment?.details?.toDetails?.accountName}
            />
            <div className="flex h-full w-full flex-col justify-between pb-0 pr-4 pt-2 text-base">
              <div className="flex items-center justify-between">
                <p className="text-sm">From</p>
                <p className="text-sm font-medium">
                  {getAccountName(payment?.details?.fromDetails)}
                </p>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-sm">You send</p>
                {currency && (
                  <div className="flex flex-none items-center gap-x-2">
                    <CurrencyText
                      amount={sendAmount}
                      className={`text-sm font-medium`}
                      currency={currency}
                    />
                    <CurrencyFlag currency={currency} size="md" />
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between">
                <p className="text-sm">To payee</p>
                <p className="text-sm font-medium">
                  {payment?.details?.toDetails?.accountName}
                </p>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-sm">Payee receives</p>
                {receiveCurrency && (
                  <div className="flex flex-none items-center gap-x-2">
                    <CurrencyText
                      amount={receiveAmount}
                      className={`text-sm font-medium`}
                      currency={receiveCurrency}
                    />
                    <CurrencyFlag currency={receiveCurrency} size="md" />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="mt-0 flex flex-col items-end p-4 pt-0">
            {payment.details?.toDetails && (
              <PayeeDetailsExpandable
                className="!w-[340px]"
                payee={payment.details?.toDetails}
                noBorder
                noCopy
              />
            )}
          </div>

          {compareCurrencyDigits(
            getAvailableBalance(),
            currency,
            sendAmount,
          ) && (
            <Card>
              <div className="flex items-start gap-4 p-4">
                <AlertTriangle className="h-6 w-6 flex-shrink-0 text-warning" />
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-semibold">
                    Available balance{" "}
                    <CurrencyText
                      amount={getAvailableBalance()}
                      className="text-sm font-medium"
                      currency={currency}
                    />
                  </p>
                  <p className="text-sm text-muted-foreground">
                    You do not have enough available balance to perform this
                    payment. Please ensure you have enough in your account at
                    the time of payment.
                  </p>
                </div>
              </div>
            </Card>
          )}
          {currency !== receiveCurrency && (
            <Card className="mt-4">
              <div className="flex items-start gap-4 p-4">
                <InfoIcon className="h-6 w-6 flex-shrink-0 text-info" />
                <div className="flex flex-col gap-2">
                  {payment?.details?.lastEditedField === "receive" ? (
                    <>
                      <p className="text-sm font-semibold">
                        The estimated amount that will be sent is{" "}
                        <CurrencyText
                          amount={sendAmount}
                          currency={currency}
                          displayModes={["amount", "code"]}
                        />
                      </p>
                      <p className="text-sm text-muted-foreground">
                        The exact amount will be set based on the FX rate at the
                        time the payment gets its final{" "}
                        {signatoryValidation?.signatoryAmountBand
                          ?.signatoryRules
                          ? "signatory"
                          : ""}{" "}
                        approval.
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-sm font-semibold">
                        The estimated amount that will be received is{" "}
                        <CurrencyText
                          amount={receiveAmount}
                          currency={receiveCurrency}
                          displayModes={["amount", "code"]}
                        />
                      </p>
                      <p className="text-sm text-muted-foreground">
                        The exact amount will be set based on the FX rate at the
                        time the payment gets its final{" "}
                        {signatoryValidation?.signatoryAmountBand
                          ?.signatoryRules
                          ? "signatory"
                          : ""}{" "}
                        approval.
                      </p>
                    </>
                  )}
                </div>
              </div>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Additional Details */}
      <Card className="mb-6 mt-4 rounded-2xl border-none bg-sidebar">
        <CardContent className="p-4 text-base">
          <ReviewRow
            className="flex items-center justify-between"
            label="Additional details"
            labelClassName="font-semibold"
            noBorder
            value={
              <Button
                className="px-1"
                onClick={() => onEdit?.(1)} // Edit Step 1
                size="sm"
                variant="link"
              >
                <Edit2 className="h-4 w-4" />
                Edit
              </Button>
            }
          />
          <ReviewRow
            label="Payment date"
            noBorder
            value={format(new Date(payment.details?.paymentDate || ""), "PPP")}
          />
          {payment.additionalDetails?.purpose && (
            <ReviewRow
              label="Payment purpose"
              noBorder
              value={payment.additionalDetails?.purpose}
            />
          )}
          {payment.additionalDetails?.purposeOtherText &&
            payment.additionalDetails?.purpose?.toLowerCase() === "other" && (
              <ReviewRow
                label="Payment purpose (Other)"
                noBorder
                value={payment.additionalDetails?.purposeOtherText}
              />
            )}
          {payment.additionalDetails?.paymentReference && (
            <ReviewRow
              label="Payment reference"
              noBorder
              value={payment.additionalDetails?.paymentReference}
            />
          )}
        </CardContent>
      </Card>

      {/* Approval Requirement Card */}
      {signatoryValidation?.signatoryAmountBand?.signatoryRules && (
        <SignatoryApprovalRequirement
          rules={signatoryValidation.signatoryAmountBand.signatoryRules}
        />
      )}

      {/* Confirmation Button */}
      <FormButtons
        isValid={signatoryValidation?.isValid}
        nextButtonProps={{ loading: isPending }}
        nextButtonText={getApprovalButtonText()}
        onCancel={onCancel}
        onNext={
          isSelfApprover && isApproveButtonVisible
            ? handleGetRate
            : handleApprovalConfirm
        }
        onSaveDraft={handleSaveDraft}
        saveDraftButtonText={"Save draft"}
      />

      {/* Rate Warning Modal */}
      <RateWarningModal
        doNotShowAgain={doNotShowAgain}
        onCancel={() => setShowRateWarning(false)}
        onNext={handleRateWarningNext}
        onOpenChange={setShowRateWarning}
        open={showRateWarning}
        setDoNotShowAgain={setDoNotShowAgain}
      />

      {/* Rate Quote Modal */}
      <RateQuoteModal
        currency={currency}
        entityId={entityId}
        onCancel={handleRateQuoteCancel}
        onConfirm={handleRateQuoteAccept}
        onOpenChange={setShowRateQuote}
        open={showRateQuote}
        receiveAmount={receiveAmount}
        receiveCurrency={receiveCurrency}
        sendAmount={sendAmount}
        lastEditedField={payment?.details?.lastEditedField}
      />

      {/* Approval Modal - self approver step 2 */}
      <ApprovalModal
        onCancel={() => setShowApproval(false)}
        onConfirm={handleApprovalConfirm}
        onOpenChange={setShowApproval}
        open={showApproval}
      />

      {/* Rate Confirmation Modal */}
      <RateConfirmationModal
        isOpen={showRateConfirmation}
        onClose={() => setShowRateConfirmation(false)}
        onConfirm={() => {
          processPayment()
        }}
        onDontShowAgainChange={(dontShow: boolean) => {
          setDoNotShowAgain(dontShow)
          localStorage.setItem(
            "dontShowRateConfirmationModal",
            dontShow.toString(),
          )
        }}
      />
    </FormLayout>
  )
}
