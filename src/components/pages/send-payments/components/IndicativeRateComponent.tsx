import { Loader2 } from "lucide-react"

import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { InfoCircledIcon } from "@radix-ui/react-icons"

interface IndicativeRateComponentProps {
  fromCurrency: string
  toCurrency: string
  rate?: number
  isLoading: boolean
}

export function IndicativeRateComponent({
  fromCurrency,
  toCurrency,
  rate,
  isLoading,
}: IndicativeRateComponentProps) {
  if (fromCurrency === toCurrency) {
    return null
  }

  return (
    <div className="flex w-full items-center justify-between rounded-xl bg-accent p-3 text-sm">
      <div className="flex items-center gap-2">
        <span>
          Indicative rate {fromCurrency}/{toCurrency}
        </span>

        <Tooltip>
          <TooltipTrigger asChild>
            <InfoCircledIcon className="h-4 w-4" />
          </TooltipTrigger>
          <TooltipContent>
            <p>This rate is indicative and may change at time of payment.</p>
          </TooltipContent>
        </Tooltip>
      </div>
      <div className="font-semibold">
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          rate?.toFixed(4)
        )}
      </div>
    </div>
  )
}
