import { useMemo } from "react"

import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import ArgentexIcon2 from "@/components/base/icons/ArgentexIcon2"

interface DottedPaymentFlowProps {
  name?: string
  className?: string
}

export function DottedPaymentFlow({ name, className }: DottedPaymentFlowProps) {
  const initials = useMemo(() => {
    if (!name) return " "
    return (
      name
        ?.split(" ")
        ?.slice(0, 3)
        .map((s) => s?.[0] ?? "")
        .join("") || " "
    )
  }, [name])

  return (
    <div
      className={cn(
        "relative mb-16 mt-12 flex w-20 flex-col items-center",
        className,
      )}
    >
      <div className="relative mb-2 flex h-10 w-10 items-center justify-center rounded-full bg-primary text-background">
        <ArgentexIcon2 />
      </div>
      <div className="h-full w-0 border-l-2 border-dotted border-foreground/25"></div>
      <div className="absolute bottom-11">
        <div className="h-0 w-0 border-l-8 border-r-8 border-t-8 border-foreground/25 border-l-transparent border-r-transparent"></div>
      </div>

      <Avatar className="mt-3 h-10 w-10 border border-muted-foreground/20 bg-muted text-muted-foreground">
        <AvatarFallback className="text-sm">{initials}</AvatarFallback>
      </Avatar>
    </div>
  )
}
