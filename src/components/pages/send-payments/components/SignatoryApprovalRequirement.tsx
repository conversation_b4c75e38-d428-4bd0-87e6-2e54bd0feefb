import { AlertTriangle, InfoIcon } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"

interface SignatoryRule {
  approverLevel: string
  requiredCount: number
  // Add other properties if needed
}

interface SignatoryApprovalRequirementProps {
  rules?: SignatoryRule[]
  className?: string
}

export function SignatoryApprovalRequirement({
  rules = [],
  className = "",
}: SignatoryApprovalRequirementProps) {
  if (!rules?.length) return null

  const formatRequirements = () => {
    const levelCounts = new Map()

    // Count occurrences of each level
    rules.forEach((rule) => {
      const level = rule.approverLevel
      levelCounts.set(level, rule.requiredCount)
    })

    // Define the order (A > B > C)
    const levelOrder = ["LevelA", "LevelB", "LevelC"]

    // Sort and format the requirements
    return Array.from(levelCounts.entries())
      .sort((a, b) => levelOrder.indexOf(a[0]) - levelOrder.indexOf(b[0]))
      .map(([level, count]) => {
        // Extract just the letter from the level (e.g., "LevelA" -> "A")
        const letter = level.replace("Level", "")
        return `${count}${letter}`
      })
      .join(" and ")
  }

  return (
    <>
      <Card className={`mb-6 shadow-sm ${className}`}>
        <div className="flex items-start gap-4 p-4">
          <InfoIcon className="h-6 w-6 flex-shrink-0 text-info" />
          <div className="flex flex-col gap-2">
            <h4 className="text-sm font-medium">
              This payment requires approval
            </h4>
            <p className="mt-1 text-sm text-muted-foreground">
              Minimum signatory requirements of {formatRequirements()}
            </p>
          </div>
        </div>
      </Card>
    </>
  )
}
