import { useMemo } from "react"
import { useForm } from "@tanstack/react-form"

import { useFormInvalid } from "@/hooks/use-form-invalid"
import { usePaymentPurposeQuery } from "@/data/payments/payments.query"
import {
  IAdditionalDetails,
  IPaymentDetails,
  IPaymentForm,
} from "@/data/payments/payments.interface"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { TrailingSpinner } from "@/components/base/loading-spinner/TrailingSpinner"
import { FormLayout, FormField, FormButtons } from "@/components/base/form/form"
import { SimpleAutoComplete } from "@/components/base/form/autocomplete"

interface AdditionalDetailsFormProps {
  payment?: Partial<IPaymentForm>
  onNext: (data: IAdditionalDetails) => void
  onCancel?: () => void
  onSaveDraft?: () => void
  onEdit?: (value: number) => void
  onInvalid?: () => void
  entityId: string
}

export function AdditionalDetailsForm({
  payment,
  onNext,
  onCancel,
  onSaveDraft,
  onEdit,
  onInvalid,
  entityId,
}: AdditionalDetailsFormProps) {
  const maxReferenceLength = 140

  const additionalDetails = useMemo<IAdditionalDetails | undefined>(
    () => payment?.additionalDetails,
    [payment],
  )

  const details = useMemo<IPaymentDetails | undefined>(
    () => payment?.details,
    [payment],
  )

  const form = useForm({
    defaultValues: {
      paymentReference: additionalDetails?.paymentReference,
      purpose: additionalDetails?.purpose,
      purposeOtherText: additionalDetails?.purposeOtherText,
    },
    onSubmit: ({ value }) => {
      onNext(value)
    },
  })

  useFormInvalid(form, onInvalid)

  const { data: paymentPurpose, isLoading: isPaymentPurposeLoading } =
    usePaymentPurposeQuery(details?.toDetails?.bank?.country?.codeIso2)

  return (
    <FormLayout>
      {/* Payment Purpose */}
      <form.Field
        children={(field) => (
          <FormField field={field} label="Purpose of payment" required>
            <TrailingSpinner isLoading={isPaymentPurposeLoading}>
              <SimpleAutoComplete
                disabled={isPaymentPurposeLoading}
                emptyMessage="No payment purpose found"
                items={
                  paymentPurpose?.map((purpose) => ({
                    value: purpose.code,
                    label: purpose.description,
                  })) || []
                }
                onSelectedValueChange={(value) => field.handleChange(value)}
                placeholder="Select payment purpose"
                selectedValue={field.state.value}
              />
            </TrailingSpinner>
          </FormField>
        )}
        listeners={{
          onChange: ({ value }) => {
            form.setFieldValue("purposeOtherText", undefined)
          },
        }}
        name="purpose"
        validators={{
          onChange: ({ value }) =>
            value?.trim() ? undefined : "Payment purpose is required",
        }}
      />

      <form.Subscribe selector={(state) => state.values.purpose}>
        {(purpose) =>
          purpose?.toLowerCase() === "other" ? (
            <form.Field
              children={(field) => (
                <FormField field={field} label="Payment purpose" required>
                  <Input
                    className="bg-background"
                    id="payment-purpose-other-text"
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter payment purpose"
                    value={field.state.value}
                  />
                </FormField>
              )}
              name="purposeOtherText"
              validators={{
                onChange: ({ value }) =>
                  value?.trim() ? undefined : "Payment purpose is required",
              }}
            />
          ) : null
        }
      </form.Subscribe>

      {/* Payment Reference */}
      <form.Field
        children={(field) => (
          <FormField field={field} label="Payment reference" required>
            <div className="relative">
              <Textarea
                className="bg-background"
                id="payment-reference"
                maxLength={maxReferenceLength}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter payment reference"
                rows={4}
                value={field.state.value}
              />
              <div className="absolute bottom-2 right-3 text-sm text-muted-foreground">
                {field.state.value?.length ?? 0}/{maxReferenceLength}
              </div>
            </div>
          </FormField>
        )}
        name="paymentReference"
        validators={{
          onChange: ({ value }) =>
            value?.trim() ? undefined : "Payment reference is required",
        }}
      />

      {/* Action Buttons */}
      <FormButtons
        cancelButtonText="Cancel"
        form={form}
        nextButtonText="Next"
        onCancel={onCancel}
      />
    </FormLayout>
  )
}
