import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { AddPayeePage } from "../../add-payee/AddPayeePage"
import { useState } from "react"

interface AddPayeeDialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onClose?: () => void
  onPayeeAdded?: (id: string) => void
  entityId: string
}

export function AddPayeeDialog({
  open,
  onOpenChange,
  onPayeeAdded,
  onClose,
  entityId,
}: AddPayeeDialogProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    onOpenChange?.(open)
  }

  const handleClose = () => {
    setIsOpen(false)
    onOpenChange?.(false)
    onClose?.()
  }

  const handlePayeeAdded = (id: string) => {
    onPayeeAdded?.(id)
    handleClose()
  }

  return (
    <Dialog onOpenChange={handleOpenChange} open={open ?? isOpen}>
      <DialogTrigger asChild>
        <Button className="rounded-full" size="sm" variant="link">
          <h1>Add new payee</h1>
        </Button>
      </DialogTrigger>

      <DialogContent className="m-2 max-w-xl overflow-hidden px-0">
        <DialogTitle className="px-6 pt-6 text-center text-xl font-semibold">
          Add Payee
        </DialogTitle>
        <DialogDescription className="sr-only">
          Add a new payee to your account
        </DialogDescription>
        <div className="overflow-hidden">
          <AddPayeePage
            entityId={entityId}
            modal={true}
            onClose={handleClose}
            onPayeeAdded={handlePayeeAdded}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
