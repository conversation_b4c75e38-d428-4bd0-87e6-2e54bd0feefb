import { useEffect, use<PERSON><PERSON><PERSON>, use<PERSON>tate } from "react"
import { CalendarI<PERSON>, Octagon, X } from "lucide-react"
import { useStore } from "@tanstack/react-store"
import { useSearch, useNavigate } from "@tanstack/react-router"
import { useForm } from "@tanstack/react-form"
import { setHours, setMinutes, parseISO } from "date-fns"

import { currencies } from "@/lib/constants/currency.constants"
import { Currency } from "@/lib/constants/currency.constants"
import { useFormInvalid } from "@/hooks/use-form-invalid"
import {
  IPaymentDetails,
  IPaymentForm,
} from "@/data/payments/payments.interface"
import { usePayeesListQuery } from "@/data/payees/payees.query"
import { useAccountsListQuery } from "@/data/account/account.query"
import { Account } from "@/data/account/account.interface"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>L<PERSON>out,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/base/form/form"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencySelector } from "@/components/base/currency/CurrencySelector"
import { IndicativeRateComponent } from "./IndicativeRateComponent"
import { DottedPaymentFlow } from "./DottedPaymentFlow"
import { useTradeRateQuery } from "@/data/trade/trade.query"
import { PopoverContent } from "@/components/ui/popover"
import { Popover, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format2Date } from "@/lib/date.utils"
import { format } from "date-fns"
import { useFxValueDatesQuery } from "@/data/payments/payments.query"
import { Calendar } from "@/components/ui/calendar"
import { PayeeBankDetailsDialog } from "./modals/PayeeBankDetailsDialog"
import { useUpdatePayeeMutation } from "@/data/payees/payees.mutation"
import { AddPayeeDialog } from "./AddPayeeDialog"
import { queryClient } from "@/main"
import { queryKeys } from "@/lib/constants/query.constants"
import { AccountSelector } from "../../account/components/AccountSelector"
import { useCurrencySelector } from "@/hooks/useCurrencySelector"
import { AlertTitle, AlertDescription } from "@/components/base/alert-box"
import { useTradableCurrenciesQuery } from "@/data/global/global.query"
import { CurrencyEnter } from "@/components/base/currency/CurrencyEnter"
import { PayeeSelectorCombobox } from "./ui/PayeeSelectorCombobox"
import { VerificationModal } from "./modals/VerificationModal"
import {
  useStartPayeeVerificationMutation,
  useVerifyPayeeMutation,
} from "@/data/payees/payees.mutation"

interface PaymentDetailsFormProps {
  payment?: Partial<IPaymentForm>
  onNext: (data: IPaymentDetails) => void
  onInvalid?: () => void
  onCancel?: () => void
  entityId: string
  getAvailableBalance: () => number
}

// Helper: check if a date is a weekend
function isWeekend(date: Date) {
  const day = date.getDay()
  return day === 0 || day === 6
}

// Helper: get the earliest available date for non-FX (same-currency) payments
function getEarliestBusinessDay(afterToday = false) {
  let date = new Date()
  if (afterToday) {
    date.setDate(date.getDate() + 1)
  }
  // Find the next business day (skip weekends)
  while (isWeekend(date)) {
    date.setDate(date.getDate() + 1)
  }
  return format2Date(date)
}

// Helper: get the earliest available date from valueDateOptions, considering FX/non-FX and cut-off
function getEarliestAvailableDate(
  options: string[],
  isFx: boolean,
  isGbpToGbp: boolean,
) {
  const now = new Date()
  const cutoff = setMinutes(setHours(new Date(now), 15), 5) // 3:05pm today
  const todayStr = format2Date(now)
  const todayIsWeekend = isWeekend(now)

  if (!isFx) {
    // Non-FX: allow today if before cut-off and not weekend, else next business day
    if (!todayIsWeekend && now <= cutoff) {
      return todayStr
    } else {
      return getEarliestBusinessDay(true)
    }
  }

  // FX: Only allow valueDateOptions, skip weekends and today after cut-off for GBP to GBP
  if (!options?.length) return undefined
  const sorted = [...options].sort()
  for (const d of sorted) {
    const dateObj = parseISO(d)
    if (isWeekend(dateObj)) continue
    if (isGbpToGbp && d === todayStr) {
      if (todayIsWeekend || now > cutoff) continue
    }
    return d
  }
  return undefined
}

// Helper: get the earliest available business day from valueDates response (not weekend, not past)
function getEarliestBusinessDayFromOptions(options: string[]) {
  if (!options?.length) return undefined
  const now = new Date().setHours(0, 0, 0, 0)
  const sorted = [...options].sort()
  for (const d of sorted) {
    const dateObj = parseISO(d)
    if (!isWeekend(dateObj) && dateObj.getTime() >= now) {
      return d
    }
  }
  return undefined
}

export function PaymentDetailsForm({
  payment,
  onNext,
  onInvalid,
  onCancel,
  entityId,
}: PaymentDetailsFormProps) {
  const search: any = useSearch({
    strict: false,
  })

  const details = useMemo<IPaymentDetails | undefined>(
    () => payment?.details,
    [payment],
  )

  const sendCurrency =
    details?.currencySend ||
    (currencies.some((c) => c === search.fromCurrency)
      ? search.fromCurrency
      : "GBP")

  const receiveCurrency =
    details?.currencyReceive ||
    (currencies.some((c) => c === search.toCurrency)
      ? search.toCurrency
      : sendCurrency)

  const noFx = useMemo(() => {
    return sendCurrency === receiveCurrency
  }, [sendCurrency, receiveCurrency])

  const form = useForm<IPaymentDetails>({
    defaultValues: {
      fromAccount: details?.fromAccount || search.from,
      currencySend: sendCurrency,
      sendAmount: details?.sendAmount || "",
      currencyReceive: receiveCurrency,
      receiveAmount: details?.receiveAmount || "",
      payee: details?.payee || search?.to,
      paymentDate: details?.paymentDate || format2Date(new Date()),
      fxQuoteRequestId: details?.fxQuoteRequestId || "",
      fxBrokeredQuoteId: details?.fxBrokeredQuoteId || "",
    },
    onSubmit: ({ value }) => {
      const toDetails = form.getFieldValue("toDetails")
      const paymentDate = form.getFieldValue("paymentDate")

      // Validate payment date for FX trades
      if (!noFx && paymentDate) {
        if (!valueDates?.valueDateOptions?.includes(paymentDate)) {
          form.setFieldValue("paymentDate", "")
          return
        }
      }

      // For FX trades, ensure we have a valid rate before proceeding
      if (!noFx && !tradeResponse) {
        return
      }

      // Check if payee has completed SCA verification
      const selectedPayee = payees?.items.find(
        (p) => p.id === form.getFieldValue("payee"),
      )
      if (selectedPayee && !selectedPayee.isScaCompleted) {
        // Start SCA verification process
        handleScaVerificationStart(selectedPayee.id)
        return
      }

      if (
        payees?.items.find((p) => p.id === form.getFieldValue("payee"))
          ?.accountDetailsConfirmed
      ) {
        onNext({
          ...value,
          lastEditedField: fixedSide === "Sell" ? "send" : "receive",
        })
      } else {
        if (
          (toDetails?.iban || toDetails?.bank?.swiftBic) &&
          !confirmedPayeeDetails[toDetails?.id] &&
          // Only require IBAN/SWIFTBIC confirmation for non-UK payments or when sending currency is not GBP
          (toDetails?.bank?.country?.codeIso2 !== "GB" ||
            form.getFieldValue("currencyReceive") !== "GBP")
        ) {
          setIsPayeeDetailsModalOpen(true)
        } else {
          onNext({
            ...value,
            lastEditedField: fixedSide === "Sell" ? "send" : "receive",
          })
        }
      }
    },
  })

  const _sendCurrency = useStore(
    form.store,
    (state) => state.values.currencySend,
  )
  const _receiveCurrency = useStore(
    form.store,
    (state) => state.values.currencyReceive,
  )

  useFormInvalid(form, onInvalid)

  const navigate = useNavigate()

  const { data: accounts, isLoading: accountsLoading } = useAccountsListQuery()
  const { data: payees } = usePayeesListQuery({
    entityId,
    pageSize: 200,
  })

  const from = useStore(form.store, (state) => state.values.fromAccount)
  const to = useStore(form.store, (state) => state.values.payee)

  const [selectedAccount, setSelectedAccount] = useState<Account | undefined>(
    undefined,
  )

  const { data: tradableCurrencies, isLoading: tradableCurrenciesLoading } =
    useTradableCurrenciesQuery(selectedAccount?.clientAccountId ?? "", {
      enabled: !!selectedAccount?.clientAccountId,
    })

  const { availableCurrencies } = useCurrencySelector(
    selectedAccount ? [selectedAccount] : [],
  )

  const [fixedSide, setFixedSide] = useState<"Sell" | "Buy" | undefined>(
    undefined,
  )

  const [isCalendarOpen, setIsCalendarOpen] = useState(false)

  const [amount, setAmount] = useState<number | undefined>(undefined)
  const paymentDate = useStore(form.store, (state) => state.values.paymentDate)

  const {
    data: tradeResponse,
    isFetching: isTradeRateFetching,
    isError: isTradeRateError,
  } = useTradeRateQuery({
    buyCurrency: _receiveCurrency as Currency,
    sellCurrency: _sendCurrency as Currency,
    fixedSide: fixedSide,
    amount: amount,
    valueDate: paymentDate || "",
  })

  useEffect(() => {
    if (tradeResponse) {
      form.setFieldValue(
        "fxQuoteRequestId",
        tradeResponse.fxQuoteRequestId || "",
      )
      form.setFieldValue(
        "fxBrokeredQuoteId",
        tradeResponse.fxBrokeredQuoteId || "",
      )
      form.setFieldValue("receiveAmount", tradeResponse.buy?.amount)
      form.setFieldValue("sendAmount", tradeResponse.sell?.amount)
      form.setFieldValue("currencyReceive", tradeResponse.buy?.currency)
      form.setFieldValue("currencySend", tradeResponse.sell?.currency)
    }
  }, [tradeResponse])

  const { data: valueDates, isLoading: isValueDatesLoading } =
    useFxValueDatesQuery(sendCurrency, receiveCurrency)

  // Initialize state values only once when component mounts
  useEffect(() => {
    if (from && accounts) {
      const account = accounts.find((a) => a.virtualIban === from)
      form.setFieldValue("fromDetails", account)
      setSelectedAccount(account)
    }
  }, [from, accounts]) // Remove form from dependencies

  useEffect(() => {
    if (to && payees) {
      const payee = payees.items.find((p) => p.id === to)
      form.setFieldValue("toDetails", payee)
    }
  }, [to, payees]) // Remove form from dependencies

  function getAvailableBalanceOnSelectedAccount() {
    if (!selectedAccount) return 0
    const balance = selectedAccount.balances?.find(
      (b) => b.currency === sendCurrency,
    )
    return balance?.balance || 0
  }

  const [isPayeeDetailsModalOpen, setIsPayeeDetailsModalOpen] = useState(false)
  const [isEditingIban, setIsEditingIban] = useState(false)
  const [confirmedPayeeDetails, setConfirmedPayeeDetails] = useState<
    Record<string, boolean>
  >({})

  // SCA verification state
  const [isScaVerificationOpen, setIsScaVerificationOpen] = useState(false)
  const [pendingPayeeId, setPendingPayeeId] = useState<string | null>(null)
  const [scaVerificationErrorMsg, setScaVerificationErrorMsg] = useState<
    string | null
  >(null)

  const { mutate: updatePayee } = useUpdatePayeeMutation()
  const { mutate: startVerification, isPending: isVerificationPending } =
    useStartPayeeVerificationMutation()
  const { mutate: verifyPayee, isPending: isVerifying } =
    useVerifyPayeeMutation()

  const handlePayeeDetailsModalConfirm = () => {
    const toDetails = form.getFieldValue("toDetails")
    setIsPayeeDetailsModalOpen(false)

    if (toDetails?.id) {
      updatePayee(
        {
          id: toDetails?.id,
          payeeData: {
            iban: toDetails?.iban || null,
            accountDetailsConfirmed: true,
            accountName: toDetails?.accountName || null,
          },
        },
        {
          onSuccess: () => {
            setConfirmedPayeeDetails((prev) => ({
              ...prev,
              [toDetails.id]: true,
            }))
            // invalidate the payees list
            queryClient.invalidateQueries({
              queryKey: [queryKeys.payee.list, entityId],
            })

            const formValues = {
              fromAccount: form.getFieldValue("fromAccount"),
              currencySend: form.getFieldValue("currencySend"),
              sendAmount: form.getFieldValue("sendAmount"),
              currencyReceive: form.getFieldValue("currencyReceive"),
              receiveAmount: form.getFieldValue("receiveAmount"),
              payee: form.getFieldValue("payee"),
              paymentDate: form.getFieldValue("paymentDate"),
              fromDetails: form.getFieldValue("fromDetails"),
              toDetails: form.getFieldValue("toDetails"),
            } as IPaymentDetails
            onNext({
              ...formValues,
              lastEditedField: fixedSide === "Sell" ? "send" : "receive",
            })
          },
        },
      )
    }
  }

  const handleScaVerificationStart = (payeeId: string) => {
    setPendingPayeeId(payeeId)
    setScaVerificationErrorMsg(null)
    startVerification(
      { payeeId, type: "Sms" },
      {
        onSuccess: () => {
          setIsScaVerificationOpen(true)
          setScaVerificationErrorMsg(null)
        },
        onError: (err: any) => {
          setScaVerificationErrorMsg(
            err?.message || "Failed to start verification.",
          )
        },
      },
    )
  }

  const handleScaVerify = (code: string) => {
    if (!pendingPayeeId) return
    verifyPayee(
      { payeeId: pendingPayeeId, verificationCode: code },
      {
        onSuccess: () => {
          setIsScaVerificationOpen(false)
          setPendingPayeeId(null)
          // Invalidate and refetch the payees list to reflect the updated SCA status
          queryClient.invalidateQueries({
            predicate: (query) => query.queryKey[0] === queryKeys.payee.list,
          })

          // After SCA verification, check if we need IBAN/SWIFTBIC confirmation
          const toDetails = form.getFieldValue("toDetails")
          const selectedPayee = payees?.items.find(
            (p) => p.id === form.getFieldValue("payee"),
          )

          if (selectedPayee?.accountDetailsConfirmed) {
            // Account details already confirmed, proceed with payment
            const formValues = {
              fromAccount: form.getFieldValue("fromAccount"),
              currencySend: form.getFieldValue("currencySend"),
              sendAmount: form.getFieldValue("sendAmount"),
              currencyReceive: form.getFieldValue("currencyReceive"),
              receiveAmount: form.getFieldValue("receiveAmount"),
              payee: form.getFieldValue("payee"),
              paymentDate: form.getFieldValue("paymentDate"),
              fromDetails: form.getFieldValue("fromDetails"),
              toDetails: form.getFieldValue("toDetails"),
            } as IPaymentDetails
            onNext({
              ...formValues,
              lastEditedField: fixedSide === "Sell" ? "send" : "receive",
            })
          } else {
            // Check if IBAN/SWIFTBIC confirmation is needed
            if (
              (toDetails?.iban || toDetails?.bank?.swiftBic) &&
              !confirmedPayeeDetails[toDetails?.id] &&
              // Only require IBAN/SWIFTBIC confirmation for non-UK payments or when sending currency is not GBP
              (toDetails?.bank?.country?.codeIso2 !== "GB" ||
                form.getFieldValue("currencyReceive") !== "GBP")
            ) {
              setIsPayeeDetailsModalOpen(true)
            } else {
              // No IBAN/SWIFTBIC confirmation needed, proceed with payment
              const formValues = {
                fromAccount: form.getFieldValue("fromAccount"),
                currencySend: form.getFieldValue("currencySend"),
                sendAmount: form.getFieldValue("sendAmount"),
                currencyReceive: form.getFieldValue("currencyReceive"),
                receiveAmount: form.getFieldValue("receiveAmount"),
                payee: form.getFieldValue("payee"),
                paymentDate: form.getFieldValue("paymentDate"),
                fromDetails: form.getFieldValue("fromDetails"),
                toDetails: form.getFieldValue("toDetails"),
              } as IPaymentDetails
              onNext({
                ...formValues,
                lastEditedField: fixedSide === "Sell" ? "send" : "receive",
              })
            }
          }
        },
        onError: (err: any) => {
          setScaVerificationErrorMsg(
            err?.message || "Verification failed. Please try again.",
          )
        },
      },
    )
  }

  const handleScaResendCode = () => {
    if (!pendingPayeeId) return
    startVerification(
      { payeeId: pendingPayeeId, type: "Sms" },
      {
        onSuccess: () => {
          setScaVerificationErrorMsg(null)
        },
        onError: (err: any) => {
          setScaVerificationErrorMsg(
            err?.message || "Failed to resend verification code.",
          )
        },
      },
    )
  }

  // Reset confirmation when payee changes
  useEffect(() => {
    const toDetails = form.getFieldValue("toDetails")
    if (toDetails?.id && !confirmedPayeeDetails[toDetails.id]) {
      setConfirmedPayeeDetails((prev) => ({
        ...prev,
        [toDetails.id]: false,
      }))
    }
  }, [to])

  function triggerTradeRate(side: "Sell" | "Buy") {
    setFixedSide(side)
    const sendAmount = form.getFieldValue("sendAmount")
    const receiveAmount = form.getFieldValue("receiveAmount")
    const valueDate = form.getFieldValue("paymentDate")

    if (noFx) {
      if (side == "Sell") {
        form.setFieldValue("receiveAmount", sendAmount)
        setAmount(Number(sendAmount))
      } else if (side == "Buy") {
        form.setFieldValue("sendAmount", receiveAmount)
        setAmount(Number(receiveAmount))
      }
      return
    } else {
      if (side == "Sell") {
        form.setFieldValue("receiveAmount", receiveAmount)
      } else if (side == "Buy") {
        form.setFieldValue("sendAmount", sendAmount)
      }
    }

    if (!valueDate) {
      return
    }

    if (side === "Sell" && sendAmount) {
      setAmount(Number(sendAmount))
    } else if (side === "Buy" && receiveAmount) {
      setAmount(Number(receiveAmount))
    } else {
      setAmount(undefined)
    }
  }

  useEffect(() => {
    if (_sendCurrency === _receiveCurrency) {
      const sendAmount = form.getFieldValue("sendAmount")
      const receiveAmount = form.getFieldValue("receiveAmount")
      if (sendAmount !== receiveAmount) {
        form.setFieldValue("receiveAmount", sendAmount)
      }
    }
  }, [_sendCurrency, _receiveCurrency])

  // Set default payment date to earliest available if not valid
  useEffect(() => {
    const isFx = sendCurrency !== receiveCurrency
    const isGbpToGbp = sendCurrency === "GBP" && receiveCurrency === "GBP"
    const current = form.getFieldValue("paymentDate")
    if (isFx) {
      if (!valueDates?.valueDateOptions?.length) return
      if (!current || !valueDates.valueDateOptions.includes(current)) {
        const earliest = getEarliestAvailableDate(
          valueDates.valueDateOptions,
          true,
          isGbpToGbp,
        )
        if (typeof earliest === "string" && earliest)
          form.setFieldValue("paymentDate", earliest)
      }
    } else {
      // Non-FX: use the earliest business day from valueDates
      if (!valueDates?.valueDateOptions?.length) return
      const earliest = getEarliestBusinessDayFromOptions(
        valueDates.valueDateOptions,
      )
      if (
        !current ||
        !earliest ||
        new Date(current).getTime() < new Date(earliest).getTime()
      ) {
        if (earliest) form.setFieldValue("paymentDate", earliest)
      }
    }
  }, [valueDates?.valueDateOptions, sendCurrency, receiveCurrency])

  return (
    <FormLayout>
      <div className="flex">
        <form.Subscribe selector={(state) => state.values.toDetails}>
          {(d) => <DottedPaymentFlow name={d?.accountName} />}
        </form.Subscribe>

        {/* Form Content */}
        <div className="mt-8 flex-grow space-y-2">
          {/* From Account */}
          <form.Field
            children={(field) => (
              <FormField
                field={field}
                label="From account"
                loading={accountsLoading}
                required
              >
                <AccountSelector
                  accounts={accounts ?? []}
                  value={field.state.value}
                  onChange={(value) => field.handleChange(value)}
                  selectOnly
                />
              </FormField>
            )}
            listeners={{
              onChange: ({ value }) => {
                navigate({
                  search: {
                    ...search,
                    from: value,
                  },
                })
              },
            }}
            name="fromAccount"
            validators={{
              onChange: ({ value }) => {
                if (!value) {
                  return "Please select an account"
                }
                return undefined
              },
            }}
          />
          <br />
          {/* You Send and Payee Receives */}
          <Card className="space-y-2 rounded-xl border-none bg-sidebar p-4">
            <form.Field
              children={(field) => (
                <FormField
                  field={field}
                  headerButton={
                    <span className="text-sm text-muted-foreground">
                      Balance{" "}
                      <CurrencyText
                        amount={getAvailableBalanceOnSelectedAccount()}
                        className="font-medium"
                        currency={sendCurrency}
                      />
                    </span>
                  }
                  label="You send"
                  required
                >
                  <form.Field
                    children={(_c) => (
                      <div className="flex overflow-hidden rounded-lg border shadow-sm">
                        <CurrencyEnter
                          disabled={selectedAccount === undefined}
                          className="flex-1"
                          inputClassName="bg-background rounded-r-none shadow-none outline-none border-none !ring-0"
                          currency={_c.state.value as Currency}
                          onChange={(e) => {
                            if (e != field.state.value) {
                              field.handleChange(e)
                              triggerTradeRate("Sell")
                            }
                          }}
                          placeholder={
                            isTradeRateFetching
                              ? "Waiting for rate..."
                              : "Enter amount"
                          }
                          value={
                            field.state.value
                              ? Number(field.state.value)
                              : undefined
                          }
                        />
                        <CurrencySelector
                          disabled={selectedAccount === undefined}
                          availableCurrencies={availableCurrencies}
                          currency={_c.state.value as any}
                          onChange={(currency) => {
                            _c.handleChange(currency)
                            navigate({
                              search: {
                                ...search,
                                fromCurrency: currency,
                              },
                            })
                          }}
                        />
                      </div>
                    )}
                    listeners={{
                      onChange: ({ value }) => {
                        // Logic moved to CurrencySelector
                        navigate({
                          search: {
                            ...search,
                            fromCurrency: value,
                          },
                        })
                      },
                    }}
                    name="currencySend"
                  />
                </FormField>
              )}
              name="sendAmount"
              validators={{
                onChange: ({ value }) => {
                  if (!value) return "Please enter an amount"
                  if (isNaN(Number(value))) return "Please enter a valid amount"
                  return undefined
                },
              }}
            />

            <form.Subscribe
              selector={(state) => [
                state.values.currencySend,
                state.values.currencyReceive,
              ]}
            >
              {([sendCurrency, receiveCurrency]) => {
                if (sendCurrency !== receiveCurrency) {
                  if (!isTradeRateError) {
                    return (
                      <div className="pt-4">
                        <IndicativeRateComponent
                          fromCurrency={
                            fixedSide === "Sell"
                              ? sendCurrency
                              : receiveCurrency
                          }
                          isLoading={isTradeRateFetching}
                          rate={
                            fixedSide === "Sell"
                              ? tradeResponse?.sellRate
                              : tradeResponse?.buyRate
                          }
                          toCurrency={
                            fixedSide === "Sell"
                              ? receiveCurrency
                              : sendCurrency
                          }
                        />
                      </div>
                    )
                  } else {
                    if (isTradeRateError) {
                      return (
                        <div className="flex flex-col rounded-2xl bg-destructive/10 px-4 py-2">
                          <AlertTitle className="my-0 flex items-center gap-2 text-sm font-bold text-destructive">
                            <span className="relative inline-flex h-4 w-4 items-center justify-center">
                              <Octagon className="h-4 w-4" />
                              <X className="absolute left-1/2 top-1/2 h-2.5 w-2.5 -translate-x-1/2 -translate-y-1/2 text-destructive" />
                            </span>
                            Quoting unavailable
                          </AlertTitle>
                          <AlertDescription className="my-0 ml-6 text-sm text-muted-foreground">
                            Please contact your dealer
                          </AlertDescription>
                        </div>
                      )
                    }
                  }
                }
                return null
              }}
            </form.Subscribe>
            <form.Field
              name="receiveAmount"
              children={(field) => {
                return (
                  <>
                    <FormLabel labelClassName={"mt-4"} required>
                      Payee receives
                    </FormLabel>
                    <form.Field
                      name="currencyReceive"
                      children={(_c) => (
                        <div className="flex overflow-hidden rounded-lg border shadow-sm">
                          <CurrencyEnter
                            disabled={selectedAccount === undefined}
                            className="flex-1"
                            inputClassName="bg-background rounded-r-none shadow-none outline-none border-none !ring-0"
                            currency={_c.state.value as Currency}
                            onChange={(e) => {
                              if (e != field.state.value) {
                                field.handleChange(`${e}`)
                                triggerTradeRate("Buy")
                              }
                            }}
                            placeholder={
                              isTradeRateFetching
                                ? "Waiting for rate..."
                                : "Enter amount"
                            }
                            value={
                              field.state.value
                                ? Number(field.state.value)
                                : undefined
                            }
                          />
                          <CurrencySelector
                            disabled={selectedAccount === undefined}
                            availableCurrencies={
                              tradableCurrencies?.map(
                                (c) => c.code as Currency,
                              ) ?? []
                            }
                            currency={_c.state.value as any}
                            onChange={(currency) => {
                              _c.handleChange(currency)
                              navigate({
                                search: {
                                  ...search,
                                  toCurrency: currency,
                                },
                              })
                            }}
                          />
                        </div>
                      )}
                      listeners={{
                        onChange: ({ value }) => {
                          // Logic moved to CurrencySelector
                          navigate({
                            search: {
                              ...search,
                              toCurrency: value,
                            },
                          })
                        },
                      }}
                    ></form.Field>
                  </>
                )
              }}
            />

            <form.Field
              children={(field) => (
                <FormField field={field} label="Payment date" required>
                  <Popover
                    onOpenChange={setIsCalendarOpen}
                    open={isCalendarOpen}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        className={cn(
                          "w-full justify-start rounded-lg py-5 text-left font-normal",
                          !field.state.value && "text-muted-foreground",
                        )}
                        disabled={selectedAccount === undefined}
                        data-testid="payment-date-button"
                        variant={"outline"}
                      >
                        {field.state.value
                          ? format(new Date(field.state.value), "PPP")
                          : "Select date"}
                        <CalendarIcon className="ml-auto h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-full p-0">
                      <Calendar
                        disabled={(date) => {
                          // Always disable weekends
                          if (isWeekend(date)) return true
                          const isFx = sendCurrency !== receiveCurrency
                          const formattedDate = format2Date(date)
                          if (isFx) {
                            // FX: Only allow valueDateOptions
                            if (
                              !valueDates?.valueDateOptions?.includes(
                                formattedDate,
                              )
                            )
                              return true
                            // GBP to GBP cut-off logic for today
                            if (
                              sendCurrency === "GBP" &&
                              receiveCurrency === "GBP"
                            ) {
                              const now = new Date()
                              const cutoff = setMinutes(
                                setHours(new Date(now), 15),
                                5,
                              )
                              const todayStr = format2Date(now)
                              if (
                                formattedDate === todayStr &&
                                (isWeekend(now) || now > cutoff)
                              ) {
                                return true
                              }
                            }
                            return false
                          } else {
                            // Non-FX: allow any business day from the earliest in valueDates onwards
                            if (!valueDates?.valueDateOptions?.length)
                              return true
                            const earliest = getEarliestBusinessDayFromOptions(
                              valueDates.valueDateOptions,
                            )
                            if (!earliest) return true
                            if (date.getTime() < parseISO(earliest).getTime())
                              return true
                            if (
                              date.getTime() < new Date().setHours(0, 0, 0, 0)
                            )
                              return true // No past dates
                            return false
                          }
                        }}
                        autoFocus
                        mode="single"
                        onSelect={(selectedDate) => {
                          if (selectedDate) {
                            field.handleChange(format2Date(selectedDate))
                            setIsCalendarOpen(false)
                          }
                        }}
                        selected={
                          field.state.value
                            ? new Date(field.state.value)
                            : undefined
                        }
                      />
                    </PopoverContent>
                  </Popover>
                </FormField>
              )}
              name="paymentDate"
              validators={{
                onChange: ({ value }) => {
                  if (!value?.trim()) return "Payment date is required"

                  const sendCurrency = form.getFieldValue("currencySend")
                  const receiveCurrency = form.getFieldValue("currencyReceive")

                  if (sendCurrency !== receiveCurrency && value) {
                    if (!valueDates?.valueDateOptions?.includes(value)) {
                      return "Invalid payment date for FX trade"
                    }
                  }

                  return undefined
                },
              }}
            />
          </Card>
          <br />
          {/* To Payee Dropdown */}
          <form.Field
            children={(field) => (
              <FormField
                field={field}
                headerButton={
                  <AddPayeeDialog
                    onPayeeAdded={(id) => {
                      field.handleChange(id)
                    }}
                    entityId={entityId}
                  />
                }
                headerButtonClassName="ml-auto"
                label="To payee"
                required
              >
                <PayeeSelectorCombobox
                  value={field.state.value}
                  entityId={entityId}
                  onChange={(value) => {
                    field.handleChange(value)
                    navigate({
                      search: {
                        ...search,
                        to: value,
                      },
                    })
                  }}
                />
              </FormField>
            )}
            listeners={{
              onChange: ({ value }) => {
                navigate({
                  search: {
                    ...search,
                    to: value,
                  },
                })
              },
            }}
            name="payee"
            validators={{
              onChange: ({ value }) => {
                if (!value) {
                  return "Please select a payee"
                }
                return undefined
              },
            }}
          />
          <br />
          {/* Action Buttons */}
          <FormButtons form={form} onCancel={onCancel} />
        </div>
      </div>
      {!payees?.items.find((p) => p.id === form.getFieldValue("payee"))
        ?.accountDetailsConfirmed && (
        <PayeeBankDetailsDialog
          open={isPayeeDetailsModalOpen}
          onOpenChange={setIsPayeeDetailsModalOpen}
          form={form}
          isEditingIban={isEditingIban}
          setIsEditingIban={setIsEditingIban}
          handleConfirm={handlePayeeDetailsModalConfirm}
        />
      )}
      <VerificationModal
        open={isScaVerificationOpen}
        onOpenChange={(open) => {
          setIsScaVerificationOpen(open)
          if (!open) {
            setPendingPayeeId(null)
            setScaVerificationErrorMsg(null)
          }
        }}
        onVerify={handleScaVerify}
        onResendCode={handleScaResendCode}
      />
    </FormLayout>
  )
}
