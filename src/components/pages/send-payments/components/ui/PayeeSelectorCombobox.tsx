import { ComboboxDropdown } from "@/components/base/form/ComboboxDropdown"
import { useGlobalStore } from "@/data/global/global.store"
import { usePayeesListQuery } from "@/data/payees/payees.query"

interface PayeeSelectorComboboxProps {
  value?: string
  entityId: string
  onChange?: (value?: string) => void
}

export function PayeeSelectorCombobox({
  value,
  entityId,
  onChange,
}: PayeeSelectorComboboxProps) {
  const { data: payees, isLoading: payeesLoading } = usePayeesListQuery({
    entityId,
    pageSize: 200,
  })
  return (
    <ComboboxDropdown
      items={
        payees?.items.map((payee) => ({
          id: payee.id,
          label: payee.accountName,
          subLabel: `${payee.bank?.name || ""}${
            payee.iban
              ? ` (...${payee.iban.slice(-4)})`
              : payee.bank?.swiftBic
                ? ` (...${payee.bank.swiftBic.slice(-4)})`
                : ""
          }`,
          value: payee.id,
          accountName: payee.accountName,
          iban: payee.iban,
          swiftBic: payee.bank?.swiftBic,
        })) || []
      }
      value={value}
      onChange={(value) => {
        onChange?.(value)
      }}
      placeholder="Select payee"
      searchPlaceholder="Search payee"
      loading={payeesLoading}
      searchFields={["label", "accountName", "iban", "swiftBic"]}
    />
  )
}
