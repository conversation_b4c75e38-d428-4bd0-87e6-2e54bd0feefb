import { CheckCircle } from "lucide-react"
import { <PERSON> } from "@tanstack/react-router"

import { But<PERSON> } from "@/components/ui/button"

interface PaymentsSuccessPageProps {
  entityId: string
  message?: string
  isSelfApprover?: boolean
}

export function PaymentsSuccessPage({
  entityId,
  message = "Payment submitted successfully",
  isSelfApprover = false,
}: PaymentsSuccessPageProps) {
  return (
    <div className="mx-auto flex max-w-md flex-col items-center gap-4">
      <CheckCircle className="h-20 w-20 text-primary" />
      <h2 className="text-2xl font-semibold">{message}</h2>
      {!isSelfApprover && (
        <p>Approval notifications for this payment have been sent.</p>
      )}
      <Link params={{ entityId }} to="/$entityId/payments">
        <Button>View payments</Button>
      </Link>
    </div>
  )
}
