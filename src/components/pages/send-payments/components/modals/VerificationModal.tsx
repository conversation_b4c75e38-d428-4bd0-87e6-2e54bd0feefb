import { useState } from "react"
import { <PERSON>ert<PERSON>riangle } from "lucide-react"

import { Input } from "@/components/ui/input"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useCurrentUser } from "@/data/user/user.query"
interface VerificationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onVerify: (code: string) => void
  onResendCode: () => void
}

export function VerificationModal({
  open,
  onOpenChange,
  onVerify,
  onResendCode,
}: VerificationModalProps) {
  const [code, setCode] = useState("")
  const [error, setError] = useState<string | null>(null)

  const { data: currentUser } = useCurrentUser()
  const phoneNumber = currentUser?.phoneNumber

  const handleSubmit = () => {
    if (!code || code.length !== 6) {
      setError("Please enter a valid 6-digit code")
      return
    }

    setError(null)
    onVerify(code)
  }

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-semibold">
            Verify Your Identity
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-6 py-4">
          <p className="text-center text-muted-foreground">
            We've sent a text message to:
          </p>

          {phoneNumber && (
            <div className="rounded-xl bg-gray-100 p-3 text-center">
              <p className="text-sm text-muted-foreground">
                {(() => {
                  // Extract just the digits
                  const digits = phoneNumber.replace(/\D/g, "")
                  // Get the last 4 digits
                  const last4 = digits.slice(-4)

                  // Replace all non-last4 digits with asterisks, preserving non-digit characters
                  return phoneNumber.replace(/./g, (char, index) => {
                    if (!/\d/.test(char)) {
                      // For non-digits (like +, spaces), always mask
                      return "*"
                    }

                    // Count how many digits we've seen so far
                    const digitsSoFar = phoneNumber
                      .slice(0, index + 1)
                      .replace(/\D/g, "").length
                    // Check if this digit is among the last 4 digits
                    const isLastFourDigits = digits.length - digitsSoFar < 4

                    return isLastFourDigits ? char : "*"
                  })
                })()}
              </p>
            </div>
          )}

          <div className="flex flex-col gap-2">
            <Input
              className="rounded-xl border border-input p-4"
              id="verification-code"
              maxLength={6}
              onChange={(e) => {
                // Only allow digits
                const value = e.target.value.replace(/\D/g, "")
                setCode(value)
                if (error) setError(null)
              }}
              placeholder="Enter the 6-digit code"
              type="text"
              value={code}
            />

            {error && (
              <div className="mt-1 flex items-start gap-2 text-sm text-destructive">
                <AlertTriangle className="mt-0.5 h-4 w-4" />
                <span>{error}</span>
              </div>
            )}
          </div>

          <Button className="w-full py-6" onClick={handleSubmit}>
            Continue
          </Button>

          <div className="text-center">
            <span className="text-sm text-muted-foreground">
              Didn't receive a code?
            </span>{" "}
            <Button
              className="p-0 text-sm text-primary no-underline"
              onClick={onResendCode}
              variant="link"
            >
              Resend
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
