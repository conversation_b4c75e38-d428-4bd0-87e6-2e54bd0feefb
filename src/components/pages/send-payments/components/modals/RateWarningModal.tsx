import { AlertTriangle } from "lucide-react"

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
interface RateWarningModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  doNotShowAgain: boolean
  setDoNotShowAgain: (value: boolean) => void
  onNext: () => void
  onCancel: () => void
}

export function RateWarningModal({
  open,
  onOpenChange,
  doNotShowAgain,
  setDoNotShowAgain,
  onNext,
  onCancel,
}: RateWarningModalProps) {
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="hidden">Rate Warning</DialogTitle>
          <DialogDescription>
            <Alert className="mt-1 border-l-4 border-orange-400 bg-orange-50 p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-6 w-6 text-orange-500" />
                <div>
                  <AlertTitle className="text-sm font-semibold text-gray-900">
                    You will have{" "}
                    <span className="font-semibold">10 seconds</span> to accept
                    the rate presented on the next screen.
                  </AlertTitle>
                  <AlertDescription className="mt-1 text-sm text-gray-700">
                    Once accepted, the payment will be sent on the scheduled
                    date subject to sufficient funds in your GBP account.
                  </AlertDescription>
                </div>
              </div>
            </Alert>
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center space-x-2 py-4">
          <Checkbox
            checked={doNotShowAgain}
            id="doNotShowAgain"
            onCheckedChange={(checked: boolean) => setDoNotShowAgain(checked)}
          />
          <label
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            htmlFor="doNotShowAgain"
          >
            Do not show this again
          </label>
        </div>
        <DialogFooter>
          <Button onClick={onCancel} variant="link">
            Cancel
          </Button>
          <Button onClick={onNext}>Next</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
