import { useEffect, useState, useRef, useMemo } from "react"
import { <PERSON><PERSON><PERSON>, Loader2, RefreshCw } from "lucide-react"
import { cn } from "@/lib/utils"
import { useFxQuoteQuery } from "@/data/payments/payments.query"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyFlag } from "@/components/base/currency/CurrencyFlag"

interface RateQuoteModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currency: string
  sendAmount: number
  receiveCurrency: string
  receiveAmount: number
  onConfirm: (fxQuoteRequestId: string, fxBrokeredQuoteId: string) => void
  onCancel: () => void
  entityId?: string
  lastEditedField?: "send" | "receive" | null
}

export function RateQuoteModal({
  open,
  onOpenChange,
  currency,
  sendAmount,
  receiveCurrency,
  receiveAmount,
  onConfirm,
  onCancel,
  entityId,
  lastEditedField,
}: RateQuoteModalProps) {
  const [localProgress, setLocalProgress] = useState(100)
  const [displayTime, setDisplayTime] = useState(10)
  const animationRef = useRef<number | null>(null)
  const previousSecondRef = useRef<number>(10)
  const [refreshCounter, setRefreshCounter] = useState(0)
  const [calculatedReceiveAmount, setCalculatedReceiveAmount] =
    useState(receiveAmount)

  // Set valueDate to tomorrow's date
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  const valueDate = tomorrow.toISOString().split("T")[0] // Tomorrow's date in YYYY-MM-DD format

  // Memoize the quote request object
  const quoteRequest = useMemo(
    () => ({
      buyCurrency: receiveCurrency,
      sellCurrency: currency,
      fixedSide: lastEditedField === "send" ? "Buy" : "Sell",
      amount: sendAmount,
      valueDate,
    }),
    [receiveCurrency, currency, sendAmount, valueDate, lastEditedField],
  )
  const {
    data: fxQuoteData,
    refetch,
    isLoading,
    error,
  } = useFxQuoteQuery(quoteRequest, open)

  const currentRate = useMemo(() => {
    if (isLoading || !fxQuoteData) return null
    return currency !== receiveCurrency ? fxQuoteData.sellRate : 1
  }, [currency, receiveCurrency, fxQuoteData, isLoading])

  useEffect(() => {
    if (currentRate && open && !isLoading) {
      setCalculatedReceiveAmount(sendAmount * currentRate)
    }
  }, [currentRate, sendAmount, open, isLoading])

  const startAnimation = () => {
    previousSecondRef.current = 10
    setDisplayTime(10)
    setLocalProgress(100)

    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
      animationRef.current = null
    }

    let startTime: number | null = null
    const totalDuration = 10000 // 10 seconds in milliseconds

    const runAnimation = (timestamp: number) => {
      if (startTime === null) {
        startTime = timestamp
      }

      const elapsedTime = timestamp - startTime
      const remainingMs = Math.max(0, totalDuration - elapsedTime)
      const remainingSeconds = remainingMs / 1000

      const progressPercentage = (remainingSeconds / 10) * 100
      setLocalProgress(progressPercentage)

      const currentSecond = Math.ceil(remainingSeconds)

      if (currentSecond !== previousSecondRef.current) {
        previousSecondRef.current = currentSecond
        setDisplayTime(currentSecond)
      }

      if (remainingSeconds <= 0) {
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current)
          animationRef.current = null
        }
        setLocalProgress(0)
        setDisplayTime(0)
      } else {
        animationRef.current = requestAnimationFrame(runAnimation)
      }
    }

    animationRef.current = requestAnimationFrame(runAnimation)
  }

  const handleRefreshRate = async () => {
    startAnimation()
    setRefreshCounter((prev) => prev + 1)

    try {
      await refetch()
    } catch (error) {
      console.error("Error refreshing rate:", error)
    }
  }

  useEffect(() => {
    if (open) {
      startAnimation()
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
        animationRef.current = null
      }
    }
  }, [open, refreshCounter])

  useEffect(() => {
    if (!open) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
        animationRef.current = null
      }
      setLocalProgress(100)
      setDisplayTime(10)
      previousSecondRef.current = 10
    }
  }, [open])

  useEffect(() => {
    if (open && entityId) {
      refetch()
    }
  }, [open, entityId, refetch])

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="rounded-lg p-6 sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            Confirm rate and send
          </DialogTitle>
        </DialogHeader>

        {/* Currency Exchange Information */}
        <div className="mx-6 flex items-center justify-between px-6 py-4">
          {/* Sending Currency */}
          <div className="text-center">
            <div className="font-semibold text-muted-foreground">You send</div>
            <div className="flex items-center justify-center gap-2">
              <CurrencyFlag currency={currency} />
              <span>{currency}</span>
            </div>
            <div
              className={`font-semibold ${lastEditedField === "send" ? "border-b-2 border-primary" : ""}`}
            >
              <CurrencyText amount={sendAmount} currency={currency} />
            </div>
          </div>

          {/* Arrow */}
          <ArrowRight className="h-6 w-6 text-muted-foreground" />

          {/* Receiving Currency */}
          <div className="text-center">
            <div className="font-semibold text-muted-foreground">
              Payee receives
            </div>
            <div className="flex flex-col items-center">
              <div className="flex items-center justify-center gap-2">
                <CurrencyFlag currency={receiveCurrency} />
                <span>{receiveCurrency}</span>
              </div>
              <div
                className={`font-semibold ${lastEditedField === "receive" ? "border-b-2 border-primary" : ""}`}
              >
                {isLoading ? (
                  <div className="text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : calculatedReceiveAmount ? (
                  <CurrencyText
                    amount={calculatedReceiveAmount}
                    currency={receiveCurrency}
                  />
                ) : null}
              </div>
            </div>
          </div>
        </div>

        {/* Rate Expired or Timer */}
        {error ? (
          <div className="mb-6 rounded-lg bg-slate-50 p-4">
            <div className="text-red-500">
              Error fetching rate. Please try again.
            </div>
          </div>
        ) : (
          <>
            <div className="mb-6 rounded-lg bg-slate-50 p-4">
              {displayTime > 0 ? (
                <div className="flex flex-col items-center">
                  <div className="text-xl font-medium">
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading rate...
                      </div>
                    ) : (
                      <>
                        1 {currency} ={" "}
                        {currentRate
                          ? currentRate.toFixed(4)
                          : (calculatedReceiveAmount / sendAmount).toFixed(
                              4,
                            )}{" "}
                        {receiveCurrency}
                        <div className="w-full flex-row items-center gap-2 space-y-4 px-2 py-1 text-center text-sm text-muted-foreground">
                          (1 {receiveCurrency} ={" "}
                          {fxQuoteData?.buyRate?.toFixed(4)} {currency})
                        </div>
                      </>
                    )}
                  </div>

                  {/* Timer Countdown Display */}
                  <div className="w-full">
                    {/* Progress Bar */}
                    <div className="h-5 w-full overflow-hidden border bg-white">
                      <div
                        className={cn(
                          "h-full transition-colors",
                          displayTime <= 3 ? "bg-destructive" : "bg-yellow-400",
                        )}
                        style={{ width: `${localProgress}%` }}
                      />
                    </div>

                    {/* Timer Text */}
                    <div className="mt-2 flex justify-center">
                      <div
                        className={cn(
                          "rounded-full px-3 py-1 text-sm font-medium transition-colors",
                        )}
                      >
                        Quote valid for{" "}
                        <span
                          className={cn(
                            "inline-block min-w-6 text-center font-bold",
                            displayTime <= 3 && "animate-pulse",
                          )}
                        >
                          {displayTime}s
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center space-y-4">
                  <div className="text-xl font-medium">Rate expired</div>
                  <Button
                    className="group flex items-center gap-2 font-medium"
                    variant="link"
                    disabled={isLoading}
                    onClick={handleRefreshRate}
                  >
                    <RefreshCw
                      className={cn("h-5 w-5", isLoading && "animate-spin")}
                    />
                    <span>
                      {isLoading ? "Refreshing rate..." : "Refresh rate"}
                    </span>
                  </Button>
                </div>
              )}
            </div>
          </>
        )}

        {/* Footer Buttons */}
        <div className="flex w-full justify-end gap-4 pt-2">
          <Button onClick={onCancel} variant="link-muted">
            Cancel
          </Button>
          <Button
            className={cn(
              "",
              (displayTime === 0 || isLoading) &&
                "cursor-not-allowed opacity-50",
            )}
            disabled={displayTime === 0 || isLoading}
            onClick={() =>
              onConfirm(
                fxQuoteData?.fxQuoteRequestId ?? "",
                fxQuoteData?.fxBrokeredQuoteId ?? "",
              )
            }
          >
            Confirm and send
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
