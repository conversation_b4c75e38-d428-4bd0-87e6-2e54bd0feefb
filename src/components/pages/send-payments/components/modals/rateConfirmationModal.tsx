import { useEffect, useState } from "react"
import { AlertTriangle } from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"

interface RateConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  onDontShowAgainChange: (dontShow: boolean) => void
}
export function RateConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  onDontShowAgainChange,
}: RateConfirmationModalProps) {
  const [dontShowAgain, setDontShowAgain] = useState(false)
  // Timer effect
  useEffect(() => {
    if (!isOpen) return
  }, [isOpen, onClose])

  return (
    <Dialog onOpenChange={(open) => !open && onClose()} open={isOpen}>
      <DialogContent className="rounded-lg p-6 sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">Get rate</DialogTitle>
        </DialogHeader>

        <div className="mt-2 text-sm text-muted-foreground">
          You will have 10 seconds to accept the rate presented on the next
          screen.
        </div>

        <div className="my-4 flex items-start gap-3 rounded-lg border bg-background p-5">
          <AlertTriangle className="mt-0.5 h-6 w-6 flex-shrink-0 text-amber-500" />
          <div className="text-sm font-medium">
            Once accepted, the trade will be executed.{" "}
          </div>
        </div>

        <div className="mb-6 flex items-center space-x-2">
          <Checkbox
            checked={dontShowAgain}
            id="dont-show-again"
            onCheckedChange={(checked: boolean) => {
              const newValue = checked === true
              localStorage.setItem(
                "dontShowRateConfirmationModal",
                newValue.toString(),
              )
              setDontShowAgain(newValue)
              onDontShowAgainChange(newValue)
            }}
          />
          <label className="cursor-pointer text-sm" htmlFor="dont-show-again">
            Do not show this again
          </label>
        </div>

        <div className="flex justify-end gap-4">
          <Button onClick={onClose} variant="outline">
            Cancel
          </Button>
          <Button
            className="bg-[#C8EACD] px-8 text-foreground shadow-none hover:bg-[#C8EACD]/90"
            onClick={onConfirm}
          >
            Get rate
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
