import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import React from "react"
import { Card } from "@/components/ui/card"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { AlertTriangle, Pencil } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
interface PayeeBankDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  form: any
  isEditingIban: boolean
  setIsEditingIban: (v: boolean) => void
  handleConfirm: () => void
}

export const PayeeBankDetailsDialog: React.FC<PayeeBankDetailsDialogProps> = ({
  open,
  onOpenChange,
  form,
  isEditingIban,
  setIsEditingIban,
  handleConfirm,
}) => (
  <Dialog onOpenChange={onOpenChange} open={open}>
    <DialogContent className="sm:max-w-[425px]">
      <DialogTitle>Payee bank details</DialogTitle>
      <DialogDescription className="hidden text-sm text-muted-foreground">
        Confirm the payee bank details to proceed with the payment
      </DialogDescription>
      <div className="mt-2">
        <Card>
          <div className="flex items-start gap-2 p-4">
            <AlertTriangle className="h-6 w-6 flex-shrink-0 text-warning" />
            <div className="flex flex-col gap-2">
              <div className="text-sm font-semibold">
                This payment requires more information
              </div>
              <div className="text-sm text-muted-foreground">
                Please confirm the following payee bank details to continue with
                this payment.
              </div>
            </div>
          </div>
        </Card>
      </div>
      <form.Subscribe selector={(state: any) => state.values.toDetails}>
        {(toDetails: any) => (
          <div className="space-y-4">
            {toDetails?.iban && (
              <div className="flex items-center justify-between">
                <div className="flex-grow pr-2">
                  <div className="text-sm font-medium">IBAN</div>
                  {isEditingIban ? (
                    <form.Field
                      name="toDetails.iban"
                      children={(field: any) => (
                        <Input
                          type="text"
                          value={field.state.value || ""}
                          onChange={(e) => field.handleChange(e.target.value)}
                          className="mt-1 w-full rounded-md border p-2"
                        />
                      )}
                    />
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      {toDetails.iban}
                    </div>
                  )}
                </div>
                <Button
                  className="mt-4 text-primary hover:bg-primary/10"
                  size="sm"
                  type="button"
                  variant="ghost"
                  onClick={() => setIsEditingIban(!isEditingIban)}
                >
                  <Pencil className="h-5 w-5" />
                  <span className="underline">
                    {" "}
                    {isEditingIban ? "Save" : "Edit"}
                  </span>
                </Button>
              </div>
            )}
            {toDetails?.bank?.swiftBic && (
              <div className="flex items-center justify-between">
                <div className="flex-grow pr-2">
                  <div className="text-sm font-medium">SWIFT/BIC</div>
                  <div className="text-sm text-muted-foreground">
                    {toDetails.bank.swiftBic}
                  </div>
                </div>
              </div>
            )}
            <div className="flex items-center space-x-2">
              <form.Field
                name="confirmPayeeDetails"
                children={(field: any) => (
                  <Checkbox
                    checked={field.state.value || false}
                    id="confirmPayeeDetails"
                    onCheckedChange={(checked) =>
                      field.handleChange(checked as boolean)
                    }
                  />
                )}
              />
              <Label
                className="mt-4 text-sm font-normal"
                htmlFor="confirmPayeeDetails"
              >
                I confirm that the payee bank details provided are accurate and
                up-to-date.{" "}
              </Label>
            </div>
          </div>
        )}
      </form.Subscribe>
      <div className="mt-4 flex justify-end space-x-2">
        <Button variant="link-muted" onClick={() => onOpenChange(false)}>
          Cancel
        </Button>
        <form.Subscribe
          selector={(state: any) => state.values.confirmPayeeDetails}
        >
          {(confirmPayeeDetails: boolean) => (
            <Button onClick={handleConfirm} disabled={!confirmPayeeDetails}>
              Confirm
            </Button>
          )}
        </form.Subscribe>
      </div>
    </DialogContent>
  </Dialog>
)
