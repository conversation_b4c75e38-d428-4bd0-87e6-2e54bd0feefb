import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"

interface ApprovalModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
  onCancel: () => void
}

export function ApprovalModal({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
}: ApprovalModalProps) {
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Approve payment</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-4">
          <p className="text-sm text-muted-foreground">
            Are you sure you want to approve this payment?
          </p>
          <div className="flex justify-end gap-2">
            <Button
              onClick={onCancel}
              className="underline"
              variant="link-muted"
            >
              Cancel
            </Button>
            <Button onClick={onConfirm} variant="default">
              Yes, approve
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
