import { useState } from "react"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"

import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"

interface PayeesFilterFormProps {
  onFilterChange: (filters: PayeesFilterValues) => void
}

export interface PayeesFilterValues {
  createdAtStart?: string
  createdAtEnd?: string
  accountName?: string
  type?: string
  orderByField?: string
  orderByDirection?: string
  pageNumber?: number
  pageSize?: number
}

export default function PayeesFilterForm({
  onFilterChange,
}: PayeesFilterFormProps) {
  const [createdAtStart, setCreatedAtStart] = useState<Date | undefined>(
    undefined,
  )
  const [createdAtEnd, setCreatedAtEnd] = useState<Date | undefined>(undefined)
  const [accountName, setAccountName] = useState<string>("")
  const [type, setType] = useState<string>("")
  const [orderByField, setOrderByField] = useState<string>("createdAt")
  const [orderByDirection, setOrderByDirection] = useState<string>("desc")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const filters: PayeesFilterValues = {
      createdAtStart: createdAtStart
        ? format(createdAtStart, "yyyy-MM-dd'T'HH:mm:ss")
        : undefined,
      createdAtEnd: createdAtEnd
        ? format(createdAtEnd, "yyyy-MM-dd'T'HH:mm:ss")
        : undefined,
      accountName: accountName || undefined,
      type: type || undefined,
      orderByField,
      orderByDirection,
      pageNumber: 1, // Reset to first page on filter change
    }

    onFilterChange(filters)
  }

  const handleClear = () => {
    setCreatedAtStart(undefined)
    setCreatedAtEnd(undefined)
    setAccountName("")
    setType("")
    setOrderByField("createdAt")
    setOrderByDirection("desc")

    onFilterChange({
      pageNumber: 1,
      orderByField: "createdAt",
      orderByDirection: "desc",
    })
  }

  return (
    <form className="space-y-6 pb-6" onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Date Range Filter */}
        <div className="space-y-2">
          <Label htmlFor="createdAtStart">Created From</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !createdAtStart && "text-muted-foreground",
                )}
                id="createdAtStart"
                variant="outline"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {createdAtStart ? format(createdAtStart, "PPP") : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                autoFocus
                mode="single"
                onSelect={setCreatedAtStart}
                selected={createdAtStart}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="createdAtEnd">Created To</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !createdAtEnd && "text-muted-foreground",
                )}
                id="createdAtEnd"
                variant="outline"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {createdAtEnd ? format(createdAtEnd, "PPP") : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                autoFocus
                mode="single"
                onSelect={setCreatedAtEnd}
                selected={createdAtEnd}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Account Name Filter */}
        <div className="space-y-2">
          <Label htmlFor="accountName">Account Name</Label>
          <Input
            id="accountName"
            onChange={(e) => setAccountName(e.target.value)}
            placeholder="Search by account name"
            value={accountName}
          />
        </div>

        {/* Type Filter */}
        <div className="space-y-2">
          <Label htmlFor="type">Type</Label>
          <Select onValueChange={setType} value={type}>
            <SelectTrigger id="type">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All</SelectItem>
              <SelectItem value="Individual">Individual</SelectItem>
              <SelectItem value="Business">Business</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Sort options */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="orderByField">Sort By</Label>
          <Select onValueChange={setOrderByField} value={orderByField}>
            <SelectTrigger id="orderByField">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt">Created Date</SelectItem>
              <SelectItem value="accountName">Account Name</SelectItem>
              <SelectItem value="type">Type</SelectItem>
              <SelectItem value="status">Status</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="orderByDirection">Sort Direction</Label>
          <Select onValueChange={setOrderByDirection} value={orderByDirection}>
            <SelectTrigger id="orderByDirection">
              <SelectValue placeholder="Sort direction" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="asc">Ascending</SelectItem>
              <SelectItem value="desc">Descending</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button onClick={handleClear} type="button" variant="outline">
          Clear
        </Button>
        <Button type="submit">Apply Filters</Button>
      </div>
    </form>
  )
}
