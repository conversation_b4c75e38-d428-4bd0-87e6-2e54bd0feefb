import { toast } from "sonner"
import { useMemo, useState } from "react"
import { ChevronDown, ChevronUp, CopyIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { IPayeeData } from "@/data/payees/payees.interface"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { Payee } from "@/data/payments/payments.interface"
interface PayeeDetailsExpandableProps {
  payee: IPayeeData | Payee
  className?: string
  noBorder?: boolean
  noCopy?: boolean
}

export function PayeeDetailsExpandable({
  payee,
  className,
}: PayeeDetailsExpandableProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const swiftBic = useMemo(() => {
    return payee.bank.swiftBic
  }, [payee.bank])

  const sortCode = useMemo(() => {
    if (!payee.bank.nationalId) return ""
    const digits = payee.bank.nationalId.replace(/-/g, "")
    return digits.match(/.{2}/g)?.join("-") || digits
  }, [payee.bank])

  const accountNumber = useMemo(() => {
    return payee.accountNumber
  }, [payee.accountNumber])

  const iban = useMemo(() => {
    return payee.iban
  }, [payee.iban])

  return (
    <Collapsible
      className={cn("flex w-full flex-col items-end sm:w-[300px]", className)}
      onOpenChange={setIsExpanded}
      open={isExpanded}
    >
      <CollapsibleContent className="mt-2 w-full space-y-2 text-sm text-muted-foreground">
        {swiftBic && (
          <DetailsRow noBorder noCopy label="Swift BIC" value={swiftBic} />
        )}
        {sortCode && (
          <DetailsRow noBorder noCopy label="Sort code" value={sortCode} />
        )}
        {accountNumber && (
          <DetailsRow
            noBorder
            noCopy
            label="Account number"
            value={accountNumber}
          />
        )}
        {iban && <DetailsRow noBorder noCopy label="IBAN" value={iban} />}
      </CollapsibleContent>
      <CollapsibleTrigger asChild>
        <Button
          className="ml-auto mr-0 px-0 font-normal !text-foreground"
          size="sm"
          variant="link"
        >
          {isExpanded ? "Close bank details" : "Bank details"}
          {isExpanded ? (
            <ChevronUp className="ml-2 h-4 w-4" />
          ) : (
            <ChevronDown className="ml-2 h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
    </Collapsible>
  )
}

interface DetailsRowProps {
  label: string
  value: string
  noBorder?: boolean
  noCopy?: boolean
}

function DetailsRow({ label, value, noBorder, noCopy }: DetailsRowProps) {
  function copyToClipboard() {
    navigator.clipboard.writeText(value)
    toast("Copied to clipboard", {
      description: `${label}: ${value}`,
      icon: "📋",
      duration: 1000,
    })
  }
  return (
    <div
      className={cn(
        "flex w-full justify-between border-b border-border pb-1 text-xs font-light",
        noBorder && "border-none",
      )}
    >
      <span>{label}</span>
      <div className="flex items-center">
        <span className="max-w-[150px] overflow-hidden truncate">{value}</span>
        <Button
          className={cn("ml-2 h-4 w-4", noCopy && "hidden")}
          onClick={copyToClipboard}
          size="icon"
          variant="ghost"
        >
          <CopyIcon className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}
