import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, X } from "lucide-react"
import { toast } from "sonner"

import { formatDate } from "@/lib/date.utils"
import { ROLES } from "@/lib/constants/roles.constants"
import { usePayeeByIdQuery } from "@/data/payees/payees.query"
import { useDeletePayeeMutation } from "@/data/payees/payees.mutation"
import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertD<PERSON>og<PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { useParams, useRouter } from "@tanstack/react-router"
import { Link, useLocation } from "@tanstack/react-router"

interface PayeeDetailsSheetProps {
  payeeId: string | null
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  bulkUploadId?: string
}

export default function PayeeDetailsSheet({
  payeeId,
  isOpen,
  onOpenChange,
  bulkUploadId,
}: PayeeDetailsSheetProps) {
  const router = useRouter()

  const { data: payee, isLoading, isError } = usePayeeByIdQuery(payeeId)
  const deletePayeeMutation = useDeletePayeeMutation()
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const { entity } = useLoaderData()
  const { data: entityAccess } = useEntityAccessQuery(entity.id ?? "")

  const isAdmin =
    entityAccess
      ?.filter((e) => e.entityId === entity.id)?.[0]
      ?.roles?.includes(ROLES.ADMINISTRATOR) ?? false

  const handleDeleteClick = () => {
    // Additional security check to ensure only admin users can delete payees
    if (!isAdmin) {
      toast.error(
        "You don't have permission to delete payees. Only administrators can perform this action.",
      )
      return
    }
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    // Additional security check to ensure only admin users can delete payees
    if (!isAdmin) {
      toast.error(
        "You don't have permission to delete payees. Only administrators can perform this action.",
      )
      setIsDeleteDialogOpen(false)
      return
    }

    if (payeeId) {
      deletePayeeMutation.mutate(payeeId, {
        onSuccess: () => {
          setIsDeleteDialogOpen(false)
          onOpenChange(false)
          router.navigate({
            to: "/$entityId/payees",
            params: {
              entityId,
            },
          })
        },
      })
    }
  }

  const InfoRow = ({
    label,
    value,
  }: {
    label: string
    value: string | React.ReactNode
  }) => (
    <div className="items-startpy-1 flex justify-between first:pt-0">
      <span className="text-sm font-normal text-foreground">{label}</span>
      <span className="max-w-48 text-right text-sm text-muted-foreground">
        {value}
      </span>
    </div>
  )

  // Format address from individual fields
  const formatAddress = (payee: any) => {
    if (!payee) return ""

    const parts = [
      payee.buildingNumber,
      payee.streetName,
      payee.city,
      payee.postalCode,
      payee.country.name,
    ].filter(Boolean)

    return parts.join(", ")
  }

  function renderBankDetails(bank: any) {
    if (!bank) return ""
    return (
      <div>
        {bank.name && <div>{bank.name}</div>}
        {(bank.buildingNumber || bank.streetName) && (
          <div>
            {[bank.buildingNumber, bank.streetName].filter(Boolean).join(" ")}
          </div>
        )}
        {bank.city && <div>{bank.city}</div>}
        {bank.regionState && <div>{bank.regionState}</div>}
        {bank.postalCode && <div>{bank.postalCode}</div>}
        {bank.country?.name && <div>{bank.country.name}</div>}
      </div>
    )
  }

  const handleCancelDelete = () => {
    setIsDeleteDialogOpen(false)
  }
  const { href } = useLocation()
  const { entityId } = useParams({ from: "/_auth/$entityId/payees" })

  return (
    <>
      <Sheet onOpenChange={onOpenChange} open={isOpen}>
        <SheetContent className="overflow-y-auto p-10 sm:max-w-md md:max-w-lg lg:max-w-xl">
          <SheetTitle className="sr-only">Payee Details</SheetTitle>
          <SheetDescription className="sr-only">
            View and manage payee information
          </SheetDescription>
          {isLoading ? (
            <div className="flex h-96 items-center justify-center">
              <LoadingSpinner size="8" />
            </div>
          ) : isError ? (
            <div className="mt-8 text-center text-destructive">
              Error loading payee details. Please try again.
            </div>
          ) : payee ? (
            <>
              <SheetHeader className="mb-8">
                <div className="mb-4 flex items-center justify-between">
                  <div className="mt-4 flex items-center gap-3">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8 bg-foreground">
                        <AvatarFallback
                          className="bg-foreground text-sm text-background"
                          data-testid="avatar-fallback"
                        >
                          {" "}
                          {payee.accountName && payee.accountName.length > 0
                            ? `${payee.accountName[0].toLocaleUpperCase() || ""}${
                                payee.accountName.length > 1
                                  ? payee.accountName[1].toLocaleUpperCase()
                                  : ""
                              }`
                            : ""}
                        </AvatarFallback>
                      </Avatar>
                      <SheetTitle className="text-xl font-semibold">
                        {payee.accountName || payee.displayName}
                      </SheetTitle>
                    </div>
                  </div>
                </div>

                {/* Add delete button if user is admin */}
                <div className="flex justify-between">
                  {!isLoading && (
                    <>
                      <Button>
                        <Link
                          className="flex w-full items-center gap-2"
                          params={{ entityId }}
                          search={{
                            to: payee.id,
                            fromRoute: href,
                          }}
                          to="/$entityId/payments/send"
                        >
                          <ArrowUpRight className="h-5 w-5 text-muted-foreground" />
                          Pay
                        </Link>
                      </Button>

                      {/* Only show delete button for admin users */}
                      {isAdmin && (
                        <Button
                          className="hover:border-b-destructive hover:text-destructive"
                          disabled={deletePayeeMutation.isPending}
                          onClick={handleDeleteClick}
                          variant="link-muted"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete Payee
                        </Button>
                      )}
                    </>
                  )}
                </div>
              </SheetHeader>

              <div className="space-y-0 rounded-2xl bg-muted p-4">
                <InfoRow label="Type" value={payee.type || "Individual"} />
                <InfoRow
                  label="Display name"
                  value={payee.displayName || payee.accountName}
                />
                <InfoRow label="Email" value={payee.email || ""} />
                <InfoRow label="Address" value={formatAddress(payee)} />
              </div>

              <div className="mt-4 space-y-0 rounded-2xl bg-muted p-4">
                <InfoRow
                  label="Bank country"
                  value={payee.payeeBank?.country.name}
                />
                {payee.payeeBank?.name && (
                  <InfoRow
                    label="Bank details"
                    value={renderBankDetails(payee.payeeBank) || "N/A"}
                  />
                )}

                {payee.iban && <InfoRow label="IBAN" value={payee.iban} />}

                {payee.payeeBank && payee.payeeBank.nationalId && (
                  <InfoRow
                    label="Sort Code / Bank Code"
                    value={
                      <div className="flex flex-col items-end">
                        <span>{payee.payeeBank.nationalId}</span>
                      </div>
                    }
                  />
                )}

                {payee.payeeBank && payee.payeeBank.swiftBic && (
                  <InfoRow label="SWIFT/BIC" value={payee.payeeBank.swiftBic} />
                )}
                {payee.accountName && (
                  <InfoRow label="Account name" value={payee.accountName} />
                )}
                {payee.accountNumber && (
                  <InfoRow label="Account number" value={payee.accountNumber} />
                )}
              </div>

              <div className="mt-4 space-y-0 rounded-2xl bg-muted p-4">
                <InfoRow label="Added on" value={formatDate(payee.createdAt)} />
                <InfoRow
                  label="Added by"
                  value={payee.createdBy?.name || payee.createdBy?.email}
                />
                <InfoRow
                  label="Last sent amount"
                  value={
                    <div className="flex flex-col items-end">
                      <span>
                        {payee.lastSentAmount && (
                          <CurrencyText
                            amount={payee.lastSentAmount}
                            currency={"GBP"}
                          />
                        )}
                      </span>
                      <span className="text-muted-foreground">
                        {payee.lastSentValueDate &&
                          formatDate(payee.lastSentValueDate)}
                      </span>
                    </div>
                  }
                />
              </div>
            </>
          ) : null}
        </SheetContent>
      </Sheet>

      {/* Add Delete Confirmation Dialog */}
      <AlertDialog
        onOpenChange={setIsDeleteDialogOpen}
        open={isDeleteDialogOpen}
      >
        <AlertDialogContent onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Payee</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this payee? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="border-none bg-transparent text-muted-foreground underline hover:bg-transparent"
              onClick={handleCancelDelete}
            >
              Go back
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={deletePayeeMutation.isPending}
              onClick={confirmDelete}
            >
              <Trash2 className="h-4 w-4" />
              {deletePayeeMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
