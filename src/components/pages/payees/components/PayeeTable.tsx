import { useMemo, useState } from "react"
import { formatAccountNumber } from "@/lib/bank.utils"
import { IPayeeData } from "@/data/payees/payees.interface"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { BasicPagination } from "@/components/base/pagination/BasicPagination"
import { VerificationModal } from "@/components/pages/send-payments/components/modals/VerificationModal"
import {
  useStartPayeeVerificationMutation,
  useVerifyPayeeMutation,
} from "@/data/payees/payees.mutation"
import { useQueryClient } from "@tanstack/react-query"
import { queryKeys } from "@/lib/constants/query.constants"
import { CheckCircle } from "lucide-react"

import { PayeesFilterValues } from "./PayeesFilterForm"
import PayeeDetailsSheet from "./PayeeDetailsSheet"
import { useNavigate, useSearch } from "@tanstack/react-router"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

export interface PayeeTableProps {
  payees: IPayeeData[]
  totalItems: number
  onFilterChange: (filters: PayeesFilterValues) => void
  currentFilters: PayeesFilterValues
  isLoading: boolean
}

export default function PayeeTable({
  payees,
  totalItems,
  onFilterChange,
  currentFilters,
  isLoading,
}: PayeeTableProps) {
  const search: any = useSearch({ strict: false }),
    navigate = useNavigate()
  const queryClient = useQueryClient()

  const selectedPayeeId = useMemo(() => {
    return search.id ?? null
  }, [search.id])

  // Verification state
  const [showVerification, setShowVerification] = useState(false)
  const [pendingPayeeId, setPendingPayeeId] = useState<string | null>(null)
  const [verificationErrorMsg, setVerificationErrorMsg] = useState<
    string | null
  >(null)

  // Verification mutations
  const {
    mutate: startVerification,
    isPending: isVerificationPending,
    reset: resetVerification,
  } = useStartPayeeVerificationMutation()
  const verifyPayeeMutation = useVerifyPayeeMutation()

  function setSelectedPayeeId(id: string | null) {
    const s = { ...search }
    if (id) {
      s.id = id
    } else {
      delete s.id
    }
    navigate({
      search: {
        ...s,
      },
      replace: true,
    })
  }

  const handleRowClick = (payee: IPayeeData) => {
    navigate({
      search: {
        ...search,
        id: payee.id,
      },
      replace: true,
    })
  }

  const handleSortChange = (field: string) => {
    const newDirection =
      currentFilters.orderByField === field &&
      currentFilters.orderByDirection === "asc"
        ? "desc"
        : "asc"

    onFilterChange({
      ...currentFilters,
      orderByField: field,
      orderByDirection: newDirection,
    })
  }

  const getSortIndicator = (field: string) => {
    if (currentFilters.orderByField !== field) return null

    return currentFilters.orderByDirection === "asc" ? " ↑" : " ↓"
  }

  const handleVerifyPayee = (payeeId: string) => {
    setPendingPayeeId(payeeId)
    setVerificationErrorMsg(null)
    startVerification(
      { payeeId, type: "Sms" },
      {
        onSuccess: () => {
          setShowVerification(true)
          setVerificationErrorMsg(null)
        },
        onError: (err: any) => {
          setVerificationErrorMsg(
            err?.message || "Failed to start verification.",
          )
        },
      },
    )
  }

  const handleVerify = (code: string) => {
    if (!pendingPayeeId) return
    verifyPayeeMutation.mutate(
      { payeeId: pendingPayeeId, verificationCode: code },
      {
        onSuccess: () => {
          setShowVerification(false)
          setPendingPayeeId(null)
          // Invalidate and refetch the payees list to reflect the updated SCA status
          queryClient.invalidateQueries({
            predicate: (query) => query.queryKey[0] === queryKeys.payee.list,
          })
        },
        onError: (err: any) => {
          setVerificationErrorMsg(
            err?.message || "Verification failed. Please try again.",
          )
        },
      },
    )
  }

  const handleResendCode = () => {
    if (!pendingPayeeId) return
    startVerification(
      { payeeId: pendingPayeeId, type: "Sms" },
      {
        onSuccess: () => {
          setVerificationErrorMsg(null)
        },
        onError: (err: any) => {
          setVerificationErrorMsg(
            err?.message || "Failed to resend verification code.",
          )
        },
      },
    )
  }

  if (isLoading) {
    return (
      <div className="mt-1 flex h-96 w-full items-center justify-center">
        <LoadingSpinner size="8" />
      </div>
    )
  }

  return (
    <>
      <div className="overflow-x-auto">
        <Table>
          {/* Table Header */}
          <TableHeader>
            <TableRow className="border-none">
              <TableHead className="w-1/12 whitespace-nowrap text-xs font-bold">
                <Button
                  className="h-8 p-0 text-xs font-bold hover:bg-transparent"
                  onClick={() => handleSortChange("accountName")}
                  variant="ghost"
                >
                  Name
                  {getSortIndicator("accountName")}
                </Button>
              </TableHead>
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                <span className="h-8 p-0">Account Details</span>
              </TableHead>
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                <Button
                  className="h-8 p-0 text-xs font-bold hover:bg-transparent"
                  onClick={() => handleSortChange("type")}
                  variant="ghost"
                >
                  Type
                  {getSortIndicator("type")}
                </Button>
              </TableHead>
              <TableHead className="w-1/12 whitespace-nowrap text-xs font-bold">
                <span className="h-8 p-0">Actions</span>
              </TableHead>
              {/* <TableHead className="whitespace-nowrap font-medium text-sm">
                <Button
                  variant="ghost"
                  onClick={() => handleSortChange("status")}
                  className="h-8 p-0 font-medium hover:bg-transparent"
                >
                  Status
                  {getSortIndicator("status")}
                </Button>
              </TableHead> */}
            </TableRow>
          </TableHeader>

          {/* Table Body */}
          <TableBody>
            {payees.length > 0 ? (
              payees.map((payee) => (
                <TableRow
                  className="cursor-pointer hover:bg-muted/5"
                  key={payee.id}
                  onClick={() => handleRowClick(payee)}
                >
                  <TableCell className="py-4">
                    <div className="flex items-center space-x-2">
                      <Avatar className="mr-4 h-8 w-8 bg-foreground">
                        <AvatarFallback
                          className="bg-foreground text-sm text-background"
                          data-testid="avatar-fallback"
                        >
                          {" "}
                          {payee.accountName && payee.accountName.length > 0
                            ? `${payee.accountName[0].toLocaleUpperCase() || ""}${
                                payee.accountName.length > 1
                                  ? payee.accountName[1].toLocaleUpperCase()
                                  : ""
                              }`
                            : ""}
                        </AvatarFallback>
                      </Avatar>
                      <span>
                        {payee.displayName &&
                        payee.displayName.trim().length > 0
                          ? payee.displayName
                          : payee.accountName || "N/A"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    {`${
                      (payee.bank?.name || "N/A").length > 20
                        ? (payee.bank?.name || "N/A").substring(0, 20)
                        : payee.bank?.name || "N/A"
                    } ${formatAccountNumber(payee.accountNumber)}`}
                  </TableCell>
                  <TableCell className="py-4">{payee.type}</TableCell>
                  <TableCell className="py-4">
                    {!payee.isScaCompleted ? (
                      <Button
                        className="text-xs"
                        disabled={
                          isVerificationPending && pendingPayeeId === payee.id
                        }
                        onClick={(e) => {
                          e.stopPropagation()
                          handleVerifyPayee(payee.id)
                        }}
                        variant="outline"
                      >
                        {isVerificationPending && pendingPayeeId === payee.id
                          ? "Starting..."
                          : "Verify Payee"}
                      </Button>
                    ) : (
                      <Badge
                        variant="success"
                        className="flex w-24 items-center gap-1 rounded-xl text-xs"
                      >
                        <CheckCircle className="h-3 w-3" />
                        Verified
                      </Badge>
                    )}
                  </TableCell>
                  {/* <TableCell className="py-6">{payee.status}</TableCell> */}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  className="py-8 text-center text-muted-foreground"
                  colSpan={5}
                >
                  No payees found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalItems > 0 && (
        <BasicPagination
          data={{
            totalCount: totalItems,
            pageNumber: currentFilters.pageNumber || 1,
            pageSize: currentFilters.pageSize || 10,
            totalPages: Math.ceil(totalItems / (currentFilters.pageSize || 10)),
            hasNextPage:
              (currentFilters.pageNumber || 1) <
              Math.ceil(totalItems / (currentFilters.pageSize || 10)),
            hasPreviousPage: (currentFilters.pageNumber || 1) > 1,
            isFirstPage: (currentFilters.pageNumber || 1) === 1,
            isLastPage:
              (currentFilters.pageNumber || 1) >=
              Math.ceil(totalItems / (currentFilters.pageSize || 10)),
            pageStartIndex:
              ((currentFilters.pageNumber || 1) - 1) *
                (currentFilters.pageSize || 10) +
              1,
            pageEndIndex: Math.min(
              (currentFilters.pageNumber || 1) *
                (currentFilters.pageSize || 10),
              totalItems,
            ),
          }}
          onNext={() =>
            onFilterChange({
              ...currentFilters,
              pageNumber: (currentFilters.pageNumber || 1) + 1,
            })
          }
          onPrevious={() =>
            onFilterChange({
              ...currentFilters,
              pageNumber: Math.max(1, (currentFilters.pageNumber || 1) - 1),
            })
          }
          totalItems={totalItems}
          pageSize={currentFilters.pageSize || 10}
        />
      )}

      <PayeeDetailsSheet
        isOpen={!!selectedPayeeId}
        onOpenChange={(v) => !v && setSelectedPayeeId(null)}
        payeeId={selectedPayeeId}
      />

      <VerificationModal
        open={showVerification}
        onOpenChange={(open) => {
          setShowVerification(open)
          if (!open) {
            setPendingPayeeId(null)
            setVerificationErrorMsg(null)
            resetVerification()
          }
        }}
        onVerify={handleVerify}
        onResendCode={handleResendCode}
      />
    </>
  )
}
