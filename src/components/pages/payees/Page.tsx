import { useMemo, useState } from "react"
import { UserPlus, Search } from "lucide-react"
import { Link, useParams } from "@tanstack/react-router"
import { useSearch, useNavigate } from "@tanstack/react-router"

import { usePayeesListQuery } from "@/data/payees/payees.query"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

import PayeeTable from "./components/PayeeTable"
import { PayeesFilterValues } from "./components/PayeesFilterForm"
import FilterTabs from "../_components/filter-tabs"

export function PayeePage() {
  const { entityId } = useParams({ from: "/_auth/$entityId/payees" })
  const navigate = useNavigate()
  const searchParams: any = useSearch({ strict: false })

  // Initialize filter state with default values
  const [filters, setFilters] = useState<PayeesFilterValues>({
    pageNumber: 1,
    pageSize: 10,
    orderByField: "createdAt",
    orderByDirection: "desc",
    type:
      searchParams.filter !== "All" && searchParams.filter !== undefined
        ? searchParams.filter
        : undefined,
  })

  // Fetch payees data using React Query with filtering and sorting
  const {
    data: payeesData,
    isLoading,
    isError,
  } = usePayeesListQuery({
    entityId,
    pageNumber: filters.pageNumber,
    pageSize: filters.pageSize,
    createdAtStart: filters.createdAtStart,
    createdAtEnd: filters.createdAtEnd,
    accountName: filters.accountName,
    type: filters.type,
    orderByField: filters.orderByField,
    orderByDirection: filters.orderByDirection,
  })

  const payees = payeesData?.items || []
  const totalPayees = payeesData?.totalItems || 0

  const statusFilters = ["All", "Individual", "Business"]

  const activeFilter = useMemo(() => {
    return (
      statusFilters.find((filter) => filter === searchParams.filter) ?? "All"
    )
  }, [searchParams.filter, statusFilters])

  const setActiveFilter = (filter: string) => {
    navigate({ search: { ...searchParams, filter } })

    // Update filter state based on selected status filter
    setFilters((prev) => ({
      ...prev,
      pageNumber: 1, // Reset to first page when changing filters
      type: filter !== "All" ? filter : undefined,
    }))
  }

  // Handle filter changes from the PayeeTable (sorting, pagination)
  const handleFilterChange = (newFilters: PayeesFilterValues) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
    }))
  }

  // Handle search by account name
  const handleSearch = (searchTerm: string) => {
    setFilters((prev) => ({
      ...prev,
      accountName: searchTerm || undefined,
      pageNumber: 1, // Reset to first page when searching
    }))
  }

  if (isError) {
    return (
      <div className="rounded-lg bg-background p-6 shadow-md">
        <p className="text-center text-destructive">
          Error fetching payees. Please try again later.
        </p>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-4 px-4">
      {/* Header with Search and Status Filters */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative w-60">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              className="w-full rounded-xl pl-9 shadow-none"
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search by account name"
              type="text"
              value={filters.accountName || ""}
            />
          </div>

          <div className="flex space-x-2">
            <FilterTabs
              active={activeFilter}
              items={statusFilters}
              setActive={setActiveFilter}
            />
          </div>
        </div>

        {/* Add Payee Button */}
        <Link
          className="ml-auto"
          params={{ entityId }}
          to="/$entityId/payees/add"
        >
          <Button>
            <UserPlus className="h-6 w-6" /> Add Payee
          </Button>
        </Link>
      </div>
      {/* Payees Table with server-side filtering, sorting and pagination */}
      <PayeeTable
        currentFilters={filters}
        onFilterChange={handleFilterChange}
        payees={payees}
        totalItems={totalPayees}
        isLoading={isLoading}
      />
    </div>
  )
}
