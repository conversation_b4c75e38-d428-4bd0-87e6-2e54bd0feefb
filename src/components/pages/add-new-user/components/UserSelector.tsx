import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface SelectorProps<T> {
  value?: T
  items: T[]
  id?: string
  placeholder: string
  onChange: (role: T) => void
  buttonClassName?: string
  triggerClassName?: string
  itemClassName?: string
}

export function UserSelector<T extends string>({
  value,
  id,
  items,
  placeholder,
  onChange,
  buttonClassName,
  triggerClassName,
  itemClassName,
}: SelectorProps<T>) {
  return (
    <Select onValueChange={onChange} value={value}>
      <SelectTrigger
        aria-label={id}
        className={cn(
          "flex w-52 bg-background p-4 py-6 [&>span]:pr-3",
          triggerClassName,
        )}
        id={id}
      >
        <SelectValue placeholder={placeholder}>
          <div className={cn("flex items-center gap-2", buttonClassName)}>
            {value}
          </div>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {items.map((role) => (
            <SelectItem key={role} value={role}>
              <div className={cn("flex items-center gap-2", itemClassName)}>
                {role}
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}
