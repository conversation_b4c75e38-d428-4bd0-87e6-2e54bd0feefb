import { useNavigate } from "@tanstack/react-router"
import { useForm } from "@tanstack/react-form"

import { validateEmail } from "@/lib/form.utils"
import { Role, Roles } from "@/lib/constants/user.constants"
import { type IAddNewUser } from "@/data/user/user.interface"
import { Input } from "@/components/ui/input"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import { FormLayout, FormButtons, FormField } from "@/components/base/form/form"

import { UserSelector } from "./UserSelector"

type FieldNames =
  | "firstName"
  | "lastName"
  | "dateOfBirth"
  | "email"
  | "role"
  | "signatory"

type ErrorMessage = `${string} is required`

const ErrorMessages: Record<FieldNames, ErrorMessage> = {
  firstName: "First name is required",
  lastName: "Last name is required",
  dateOfBirth: "Date of birth is required",
  email: "Email is required",
  role: "Role is required",
  signatory: "Signatory is required",
}

function fieldIsNotEmpty(name: FieldName<PERSON>, value: string) {
  return value.trim() ? undefined : ErrorMessages[name]
}

function NoEntityIdError() {
  return <div>Entity id not defined</div>
}

interface AddUserFormProps extends Partial<IAddNewUser> {
  onComplete?: () => void
}

export function AddNewUserForm(props: AddUserFormProps) {
  const form = useForm<IAddNewUser>({
    defaultValues: {
      firstName: props.firstName ?? "",
      lastName: props.lastName ?? "",
      dateOfBirth: props.dateOfBirth ?? "",
      email: props.email ?? "",
      role: props.role ?? "",
      signatory: props.signatory ?? "",
    },
  })
  const { entity } = useLoaderData()
  const navigate = useNavigate()

  if (!entity || !("id" in entity)) return <NoEntityIdError />

  const handleCancel = () => {
    form.reset()
    navigate({
      to: "/$entityId/user-admin",
      params: { entityId: entity.id! },
    })
  }

  return (
    <FormLayout
      aria-label="Add new User"
      className="mt-7 flex flex-col"
      title="Enter user details"
    >
      <form.Field
        children={(field) => (
          <FormField
            aria-label={field.name}
            field={field}
            label="First name"
            required
          >
            <Input
              className="bg-background"
              id={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              type="text"
              value={field.state.value}
            />
          </FormField>
        )}
        name="firstName"
        validators={{
          onChange: ({ value, fieldApi }) =>
            fieldIsNotEmpty(fieldApi.name, value),
        }}
      />

      <form.Field
        children={(field) => (
          <FormField
            aria-label={field.name}
            field={field}
            label="Last name"
            required
          >
            <Input
              className="bg-background"
              id={field.name}
              name={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              type="text"
              value={field.state.value}
            />
          </FormField>
        )}
        name="lastName"
        validators={{
          onChange: ({ value, fieldApi }) =>
            fieldIsNotEmpty(fieldApi.name, value),
        }}
      />

      <form.Field
        children={(field) => (
          <FormField
            aria-label={field.name}
            field={field}
            label="Date of birth"
            required
          >
            <Input
              className="bg-background"
              id={field.name}
              name={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              type="date"
              value={field.state.value}
            />
          </FormField>
        )}
        name="dateOfBirth"
        validators={{
          onChange: ({ value, fieldApi }) =>
            fieldIsNotEmpty(fieldApi.name, value),
        }}
      />

      <form.Field
        children={(field) => (
          <FormField
            aria-label={field.name}
            field={field}
            label="Email"
            required
          >
            <Input
              className="bg-background"
              id={field.name}
              name={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              type="email"
              value={field.state.value}
            />
          </FormField>
        )}
        name="email"
        validators={{
          onChange: ({ value }) => validateEmail(value, true),
        }}
      />

      <form.Field
        children={(field) => (
          <FormField
            aria-label={field.name}
            field={field}
            label={`What role will this user have within ${entity.legalEntity?.name}`}
            required
          >
            <UserSelector
              id={field.name}
              items={Roles}
              onChange={(role) => {
                if (role === Role.Approver) form.setFieldValue("signatory", "")

                field.handleChange(role)
              }}
              placeholder="Select role"
              value={field.state.value as Role}
            />
          </FormField>
        )}
        name="role"
        validators={{
          onChange: ({ value, fieldApi }) =>
            fieldIsNotEmpty(fieldApi.name, value),
        }}
      />

      <form.Subscribe selector={(state) => state.values.role as Role}>
        {(roleField) => (
          <>
            <form.Field
              children={(field) => (
                <>
                  {roleField === Role.Approver && (
                    <FormField
                      aria-label={field.name}
                      field={field}
                      label="Signatory type"
                      required
                    >
                      <UserSelector
                        id={field.name}
                        items={[Role.Viewer]}
                        onChange={(role) => field.handleChange(role)}
                        placeholder="Select signatory type"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                </>
              )}
              name="signatory"
            />
          </>
        )}
      </form.Subscribe>

      <FormButtons
        form={form}
        isValid={!form.state.isDirty || form.state.isValid}
        nextButtonText="Add user"
        onCancel={handleCancel}
      />
    </FormLayout>
  )
}
