import { useNavigate } from "@tanstack/react-router"

import { Account } from "@/data/account/account.interface"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { AccountSelector } from "@/components/pages/account/components/AccountSelector"
import { AccountBalanceTile } from "@/components/pages/account/components/AccountBalanceTile"

interface AccountPageProps {
  entityId: string
  accountId: string
  account?: Account
  accounts?: Account[]
}

export default function AccountPage({
  account,
  entityId,
  accounts,
  accountId,
}: AccountPageProps) {
  const navigate = useNavigate()

  return (
    <>
      {account && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              {accounts && (
                <div className="flex w-full flex-col items-end justify-between md:flex-row md:items-start">
                  <div className="w-full md:w-auto">
                    <AccountSelector
                      accounts={accounts}
                      onChange={(value) => {
                        navigate({
                          to: "/$entityId/accounts",
                          params: { entityId },
                          search: { open: true },
                        })
                      }}
                      value={accountId}
                    />
                  </div>
                  {/* <AccountDetailsExpandable account={account} /> */}
                </div>
              )}
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Balances</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {account.balances?.map((balance, i) => (
                <AccountBalanceTile
                  account={account}
                  balance={balance}
                  entityId={entityId}
                  key={i}
                />
              ))}
            </CardContent>
          </Card>
        </div>
      )}
    </>
  )
}
