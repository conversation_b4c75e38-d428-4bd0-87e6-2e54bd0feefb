import { toast } from "sonner"
import { useState } from "react"
import { ChevronDown, ChevronUp, CopyIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { Account } from "@/data/account/account.interface"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"

interface AccountDetailsExpandableProps {
  account: Account
  className?: string
}

export function AccountDetailsExpandable({
  account,
  className,
}: AccountDetailsExpandableProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <Collapsible
      className={cn("flex w-full flex-col items-end sm:w-[300px]", className)}
      onOpenChange={setIsExpanded}
      open={isExpanded}
    >
      <CollapsibleContent className="mt-2 w-full space-y-2 text-sm">
        <AccountDetailsRow label="Account name" value={account.accountName} />
        <AccountDetailsRow label="IBAN" value={account.virtualIban} />
        <AccountDetailsRow label="Bank name" value={account.bankName} />
        <AccountDetailsRow label="Bank address" value={account.bankAddress} />
      </CollapsibleContent>
      <CollapsibleTrigger asChild>
        <Button className="ml-auto mr-0 px-0" size="sm" variant="link">
          {isExpanded ? "Close account details" : "Show account details"}
          {isExpanded ? (
            <ChevronUp className="ml-2 h-4 w-4" />
          ) : (
            <ChevronDown className="ml-2 h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
    </Collapsible>
  )
}

interface AccountDetailsRowProps {
  label: string
  value: string
}

function AccountDetailsRow({ label, value }: AccountDetailsRowProps) {
  function copyToClipboard() {
    navigator.clipboard.writeText(value)
    toast("Copied to clipboard", {
      description: `${label}: ${value}`,
      icon: "📋",

      duration: 1000,
    })
  }
  return (
    <div className="flex w-full justify-between border-b border-border pb-2 text-xs font-thin">
      <span>{label}</span>
      <div className="flex items-center">
        <span className="max-w-[150px] overflow-hidden truncate">{value}</span>
        <Button
          className="ml-2 h-4 w-4"
          onClick={copyToClipboard}
          size="icon"
          variant="ghost"
        >
          <CopyIcon className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}
