import { ArrowUpRightIcon, RefreshCcw } from "lucide-react"
import { useLocation } from "@tanstack/react-router"

import { Account, AccountBalance } from "@/data/account/account.interface"
import { MenuDotsDropdown } from "@/components/base/triggers/MenuDotsDropdown"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyName } from "@/components/base/currency/CurrencyName"
import { CurrencyFlag } from "@/components/base/currency/CurrencyFlag"

interface AccountBalanceTileProps {
  entityId: string
  account: Account
  balance: AccountBalance
}

export function AccountBalanceTile({
  balance,
  account,
}: AccountBalanceTileProps) {
  const { href } = useLocation()

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
          <CurrencyFlag currency={balance.currency} size="lg" />
        </div>

        <div className="flex flex-col items-start md:flex-row md:items-center md:gap-2">
          <span className="font-medium">{balance.currency}</span>
          <CurrencyName
            className="text-sm text-muted-foreground"
            currency={balance.currency}
          />
        </div>
      </div>
      <div className="flex items-center gap-4">
        <CurrencyText
          amount={balance.balance}
          className="font-medium"
          currency={balance.currency}
        />
        <MenuDotsDropdown
          items={[
            {
              type: "link",
              label: "Pay",
              icon: ArrowUpRightIcon,
              linkProps: {
                to: "/$entityId/payments/send",
                search: {
                  from: account.virtualIban,
                  fromCurrency: balance.currency,
                  fromRoute: href,
                } as any,
              },
            },
            {
              type: "link",
              label: "Exchange",
              icon: RefreshCcw,
              linkProps: {
                to: "/$entityId/payees",
              },
            },
          ]}
        />
      </div>
    </div>
  )
}
