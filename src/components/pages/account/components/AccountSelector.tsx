import { useEffect } from "react"

import { cn } from "@/lib/utils"
import { Account } from "@/data/account/account.interface"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface AccountSelectorProps {
  accounts: Account[]
  value?: string
  onChange?: (value: string) => void
  height?: string
  className?: string
  selector?: "virtualIban" | "clientAccountId"
  defaultFirst?: boolean
  selectOnly?: boolean
}

export function AccountSelector({
  accounts,
  value,
  onChange,
  height = "4rem",
  className,
  selector = "virtualIban",
  defaultFirst,
  selectOnly,
}: AccountSelectorProps) {
  const account = accounts.find((acc) => acc[selector] === value)

  useEffect(() => {
    const firstAccount = accounts.at(0)

    if (selectOnly && !value && firstAccount && accounts.length == 1) {
      onChange?.(firstAccount[selector])
    }

    if (defaultFirst && !value && firstAccount) {
      onChange?.(firstAccount[selector])
    }
  }, [accounts])

  return (
    <Select
      onValueChange={(value) => {
        onChange?.(value)
      }}
      value={value}
    >
      <SelectTrigger className={cn("flex h-10 items-center", className)}>
        {account ? (
          <SelectValue asChild>
            <div className="flex items-center">
              <span className="font-medium">{account.accountName}</span>
              <span className="ml-1 text-muted-foreground">
                (..{account.virtualIban.slice(-4)})
              </span>
            </div>
          </SelectValue>
        ) : (
          <SelectValue placeholder="Select an account" />
        )}
      </SelectTrigger>
      <SelectContent>
        {accounts?.map((acc) => (
          <SelectItem key={acc.virtualIban} value={acc.virtualIban}>
            <div className="flex items-center">
              <span className="font-medium">{acc.accountName}</span>
              <span className="ml-1 text-muted-foreground">
                (..{acc.virtualIban.slice(-4)})
              </span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
