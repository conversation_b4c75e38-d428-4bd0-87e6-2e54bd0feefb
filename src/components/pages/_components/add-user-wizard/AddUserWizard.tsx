import { useState } from "react"

import type {
  AddressModalButtonProps,
  AddUserWizardStateProps,
  StepId,
} from "./interface"

import { ExistingUser, NewUser, UserEmailForm } from "./components/steps"
import { Modal } from "./components"
import { Step, Stepper } from "../../../base/triggers/Stepper"

const MIN_STEP = 0
const MAX_STEP = 2

function useAddWizardProps() {
  const [activeStep, setActiveStep] = useState(0)
  const [modalOpen, setModalOpen] = useState(false)
  const [form, setForm] = useState<AddUserWizardStateProps>({
    email: "",
    isSignatory: false,
    name: "",
    roles: [],
  })

  function handleNext(
    payload: Partial<AddUserWizardStateProps>,
    goToStep?: StepId,
  ) {
    const nextStepIndex = Math.min(goToStep ?? activeStep + 1, MAX_STEP)

    setForm({ ...form, ...payload } as AddUserWizardStateProps)

    setActiveStep(nextStepIndex)
  }

  function handleBack(goToStep?: StepId) {
    if (activeStep === 0) return

    const prevStepIndex = Math.max(goToStep ?? activeStep - 1, MIN_STEP)

    setActiveStep(prevStepIndex)
  }

  function handleStepChange(step: number) {
    setActiveStep(step)
  }

  function resetWizard() {
    setActiveStep(0)
    setForm({
      email: "",
      isSignatory: false,
      name: "",
      roles: [],
    })
    setModalOpen(false)
  }

  function handleUserAdded() {
    setModalOpen(false)
  }

  function handleModalOpenChange(open: boolean) {
    setModalOpen(open)

    // If the modal is closed, reset the wizard to first step
    if (!open) {
      setActiveStep(0)
      setForm({
        email: "",
        isSignatory: false,
        name: "",
        roles: [],
      })
    }
  }

  return {
    modalOpen,
    activeStep,
    form,
    handleModalOpenChange,
    handleStepChange,
    handleNext,
    handleBack,
    resetWizard,
    handleUserAdded,
  }
}

interface AddUserWizardProps {
  entityId: string
  entityName: string
  Button?: Partial<AddressModalButtonProps>
}

export default function AddUserWizard(props: AddUserWizardProps) {
  const {
    modalOpen,
    activeStep,
    form,
    handleModalOpenChange,
    handleStepChange,
    handleNext,
    handleBack,
    resetWizard,
    handleUserAdded,
  } = useAddWizardProps()

  return (
    <Modal
      Button={{ copy: "Add a user", "aria-label": "add-user", ...props.Button }}
      onOpenChange={handleModalOpenChange}
      open={modalOpen}
    >
      <div className="flex flex-col gap-2">
        <h3 className="text-2xl font-semibold text-foreground">Add new user</h3>
      </div>

      <Stepper
        onStepChange={handleStepChange}
        showHeader={false}
        value={activeStep}
      >
        <Step>
          <UserEmailForm
            data={form}
            entityId={props.entityId}
            onNext={handleNext}
          />
        </Step>
        <Step>
          <ExistingUser
            data={form}
            entityId={props.entityId}
            entityName={props.entityName}
            onBack={handleBack}
            onNext={handleNext}
            onReset={resetWizard}
          />
        </Step>
        <Step>
          <NewUser
            data={form}
            entityId={props.entityId}
            onBack={handleBack}
            onCloseModal={resetWizard}
            onNext={handleNext}
            onUserAdded={handleUserAdded}
          />
        </Step>
      </Stepper>
    </Modal>
  )
}
