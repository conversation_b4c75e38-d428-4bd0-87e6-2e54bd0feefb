import { PropsWithChildren } from "react"
import { ReactNode } from "@tanstack/react-router"

import { ButtonProps } from "@/components/ui/button"

export enum StepId {
  UserEmailForm = 0,
  ExistingUserForm = 1,
  NewUserForm = 2,
}

export interface User {
  name: string
  email: string
  roles: string[]
  isSignatory: boolean
}

export interface AddUserWizardProps {
  users: User[]
}

export type AddUserWizardStateProps = User

export interface AddressModalButtonProps extends Pick<ButtonProps, "variant"> {
  icon?: ReactNode
  copy: string
  className?: string
  "aria-label"?: string
}

export interface AddressModalProps extends PropsWithChildren {
  Button: AddressModalButtonProps
  open?: boolean
  onOpenChange?: (open: boolean) => void
}
