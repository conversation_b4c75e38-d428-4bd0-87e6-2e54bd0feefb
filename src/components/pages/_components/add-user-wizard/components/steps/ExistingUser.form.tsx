import { useState } from "react"
import { useQueryClient } from "@tanstack/react-query"

import { useGetAllUsersWithEntitiesQry } from "@/data/user"
import { GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY } from "@/data/entity/entity.query"
import { useEntityAccessMutations } from "@/data/entity-access/entity-access.mutation"
import { Button } from "@/components/ui/button"
import EditUserCard from "@/components/pages/_components/edit-user-card"
import { AlertBox, AlertTitle } from "@/components/base/alert-box"
import {
  EnumValueDto,
  LoginEntityApproverLevel,
} from "@/client/onboarding/types.gen"

import { User } from "../../interface"

export interface ExistingUserProps {
  entityId: string
  entityName: string
  data: User
  onNext?: (payload: { roles: string[] }, goToStep?: number) => void
  onBack?: () => void
  onReset?: () => void
}

function useExistingUser(props: ExistingUserProps) {
  const queryClient = useQueryClient()
  const { data: allUsers } = useGetAllUsersWithEntitiesQry({ enabled: true })
  const { createEntityAccess } = useEntityAccessMutations(props.entityId)
  const [role, setRole] = useState<EnumValueDto | undefined>()
  const [signatory, setSignatory] = useState<
    LoginEntityApproverLevel | undefined
  >()

  const currentUser = allUsers?.find((user) => user.email === props.data.email)

  function handleSubmit() {
    createEntityAccess(
      {
        roles: [role?.display ?? ""],
        userLoginId: currentUser?.id,
        approverLevel: signatory,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [
              "entity",
              GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY,
              props.entityId,
            ],
          })
        },
        onSettled: () => {
          props.onReset?.()
        },
      },
    )
  }

  function handleRole(role: EnumValueDto) {
    setRole(role)
  }

  function handleSignatoryChange(signatory: LoginEntityApproverLevel) {
    setSignatory(signatory)
  }

  return {
    handleRole,
    handleSignatoryChange,
    handleSubmit,
    currentUser,
  }
}

export default function ExistingUser(props: ExistingUserProps) {
  const { currentUser, handleRole, handleSignatoryChange, handleSubmit } =
    useExistingUser(props)

  return (
    <div className="flex flex-col gap-y-6">
      <AlertBox severity="info">
        <AlertTitle className="mb-0">
          A user with provided email already exists
        </AlertTitle>
      </AlertBox>
      <span className="text-base">Review details for the existing user</span>

      <EditUserCard
        email={props.data.email}
        entities={currentUser?.entities?.map(
          ({ displayName }) => displayName ?? "",
        )}
        entityId={props.entityId}
        entityName={props.entityName}
        name={props.data.name}
        onRoleChange={handleRole}
        onSignatoryChange={handleSignatoryChange}
      />

      <div className="flex justify-end">
        <Button
          className="w-full text-muted-foreground sm:w-auto"
          onClick={() => props.onBack?.()}
          type="button"
          variant="link"
        >
          Back
        </Button>

        <Button className="w-full sm:w-auto" onClick={handleSubmit}>
          Add user
        </Button>
      </div>
    </div>
  )
}
