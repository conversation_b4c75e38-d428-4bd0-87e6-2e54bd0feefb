import { toast } from "sonner"
import { useForm } from "@tanstack/react-form"

import { validateEmail } from "@/lib/form.utils"
import { useGetAllUsersWithEntitiesQry } from "@/data/user"
import { useGetAllUsersQry } from "@/data/entity/entity.query"
import { Input } from "@/components/ui/input"
import { DialogClose } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { FormField, FormLayout } from "@/components/base/form/form"

import { User, StepId } from "../../interface"
interface UserEmailFormValues {
  email: string
}

export interface UserEmailProps {
  data: User
  entityId: string
  onNext?: (
    payload: { email: string; name?: string },
    goToStep?: StepId,
  ) => void
}

function useUserEmail(props: UserEmailProps) {
  const { refetch: fetchAllUsersWithEntities } = useGetAllUsersWithEntitiesQry()
  const { refetch: fetchAllUsersQry } = useGetAllUsersQry(props.entityId)

  // Email form functionality
  const form = useForm<UserEmailFormValues>({
    defaultValues: {
      email: props.data.email ?? "",
    },
    onSubmit: async ({ value: { email } }) => {
      const { data: allUsersWithEntities } = await fetchAllUsersWithEntities()
      const { data: users } = await fetchAllUsersQry()

      const listUsersEmailExists = users?.find(
        (user) => user.email?.toLowerCase() === email.toLowerCase(),
      )
      if (listUsersEmailExists) {
        toast.error(`User already added!`)
        return
      }

      const allUsersEmailExists = allUsersWithEntities?.find(
        (user) => user.email?.toLowerCase() === email.toLowerCase(),
      )

      if (allUsersEmailExists) {
        return props.onNext?.({ email, name: allUsersEmailExists.displayName })
      }

      props.onNext?.({ email }, StepId.NewUserForm)
    },
  })

  return { form }
}

export default function UserEmail(props: UserEmailProps) {
  const { form } = useUserEmail(props)

  return (
    <FormLayout className="w-full pb-0 [&>:first-child]:mb-4">
      <form
        className="space-y-4"
        data-testid="email-form"
        onSubmit={(e) => {
          e.preventDefault()
          form.handleSubmit()
        }}
      >
        <form.Field
          name="email"
          validators={{
            onSubmit: ({ value }) => validateEmail(value, true),
          }}
        >
          {(field) => (
            <FormField
              aria-label={field.name}
              field={field}
              label="Email"
              required
            >
              <Input
                autoFocus
                className="bg-background"
                id={field.name}
                name={field.name}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter email address"
                type="email"
                value={field.state.value}
              />
            </FormField>
          )}
        </form.Field>

        <div className="mt-6 flex justify-end">
          <DialogClose asChild>
            <Button
              className="w-full text-muted-foreground sm:w-auto"
              type="button"
              variant="link"
            >
              Cancel
            </Button>
          </DialogClose>

          <Button
            className="w-full sm:w-auto"
            disabled={form.state.isSubmitting}
            type="submit"
          >
            {form.state.isSubmitting ? "Checking..." : "Continue"}
          </Button>
        </div>
      </form>
    </FormLayout>
  )
}
