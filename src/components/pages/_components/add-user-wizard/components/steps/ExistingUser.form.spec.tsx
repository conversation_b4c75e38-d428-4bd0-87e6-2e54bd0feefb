import { vi, describe, it, expect, beforeEach, Mock } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, act } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { TooltipProvider } from "@radix-ui/react-tooltip"

import { useGetAllUsersWithEntitiesQry } from "@/data/user/user.query"
import { useEntityAccessMutations } from "@/data/entity-access/entity-access.mutation"
import { EnumValueDto } from "@/client/onboarding/types.gen"

import ExistingUser from "./ExistingUser.form"

// Mock hooks
vi.mock("@/data/onboarding/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entityId: "entity-123",
    staticDataUserRoles: [
      { display: "Administrator", key: "Administrator" },
      { display: "Approver", key: "Approver" },
      { display: "Viewer", key: "Viewer" },
      { display: "Submitter", key: "Submitter" },
    ],
  })),
}))

vi.mock("@/data/user/user.query", () => ({
  GET_ALL_USERS_WITH_ENTITIES_QRY_KEY: "get-all-users-with-entities",
  useGetAllUsersWithEntitiesQry: vi.fn(),
}))

vi.mock("@/data/onboarding/static-data.query", () => ({
  useGetUserRoleSuspenseQry: vi.fn(() => ({
    data: [
      { display: "Administrator", key: "administrator" },
      { display: "Submitter", key: "submitter" },
    ] as EnumValueDto[],
    isLoading: false,
  })),
}))

vi.mock("@/data/onboarding/entity-access.mutation", () => ({
  useCreateEntityAccessMutation: vi.fn(),
}))

// Add the missing mock for useEntityAccessMutations
vi.mock("@/data/entity-access/entity-access.mutation", () => ({
  useEntityAccessMutations: vi.fn(),
}))

const mockUseEntityAccessMutations = vi.mocked(useEntityAccessMutations)

// Create a wrapper with QueryClientProvider
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>{children}</TooltipProvider>
  </QueryClientProvider>
)

describe("ExistingUser Form", () => {
  const mockUser = {
    id: "user-123",
    email: "<EMAIL>",
    displayName: "John Doe",
    entities: [
      { id: "entity-1", displayName: "Entity 1" },
      { id: "entity-2", displayName: "Entity 2" },
    ],
  }

  const defaultProps = {
    entityId: "entity-1",
    entityName: "john-foo",
    data: {
      email: "<EMAIL>",
      name: "Test User",
      roles: ["Viewer"],
      isSignatory: false,
    },
    onNext: vi.fn(),
    onBack: vi.fn(),
    onReset: vi.fn(),
  }

  const createMockMutation = () => {
    const callbacks: Record<string, () => void> = {}

    return {
      mutate: vi.fn((_data, options) => {
        if (options?.onSuccess) {
          callbacks.onSuccess = options.onSuccess
        }
      }),
      mutateAsync: vi.fn((_data, options) => {
        if (options?.onSuccess) {
          callbacks.onSuccess = options.onSuccess
        }
      }),
      callbacks,
      isLoading: false,
      isError: false,
      isSuccess: false,
      error: null,
    }
  }

  let mockMutation: ReturnType<typeof createMockMutation>
  let mockCreateEntityAccess: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient.clear()
    mockMutation = createMockMutation()
    mockCreateEntityAccess = vi.fn()

    // Setup mock implementation
    ;(useGetAllUsersWithEntitiesQry as Mock).mockReturnValue({
      data: [mockUser],
      isLoading: false,
    })
    ;(mockUseEntityAccessMutations as Mock).mockReturnValue({
      createEntityAccess: mockCreateEntityAccess,
    })
  })

  it("renders the existing user form with user details", async () => {
    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Check for info alert
    expect(
      screen.getByText("A user with provided email already exists"),
    ).toBeInTheDocument()

    // Check user details are displayed
    expect(screen.getByText("Name")).toBeInTheDocument()
    expect(screen.getByText("Test User")).toBeInTheDocument()

    expect(screen.getByText("Email")).toBeInTheDocument()
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument()

    expect(screen.getByText("Entities")).toBeInTheDocument()
    expect(screen.getByText("entity 1, entity 2")).toBeInTheDocument()

    // Check for role label
    expect(screen.getByRole("combobox", { name: /role/i })).toBeInTheDocument()
  })

  it("calls onBack when back button is clicked", async () => {
    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Click back button
    await userEvent.click(screen.getByText("Back"))

    // Verify onBack was called
    expect(defaultProps.onBack).toHaveBeenCalled()
  })

  it("calls mutate when Add user button is clicked", async () => {
    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Click Add User button
    await userEvent.click(screen.getByText("Add user"))

    // Verify mutation was called
    expect(mockCreateEntityAccess).toHaveBeenCalled()
  })

  it("calls onReset when mutation is successful", async () => {
    // Mock createEntityAccess to simulate success callback
    const mockCreateEntityAccessWithCallback = vi.fn((_payload, options) => {
      if (options?.onSettled) {
        options.onSettled()
      }
    })

    ;(mockUseEntityAccessMutations as Mock).mockReturnValue({
      createEntityAccess: mockCreateEntityAccessWithCallback,
    })

    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Click Add User button to trigger mutation
    await userEvent.click(screen.getByText("Add user"))

    // Simulate successful mutation by calling success callback
    act(() => {
      if (typeof mockMutation.callbacks.onSuccess === "function") {
        mockMutation.callbacks.onSuccess()
      }
    })

    // Verify onReset was called
    expect(defaultProps.onReset).toHaveBeenCalled()
  })

  it("handles loading state when users data is loading", async () => {
    ;(useGetAllUsersWithEntitiesQry as Mock).mockReturnValue({
      data: undefined,
      isLoading: true,
    })

    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Basic structure should still be rendered
    expect(
      screen.getByText("A user with provided email already exists"),
    ).toBeInTheDocument()
  })

  it("handles error state in create entity access mutation", async () => {
    const errorMockCreateEntityAccess = vi.fn()

    ;(mockUseEntityAccessMutations as Mock).mockReturnValue({
      createEntityAccess: errorMockCreateEntityAccess,
    })
    ;(useGetAllUsersWithEntitiesQry as Mock).mockReturnValue({
      data: [mockUser],
      isLoading: false,
    })

    render(<ExistingUser {...defaultProps} />, { wrapper })

    // Check basic rendering works
    expect(
      screen.getByText("A user with provided email already exists"),
    ).toBeInTheDocument()

    // Submit form (shouldn't throw)
    await userEvent.click(screen.getByText("Add user"))

    // Verify mutation was called
    expect(errorMockCreateEntityAccess).toHaveBeenCalled()
  })
})
