import { Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { act, fireEvent, render, screen } from "@testing-library/react"

import { TestRouter } from "@/tests/utils/TestRouter"
import { useAddNewUserToExistingEntityMutation } from "@/data/entity/entity.mutation"
import { User } from "@/components/pages/_components/add-user-wizard/interface"
import NewUser, {
  AddUserProps,
} from "@/components/pages/_components/add-user-wizard/components/steps/NewUser.form"

// Mock dependencies
vi.mock("@/data/onboarding/static-data.query", () => ({
  useGetCountriesSuspenseQry: vi.fn(() => ({
    data: [
      { code: "US", dialCode: "+1" },
      { code: "UK", dialCode: "+44" },
    ],
  })),
}))

vi.mock("@/data/entity/entity.mutation", () => ({
  useAddNewUserToExistingEntityMutation: vi.fn(),
}))

const defaultProps = {
  entityId: "sd-sd-sfd",
  data: {
    email: "<EMAIL>",
  } as User,
  onNext: vi.fn(),
  onBack: vi.fn(),
  onCloseModal: vi.fn(),
}

const setup = (props: Partial<AddUserProps> = defaultProps) => {
  const user = userEvent.setup()

  const component = render(<NewUser {...defaultProps} {...props} />, {
    wrapper: TestRouter,
  })

  window.HTMLElement.prototype.scrollIntoView = vi.fn()
  window.HTMLElement.prototype.hasPointerCapture = vi.fn()

  const form = screen.getByRole("form", { name: /New user form/i })

  const [firstName, lastName, email, dialCode, phone, role, submit] = [
    screen.getByRole("textbox", { name: /first name/i }),
    screen.getByRole("textbox", { name: /last name/i }),
    screen.getByRole("textbox", { name: /email/i }),
    screen.getByRole("combobox", { name: /dialCode/ }),
    screen.getByRole("textbox", { name: /phone/i }),
    screen.getByRole("combobox", { name: /role/i }),
    screen.getByRole("button", { name: /Add user/i }),
  ]
  return {
    ...component,
    form,
    firstName,
    lastName,
    email,
    dialCode,
    phone,
    role,
    submit,
    user,
  }
}

describe("NewUsasdder Form", () => {
  const mockMutateAsync = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup the mock return value for each test
    ;(useAddNewUserToExistingEntityMutation as Mock).mockReturnValue({
      mutateAsync: mockMutateAsync,
      mutate: vi.fn(),
      isLoading: false,
      isError: false,
      error: null,
    })
  })

  it("renders the user form with email pre-populated", async () => {
    const { firstName, lastName, email, dialCode, phone, role, submit } =
      setup()

    expect(firstName).toBeInTheDocument()
    expect(lastName).toBeInTheDocument()
    expect(email).toBeInTheDocument()
    expect(dialCode).toBeInTheDocument()
    expect(phone).toBeInTheDocument()
    expect(role).toBeInTheDocument()
    expect(submit).toBeInTheDocument()

    // Email state validation
    expect(email).toBeDisabled()
  })

  it("calls mutation when form is submitted directly", async () => {
    mockMutateAsync.mockResolvedValue({})

    const { form } = setup()

    act(() => {
      fireEvent.submit(form)
    })

    expect(mockMutateAsync).not.toHaveBeenCalled()
    // Verify onCloseModal was called
    expect(defaultProps.onCloseModal).not.toHaveBeenCalled()
  })

  it("validates required fields and enables submit when all fields are filled", async () => {
    mockMutateAsync.mockResolvedValue({})

    const { user, firstName, lastName, email, dialCode, phone, role, submit } =
      setup()

    await user.type(firstName, "John")
    await user.type(lastName, "Doe")
    await user.type(email, "1234567890")
    await user.selectOptions(dialCode, "+1")
    await user.type(phone, "1234567890")

    /** Role selector */
    await user.click(role)
    expect(role).toHaveAttribute("aria-expanded", "true")
    const viewer = screen.getByRole("option", { name: "Viewer" })
    await user.click(viewer)

    /** Form submission */
    await user.click(submit)

    expect(mockMutateAsync).toHaveBeenCalled()
    expect(defaultProps.onCloseModal).toHaveBeenCalled()
  })
})
