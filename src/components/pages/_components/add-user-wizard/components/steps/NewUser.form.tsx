import { toast } from "sonner"
import { useStore } from "@tanstack/react-store"
import { useForm } from "@tanstack/react-form"

import { cn } from "@/lib/utils"
import { Role, Roles } from "@/lib/constants/user.constants"
import { useGetCountriesSuspenseQry } from "@/data/onboarding/static-data.query"
import {
  AddNewUserToExistingEntityPayload,
  useAddNewUserToExistingEntityMutation,
} from "@/data/entity/entity.mutation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { UserSelector } from "@/components/pages/add-new-user/components/UserSelector"
import { FormField, FormLayout } from "@/components/base/form/form"
import { FormFieldError } from "@/components/base/form/form"
import { LoginEntityApproverLevel } from "@/client/onboarding/types.gen"

import { User, StepId } from "../../interface"

interface UserFormValues {
  firstName: string
  lastName: string
  phone: string
  dialCode: string
  role: string
  signatory: string
}

type FieldNames =
  | "firstName"
  | "lastName"
  | "phone"
  | "dialCode"
  | "role"
  | "signatory"

type ErrorMessage = `${string} is required`

const ErrorMessages: Record<FieldNames, ErrorMessage> = {
  firstName: "First name is required",
  lastName: "Last name is required",
  phone: "Phone is required",
  dialCode: "Dial code is required",
  role: "Role is required",
  signatory: "Signatory is required",
}

function fieldIsNotEmpty(name: FieldNames, value: string) {
  return value.trim() ? undefined : ErrorMessages[name]
}

export interface AddUserProps {
  data: User
  entityId: string
  onNext?: (
    payload: AddNewUserToExistingEntityPayload,
    goToStep?: number,
  ) => void
  onBack?: (goToStep?: StepId) => void
  onCloseModal?: () => void
  onOpenChange?: (open: boolean) => void
  onUserAdded?: () => void
}

function useAddUser(props: AddUserProps) {
  const { data: countriesOptions } = useGetCountriesSuspenseQry()
  const { mutateAsync: addNewUserToEntity } =
    useAddNewUserToExistingEntityMutation(props.entityId)

  const form = useForm<UserFormValues>({
    defaultValues: {
      firstName: "",
      lastName: "",
      phone: "",
      dialCode: "",
      role: "",
      signatory: "",
    },
    onSubmit: async ({ value }) => {
      try {
        await addNewUserToEntity({
          email: props.data.email,
          ...value,
        })
      } catch (error) {
        toast.error(`Error adding user: ${error}`)
      } finally {
        if (props.onCloseModal) {
          props.onCloseModal()
        }
      }
    },
  })

  const handleFormSubmit = () => {
    form.handleSubmit()
  }

  return { form, countriesOptions, handleFormSubmit }
}

export default function AddUser(props: AddUserProps) {
  const { form, countriesOptions, handleFormSubmit } = useAddUser(props)
  const dialCodeErrors = useStore(
    form?.store,
    (state) => (state.fieldMeta.dialCode?.errors ?? []) as string[],
  )
  const phoneErrors = useStore(
    form?.store,
    (state) => (state.fieldMeta.phone?.errors ?? []) as string[],
  )

  return (
    <FormLayout className="w-full pb-0 [&>:first-child]:mb-4">
      <form
        aria-label="New user form"
        className="space-y-2"
        data-testid="user-form"
        onSubmit={(e) => {
          e.preventDefault()
          handleFormSubmit()
        }}
      >
        <form.Field
          name="firstName"
          validators={{
            onChange: ({ value, fieldApi }) =>
              fieldIsNotEmpty(fieldApi.name, value),
          }}
        >
          {(field) => (
            <FormField
              aria-label={field.name}
              field={field}
              label="First name"
              required
            >
              <Input
                className="bg-background"
                id={field.name}
                onChange={(e) => field.handleChange(e.target.value)}
                type="text"
                value={field.state.value}
              />
            </FormField>
          )}
        </form.Field>

        <form.Field
          name="lastName"
          validators={{
            onChange: ({ value, fieldApi }) =>
              fieldIsNotEmpty(fieldApi.name, value),
          }}
        >
          {(field) => (
            <FormField
              aria-label={field.name}
              field={field}
              label="Last name"
              required
            >
              <Input
                className="bg-background"
                id={field.name}
                name={field.name}
                onChange={(e) => field.handleChange(e.target.value)}
                type="text"
                value={field.state.value}
              />
            </FormField>
          )}
        </form.Field>

        <div className="space-y-2">
          <label
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            htmlFor="email"
          >
            Email
          </label>
          <Input
            className="!mt-1 bg-background font-medium"
            disabled
            id="email"
            name="email"
            readOnly
            type="email"
            value={props.data.email || ""}
          />
        </div>

        <form.Field
          name="dialCode"
          validators={{
            onChange: ({ value, fieldApi }) =>
              fieldIsNotEmpty(fieldApi.name, value),
          }}
        >
          {(field) => (
            <div>
              <label
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                htmlFor="phone"
              >
                Phone
              </label>
              <div className="mb-2 flex gap-x-3">
                <div
                  className={cn("rounded-xl border", {
                    "border-destructive ring-destructive":
                      dialCodeErrors.length > 0,
                  })}
                >
                  <select
                    aria-label="dialCode"
                    className="flex h-10 w-28 rounded-xl border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    data-test={field.state.value}
                    id={field.name}
                    name={field.name}
                    onChange={(e) => field.handleChange(e.target.value)}
                    role="combobox"
                  >
                    <option value="">Select</option>
                    {countriesOptions.map((country) => (
                      <option key={country.code} value={country.dialCode}>
                        {country.code} ({country.dialCode})
                      </option>
                    ))}
                  </select>
                </div>

                <div
                  className={cn("rounded-xl border", {
                    "border-destructive ring-destructive":
                      phoneErrors.length > 0,
                  })}
                >
                  <form.Field
                    name="phone"
                    validators={{
                      onChange: ({ value, fieldApi }) =>
                        fieldIsNotEmpty(fieldApi.name, value),
                    }}
                  >
                    {(field) => (
                      <Input
                        className="flex max-h-10 w-[unset] flex-1 bg-background"
                        id={field.name}
                        name={field.name}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Number"
                        type="tel"
                        value={field.state.value}
                      />
                    )}
                  </form.Field>
                </div>
              </div>
              <FormFieldError
                aria-label={`phone error`}
                errors={phoneErrors.length > 0 ? phoneErrors : dialCodeErrors}
              />
            </div>
          )}
        </form.Field>

        <form.Field
          name="role"
          validators={{
            onChange: ({ value, fieldApi }) =>
              fieldIsNotEmpty(fieldApi.name, value),
          }}
        >
          {(field) => (
            <FormField
              aria-label={field.name}
              field={field}
              label="Role"
              required
            >
              <UserSelector
                id={field.name}
                items={Roles}
                onChange={(role) => {
                  field.handleChange(role)
                  form.setFieldValue("signatory", "")
                }}
                placeholder="Select role"
                triggerClassName="w-full h-10"
                value={field.state.value as Role}
              />
            </FormField>
          )}
        </form.Field>

        <form.Subscribe selector={(state) => state.values.role as Role}>
          {(roleField) => (
            <>
              <form.Field name="signatory">
                {(field) => (
                  <>
                    {[Role.Approver, Role.Administrator].includes(
                      roleField,
                    ) && (
                      <FormField
                        aria-label={field.name}
                        field={field}
                        label="Signatory type"
                        required
                      >
                        <UserSelector
                          id={field.name}
                          items={
                            [
                              "LevelA",
                              "LevelB",
                              "LevelC",
                            ] as LoginEntityApproverLevel[]
                          }
                          onChange={(role) => field.handleChange(role)}
                          placeholder="Select signatory type"
                          triggerClassName="w-full h-10"
                          value={field.state.value}
                        />
                      </FormField>
                    )}
                  </>
                )}
              </form.Field>
            </>
          )}
        </form.Subscribe>

        <div className="!mt-10 flex justify-end gap-4">
          <Button
            className="w-full text-muted-foreground sm:w-auto"
            onClick={() => props.onBack?.(StepId.UserEmailForm)}
            type="button"
            variant="link"
          >
            Back
          </Button>

          <Button
            className="w-full sm:w-auto"
            disabled={form.state.isSubmitting}
            type="submit"
          >
            {form.state.isSubmitting ? "Adding..." : "Add user"}
          </Button>
        </div>
      </form>
    </FormLayout>
  )
}
