import { Plus } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { cn } from "@/lib/utils"
import { useModal } from "@/hooks/use-modal"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

import { AddressModalProps } from "../interface"

export function Modal({
  children,
  Button: { copy, className: btnClassName, variant, icon, ...triggerProps },
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange,
}: AddressModalProps) {
  const { visible: internalVisible, show, close } = useModal()

  // Use controlled props if provided, otherwise use internal state
  const isControlled =
    controlledOpen !== undefined && controlledOnOpenChange !== undefined
  const visible = isControlled ? controlledOpen : internalVisible

  const handleOpen = (isOpen: boolean) => {
    if (isControlled) {
      controlledOnOpenChange(isOpen)
    } else {
      if (isOpen) {
        show()
      } else {
        close()
      }
    }
  }

  return (
    <Dialog modal onOpenChange={handleOpen} open={visible}>
      <DialogTrigger asChild>
        <Button
          className={cn(["w-fit py-0 pl-0 pr-0", btnClassName])}
          variant={variant}
          {...triggerProps}
        >
          {icon ?? <Plus className="h-4 w-4" />}
          {copy}
        </Button>
      </DialogTrigger>

      <DialogPortal>
        <DialogContent className="overflow-hidden">
          <VisuallyHidden>
            <DialogDescription className="hidden"></DialogDescription>
            <DialogTitle>Edit address</DialogTitle>
          </VisuallyHidden>

          {children}
        </DialogContent>
      </DialogPortal>
    </Dialog>
  )
}
