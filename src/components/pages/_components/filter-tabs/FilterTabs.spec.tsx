import { describe, expect, it } from "vitest"
import { render, screen } from "@testing-library/react"

import FilterTabs from "./FilterTabs"

describe("ButtonGroup", () => {
  const mockItems = ["All", "Draft", "Pending", "Sent", "Failed"]

  it("should render all filter buttons", () => {
    render(<FilterTabs active="All" items={mockItems} setActive={() => {}} />)

    mockItems.forEach((item) => {
      expect(screen.getByRole("tab", { name: item })).toBeInTheDocument()
    })
  })

  it("should highlight active filter button", () => {
    render(<FilterTabs active="All" items={mockItems} setActive={() => {}} />)

    const activeButton = screen.getByRole("tab", { name: "All" })
    const inactiveButton = screen.getByRole("tab", { name: "Draft" })

    expect(activeButton).toHaveAttribute("data-state", "active")
    expect(inactiveButton).toHaveAttribute("data-state", "inactive")
  })

  it("should maintain button order", () => {
    render(<FilterTabs active="All" items={mockItems} setActive={() => {}} />)

    const tabs = screen.getAllByRole("tab")
    expect(tabs).toHaveLength(mockItems.length)
    mockItems.forEach((item, index) => {
      expect(tabs[index]).toHaveTextContent(item)
    })
  })
})
