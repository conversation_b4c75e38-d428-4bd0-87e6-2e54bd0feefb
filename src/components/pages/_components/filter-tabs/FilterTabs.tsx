import { PropsWithChildren } from "react"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { ShowChildren } from "@/components/base/show-children"

export interface FilterTabsProps<T> extends PropsWithChildren {
  defaultValue?: T
  items: T[]
  active?: string
  setActive?: (filter: string) => void
}

function Item<T extends string>({
  children,
  value,
  "aria-label": ariaLabel,
}: PropsWithChildren<{ value: T; "aria-label"?: string }>) {
  return (
    <TabsContent aria-label={ariaLabel} value={value}>
      {children}
    </TabsContent>
  )
}

export default function FilterTabs<T extends string>({
  children,
  defaultValue,
  items,
  active,
  setActive,
}: FilterTabsProps<T>) {
  return (
    <Tabs
      defaultValue={defaultValue ?? items?.at(0)}
      onValueChange={setActive}
      value={active}
    >
      <TabsList className="flex w-fit justify-start rounded-full bg-background">
        {items.map((item, index) => (
          <TabsTrigger
            className="px-4 data-[state=active]:rounded-full data-[state=active]:border-none data-[state=active]:bg-muted data-[state=active]:shadow-none"
            key={`${item}-${index}`}
            value={item}
          >
            {item}
          </TabsTrigger>
        ))}
      </TabsList>

      <ShowChildren when={!!children}>{children}</ShowChildren>
    </Tabs>
  )
}

FilterTabs.Item = Item
