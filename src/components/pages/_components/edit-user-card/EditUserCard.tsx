import { useState } from "react"
import { InfoIcon } from "lucide-react"
import { toLower } from "lodash-es"
import { TooltipTrigger } from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"
import { Role as RoleType } from "@/lib/constants/user.constants"
import { useToggle } from "@/hooks/use-toggle"
import { useGetUserRoleSuspenseQry } from "@/data/onboarding/static-data.query"
import { Tooltip } from "@/components/ui/tooltip"
import Subline from "@/components/ui/subline"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ShowChildren } from "@/components/base/show-children"
import CopyToClipboard from "@/components/base/copy-to-clipboard"
import {
  EntityType,
  EnumValueDto,
  LoginEntityApproverLevel,
} from "@/client/onboarding/types.gen"

import { UserSelector } from "../../add-new-user/components/UserSelector"

function Name({ name }: { name: string }) {
  return (
    <div
      aria-label="Name"
      className="flex items-center justify-between text-sm"
    >
      <span className="font-medium">Name</span>

      <div className="flex items-center gap-x-2">
        <span className="select-none border-none text-muted-foreground">
          {name}
        </span>
        <CopyToClipboard
          className="size-5 p-0 [&_svg]:size-2.5"
          text={name}
          toastSuccessMessage={`Name ${name} copied`}
        />
      </div>
    </div>
  )
}

function Email({ email }: { email: string }) {
  return (
    <div
      aria-label="Email"
      className="flex items-center justify-between text-sm"
    >
      <span className="font-medium">Email</span>

      <div className="flex items-center gap-x-2">
        <span className="select-none border-none text-muted-foreground">
          {email}
        </span>

        <CopyToClipboard className="size-5 p-0 [&_svg]:size-2.5" text={email} />
      </div>
    </div>
  )
}

function Entities({ entities }: { entities?: string[] }) {
  return (
    <ShowChildren when={!!entities}>
      <div
        aria-label="Current entities"
        className="flex items-start justify-between text-sm"
      >
        <span className="font-medium">Entities</span>
        <div className="flex max-w-80 flex-wrap items-center justify-end gap-1">
          <span className="max-w-80 select-none border-none text-right text-muted-foreground">
            {toLower(entities?.join(", "))}
          </span>
        </div>
      </div>
    </ShowChildren>
  )
}

// <EMAIL>
function Role({
  entityName,
  onRoleChange,
  onSignatoryChange,
}: {
  entityName: string
  onRoleChange: (role: EnumValueDto) => void
  onSignatoryChange: (signatory: LoginEntityApproverLevel) => void
}) {
  const { data: options } = useGetUserRoleSuspenseQry()
  const [role, setRole] = useState<EnumValueDto | undefined>()
  const [signatory, setSignatory] = useState<
    LoginEntityApproverLevel | undefined
  >()
  const [isRole, { on, off }] = useToggle()

  function handleRoleChange(display: string) {
    const role = options.find((op) => op.display === display)

    if (!role) return

    setRole(role)

    onRoleChange(role)

    const isAdminOrApprover =
      role?.display &&
      [RoleType.Approver, RoleType.Administrator].includes(
        role.display as RoleType,
      )

    if (isAdminOrApprover) {
      on()
    } else {
      off()
      setSignatory(undefined)
    }

    if (!signatory) return

    onSignatoryChange?.(signatory)
  }

  return (
    <div className="mt-5 flex flex-col gap-y-2">
      <div className="flex flex-col gap-y-2">
        <div className="flex items-center gap-x-1">
          <label className="text-sm font-medium">
            Role within {entityName}
          </label>

          <Tooltip>
            <TooltipTrigger>
              <InfoIcon className="size-3.5 text-muted-foreground" />
            </TooltipTrigger>
          </Tooltip>
        </div>

        <Select
          onValueChange={handleRoleChange}
          value={role?.key as EntityType}
        >
          <SelectTrigger
            aria-label="role"
            className="flex bg-background p-4 py-6 [&>span]:pr-3"
            id="role"
          >
            <SelectValue placeholder="Select role">
              <div className="flex items-center">{role?.display}</div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {options.map((entityType) => (
                <SelectItem key={entityType.key} value={entityType.display!}>
                  <div className="flex items-center gap-2">
                    {entityType.display}
                  </div>
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      {isRole && (
        <div className="flex items-start gap-x-2 [&>svg]:top-0">
          <Subline />

          <div className="relative top-1 flex w-60 flex-col gap-y-3">
            <div className="flex items-center gap-x-1">
              <label className="text-sm font-medium">Signatory</label>

              <Tooltip>
                <TooltipTrigger>
                  <InfoIcon className="size-3.5 text-muted-foreground" />
                </TooltipTrigger>
              </Tooltip>
            </div>

            <UserSelector
              id="signatory"
              items={
                ["LevelA", "LevelB", "LevelC"] as LoginEntityApproverLevel[]
              }
              onChange={setSignatory}
              placeholder="Select signatory type"
              triggerClassName="w-full h-10"
              value={signatory}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export interface EditUserCardProps {
  entityId: string
  entityName: string
  name: string
  email: string
  entities?: string[]
  className?: string
  onRoleChange?: (role: EnumValueDto) => void
  onSignatoryChange?: (signatory: LoginEntityApproverLevel) => void
}

export default function EditUserCard(props: EditUserCardProps) {
  return (
    <div
      className={cn(
        "flex flex-1 flex-col gap-y-1 rounded-xl bg-muted/50 p-4",
        props.className,
      )}
    >
      <Name name={props.name} />

      <Email email={props.email} />

      <Entities entities={props.entities} />

      <Role
        entityName={props.entityName}
        onRoleChange={(role) => props.onRoleChange?.(role)}
        onSignatoryChange={(signatory) => props.onSignatoryChange?.(signatory)}
      />
    </div>
  )
}
