import { ReactNode } from "react"
import {
  CircleCheckIcon,
  CircleXIcon,
  InfoIcon,
  LucideIcon,
  TriangleAlertIcon,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { ShowChildren } from "@/components/base/show-children"

const VARIANTS = {
  default: cn("text-foreground"),
  success: cn("border-success/40 bg-success/5 text-success"),
  warning: cn("border-warning/40 bg-warning/5 text-warning"),
  error: cn("border-destructive/40 bg-destructive/5 text-destructive"),
  info: cn("border-info/40 bg-info/5 text-info"),
  neutral: cn("border-weak-border bg-weak text-weak-foreground"),
} as const

export type StatusVariant = keyof typeof VARIANTS

const DEFAULT_ICONS = {
  default: () => <></>,
  success: CircleCheckIcon,
  warning: TriangleAlertIcon,
  error: CircleXIcon,
  info: InfoIcon,
  neutral: () => <></>,
} as const

export interface StatusBadgeProps {
  variant?: StatusVariant
  text?: string
  icon?: LucideIcon
  className?: string
  children?: ReactNode
}

export default function StatusBadge({
  variant = "default",
  text,
  icon,
  className,
  children,
}: StatusBadgeProps) {
  const IconComponent = icon || DEFAULT_ICONS[variant]

  return (
    <Badge
      className={cn(
        "gap-x-1 rounded-lg px-1.5 text-left font-normal",
        VARIANTS[variant],
        className,
      )}
      variant="outline"
    >
      <IconComponent className="size-3 shrink-0" />

      <ShowChildren when={!!children && !text}>{children}</ShowChildren>

      <ShowChildren when={!!text}>
        <span className="line-clamp-1 text-xs">{text}</span>
      </ShowChildren>
    </Badge>
  )
}
