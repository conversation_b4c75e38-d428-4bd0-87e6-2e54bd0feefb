import { useMemo } from "react"

import { useModal } from "@/hooks/use-modal"
import { useGetAllUsersQry } from "@/data/entity/entity.query"
import { useEntityAccessMutations } from "@/data/entity-access/entity-access.mutation"

import { type RemoveUserProps } from "./RemoveUser"

export function useRemoveUser({
  entityId,
  email: currentEmail,
  onRemoveSuccess,
}: RemoveUserProps) {
  const { deleteEntityAccess: removeUser } = useEntityAccessMutations(entityId)
  const {
    visible: isModalOpen,
    show: showModal,
    close: closeModal,
    toggle: toggleModal,
  } = useModal()
  const { data: users } = useGetAllUsersQry(entityId)

  const entityAccessId = useMemo(() => {
    const currentUser = users?.find((user) => user.email === currentEmail)

    return currentUser?.entityAccess?.entityAccessId
  }, [currentEmail, users])

  function handleRemove() {
    if (entityAccessId)
      removeUser(entityAccessId, { onSuccess: onRemoveSuccess })
  }

  return {
    isModalOpen,
    showModal,
    closeModal,
    toggleModal,
    handleRemove,
  }
}
