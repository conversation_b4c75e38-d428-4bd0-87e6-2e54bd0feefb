import { Trash2 } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { But<PERSON> } from "@/components/ui/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import { useRemoveUser } from "./RemoveUser.hook"

export interface RemoveUserProps {
  email?: string | undefined
  entityId: string
  entityName?: string | null
  disabled?: boolean
  primaryText?: string
  ButtonProps?: {
    disabled?: boolean
    text?: string
  }
  onRemoveSuccess?: () => void
}

export default function RemoveUser(props: RemoveUserProps) {
  const { isModalOpen, toggleModal, showModal, handleRemove } =
    useRemoveUser(props)

  return (
    <AlertDialog onOpenChange={toggleModal} open={isModalOpen}>
      <AlertDialogTrigger asChild>
        <Button
          className="self-end pr-0 underline underline-offset-4 hover:bg-transparent"
          disabled={props.ButtonProps?.disabled}
          onClick={showModal}
          variant="destructive-ghost"
        >
          <Trash2 />
          <span>{props.ButtonProps?.text ?? "Remove"}</span>
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent>
        <VisuallyHidden>
          <AlertDialogDescription className="hidden"></AlertDialogDescription>
          <AlertDialogTitle>Edit address</AlertDialogTitle>
        </VisuallyHidden>

        <AlertDialogHeader>
          <AlertDialogTitle>Remove user</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to remove this user from{" "}
            <span className="font-semibold italic">{props.entityName}</span>
            ?
            <br />
            Other entity access the user has will be unaffected.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="mx-auto mt-3 flex w-full max-w-md justify-end gap-2">
          <AlertDialogCancel>Cancel</AlertDialogCancel>

          <AlertDialogAction asChild>
            <Button
              disabled={props.disabled}
              onClick={handleRemove}
              type="submit"
              variant="destructive"
            >
              <Trash2 />
              {props.primaryText ?? props.ButtonProps?.text ?? "Remove"}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
