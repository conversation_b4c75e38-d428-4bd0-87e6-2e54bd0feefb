import { usePendingPaymentsQuery } from "@/data/payments/payments.query"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

import { BulkPaymentStepper } from "./components/BulkPaymentStepper"
interface BulkPaymentDetailsPageProps {
  id: string
  entityId: string
}

export function BulkPaymentDetailsPage({
  id,
  entityId,
}: BulkPaymentDetailsPageProps) {
  const { data: bulkPayment, isLoading } = usePendingPaymentsQuery(id)

  return (
    <div>
      {isLoading && (
        <div className="flex h-full w-full items-center justify-center">
          <LoadingSpinner />
        </div>
      )}
      {!isLoading && bulkPayment && (
        <BulkPaymentStepper
          entityId={entityId}
          id={id}
          pendingPayments={bulkPayment.pendingPayments}
        />
      )}
    </div>
  )
}
