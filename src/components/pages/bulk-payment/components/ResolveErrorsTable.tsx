import { IPendingPayment } from "@/data/payments/payments.interface"
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { ErrorRow } from "./ErrorRow"

interface ResolveErrorsTableProps {
  payments: IPendingPayment[]
  errors: IPendingPayment[]
  onResolve?: (rowNumber: number) => void
}

export function ResolveErrorsTable({
  payments,
  errors,
  onResolve,
}: ResolveErrorsTableProps) {
  const handleResolve = (rowNumber: number) => {
    if (onResolve) {
      onResolve(rowNumber)
    }
  }

  return (
    <div className="container rounded-lg bg-background p-4">
      <h4 className="pb-4 text-xl font-semibold">
        Errors {errors.length} out of {payments.length} payments
      </h4>

      <div className="-mx-4 max-h-96 overflow-auto px-4">
        <Table className="w-full">
          <TableHeader className="sticky top-0 bg-background">
            <TableRow>
              <TableHead className="w-24 text-left">Row</TableHead>
              <TableHead className="w-48 text-left">Payee</TableHead>
              <TableHead className="w-auto text-left">Error</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {errors.map((error) => {
              const errorMessage =
                error.validationErrors?.length > 1
                  ? "This payment has one or more errors"
                  : error.validationErrors[0].message

              return (
                <ErrorRow
                  error={errorMessage}
                  key={error.rowNumber}
                  onResolve={() => handleResolve(error.rowNumber)}
                  payee={error.payeeAccountName}
                  rowNumber={error.rowNumber}
                />
              )
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
