import { ReactNode } from "react"
import { AlertTriangle, ChevronDown } from "lucide-react"

import { cn } from "@/lib/utils"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"

interface CollapsibleSectionProps {
  title: string
  children: ReactNode
  open: boolean
  hasError?: boolean
  onOpenChange: () => void
}

export function CollapsibleSection({
  title,
  children,
  open,
  hasError,
  onOpenChange,
}: CollapsibleSectionProps) {
  return (
    <Collapsible
      className={cn(
        "rounded-lg border bg-background",
        hasError && "border-destructive",
      )}
      onOpenChange={onOpenChange}
      open={open}
    >
      <CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg p-4 hover:bg-gray-50">
        <div className="flex items-center gap-2">
          {hasError && <AlertTriangle className="h-5 w-5 text-destructive" />}
          <h3
            className={cn(
              "text-xl font-semibold",
              hasError && "text-destructive",
            )}
          >
            {title}
          </h3>
        </div>
        <ChevronDown
          className={cn(
            "h-5 w-5 text-gray-500 transition-transform duration-200",
            open ? "rotate-180 transform" : "",
          )}
        />
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-4 p-4 pt-2">
        {children}
      </CollapsibleContent>
    </Collapsible>
  )
}
