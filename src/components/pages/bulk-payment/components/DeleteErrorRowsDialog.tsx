import { <PERSON><PERSON><PERSON><PERSON>gle, Trash2 } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import { DialogProps } from "@radix-ui/react-dialog"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { TrailingSpinner } from "@/components/base/loading-spinner/TrailingSpinner"
import { AlertBox } from "@/components/base/alert-box"

interface DeleteErrorRowsDialogProps extends DialogProps {
  rowNumbers: string[]
  onConfirm: () => void
  isDisabled?: boolean
}

export function DeleteErrorRowsDialog({
  rowNumbers = [],
  onOpenChange,
  onConfirm,
  isDisabled,
}: DeleteErrorRowsDialogProps) {
  return (
    <Dialog onOpenChange={onOpenChange} open={rowNumbers.length > 0}>
      <DialogContent className="rounded-xl p-6 shadow-lg">
        <div className="flex flex-col gap-2">
          <DialogTitle className="text-xl font-semibold">
            Delete error rows
          </DialogTitle>
          <DialogDescription className="text-base text-muted-foreground">
            {`You are about to delete ${
              rowNumbers.length > 1 ? `${rowNumbers.length} rows` : "a row"
            } from bulk payment`}
          </DialogDescription>
        </div>

        <div className="mt-6 flex justify-end gap-3">
          <Button
            className="flex-1"
            disabled={isDisabled}
            onClick={() => onOpenChange?.(false)}
            variant="outline"
          >
            Cancel
          </Button>
          <TrailingSpinner isLoading={isDisabled}>
            <Button
              className="flex-1"
              disabled={isDisabled}
              onClick={onConfirm}
              variant="destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              {rowNumbers.length > 1
                ? `Delete error rows and continue`
                : "Delete error row and continue"}
            </Button>
          </TrailingSpinner>
        </div>
      </DialogContent>
    </Dialog>
  )
}
