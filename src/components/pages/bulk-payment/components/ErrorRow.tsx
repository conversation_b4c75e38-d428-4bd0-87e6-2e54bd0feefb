import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { TableRow } from "@/components/ui/table"
import { TableCell } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
interface ErrorRowProps {
  rowNumber: number
  payee: string
  error: string
  onResolve?: () => void
}

export function ErrorRow({
  rowNumber,
  payee,
  error,
  onResolve,
}: ErrorRowProps) {
  const threshold = 70
  function getError(error: string) {
    if (error.length > threshold) return error.slice(0, threshold) + "..."
    return error
  }
  const isErrorBig = error.length > threshold
  return (
    <TableRow className="group">
      <TableCell className="pl-2">{rowNumber}</TableCell>
      <TableCell>{payee}</TableCell>
      <TableCell>
        {isErrorBig ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <span>{getError(error)}</span>
            </TooltipTrigger>
            <TooltipContent className="border-1 max-w-md border-border bg-background p-2 text-foreground shadow-md">
              <span className="text-sm font-medium text-destructive">
                Error:
              </span>
              <span className="ms-2 font-medium text-foreground">{error}</span>
            </TooltipContent>
          </Tooltip>
        ) : (
          <span className="">{error}</span>
        )}
      </TableCell>
      <TableCell className="text-right">
        <Button
          className="opacity-0 group-hover:opacity-100"
          onClick={onResolve}
          size="sm"
        >
          Resolve
        </Button>
      </TableCell>
    </TableRow>
  )
}
