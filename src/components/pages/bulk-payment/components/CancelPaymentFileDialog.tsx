import { Trash2 } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import {
  DialogDescription,
  DialogProps,
  DialogTitle,
} from "@radix-ui/react-dialog"

import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

interface CancelPaymentFileDialogProps extends DialogProps {
  onConfirm: () => void
}

export function CancelPaymentFileDialog({
  open,
  onOpenChange,
  onConfirm,
}: CancelPaymentFileDialogProps) {
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="rounded-xl p-6 shadow-lg" hideClose={false}>
        <div className="flex flex-col gap-2">
          <DialogTitle className="text-xl font-semibold">
            Cancel payment file
          </DialogTitle>
          <DialogDescription className="text-base text-muted-foreground">
            Are you sure you want to cancel adding this payment file?
          </DialogDescription>
        </div>
        <div className="mt-6 flex justify-end gap-3">
          <Button onClick={() => onOpenChange?.(false)} variant="outline">
            Continue
          </Button>
          <Button onClick={onConfirm} variant="destructive">
            <Trash2 className="mr-2 h-5 w-5" />
            Cancel payment file
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
