import { formatDate } from "@/lib/date.utils"
import { formatWithMask } from "@/lib/bank.utils"
import { IPendingPayment } from "@/data/payments/payments.interface"
import { TableCell, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

interface ReviewRowProps {
  data: IPendingPayment
  onClick?: () => void
}

export function ReviewRow({ data, onClick }: ReviewRowProps) {
  return (
    <TableRow
      className="group border-b text-sm hover:bg-muted/50"
      onClick={onClick}
    >
      <TableCell>{data.rowNumber}</TableCell>
      <TableCell className="text-nowrap">
        {formatDate(data.paymentDate)}
      </TableCell>
      <TableCell>
        {formatWithMask(data.originatingAccountIban, {
          length: 4,
          maxLength: 8,
        })}
      </TableCell>
      <TableCell>{data.payeeType}</TableCell>
      <TableCell>{data.payeeAccountName}</TableCell>
      <TableCell className="text-right">
        <CurrencyText
          amount={data.paymentAmount}
          currency={data.paymentCurrency}
          dontDivide
        />
      </TableCell>
      <TableCell>{data.paymentCurrency}</TableCell>
      <TableCell className="text-right">
        <Button
          className="opacity-0 group-hover:opacity-100"
          onClick={onClick}
          size="sm"
        >
          Details
        </Button>
      </TableCell>
    </TableRow>
  )
}
