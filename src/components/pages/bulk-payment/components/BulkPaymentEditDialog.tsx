import { useState, useEffect } from "react"
import { AxiosError } from "axios"
import { useForm } from "@tanstack/react-form"
import { DialogProps } from "@radix-ui/react-dialog"

import { required } from "@/lib/form.utils"
import { format2Date, parseDate } from "@/lib/date.utils"
import { Currency } from "@/lib/constants/currency.constants"
import { bulkPaymentPatchRowMutation } from "@/data/payments/payments.mutation"
import {
  IPendingPayment,
  IPendingPaymentValidationErrors,
  TPendingPaymentKeys,
} from "@/data/payments/payments.interface"
import { useAccountsListQuery } from "@/data/account/account.query"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { TrailingSpinner } from "@/components/base/loading-spinner/TrailingSpinner"
import { FormField } from "@/components/base/form/form"
import { DatePicker } from "@/components/base/form/datepicker"
import { CurrencySelector } from "@/components/base/currency/CurrencySelector"

import { CollapsibleSection } from "./CollapsibleSection"
import { AccountSelector } from "../../account/components/AccountSelector"
import { CountryCombobox } from "@/components/base/country/CountryCombobox"

type BulkPaymentEditDialogProps = {
  bulkUploadId: string
  data?: IPendingPayment
  expandAll?: boolean
  onDelete?: (payment: IPendingPayment) => void
} & DialogProps

const sectionFieldMap: Record<string, TPendingPaymentKeys[]> = {
  currencyAccount: ["originatingAccountIban"],
  payeeDetails: ["payeeType", "payeeNationality"],
  payeeAddress: [
    "payeeBuildingNumber",
    "payeeStreetName",
    "payeeCity",
    "payeeRegionState",
    "payeePostalCode",
    "payeeCountry",
  ],
  payeeBank: [
    "payeeAccountName",
    "payeeAccountNumber",
    "payeeSWIFTBIC",
    "payeeLocalBankCode",
    "payeeIBAN",
  ],
  payment: [
    "paymentCurrency",
    "paymentAmount",
    "paymentDate",
    "paymentType",
    "paymentPurpose",
    "paymentReference",
  ],
}

export function BulkPaymentEditDialog({
  open,
  onOpenChange,
  data,
  bulkUploadId,
  expandAll = false,
  onDelete,
}: BulkPaymentEditDialogProps) {
  const { mutate: patchRow, isPending: isPatching } =
    bulkPaymentPatchRowMutation()

  const { data: accounts, isLoading: isAccountsLoading } =
    useAccountsListQuery()

  const [validationErrors, setValidationErrors] = useState<
    IPendingPaymentValidationErrors[]
  >([])

  interface IValidationErrorFromPatchRow {
    name: string
    reason: string
  }

  const form = useForm<IPendingPayment>({
    defaultValues: data
      ? (Object.fromEntries(
          Object.entries(data).map(([key, value]) => [key, value ?? ""]),
        ) as IPendingPayment)
      : undefined,
    onSubmit: async ({ value }) => {
      patchRow(
        {
          bulkUploadId,
          payload: value,
        },
        {
          onSuccess() {
            onOpenChange?.(false)
          },
          onError(error) {
            const err = error as AxiosError
            if (err.response?.status === 422 && err.response?.data) {
              const _errors = ((err.response?.data as any)?.errors ??
                []) as IValidationErrorFromPatchRow[]
              const _validationErrors = [] as IPendingPaymentValidationErrors[]

              const _responseDataKeysWithoutKeys = _errors.filter(
                (error) =>
                  data &&
                  Object.keys(data).some(
                    (v) => v.toLowerCase() === error.name.toLowerCase(),
                  ),
              )

              _responseDataKeysWithoutKeys.forEach(({ name, reason }) => {
                _validationErrors.push({
                  type: name as TPendingPaymentKeys,
                  message: reason,
                })
              })
              setValidationErrors(_validationErrors)
            }
          },
        },
      )
    },
  })

  useEffect(() => {
    const _validationErrors = [] as IPendingPaymentValidationErrors[]

    data?.validationErrors.forEach((error) => {
      if (error.type.includes(",")) {
        const fields = error.type.split(",")
        fields.forEach((field) => {
          _validationErrors.push({
            type: field as TPendingPaymentKeys,
            message: error.message,
          })
        })
      } else {
        _validationErrors.push({
          type: error.type as TPendingPaymentKeys,
          message: error.message,
        })
      }
    })

    setValidationErrors(_validationErrors)
  }, [data])

  const [openSections, setOpenSections] = useState({
    currencyAccount: expandAll,
    payeeDetails: expandAll,
    payeeAddress: expandAll,
    payeeBank: expandAll,
    payment: expandAll,
  })

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const hasSectionError = (section: keyof typeof sectionFieldMap) => {
    return sectionFieldMap[section].some((field) =>
      validationErrors.some(
        (error) => error.type.toLowerCase() === field.toLowerCase(),
      ),
    )
  }

  // Auto-open sections with errors
  useEffect(() => {
    Object.keys(sectionFieldMap).forEach((section) => {
      if (hasSectionError(section as keyof typeof sectionFieldMap)) {
        setOpenSections((prev) => ({
          ...prev,
          [section]: true,
        }))
      }
    })

    validationErrors.forEach((error) => {
      const fieldKey = Object.keys(data ?? {}).find(
        (key) => key.toLowerCase() === error.type.toLowerCase(),
      ) as TPendingPaymentKeys
      if (fieldKey) {
        form.setFieldMeta(fieldKey, (meta) => ({
          ...meta,
          errors: [error.message],
          errorMap: {
            onChange: error.message,
            //onMount: error.message,
          },
        }))
      }
    })
  }, [validationErrors])

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="max-w-[600px] p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-xl">Row {data?.rowNumber}</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Required fields are marked with an asterisk *
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[calc(80vh)]">
          {data && (
            <div className="space-y-2 px-3 pt-0">
              {/* Payee Details Section */}
              <CollapsibleSection
                hasError={hasSectionError("payeeDetails")}
                onOpenChange={() => toggleSection("payeeDetails")}
                open={openSections.payeeDetails}
                title="Payee details"
              >
                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Type" required>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Individual">Individual</SelectItem>
                          <SelectItem value="Business">Business</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormField>
                  )}
                  name="payeeType"
                  validators={{
                    onChange: ({ value }) =>
                      required(value, "Type is required"),
                  }}
                />
                <form.Subscribe selector={(state) => state.values.payeeType}>
                  {(payeeType) => {
                    if (payeeType === "Business") {
                      return null
                    }
                    return (
                      <form.Field
                        children={(field) => (
                          <FormField
                            field={field}
                            label="Country of nationality"
                            required
                          >
                            <CountryCombobox
                              hasErrors={field.state.meta.errors?.length > 0}
                              value={field.state.value}
                              matchValue="codeIso2"
                              onChange={(value) => {
                                field.handleChange(value)
                              }}
                            />
                          </FormField>
                        )}
                        name="payeeNationality"
                        validators={{
                          onChange: ({ value }) =>
                            required(
                              value,
                              "Country of nationality is required",
                            ),
                        }}
                      />
                    )
                  }}
                </form.Subscribe>
              </CollapsibleSection>

              {/* Payee Address Section */}
              <CollapsibleSection
                hasError={hasSectionError("payeeAddress")}
                onOpenChange={() => toggleSection("payeeAddress")}
                open={openSections.payeeAddress}
                title="Payee address"
              >
                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Country" required>
                      <CountryCombobox
                        hasErrors={field.state.meta.errors?.length > 0}
                        value={field.state.value}
                        matchValue="codeIso2"
                        onChange={(value) => {
                          field.handleChange(value)
                        }}
                      />
                    </FormField>
                  )}
                  name="payeeCountry"
                  validators={{
                    onChange: ({ value }) =>
                      required(value, "Country is required"),
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="State/region">
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter state/region"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeRegionState"
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="City" required>
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter city"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeCity"
                  validators={{
                    onChange: ({ value }) =>
                      required(value, "City is required"),
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Street name">
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter street name"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeStreetName"
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Building number">
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter building number"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeBuildingNumber"
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Postcode">
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter postcode"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeePostalCode"
                />
              </CollapsibleSection>

              {/* Payee Bank Section */}
              <CollapsibleSection
                hasError={hasSectionError("payeeBank")}
                onOpenChange={() => toggleSection("payeeBank")}
                open={openSections.payeeBank}
                title="Payee bank"
              >
                <form.Field
                  children={(field) => (
                    <>
                      {field.state.meta.errors && (
                        <p className="text-sm text-destructive">
                          {field.state.meta.errors.join(", ")}
                        </p>
                      )}

                      <FormField field={field} label="Account name" required>
                        <Input
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Enter account name"
                          value={field.state.value}
                        />
                      </FormField>
                    </>
                  )}
                  name="payeeAccountName"
                  validators={{
                    onChange: ({ value }) => {
                      if (value?.length === 0) {
                        return "Account name is required"
                      }
                      if (value && value.length < 5) {
                        return "Account name must be at least 5 characters"
                      }
                      return undefined
                    },
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Account number" required>
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter account number"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeAccountNumber"
                  validators={{
                    onChangeListenTo: ["payeeIBAN"],
                    onChange: ({ value }) => {
                      const ibanValue = form.getFieldValue("payeeIBAN")
                      if (!value && !ibanValue) {
                        return "Either account number or IBAN is required"
                      }
                      return undefined
                    },
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="IBAN" required>
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter IBAN"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeIBAN"
                  validators={{
                    onChangeListenTo: ["payeeAccountNumber"],
                    onChange: ({ value }) => {
                      const accountNumberValue =
                        form.getFieldValue("payeeAccountNumber")
                      if (!value && !accountNumberValue) {
                        return "Either account number or IBAN is required"
                      }
                      return undefined
                    },
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="SWIFT BIC" required>
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter SWIFT BIC"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeSWIFTBIC"
                  validators={{
                    onChangeListenTo: ["payeeLocalBankCode"],
                    onChange: ({ value }) => {
                      const payeeLocalBankCode =
                        form.getFieldValue("payeeLocalBankCode")
                      if (!payeeLocalBankCode && !value) {
                        return "SWIFT BIC is required"
                      }
                      return undefined
                    },
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Local bank code" required>
                      <Input
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter local bank code"
                        value={field.state.value}
                      />
                    </FormField>
                  )}
                  name="payeeLocalBankCode"
                  validators={{
                    onChangeListenTo: ["payeeSWIFTBIC"],
                    onChange: ({ value }) => {
                      const payeeSWIFTBIC = form.getFieldValue("payeeSWIFTBIC")
                      if (!value && !payeeSWIFTBIC) {
                        return "Local bank code is required"
                      }
                      return undefined
                    },
                  }}
                />
              </CollapsibleSection>

              {/* Payment Section */}
              <CollapsibleSection
                hasError={hasSectionError("payment")}
                onOpenChange={() => toggleSection("payment")}
                open={openSections.payment}
                title="Payment"
              >
                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Currency" required>
                      <CurrencySelector
                        buttonClassName="border rounded-xl px-3 py-1 h-full bg-background shadow-sm"
                        currency={field.state.value as Currency}
                        onChange={(value) => field.handleChange(value ?? "")}
                      />
                    </FormField>
                  )}
                  name="paymentCurrency"
                  validators={{
                    onChange: ({ value }) =>
                      required(value, "Currency is required"),
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Amount" required>
                      <Input
                        onChange={(e) =>
                          field.handleChange(e.target.value || "")
                        }
                        placeholder="Enter amount"
                        value={field.state.value || ""}
                      />
                    </FormField>
                  )}
                  name="paymentAmount"
                  validators={{
                    onChange: ({ value }) =>
                      required(value, "Amount is required"),
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Payment date" required>
                      <DatePicker
                        calendarProps={{
                          disabled: (date) =>
                            date.getTime() < new Date().setHours(0, 0, 0, 0),
                        }}
                        onChange={(value) =>
                          field.handleChange(value ? format2Date(value) : "")
                        }
                        value={
                          field.state.value
                            ? parseDate(field.state.value)
                            : undefined
                        }
                      />
                    </FormField>
                  )}
                  name="paymentDate"
                  validators={{
                    onChange: ({ value }) =>
                      required(value, "Payment date is required"),
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Payment type" required>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value?.toUpperCase()}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="FPS">FPS</SelectItem>
                          <SelectItem value="CHAPS">CHAPS</SelectItem>
                          <SelectItem value="SEPA">SEPA</SelectItem>
                          <SelectItem value="SEPA INSTANT">
                            SEPA Instant
                          </SelectItem>
                          <SelectItem value="SEPA URGENT">
                            SEPA Urgent
                          </SelectItem>
                          <SelectItem value="SWIFT">SWIFT</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormField>
                  )}
                  name="paymentType"
                  validators={{
                    onChange: ({ value }) =>
                      required(value, "Payment type is required"),
                  }}
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Purpose">
                      <Input
                        onChange={(e) =>
                          field.handleChange(e.target.value || "")
                        }
                        placeholder="Enter purpose"
                        value={field.state.value || ""}
                      />
                    </FormField>
                  )}
                  name="paymentPurpose"
                />

                <form.Field
                  children={(field) => (
                    <FormField field={field} label="Reference">
                      <Input
                        onChange={(e) =>
                          field.handleChange(e.target.value || "")
                        }
                        placeholder="Enter reference"
                        value={field.state.value || ""}
                      />
                    </FormField>
                  )}
                  name="paymentReference"
                />
              </CollapsibleSection>
              {!isAccountsLoading && (
                <CollapsibleSection
                  /* hasError={accountMismatch} */
                  onOpenChange={() => toggleSection("currencyAccount")}
                  open={openSections.currencyAccount}
                  title="Account"
                >
                  <form.Field
                    children={(field) => (
                      <FormField field={field} label="From account" required>
                        <AccountSelector
                          accounts={accounts || []}
                          className="w-full"
                          onChange={(value) => {
                            field.handleChange(value)
                          }}
                          value={field.state.value}
                        />
                      </FormField>
                    )}
                    name="originatingAccountIban"
                    validators={{
                      onChangeListenTo: ["paymentCurrency"],

                      onChange: ({ value }) => {
                        const account = accounts?.find(
                          (account) => account.virtualIban === value,
                        )
                        if (!account) {
                          return "Account not found"
                        }
                        return undefined
                      },
                    }}
                  />
                </CollapsibleSection>
              )}
            </div>
          )}

          <DialogFooter className="flex justify-end gap-2 border-t p-4">
            <Button onClick={() => onOpenChange?.(false)} variant="outline">
              Cancel
            </Button>
            <TrailingSpinner isLoading={isPatching}>
              <Button
                disabled={isPatching}
                onClick={() => data && onDelete?.(data)}
                variant="destructive-outline"
              >
                Delete Row
              </Button>
            </TrailingSpinner>
            <TrailingSpinner isLoading={isPatching}>
              <Button disabled={isPatching} onClick={() => form.handleSubmit()}>
                Save
              </Button>
            </TrailingSpinner>
          </DialogFooter>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
