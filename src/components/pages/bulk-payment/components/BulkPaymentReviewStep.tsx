import { useMemo, useState } from "react"
import { ChevronDown, PencilIcon, Search } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { cn } from "@/lib/utils"
import { formatDate } from "@/lib/date.utils"
import { bulkPaymentProcessMutation } from "@/data/payments/payments.mutation"
import {
  IBulkPaymentPendingPayments,
  IPendingPayment,
} from "@/data/payments/payments.interface"
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTitle,
  SheetHeader,
  SheetDescription,
} from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"

import { ReviewTable, PaymentRow } from "./ReviewTable"

export function BulkPaymentReviewStep({
  pendingPayments,
  bulkUploadId,
  onNext,
  onCancel,
  onDeleteRows,
  onEditRow,
}: {
  pendingPayments: IPendingPayment[]
  bulkUploadId: string
  onNext?: (success: boolean) => void
  onCancel?: () => void
  onDeleteRows?: (rowNumbers: string[]) => void
  onEditRow?: (payment: IPendingPayment) => void
}) {
  const handleRowClick = (row: IPendingPayment) => {
    setSelectedRowId(row.id)
  }
  const handleEditRow = (row: IPendingPayment) => {
    onEditRow?.(row)
  }

  const [searchTerm, setSearchTerm] = useState("")

  const filteredData = useMemo(() => {
    return pendingPayments
      .filter((row) =>
        row.payeeAccountName.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      .sort((a, b) => a.rowNumber - b.rowNumber)
  }, [pendingPayments, searchTerm])

  const [selectedRowId, setSelectedRowId] = useState<string | undefined>(
    undefined,
  )

  const selectedRow = useMemo(() => {
    return pendingPayments.find((row) => row.id === selectedRowId)
  }, [pendingPayments, selectedRowId])

  const { mutate: processBulkPayment, isPending } = bulkPaymentProcessMutation()

  const handleProcessBulkPayment = () => {
    processBulkPayment(bulkUploadId, {
      onSuccess: () => {
        onNext?.(true)
      },
      onError: () => {
        onNext?.(false)
      },
    })
  }

  return (
    <div className="mx-auto flex w-full max-w-4xl flex-col items-center justify-center gap-4">
      <div className="w-full py-4">
        <div className="flex flex-col items-start">
          <h1 className="text-2xl font-semibold">
            Review all data ({filteredData.length} rows)
          </h1>
          <p className="mt-2 text-sm text-muted-foreground">
            You can perform a final review and edit of each row below before
            submission of the file. Click on each row to show full data.
          </p>
        </div>

        <div className="mt-4 w-full rounded-lg bg-background shadow-md">
          <div className="p-4">
            <div className="relative mb-6 w-1/3">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                className="pl-9"
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search"
                value={searchTerm}
              />
            </div>

            <ReviewTable data={filteredData} onRowClick={handleRowClick} />
          </div>
        </div>
      </div>

      <div className="flex w-full justify-start gap-2">
        <Button disabled={isPending} onClick={() => handleProcessBulkPayment()}>
          Confirm all payments
        </Button>
        <Button onClick={onCancel} variant="destructive-outline">
          Cancel payment file
        </Button>
      </div>
      {selectedRow && (
        <Sheet
          onOpenChange={() => setSelectedRowId(undefined)}
          open={!!selectedRow}
        >
          <SheetContent>
            <VisuallyHidden>
              <SheetDescription>
                {selectedRow?.payeeAccountName} -{" "}
                {selectedRow?.payeeAccountNumber}
              </SheetDescription>
            </VisuallyHidden>
            <SheetHeader>
              <SheetTitle className="text-2xl font-semibold">
                {`Row ${selectedRow?.rowNumber}`}
              </SheetTitle>
            </SheetHeader>
            <ScrollArea className="-mx-6 h-[calc(100dvh-80px)] px-6">
              <div className="space-y-4 pt-4">
                <CollapsibleSection
                  title="Payee"
                  onEdit={() => handleEditRow(selectedRow)}
                >
                  <CollapsibleSectionRow
                    label="Type"
                    value={selectedRow?.payeeType}
                  />
                  <CollapsibleSectionRow
                    label="Nationality"
                    value={selectedRow?.payeeNationality}
                  />
                </CollapsibleSection>

                <CollapsibleSection
                  title="Payee address"
                  onEdit={() => handleEditRow(selectedRow)}
                >
                  <CollapsibleSectionRow
                    label="Building number"
                    value={selectedRow?.payeeBuildingNumber}
                  />
                  <CollapsibleSectionRow
                    label="Street name"
                    value={selectedRow?.payeeStreetName}
                  />
                  <CollapsibleSectionRow
                    label="City"
                    value={selectedRow?.payeeCity}
                  />
                  <CollapsibleSectionRow
                    label="State / region"
                    value={selectedRow?.payeeRegionState}
                  />
                  <CollapsibleSectionRow
                    label="Postcode"
                    value={selectedRow?.payeePostalCode}
                  />
                  <CollapsibleSectionRow
                    label="Country"
                    value={selectedRow?.payeeCountry}
                  />
                </CollapsibleSection>

                <CollapsibleSection
                  title="Payee bank"
                  onEdit={() => handleEditRow(selectedRow)}
                >
                  <CollapsibleSectionRow
                    label="Account name"
                    value={selectedRow?.payeeAccountName}
                  />
                  <CollapsibleSectionRow
                    label="Account number"
                    value={selectedRow?.payeeAccountNumber}
                  />
                  <CollapsibleSectionRow
                    label="Sort code"
                    value={selectedRow?.payeeSWIFTBIC}
                  />
                </CollapsibleSection>

                <CollapsibleSection
                  title="Payment details"
                  onEdit={() => handleEditRow(selectedRow)}
                >
                  <CollapsibleSectionRow
                    label="From account"
                    value={selectedRow?.originatingAccountIban}
                  />
                  <CollapsibleSectionRow
                    label="Currency"
                    value={selectedRow?.paymentCurrency}
                  />
                  <CollapsibleSectionRow
                    label="Amount"
                    value={selectedRow?.paymentAmount}
                  />
                  <CollapsibleSectionRow
                    label="Payment date"
                    value={formatDate(selectedRow?.paymentDate)}
                  />
                  <CollapsibleSectionRow
                    label="Payment type"
                    value={selectedRow?.paymentType}
                  />
                  <CollapsibleSectionRow
                    label="Purpose of payment"
                    value={selectedRow?.paymentPurpose}
                  />
                  <CollapsibleSectionRow
                    label="Payment reference"
                    value={selectedRow?.paymentReference}
                  />
                </CollapsibleSection>
                <div className="flex justify-end gap-2">
                  {selectedRow?.id && (
                    <Button
                      onClick={() => onDeleteRows?.([selectedRow.id])}
                      variant="link-muted"
                    >
                      Delete row
                    </Button>
                  )}
                </div>
              </div>
            </ScrollArea>
          </SheetContent>
        </Sheet>
      )}
    </div>
  )
}

export function CollapsibleSection({
  title,
  children,
  onEdit,
}: {
  title: string
  children: React.ReactNode
  onEdit?: () => void
}) {
  const [open, setOpen] = useState(true)
  return (
    <Collapsible
      onOpenChange={setOpen}
      open={open}
      className="w-full rounded-xl bg-muted/50 p-4"
    >
      <CollapsibleTrigger className="flex w-full items-center justify-between py-2">
        <h2 className="text-base font-semibold">{title}</h2>
        <Button
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onEdit?.()
          }}
          variant="link"
          size="sm"
          className="px-0"
        >
          <PencilIcon className="h-4 w-4" />
          <span>Edit</span>
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-2 text-sm">
        {children}
      </CollapsibleContent>
    </Collapsible>
  )
}

export function CollapsibleSectionRow({
  label,
  value,
}: {
  label: string
  value: string | number | undefined
}) {
  return (
    <div className="flex justify-between border-b border-dotted pt-2 text-sm">
      <span className="text-muted-foreground">{label}</span>
      <span>{value}</span>
    </div>
  )
}
