import { useEffect, useMemo, useRef } from "react"
import { <PERSON>Cir<PERSON>, Trash2 } from "lucide-react"

import { cn } from "@/lib/utils"
import {
  IBulkPaymentPendingPayments,
  IPendingPayment,
} from "@/data/payments/payments.interface"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

import { ResolveErrorsTable } from "./ResolveErrorsTable"

interface BulkPaymentResolveErrorsStepProps {
  pendingPayments: IPendingPayment[]
  onNext?: () => void
  onCancel?: () => void
  onDeleteRows?: (rowNumbers: string[]) => void
  onEditRow?: (payment: IPendingPayment) => void
}

export function BulkPaymentResolveErrorsStep({
  pendingPayments,
  onNext,
  onCancel,
  onDeleteRows,
  onEditRow,
}: BulkPaymentResolveErrorsStepProps) {
  const previousErrorCount = useRef<number>(0)
  const errorData = useMemo(() => {
    return pendingPayments
      .filter((payment) => payment.validationErrors.length > 0)
      .sort((a, b) => a.rowNumber - b.rowNumber)
  }, [pendingPayments])

  const handleResolve = (rowNumber: number) => {
    const payment = pendingPayments.find(
      (payment) => payment.rowNumber === rowNumber,
    )
    if (payment) {
      onEditRow?.(payment)
    }
  }

  useEffect(() => {
    if (previousErrorCount.current && errorData.length === 0) {
      onNext?.()
    }
    previousErrorCount.current = errorData.length
  }, [errorData])

  return (
    <div className="mx-auto flex w-full max-w-4xl flex-col items-center justify-center gap-4">
      {errorData.length > 0 && (
        <div className="w-full py-4">
          <div className="flex flex-col items-start">
            <h3 className="text-left text-2xl font-semibold">Resolve errors</h3>
            <p className="pb-4 pt-2 text-sm font-light text-muted-foreground">
              Before this payment file can be accepted, please review and
              resolve the errors listed below.
            </p>
          </div>
          <div
            className={cn(
              "w-full rounded-lg bg-background shadow-md",
              errorData.length === 0 && "hidden",
            )}
          >
            <ResolveErrorsTable
              errors={errorData}
              onResolve={handleResolve}
              payments={pendingPayments}
            />
          </div>
        </div>
      )}
      {errorData.length === 0 && (
        <Card className="w-full p-4">
          <div className="flex h-80 max-w-md items-center justify-center gap-8">
            <CheckCircle className="h-20 w-20 text-primary" />
            <div className="flex flex-col gap-2">
              <h2 className="text-2xl font-semibold">No errors</h2>
              <p>
                This payment file has no errors. You can continue to the next
                step.
              </p>
            </div>
          </div>
        </Card>
      )}

      <div className="mt-4 flex w-full gap-2">
        {errorData.length > 0 && (
          <>
            <Button onClick={() => handleResolve(errorData[0].rowNumber)}>
              Begin resolving errors
            </Button>
            <Button
              onClick={() => onDeleteRows?.(errorData.map((error) => error.id))}
              variant="destructive-outline"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete rows with errors
            </Button>
          </>
        )}
        {errorData.length === 0 && (
          <Button onClick={onNext} variant="default">
            Continue
          </Button>
        )}

        <Button onClick={onCancel} variant="destructive-outline">
          Cancel payment file
        </Button>
      </div>
    </div>
  )
}
