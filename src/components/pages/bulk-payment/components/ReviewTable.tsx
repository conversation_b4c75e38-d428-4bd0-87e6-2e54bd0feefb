import { IPendingPayment } from "@/data/payments/payments.interface"
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { ReviewRow } from "./ReviewRow"

export interface PaymentRow {
  row: number
  date: string
  fromAccount: string
  payeeAccountName: string
  amount: number
  currency: string
}

interface ReviewTableProps {
  data: IPendingPayment[]
  onRowClick?: (row: IPendingPayment) => void
}

export function ReviewTable({ data, onRowClick }: ReviewTableProps) {
  return (
    <div className="container mx-auto rounded-lg">
      <div className="overflow-auto">
        <Table>
          <TableHeader className="sticky top-0">
            <TableRow className="border-b text-sm text-muted-foreground">
              <TableHead className="w-24 text-left font-medium">Row</TableHead>
              <TableHead className="w-32 text-left font-medium">Date</TableHead>
              <TableHead className="w-48 text-left font-medium">From</TableHead>
              <TableHead className="w-48 text-left font-medium">
                Payee Type
              </TableHead>
              <TableHead className="w-48 text-left font-medium">
                Payee
              </TableHead>
              <TableHead className="w-32 text-right font-medium">
                Amount
              </TableHead>
              <TableHead className="w-24 text-left font-medium">
                Currency
              </TableHead>
              <TableHead className="w-24 text-right font-medium"> </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row) => (
              <ReviewRow
                data={row}
                key={row.id}
                onClick={() => onRowClick?.(row)}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
