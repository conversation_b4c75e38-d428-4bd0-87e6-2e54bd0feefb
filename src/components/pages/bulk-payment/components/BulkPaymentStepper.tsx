import { useCallback, useEffect, useMemo, useState } from "react"
import { useNavigate, useSearch } from "@tanstack/react-router"

import { bulkPaymentDeletePaymentsMutation } from "@/data/payments/payments.mutation"
import { bulkPaymentCancelMutation } from "@/data/payments/payments.mutation"
import { IPendingPayment } from "@/data/payments/payments.interface"
import { Step, Stepper } from "@/components/base/triggers/Stepper"

import { DeleteErrorRowsDialog } from "./DeleteErrorRowsDialog"
import { CancelPaymentFileDialog } from "./CancelPaymentFileDialog"
import { BulkPaymentReviewStep } from "./BulkPaymentReviewStep"
import { BulkPaymentResolveErrorsStep } from "./BulkPaymentResolveErrorsStep"
import { BulkPaymentEditDialog } from "./BulkPaymentEditDialog"
import { toast } from "sonner"

interface BulkPaymentStepperProps {
  pendingPayments: IPendingPayment[]
  id: string
  entityId: string
}

export function BulkPaymentStepper({
  pendingPayments,
  id,
  entityId,
}: BulkPaymentStepperProps) {
  const navigate = useNavigate()
  const search: any = useSearch({
    strict: false,
  })

  const [editRow, setEditRow] = useState<{
    payment: IPendingPayment
    isEdit: boolean
  } | null>(null)

  const tabs = ["errors", "review", "summary"]

  const [showCancelDialog, setShowCancelDialog] = useState(false)
  const [deletePaymentRowNumbers, setDeletePaymentRowNumbers] = useState<
    string[]
  >([])

  useEffect(() => {
    if (
      !search.tab &&
      !pendingPayments.some((payment) => payment.validationErrors.length > 0)
    ) {
      setActiveStep(1)
    }
  }, [])

  const hasErrors = useMemo(() => {
    return pendingPayments.some(
      (payment) => payment.validationErrors.length > 0,
    )
  }, [pendingPayments])

  const activeStep = useMemo(() => {
    let index = 0
    if (!search.tab) return index
    if (tabs.includes(search.tab)) index = tabs.indexOf(search.tab)
    if (hasErrors) return 0
    return index
  }, [search.tab, hasErrors])

  const setActiveStep = useCallback(
    (step: number) => {
      navigate({
        search: { ...search, tab: tabs[step] },
        replace: true,
      })
    },
    [navigate, search, tabs],
  )

  function handleProcessBulkPayment(success: boolean) {
    if (success) {
      toast.success("Bulk payment processed successfully")
      navigate({
        to: "/$entityId/payments",
        params: {
          entityId: entityId,
        },
        search: {
          tab: "bulk",
        },
        replace: true,
      })
    }
  }
  const { mutate: cancelBulkPayment, isPending } = bulkPaymentCancelMutation()

  function handleDeleteRows(rowNumbers: string[]) {
    setDeletePaymentRowNumbers(rowNumbers)
  }

  const { mutate: deletePayments, isPending: isDeletingPayments } =
    bulkPaymentDeletePaymentsMutation()

  function handleCancelBulkPayment() {
    //TODO: cancel mutation
    setShowCancelDialog(true)
  }

  function handleCancelFile() {
    cancelBulkPayment(id, {
      onSuccess: () => {
        setShowCancelDialog(false)
        navigate({
          to: "/$entityId/payments",
          params: {
            entityId: entityId,
          },
          replace: true,
        })
      },
    })
  }

  function handleDeleteConfirm() {
    deletePayments(
      {
        bulkUploadId: id,
        rowNumbers: deletePaymentRowNumbers,
      },
      {
        onSuccess: () => {
          setDeletePaymentRowNumbers([])
          if (deletePaymentRowNumbers.some((v) => editRow?.payment.id === v)) {
            setEditRow(null)
          }
          const remainingPayments = pendingPayments.filter(
            (payment) => !deletePaymentRowNumbers.includes(payment.id),
          )
          // if there are no payments left, navigate to the summary step
          if (remainingPayments?.length === 0) {
            navigate({
              to: "/$entityId/payments",
              params: {
                entityId: entityId,
              },
              replace: true,
            })
          }
        },
      },
    )
  }
  return (
    <>
      <Stepper onStepChange={setActiveStep} value={activeStep}>
        <Step title="Resolve errors">
          <BulkPaymentResolveErrorsStep
            onCancel={handleCancelBulkPayment}
            onDeleteRows={handleDeleteRows}
            onEditRow={(payment) => setEditRow({ payment, isEdit: false })}
            onNext={() => setActiveStep(1)}
            pendingPayments={pendingPayments}
          />
        </Step>
        <Step disabled={hasErrors} title="Review">
          <BulkPaymentReviewStep
            bulkUploadId={id}
            onCancel={handleCancelBulkPayment}
            onDeleteRows={handleDeleteRows}
            onEditRow={(payment) => setEditRow({ payment, isEdit: true })}
            onNext={handleProcessBulkPayment}
            pendingPayments={pendingPayments}
          />
        </Step>
        {/*   <Step disabled={!hasProcessed || hasErrors} title="Summary">
          <BulkPaymentSummaryStep
            bulkUploadId={id}
            onCancel={handleCancelBulkPayment}
          />
        </Step> */}
      </Stepper>
      <CancelPaymentFileDialog
        onConfirm={handleCancelFile}
        onOpenChange={setShowCancelDialog}
        open={showCancelDialog}
      />
      <DeleteErrorRowsDialog
        isDisabled={isDeletingPayments}
        onConfirm={handleDeleteConfirm}
        onOpenChange={(v) => !v && handleDeleteRows([])}
        rowNumbers={deletePaymentRowNumbers}
      />
      {editRow && (
        <BulkPaymentEditDialog
          bulkUploadId={id}
          data={editRow?.payment}
          expandAll={editRow?.isEdit}
          onDelete={(p) => handleDeleteRows([p.id])}
          onOpenChange={(v) => !v && setEditRow(null)}
          open={!!editRow}
        />
      )}
    </>
  )
}
