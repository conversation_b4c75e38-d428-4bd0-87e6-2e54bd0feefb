import { AlertTriangle } from "lucide-react"

import { IAccountSummary } from "@/data/payments/payments.interface"
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableCell,
  TableBody,
} from "@/components/ui/table"
import { Card } from "@/components/ui/card"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyBadge } from "@/components/base/currency/CurrencyBadge"

interface BulkAccountSummaryTableProps {
  accountSummaries: IAccountSummary[]
}

export function BulkAccountSummaryTable({
  accountSummaries,
}: BulkAccountSummaryTableProps) {
  if (accountSummaries.length === 0) {
    return null
  }

  return (
    <>
      {accountSummaries.map((account) => (
        <Card className="p-4" key={account.virtualIban}>
          <div className="mb-2 flex items-center gap-2">
            <h2 className="text-lg font-semibold">{account.displayName}</h2>
            <span className="text-sm text-muted-foreground">
              {account.virtualIban}
            </span>
          </div>

          <Table>
            <TableHeader>
              <TableRow className="border-b text-sm text-muted-foreground">
                <TableHead>Currency</TableHead>
                <TableHead>Payments</TableHead>
                <TableHead>Payment amount</TableHead>
                <TableHead>Fees</TableHead>
                <TableHead>Available balance</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {account.currencyAccounts.map((_account) => (
                <TableRow className="text-sm" key={_account.currency}>
                  <TableCell className="py-2">
                    <div className="flex items-center gap-2">
                      <CurrencyBadge currency={_account.currency} />
                    </div>
                  </TableCell>
                  <TableCell>
                    {_account.paymentsCount.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <CurrencyText
                      amount={_account.paymentsAmount}
                      currency={_account.currency}
                    />
                  </TableCell>
                  <TableCell>
                    <CurrencyText
                      amount={_account.fees}
                      currency={_account.feesCurrency}
                    />
                  </TableCell>
                  <TableCell>
                    <CurrencyText
                      amount={_account.availableBalance}
                      currency={_account.currency}
                    />
                  </TableCell>
                  <TableCell>
                    {_account.availableBalance < _account.paymentsAmount && (
                      <div className="flex items-center gap-1 rounded bg-orange-100 px-2 py-1 text-xs text-orange-700">
                        <AlertTriangle className="h-3 w-3" />
                        Balance alert
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      ))}
    </>
  )
}
