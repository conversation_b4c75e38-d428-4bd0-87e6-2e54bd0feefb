import { useState } from "react"
import { useRouter } from "@tanstack/react-router"

import { AddPayeeStepper } from "./components/AddPayeeStepper"

interface AddPayeePageProps {
  entityId: string
  modal?: boolean
  onPayeeAdded?: (val: string) => void
  onClose?: () => void
}

export function AddPayeePage({
  entityId,
  modal = false,
  onPayeeAdded,
  onClose,
}: AddPayeePageProps) {
  const [payeeId, setPayeeId] = useState("")
  const router = useRouter()

  function onComplete(v?: { id: string }) {
    if (v) {
      setPayeeId(v?.id ?? "2")
      if (v?.id && onPayeeAdded) {
        onPayeeAdded?.(v.id)
      }

      if (modal) {
        onClose?.()
      } else if (router) {
        router.navigate({
          to: "/$entityId/payees",
          params: {
            entityId,
          },
        })
      }
    }
  }

  return (
    <div data-testid="add-payee-page">
      <AddPayeeStepper
        data-testid="stepper"
        entityId={entityId}
        modal={modal}
        onClose={onClose}
        onComplete={onComplete}
      />
    </div>
  )
}
