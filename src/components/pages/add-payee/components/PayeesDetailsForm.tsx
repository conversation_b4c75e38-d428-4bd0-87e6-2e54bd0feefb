import { useState } from "react"
import { useForm } from "@tanstack/react-form"

import { cn } from "@/lib/utils"
import { required, validateEmail } from "@/lib/form.utils"
import { useFormInvalid } from "@/hooks/use-form-invalid"
import { IPayeeDetails } from "@/data/payees/payees.interface"
import { useCountriesQuery } from "@/data/global/global.query"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  FormButtons,
  FormField,
  FormLabel,
  FormLayout,
} from "@/components/base/form/form"

import { CountryCombobox } from "@/components/base/country/CountryCombobox"

interface PayeeDetailsFormProps {
  details?: IPayeeDetails
  onNext?: (payload: IPayeeDetails) => void
  onInvalid?: () => void
  modalClose?: () => void
}

export function PayeeDetailsForm({
  details,
  onNext,
  onInvalid,
  modalClose,
}: PayeeDetailsFormProps) {
  const [isManualAddress, setIsManualAddress] = useState(true)

  const form = useForm<IPayeeDetails>({
    defaultValues: {
      displayName: details?.displayName || "",
      nationality: details?.nationality || "",
      email: details?.email || "",
      searchTextAddress: "",
      selectedAddress: details?.selectedAddress || "",
      address: details?.address || {
        buildingNumber: "",
        street: "",
        state: "",
        city: "",
        postalCode: "",
        country: undefined,
      },
      countryDetails: details?.countryDetails,
      nationalityDetails: details?.nationalityDetails,
    },
    asyncAlways: true,
    onSubmit: ({ value }) => {
      onNext?.(value)
    },
  })

  const { data: countries, isLoading: isCountriesLoading } = useCountriesQuery()

  useFormInvalid(form, onInvalid)

  return (
    <div className="mx-auto" style={{ maxWidth: "478px" }}>
      <FormLayout>
        <form.Field
          children={(field) => (
            <FormField field={field} label="Display name" required>
              <Input
                className={cn(
                  "bg-background",
                  field.state.meta.errors?.length > 0 &&
                    "border-destructive ring-destructive",
                )}
                data-testid="display-name-input"
                onChange={(e) => field.handleChange(e.target.value)}
                type="text"
                value={field.state.value}
              />
            </FormField>
          )}
          name="displayName"
          validators={{
            onChange: ({ value }) => {
              return value.trim() ? undefined : "Display name is required"
            },
          }}
        />
        <form.Field
          children={(field) => (
            <FormField field={field} label="Country of nationality" required>
              <CountryCombobox
                hasErrors={field.state.meta.errors?.length > 0}
                value={field.state.value}
                onChange={(value) => {
                  field.handleChange(value)
                }}
              />
            </FormField>
          )}
          listeners={{
            onChange: ({ value }) => {
              form.setFieldValue(
                "countryDetails",
                countries?.find((country) => country.name === value),
              )
            },
          }}
          name="nationality"
          validators={{
            onChange: ({ value }) => {
              return required(value, "Country of nationality is required")
            },
          }}
        />
        <form.Field
          children={(field) => (
            <FormField field={field} label="Email">
              <Input
                className={cn(
                  "bg-background",
                  field.state.meta.errors?.length > 0 &&
                    "border-destructive ring-destructive",
                )}
                data-testid="email-input"
                onChange={(e) => field.handleChange(e.target.value)}
                type="email"
                value={field.state.value}
              />
            </FormField>
          )}
          name="email"
          validators={{
            onChange: ({ value }) => validateEmail(value),
          }}
        />
        <h3 className="mt-4 text-lg font-semibold">Address details</h3>

        {!isManualAddress && (
          <>
            <FormLabel>Find address</FormLabel>
            <Input
              className="mb-2"
              disabled
              placeholder="Search for an address"
            />
            <div className="mb-2 text-xs text-muted-foreground">
              Address search functionality is currently unavailable
            </div>
            <div className="flex justify-end">
              <Button onClick={() => setIsManualAddress(true)} variant="link">
                Manually enter address
              </Button>
            </div>
          </>
        )}
        {isManualAddress && (
          <>
            <form.Field
              children={(field) => (
                <FormField field={field} label="Country" required>
                  <CountryCombobox
                    hasErrors={field.state.meta.errors?.length > 0}
                    value={field.state.value}
                    onChange={(value) => {
                      field.handleChange(value)
                    }}
                  />
                </FormField>
              )}
              listeners={{
                onChange: ({ value }) => {
                  form.setFieldValue(
                    "countryDetails",
                    countries?.find((country) => country.name === value),
                  )
                },
              }}
              name="address.country"
              validators={{
                onChange: ({ value }) => {
                  return required(value, "Country is required")
                },
              }}
            />
            <form.Field
              children={(field) => (
                <FormField
                  field={field}
                  label="House / Building number"
                  required
                >
                  <Input
                    className={cn(
                      "bg-background",
                      field.state.meta.errors?.length > 0 &&
                        "border-destructive ring-destructive",
                    )}
                    data-testid="building-number-input"
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    value={field.state.value}
                  />
                </FormField>
              )}
              name="address.buildingNumber"
              validators={{
                onChange: ({ value }) => {
                  return value.trim()
                    ? undefined
                    : "House / Building number is required"
                },
              }}
            />
            <form.Field
              children={(field) => (
                <FormField field={field} label="Street address" required>
                  <Input
                    className={cn(
                      "bg-background",
                      field.state.meta.errors?.length > 0 &&
                        "border-destructive ring-destructive",
                    )}
                    data-testid="street-input"
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    value={field.state.value}
                  />
                </FormField>
              )}
              name="address.street"
              validators={{
                onChange: ({ value }) => {
                  return value.trim() ? undefined : "Street is required"
                },
              }}
            />
            <form.Field
              children={(field) => (
                <FormField field={field} label="Town / City" required>
                  <Input
                    className={cn(
                      "bg-background",
                      field.state.meta.errors?.length > 0 &&
                        "border-destructive ring-destructive",
                    )}
                    data-testid="city-input"
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    value={field.state.value}
                  />
                </FormField>
              )}
              name="address.city"
              validators={{
                onChange: ({ value }) => {
                  return value.trim() ? undefined : "Town / City is required"
                },
              }}
            />
            <form.Field
              children={(field) => (
                <FormField field={field} label="County">
                  <Input
                    className={cn(
                      "bg-background",
                      field.state.meta.errors?.length > 0 &&
                        "border-destructive ring-destructive",
                    )}
                    data-testid="state-input"
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    value={field.state.value}
                  />
                </FormField>
              )}
              name="address.state"
            />
            <form.Field
              children={(field) => (
                <FormField field={field} label="Postcode" required>
                  <Input
                    className={cn(
                      "bg-background",
                      field.state.meta.errors?.length > 0 &&
                        "border-destructive ring-destructive",
                    )}
                    data-testid="postcode-input"
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    value={field.state.value}
                  />
                </FormField>
              )}
              name="address.postalCode"
              validators={{
                onChange: ({ value }) => {
                  return value.trim() ? undefined : "Postcode is required"
                },
              }}
            />
            {/*   <div className="flex justify-end">
            <Button variant="link" onClick={() => setIsManualAddress(false)}>
              Search address
            </Button>
          </div> */}
          </>
        )}

        <FormButtons
          form={form}
          onCancel={() => {
            form.reset()
            onInvalid?.()
            modalClose?.()
          }}
          modal={modalClose !== undefined}
        />
      </FormLayout>
    </div>
  )
}
