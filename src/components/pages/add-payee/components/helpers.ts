import { FieldValidators } from "@tanstack/react-form"

import { required } from "@/lib/form.utils"
import { ICountry } from "@/data/global/global.interface"

export interface IFormInputType {
  type: string
  label: string
  placeholder?: string
  valuePipe?: (value?: string) => string
  onChangePipe?: (value?: string) => string
  validators?: Pick<
    FieldValidators<any, any>,
    "onChange" | "onChangeAsync" | "onChangeAsyncDebounceMs"
  >
}

export const bankCodeTypes: IFormInputType[] = [
  {
    type: "Sort Code",
    label: "Sort Code",
    placeholder: "",
    valuePipe: (value?: string) =>
      value?.replace(/(\d{2})(?=\d)/g, "$1-") ?? "",
    onChangePipe: (value?: string) => value?.replace(/-/g, "") ?? "",
    validators: {
      onChange: ({ value }: any) => {
        return required(value, "Sort code is required")
      },
    },
  },
  {
    type: "SwiftBic",
    label: "Swift / BIC",
    validators: {
      onChange: ({ value }: any) => {
        return required(value, "Swift BIC is required")
      },
    },
  },
  {
    type: "BSB Number",
    label: "BSB Number",
    validators: {
      onChange: ({ value }: any) => {
        return required(value, "BSB Number is required")
      },
    },
  },
  {
    type: "Local Bank/Clearing Code",
    label: "Local Bank/Clearing Code",
    validators: {
      onChange: ({ value }: any) => {
        return required(value, "Local Bank/Clearing Code is required")
      },
    },
  },
  {
    type: "ABA Routing Number",
    label: "ABA Routing Number",
    placeholder: "XXXXXXXXXXXXXXXX",
    validators: {
      onChange: ({ value }: any) => {
        return required(value, "ABA Routing Number is required")
      },
    },
  },
  {
    type: "IFSC Code",
    label: "IFSC Code",
    validators: {
      onChange: ({ value }: any) => {
        return required(value, "IFSC Code is required")
      },
    },
  },
]

export const accountNumberTypes: IFormInputType[] = [
  {
    type: "Account Number",
    label: "Account Number",
    placeholder: "XXXXXXXXXXXXXXXX",
  },
  {
    type: "IBAN",
    label: "IBAN",
  },
]

export const getBankCodeTypeLabel = (country: ICountry) => {
  return bankCodeTypes.find((v) => v.type === country.nationalIdType)?.label
}
