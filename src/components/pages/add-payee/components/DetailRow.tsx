import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"

interface DetailRowProps {
  label: string
  value?: string
  className?: string
}

export function DetailRow({ label, value, className }: DetailRowProps) {
  return (
    <div className={cn("flex justify-between border-b", className)}>
      <Label className="flex-none text-sm text-foreground/90">{label}</Label>
      <p className="text-right text-sm text-muted-foreground" role="paragraph">
        {value}
      </p>
    </div>
  )
}
