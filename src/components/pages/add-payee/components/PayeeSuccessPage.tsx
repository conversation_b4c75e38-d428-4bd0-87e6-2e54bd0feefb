import { CheckCircle } from "lucide-react"
import { Link, useLocation } from "@tanstack/react-router"

import { Button } from "@/components/ui/button"

interface PayeeSuccessPageProps {
  entityId: string
  payeeId?: string
  modal?: boolean
  onClose?: () => void
}

export function PayeeSuccessPage({
  entityId,
  payeeId,
  modal = false,
  onClose,
}: PayeeSuccessPageProps) {
  const { href } = useLocation()
  return (
    <div className="mx-auto flex max-w-md flex-col justify-center gap-2 pb-4 pt-4">
      <div className="mb-6">
        <CheckCircle className="h-20 w-20 text-primary" />
      </div>

      <h2 className="mb-4 text-2xl font-semibold">
        New payee added successfully
      </h2>

      <p className="mb-8 text-muted-foreground">
        The payee has been added to your list of payees.
      </p>

      <div className="flex gap-4">
        {!modal ? (
          <>
            <Link
              params={{
                entityId,
              }}
              search={{
                to: payeeId,
                fromRoute: href,
              }}
              to="/$entityId/payments/send"
            >
              Send payment to payee
            </Link>

            <Link
              params={{
                entityId,
              }}
              to="/$entityId/payees"
            >
              <Button variant="primary-outline">Finish</Button>
            </Link>
          </>
        ) : (
          <Button onClick={onClose}>Continue to payment</Button>
        )}
      </div>
    </div>
  )
}
