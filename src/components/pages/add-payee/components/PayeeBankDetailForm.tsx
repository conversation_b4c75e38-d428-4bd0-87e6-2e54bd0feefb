import { useEffect, useMemo, useRef } from "react"
import { CheckIcon } from "lucide-react"
import { useStore } from "@tanstack/react-store"
import { useRouter } from "@tanstack/react-router"
import { useQueryClient } from "@tanstack/react-query"
import { useForm } from "@tanstack/react-form"
import { Icon } from "@iconify/react/dist/iconify.js"

import { cn } from "@/lib/utils"
import { required, resetFieldMeta, validateIBan } from "@/lib/form.utils"
import { formatBankDetails } from "@/lib/bank.utils"
import { useFormInvalid } from "@/hooks/use-form-invalid"
import { IPayeeBankDetails } from "@/data/payees/payees.interface"
import { fetchShortCode } from "@/data/payees/payees.api"
import { useCountriesQuery } from "@/data/global/global.query"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { FormLayout, FormButtons, FormField } from "@/components/base/form/form"
import { ComboboxDropdown } from "@/components/base/form/ComboboxDropdown"

import { accountNumberTypes, bankCodeTypes } from "./helpers"
import { CountryCombobox } from "@/components/base/country/CountryCombobox"

interface PayeeBankDetailsProps {
  bankDetails?: IPayeeBankDetails
  modal?: boolean
  onNext?: (payload: IPayeeBankDetails) => void
  onInvalid?: () => void
  modalClose?: () => void
  entityId: string
}

export function PayeeBankDetailForm({
  bankDetails,
  modal = false,
  onNext,
  onInvalid,
  modalClose,
  entityId,
}: PayeeBankDetailsProps) {
  const form = useForm<IPayeeBankDetails>({
    defaultValues: {
      accountName: bankDetails?.accountName || "",
      isBusiness: bankDetails?.isBusiness || true,
      country: bankDetails?.country,
      countryDetails: bankDetails?.countryDetails,
      bankCodeValue: bankDetails?.bankCodeValue || "",
      accountNumberValue: bankDetails?.accountNumberValue || "",
      bankCodeDetails: bankDetails?.bankCodeDetails,
    },

    onSubmit: ({ value }) => {
      onNext?.(value)
    },
  })

  const queryClient = useQueryClient()

  useFormInvalid(form, onInvalid)

  const country = useStore(form.store, (state) => state.values.country)

  const { data: countries } = useCountriesQuery()

  const selectedCountry = useMemo(() => {
    return countries?.find((c) => c.name === country)
  }, [country, countries])

  const previousCountry = useRef(selectedCountry)

  const router = useRouter()

  useEffect(() => {
    if (previousCountry.current !== selectedCountry) {
      resetFieldMeta(form, "accountNumberValue")
      resetFieldMeta(form, "bankCodeValue")

      form.setFieldValue("bankCodeValue", "")

      if (
        selectedCountry?.accountNumberType ===
        previousCountry.current?.accountNumberType
      ) {
        const accountNumberValue = form.getFieldValue("accountNumberValue")
        form.setFieldValue("accountNumberValue", accountNumberValue)
      } else {
        form.setFieldValue("accountNumberValue", "")
      }

      previousCountry.current = selectedCountry
    }
    form.setFieldValue("countryDetails", selectedCountry)
  }, [selectedCountry])

  const bankCodeType = useMemo(() => {
    return bankCodeTypes.find((v) => v.type === selectedCountry?.nationalIdType)
  }, [selectedCountry])

  const accountNumberType = useMemo(() => {
    const v = accountNumberTypes.find(
      (v) => v.type === selectedCountry?.accountNumberType,
    )
    if (!v) return undefined
    if (v.type === "IBAN") {
      v.validators = {
        ...v.validators,
        onChange: ({ value }: any) => {
          return validateIBan(value, selectedCountry)
        },
      }
    } else {
      v.validators = {
        ...v.validators,
        onChange: ({ value }: any) => {
          return required(value, "Account number is required")
        },
      }
    }
    return v
  }, [selectedCountry])

  function cancelShortCodeQuery(sortCode?: string) {
    form.setFieldValue("bankCodeDetails", undefined)
    if (!selectedCountry) return

    const v = sortCode?.replace(/[\s-]+/g, "")
    queryClient.cancelQueries({
      predicate: (query) => {
        const queryKey = query.queryKey as string[]
        return (
          queryKey[0] === "shortCode" &&
          queryKey.join(",") !==
            ["shortCode", v, selectedCountry.name].join(",")
        )
      },
    })
  }

  async function checkSortCodeValidity(sortCode?: string) {
    const v = sortCode?.replace(/[\s-]+/g, "")
    if (!v) return "Field is required"
    if (!selectedCountry) return "Select a country"

    try {
      const data = await queryClient.fetchQuery({
        queryKey: ["shortCode", v, selectedCountry.name],
        queryFn: () => fetchShortCode(v, selectedCountry),
        retry: false,

        staleTime: Infinity,
      })
      form.setFieldValue("bankCodeDetails", data)

      return data ? undefined : "Code is not valid"
    } catch (error) {
      return "Code is not valid"
    }
  }

  return (
    <div className="mx-auto" style={{ maxWidth: "478px" }}>
      <FormLayout>
        <form.Field
          children={(field) => (
            <div className="my-3 flex items-center">
              <Switch
                checked={field.state.value}
                className="mr-4"
                id="businessSwitch"
                onCheckedChange={field.handleChange}
              />
              <Label
                className="font-normal text-muted-foreground"
                htmlFor="businessSwitch"
              >
                Payee is a business
              </Label>
            </div>
          )}
          name="isBusiness"
        />

        <form.Field
          children={(field) => (
            <FormField field={field} label="Country of payee bank" required>
              <CountryCombobox
                hasErrors={field.state.meta.errors?.length > 0}
                value={field.state.value}
                onChange={(value) => {
                  field.handleChange(value)
                  form.setFieldValue("bankCodeDetails", undefined)
                }}
              />
            </FormField>
          )}
          name="country"
          validators={{
            onChange: ({ value }) => {
              form.setFieldValue("bankCodeDetails", undefined)
              return countries?.find((country) => country.name === value)
                ? undefined
                : "Select a country"
            },
          }}
        />
        {bankCodeType && (
          <>
            <form.Field
              children={(field) => (
                <FormField field={field} label={bankCodeType.label} required>
                  <div
                    className={cn(
                      "relative",
                      field.state.meta.errors?.length > 0 && "ring-destructive",
                    )}
                  >
                    <Input
                      className={cn(
                        "bg-background",
                        field.state.meta.errors?.length > 0 &&
                          "border-destructive",
                      )}
                      onChange={(e) => {
                        const value = bankCodeType.onChangePipe
                          ? bankCodeType.onChangePipe(e.target.value)
                          : e.target.value
                        field.handleChange(value)
                      }}
                      placeholder={bankCodeType.placeholder}
                      value={
                        bankCodeType.valuePipe
                          ? bankCodeType.valuePipe(field.state.value)
                          : field.state.value
                      }
                    />
                    {field.state.meta.isValidating && (
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        <Icon className="animate-spin" icon="tabler:loader" />
                      </div>
                    )}
                  </div>
                </FormField>
              )}
              listeners={{
                onChange: ({ value }) => {
                  cancelShortCodeQuery(value)
                },
              }}
              name="bankCodeValue"
              validators={{
                onChangeAsyncDebounceMs: 1000,

                ...bankCodeType.validators,
                onChangeAsync: async ({ value }) =>
                  await checkSortCodeValidity(value),
              }}
            />
            <form.Subscribe selector={(state) => state.values.bankCodeDetails}>
              {(field) => (
                <>
                  {field && (
                    <div className="mt-2 flex items-start gap-2 rounded-lg bg-accent p-4 text-muted-foreground">
                      <CheckIcon
                        className="flex-none"
                        color="green"
                        size={24}
                      />
                      <div className="flex-1 text-sm">
                        {formatBankDetails(field)}
                      </div>
                    </div>
                  )}
                </>
              )}
            </form.Subscribe>
          </>
        )}
        {accountNumberType && (
          <form.Field
            children={(field) => (
              <FormField field={field} label={accountNumberType.label} required>
                <Input
                  className="bg-background"
                  onChange={(e) => {
                    const value = accountNumberType.onChangePipe
                      ? accountNumberType.onChangePipe(e.target.value)
                      : e.target.value
                    field.handleChange(value)
                  }}
                  value={
                    accountNumberType.valuePipe
                      ? accountNumberType.valuePipe(field.state.value)
                      : field.state.value
                  }
                />
              </FormField>
            )}
            name="accountNumberValue"
            validators={{
              ...accountNumberType.validators,
            }}
          />
        )}

        <form.Subscribe selector={(state) => state.values.isBusiness}>
          {(isBusiness) => (
            <form.Field
              children={(field) => (
                <FormField
                  field={field}
                  label={
                    isBusiness ? "Name on account" : "Full name on account"
                  }
                  required
                >
                  <Input
                    className="bg-background"
                    data-testid="account-name-input"
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    value={field.state.value}
                  />
                </FormField>
              )}
              name="accountName"
              validators={{
                onChange: ({ value }) => {
                  return value.trim() ? undefined : "Account name is required"
                },
              }}
            />
          )}
        </form.Subscribe>

        <FormButtons
          form={form}
          onCancel={() => {
            onInvalid?.()
            modalClose?.()
            if (!modal) {
              router.navigate({
                to: "/$entityId/payees",
                params: {
                  entityId,
                },
              })
            }
          }}
          modal={modal}
        />
      </FormLayout>
    </div>
  )
}
