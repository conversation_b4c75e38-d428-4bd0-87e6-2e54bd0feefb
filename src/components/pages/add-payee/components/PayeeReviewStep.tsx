import { useMemo, useState } from "react"
import { <PERSON>ert<PERSON>ircle, PencilIcon, XIcon } from "lucide-react"
import { useNavigate } from "@tanstack/react-router"

import { formatBankDetails } from "@/lib/bank.utils"
import {
  addPayeeMutation,
  useStartPayeeVerificationMutation,
  useVerifyPayeeMutation,
} from "@/data/payees/payees.mutation"
import { IPayeeForm } from "@/data/payees/payees.interface"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { FormButtons, FormLayout } from "@/components/base/form/form"
import { VerificationModal } from "@/components/pages/send-payments/components/modals/VerificationModal"

import { getBankCodeTypeLabel } from "./helpers"
import { DetailRow } from "./DetailRow"

interface PayeeReviewStepProps {
  payee: Partial<IPayeeForm>
  onEdit?: (value: number) => void
  onComplete?: (value: any) => void
  entityId: string
}

export function PayeeReviewStep({
  payee,
  onEdit,
  onComplete,
  entityId,
}: PayeeReviewStepProps) {
  const bankCountry = useMemo(() => {
    return payee?.bankDetails?.countryDetails
  }, [payee?.bankDetails?.countryDetails])

  const bankDetails = useMemo(() => {
    if (!payee?.bankDetails?.bankCodeDetails) return "No bank details found"
    return formatBankDetails(payee?.bankDetails?.bankCodeDetails)
  }, [payee?.bankDetails?.bankCodeDetails])

  const payeeAddress = useMemo(() => {
    const _addressArray = [
      payee?.payeeDetails?.address?.buildingNumber,
      payee?.payeeDetails?.address?.street,
      payee?.payeeDetails?.address?.city,
      payee?.payeeDetails?.address?.state,
      payee?.payeeDetails?.address?.postalCode,
      payee?.payeeDetails?.address?.country,
    ]
    return _addressArray.filter(Boolean).join(", ")
  }, [payee?.payeeDetails?.address])

  const { mutate, isPending, isError, error, reset } = addPayeeMutation()
  const {
    mutate: startVerification,
    isPending: isVerificationPending,
    isError: isVerificationError,
    error: verificationError,
    reset: resetVerification,
  } = useStartPayeeVerificationMutation()
  const [showVerification, setShowVerification] = useState(false)
  const [pendingPayee, setPendingPayee] = useState<Partial<IPayeeForm> | null>(
    null,
  )
  const [payeeId, setPayeeId] = useState<string | null>(null)
  const [verificationErrorMsg, setVerificationErrorMsg] = useState<
    string | null
  >(null)
  const navigate = useNavigate()
  const verifyPayeeMutation = useVerifyPayeeMutation()

  function handleConfirmPayee() {
    setPendingPayee(payee)
    mutate(payee as IPayeeForm, {
      onSuccess: (data) => {
        // Use aggregateId as the payee ID
        const id = data?.aggregateId
        if (id) {
          setPayeeId(id)
          startVerification(
            { payeeId: id, type: "Sms" },
            {
              onSuccess: () => {
                setShowVerification(true)
                setVerificationErrorMsg(null)
              },
              onError: (err: any) => {
                setVerificationErrorMsg(
                  err?.message || "Failed to start verification.",
                )
              },
            },
          )
        } else {
          setVerificationErrorMsg("Payee ID not returned from API.")
        }
      },
    })
  }

  function handleVerify(code: string) {
    if (!payeeId) return
    verifyPayeeMutation.mutate(
      { payeeId, verificationCode: code },
      {
        onSuccess: () => {
          setShowVerification(false)
          // Call onComplete with the expected format { id: string }
          if (onComplete) {
            onComplete({ id: payeeId })
          }
        },
        onError: (err: any) => {
          setVerificationErrorMsg(
            err?.message || "Verification failed. Please try again.",
          )
        },
      },
    )
  }

  function handleResendCode() {
    // Implement resend code logic here if needed
  }

  const bankCodeTypeLabel = useMemo(() => {
    return bankCountry ? getBankCodeTypeLabel(bankCountry) : undefined
  }, [bankCountry])

  return (
    <div className="mx-auto" style={{ maxWidth: "478px" }}>
      <FormLayout>
        {(isError || verificationErrorMsg) && (
          <Alert className="relative bg-background" variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error </AlertTitle>
            <AlertDescription>
              {error?.message || verificationErrorMsg}
            </AlertDescription>
            <div className="absolute right-0 top-0">
              <Button
                className="rounded-full"
                onClick={() => {
                  reset()
                  setVerificationErrorMsg(null)
                  resetVerification()
                }}
                size="icon"
                variant="ghost"
              >
                <XIcon className="h-4 w-4 text-destructive" />
              </Button>
            </div>
          </Alert>
        )}
        <Card className="border-none bg-sidebar p-2">
          <CardHeader className="flex flex-row items-center justify-between p-4 pb-0">
            <strong className="text-sm text-foreground">Bank details</strong>
            <Button
              className="px-1"
              onClick={() => onEdit?.(0)}
              size="sm"
              variant="link"
            >
              <PencilIcon className="h-4 w-4" />
              Edit
            </Button>
          </CardHeader>

          <CardContent className="p-4">
            <div className="space-y-2">
              <DetailRow
                className="border-none"
                label="Payee type"
                value={
                  payee?.bankDetails?.isBusiness ? "Business" : "Individual"
                }
              />
              {bankCountry && (
                <DetailRow
                  className="border-none"
                  label="Bank country"
                  value={bankCountry?.name}
                />
              )}
              {bankDetails && (
                <DetailRow
                  className="border-none"
                  label="Bank details"
                  value={bankDetails}
                />
              )}
              {payee?.bankDetails?.bankCodeValue && (
                <DetailRow
                  className="border-none"
                  label={bankCodeTypeLabel ?? "Bank code"}
                  value={payee?.bankDetails?.bankCodeValue}
                />
              )}
              {payee?.bankDetails?.accountNumberValue && (
                <DetailRow
                  className="border-none"
                  label={bankCountry?.accountNumberType ?? "Account number"}
                  value={payee?.bankDetails?.accountNumberValue}
                />
              )}
              {payee?.bankDetails?.accountName && (
                <DetailRow
                  className="border-none"
                  label={
                    payee?.bankDetails?.isBusiness
                      ? "Name on account"
                      : "Full name on account"
                  }
                  value={payee?.bankDetails?.accountName}
                />
              )}
            </div>
          </CardContent>
        </Card>
        {/* <h3 className="text-xl font-semibold mt-8 mb-2">Payee Details</h3> */}
        <Card className="mt-4 border-none bg-sidebar p-2">
          <CardHeader className="flex flex-row items-center justify-between p-4 pb-0">
            <strong className="text-sm text-foreground">Payee details</strong>
            <Button
              className="px-1"
              onClick={() => onEdit?.(1)}
              size="sm"
              variant="link"
            >
              <PencilIcon className="h-4 w-4" />
              Edit
            </Button>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-2">
              <DetailRow
                className="border-none"
                label="Display name"
                value={payee?.payeeDetails?.displayName}
              />

              <DetailRow
                className="border-none"
                label="Nationality"
                value={payee?.payeeDetails?.nationality}
              />

              {payee?.payeeDetails?.email && (
                <DetailRow
                  className="border-none"
                  label="Email"
                  value={payee?.payeeDetails?.email}
                />
              )}
              {payeeAddress && (
                <DetailRow
                  className="border-none"
                  label="Address"
                  value={payeeAddress}
                />
              )}
            </div>
          </CardContent>
        </Card>

        <FormButtons
          onNext={handleConfirmPayee}
          nextButtonText={"Confirm payee"}
          isValid={!!payee?.bankDetails && !!payee?.payeeDetails}
          modal={true}
        />
        <VerificationModal
          open={showVerification}
          onOpenChange={setShowVerification}
          onVerify={handleVerify}
          onResendCode={handleResendCode}
        />
        {/* <pre>{JSON.stringify(payee, null, 2)}</pre> */}
      </FormLayout>
    </div>
  )
}
