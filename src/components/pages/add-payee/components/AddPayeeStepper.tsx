import { useState } from "react"

import {
  IPayeeBankDetails,
  IPayeeDetails,
  IPayeeForm,
} from "@/data/payees/payees.interface"
import { Step, Stepper } from "@/components/base/triggers/Stepper"

import { PayeeDetailsForm } from "./PayeesDetailsForm"
import { PayeeReviewStep } from "./PayeeReviewStep"
import { PayeeBankDetailForm } from "./PayeeBankDetailForm"

interface AddPayeeStepperProps {
  payee?: IPayeeForm
  onComplete?: (val: { id: string }) => void
  modal?: boolean
  onClose?: () => void
  entityId: string
}

export function AddPayeeStepper({
  onComplete,
  modal = false,
  onClose,
  entityId,
  payee,
}: AddPayeeStepperProps) {
  const [activeStep, setActiveStep] = useState(0)
  const [maxStep, setMaxStep] = useState(0)

  const [payeeForm, setPayeeForm] = useState<Partial<IPayeeForm>>({})

  function onBankDetailsNext(payload: IPayeeBankDetails) {
    setPayeeForm((prev) => ({
      ...prev,
      bankDetails: payload,
    }))
    setActiveStep(1)
    if (maxStep < 1) {
      setMaxStep(1)
    }
  }

  function handleStepChange(step: number) {
    if (step > maxStep) return
    setActiveStep(step)
  }

  function onDetailsNext(payload: IPayeeDetails) {
    setPayeeForm((prev) => ({
      ...prev,
      payeeDetails: payload,
    }))
    setActiveStep(2)
    if (maxStep < 2) {
      setMaxStep(2)
    }
  }

  function handleEdit(step: number) {
    setActiveStep(step)
  }

  return (
    <Stepper modal={modal} onStepChange={handleStepChange} value={activeStep}>
      <Step title="Bank details">
        <PayeeBankDetailForm
          bankDetails={payeeForm.bankDetails || payee?.bankDetails}
          entityId={entityId}
          modal={modal}
          modalClose={onClose}
          onInvalid={() => setMaxStep(0)}
          onNext={onBankDetailsNext}
        />
      </Step>
      <Step disabled={maxStep < 1} title="Payee details">
        <PayeeDetailsForm
          details={payeeForm.payeeDetails || payee?.payeeDetails}
          modalClose={onClose}
          onInvalid={() => setMaxStep(1)}
          onNext={onDetailsNext}
        />
      </Step>
      <Step disabled={maxStep < 2} title="Review">
        {payeeForm.bankDetails && payeeForm.payeeDetails && (
          <PayeeReviewStep
            onComplete={onComplete}
            onEdit={handleEdit}
            payee={payeeForm as IPayeeForm}
            entityId={entityId}
          />
        )}
      </Step>
    </Stepper>
  )
}
