import { useState } from "react"
import { format } from "date-fns"
import { useRouter } from "@tanstack/react-router"

import { formatAccountName } from "@/lib/bank.utils"
import { usePaymentQuery } from "@/data/payments/payments.query"
import { usePaymentApprovalDetailsQuery } from "@/data/payments/payments.query"
import {
  cancelWithReasonMutation,
  revertPaymentToDraftMutation,
  approvePaymentMutation,
} from "@/data/payments/payments.mutation"
import { IPaymentRejectionReason } from "@/data/payments/payments.interface"
import { IPayeeData } from "@/data/payees/payees.interface"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ReviewRow } from "@/components/base/review/ReviewRow"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { FormLayout } from "@/components/base/form/form"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyFlag } from "@/components/base/currency/CurrencyFlag"

import { PaymentActionDialog } from "./components/PaymentActionDialog"
import { DottedPaymentFlow } from "../send-payments/components/DottedPaymentFlow"
import { PayeeDetailsExpandable } from "../payees/components/PayeeDetailsExpandable"

interface ApprovePaymentsPageProps {
  entityId: string
  paymentId: string
}

export function ApprovePaymentsPage({
  entityId,
  paymentId,
}: ApprovePaymentsPageProps) {
  const { mutate: revertToDraft } = revertPaymentToDraftMutation()
  const { mutate: cancelWithReason } = cancelWithReasonMutation()
  const { mutate: approvePayment } = approvePaymentMutation()
  const [dialogType, setDialogType] = useState<"edit" | "cancel" | "approve">()

  const handleConfirm = (
    type: "edit" | "cancel",
    reason?: IPaymentRejectionReason,
  ) => {
    switch (type) {
      case "edit":
        handleEditPayment()
        break
      case "cancel":
        handleCancelPayment(reason)
        break
    }
  }

  const router = useRouter()

  const handleEditPayment = () => {
    revertToDraft(paymentId, {
      onSuccess: () => {
        setDialogType(undefined)
        router.navigate({
          to: "/$entityId/payments",
          params: {
            entityId,
          },
        })
      },
      onError: (error) => {
        console.error("Error reverting payment to draft:", error)
      },
    })
  }

  const { data: payment, isLoading, isError } = usePaymentQuery(paymentId)
  const { data: approvalDetails, isLoading: approvalDetailsLoading } =
    usePaymentApprovalDetailsQuery(paymentId)

  const handleCancelPayment = (reason?: IPaymentRejectionReason) => {
    if (reason) {
      cancelWithReason(
        { paymentId, reason },
        {
          onSuccess: () => {
            setDialogType(undefined)
            router.navigate({
              to: "/$entityId/payments",
              params: {
                entityId,
              },
            })
          },
        },
      )
    }
    setDialogType(undefined)
  }

  const openApproveDialog = () => {
    approvePayment(
      {
        paymentId: paymentId,
      },
      {
        onSuccess: () => {
          router.navigate({
            to: "/$entityId/payments",
            params: { entityId },
          })
        },
      },
    )
    setDialogType(undefined)
  }

  const formatSignatoryRules = (rules: { approverLevel: string }[] = []) => {
    // Count occurrences of each level
    const levelCounts = new Map()
    rules.forEach((rule) => {
      const level = rule.approverLevel
      levelCounts.set(level, (levelCounts.get(level) || 0) + 1)
    })

    // Define the order (A > B > C)
    const levelOrder = ["LevelA", "LevelB", "LevelC"]

    // Format and process in specific order
    return Array.from(levelCounts.entries())
      .sort((a, b) => levelOrder.indexOf(a[0]) - levelOrder.indexOf(b[0]))
      .map(([level, count], index, array) => {
        // Extract just the letter from the level (e.g., "LevelA" -> "A")
        const letter = level.replace("Level", "")

        // Add "or higher" for B and C if they're not the last elements
        const isLastElement = index === array.length - 1
        const needsOrHigher = level !== "LevelA" && !isLastElement

        return `${count}${letter}${needsOrHigher ? " or higher" : ""}`
      })
  }

  // Format the completed signatories list
  const formatCompletedSignatories = (approvers: any[] = []) => {
    return approvers.map(
      (a) =>
        a.userDisplayName + " (" + a.approverLevel.replace("Level", "") + ")",
    )
  }

  if (isLoading) {
    return (
      <div className="mt-1 flex h-96 w-full items-center justify-center">
        <LoadingSpinner size="8" />
      </div>
    )
  }

  if (isError || !payment) {
    return (
      <div className="rounded-lg bg-background p-6 shadow-md">
        <p className="text-center text-destructive">
          Error fetching payees. Please try again later.
        </p>
      </div>
    )
  }

  return (
    <FormLayout title="Payment to approve">
      <Card className="w-full max-w-md rounded-lg border border-border shadow-sm">
        <CardContent className="p-4">
          <div className="flex h-32 items-center justify-between">
            {/* Left Side Flow */}
            <DottedPaymentFlow
              className="mb-4 mt-4 h-full"
              name={payment.toAccount?.accountName}
            />

            <div className="flex h-full w-full flex-col justify-between py-2 text-base">
              <div className="flex items-center justify-between">
                <p className="text-muted-foreground">From account</p>
                <p className="font-medium">
                  {formatAccountName({
                    name: payment.fromAccount.clientAccount.accountName,
                    iban: payment.fromAccount.clientAccount.virtualIban,
                  })}
                </p>
              </div>
              {payment.currency && (
                <div className="flex flex-none items-center justify-end gap-x-2">
                  <CurrencyFlag currency={payment.currency} size="md" />
                  {payment.currency}
                  <CurrencyText
                    amount={payment.amount}
                    className="text-lg font-medium"
                    currency={payment.currency}
                  />
                </div>
              )}
              <div className="flex items-center justify-between">
                <p className="text-muted-foreground">To payee</p>
                <p className="font-medium">{payment.toAccount?.accountName}</p>
              </div>
            </div>
          </div>
          <div className="flex flex-col items-end gap-y-2">
            {payment.toAccount && (
              <PayeeDetailsExpandable
                className="!w-[340px]"
                payee={payment.toAccount as unknown as IPayeeData}
              />
            )}
          </div>
        </CardContent>
      </Card>
      {/* Payment Details */}
      <Card className="mt-2 w-full max-w-md rounded-lg border border-border shadow-sm">
        <CardContent className="p-4">
          <ReviewRow label="Payment code" value={payment.code} />
          <ReviewRow
            label="Submitted by"
            value={approvalDetails?.submittedBy.displayName}
          />
          <ReviewRow
            label="Payment date"
            value={format(new Date(payment.createdAt), "dd/MM/yy HH:mm")}
          />
          <ReviewRow label="Purpose of payment" value={payment.purpose || ""} />
          <ReviewRow
            label="Payment reference"
            value={payment.reference || ""}
          />
          <ReviewRow
            label="Signatories required"
            value={formatSignatoryRules(
              approvalDetails?.signatoryAmountBand?.signatoryRules,
            )}
          />
          <ReviewRow
            label="Approved by"
            value={formatCompletedSignatories(approvalDetails?.approvals) || []}
          />
        </CardContent>
      </Card>
      {payment.currentStatus === "Pending Signatory Approval" && (
        <div className="mt-5 flex space-x-4">
          {approvalDetails?.canApprove && (
            <Button onClick={() => setDialogType("approve")}>Approve</Button>
          )}
          <Button
            onClick={() => setDialogType("edit")}
            variant="primary-outline"
          >
            Reject for edit
          </Button>
          <Button onClick={() => setDialogType("cancel")} variant="link">
            Reject and cancel
          </Button>
        </div>
      )}
      <PaymentActionDialog
        isOpen={dialogType !== undefined}
        onClose={() => setDialogType(undefined)}
        onConfirm={(type, reason) => {
          if (type === "approve") {
            openApproveDialog()
          } else {
            handleConfirm(type, reason)
          }
        }}
        paymentId={paymentId}
        type={dialogType}
      />
    </FormLayout>
  )
}
