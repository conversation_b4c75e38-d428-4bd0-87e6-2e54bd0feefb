import { useForm } from "@tanstack/react-form"

import { usePaymentRejectionReasonsQuery } from "@/data/payments/payments.query"
import { IPaymentRejectionReason } from "@/data/payments/payments.interface"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { TrailingSpinner } from "@/components/base/loading-spinner/TrailingSpinner"
import { FormLayout, FormField } from "@/components/base/form/form"

interface PaymentRejectionReasonProps {
  onSubmit: (reason: IPaymentRejectionReason) => void
  onCancel: () => void
}

interface SelectReasonFormData {
  reason: string
}

export function PaymentRejectionReason({
  //   entityId,
  onSubmit,
  onCancel,
}: PaymentRejectionReasonProps) {
  const { data: rejectionReasons, isLoading: rejectionReasonsLoading } =
    usePaymentRejectionReasonsQuery()

  const form = useForm<SelectReasonFormData>({
    defaultValues: {
      reason: "",
    },
    onSubmit: ({ value }) => {
      const reason: IPaymentRejectionReason | undefined =
        rejectionReasons?.find((reason) => reason.key === value.reason)
      if (reason) {
        onSubmit(reason)
      }
    },
  })

  return (
    <FormLayout className="w-full" title="">
      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
      >
        <div className="w-full">
          <form.Field
            name="reason"
            validators={{
              onChange: ({ value }) => {
                return !value ? "Please select a reason" : undefined
              },
            }}
          >
            {(field) => (
              <FormField field={field} label="Reason" required>
                <Select
                  onValueChange={field.handleChange}
                  value={field.state.value}
                >
                  <SelectTrigger disabled={rejectionReasonsLoading}>
                    <TrailingSpinner isLoading={rejectionReasonsLoading}>
                      <SelectValue placeholder="Select a reason" />
                    </TrailingSpinner>
                  </SelectTrigger>
                  <SelectContent>
                    {rejectionReasons?.map((reason) => (
                      <SelectItem key={reason.key} value={reason.key}>
                        {reason.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormField>
            )}
          </form.Field>

          <div className="flex justify-end space-x-4 pt-4">
            <Button onClick={onCancel} type="button" variant="link">
              Go back
            </Button>
            <Button onClick={() => form.handleSubmit()} type="submit">
              Cancel payment
            </Button>
          </div>
        </div>
      </form>
    </FormLayout>
  )
}
