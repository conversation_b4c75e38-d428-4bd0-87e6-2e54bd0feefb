import { describe, it, expect, beforeEach, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"

import { usePaymentRejectionReasonsQuery } from "@/data/payments/payments.query"

import { PaymentRejectionReason } from "./PaymentRejectionReason"

// Mock only the query hook
vi.mock("@/data/payments/payments.query", () => ({
  usePaymentRejectionReasonsQuery: vi.fn(),
}))

const mockRejectionReasons = [
  { key: "reason1", value: "Invalid Account" },
  { key: "reason2", value: "Insufficient Funds" },
]

describe("PaymentRejectionReason", () => {
  const mockOnSubmit = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(usePaymentRejectionReasonsQuery as any).mockReturnValue({
      data: mockRejectionReasons,
      isLoading: false,
    })
  })

  it("renders correctly with rejection reasons", () => {
    render(
      <PaymentRejectionReason
        onCancel={mockOnCancel}
        onSubmit={mockOnSubmit}
      />,
    )

    expect(screen.getByRole("combobox")).toBeDefined()
    expect(screen.getByText("Go back")).toBeDefined()
    expect(screen.getByText("Cancel payment")).toBeDefined()
  })

  it("shows loading state", () => {
    ;(usePaymentRejectionReasonsQuery as any).mockReturnValue({
      isLoading: true,
    })

    render(
      <PaymentRejectionReason
        onCancel={mockOnCancel}
        onSubmit={mockOnSubmit}
      />,
    )

    const combobox = screen.getByRole("combobox")
    expect(combobox).toHaveClass("disabled:opacity-50")
  })

  it("calls onCancel when clicking Go back", async () => {
    render(
      <PaymentRejectionReason
        onCancel={mockOnCancel}
        onSubmit={mockOnSubmit}
      />,
    )

    await userEvent.click(screen.getByText("Go back"))
    expect(mockOnCancel).toHaveBeenCalled()
  })

  it("shows validation error when submitting without selection", async () => {
    render(
      <PaymentRejectionReason
        onCancel={mockOnCancel}
        onSubmit={mockOnSubmit}
      />,
    )

    // Try to submit without selecting a reason
    await userEvent.click(screen.getByText("Cancel payment"))

    expect(await screen.findByText("Please select a reason")).toBeDefined()
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })
})
