import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { IPaymentRejectionReason } from "@/data/payments/payments.interface"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import {
  AlertBox,
  AlertTitle,
  AlertDescription,
} from "@/components/base/alert-box"

import { PaymentRejectionReason } from "./PaymentRejectionReason"

interface PaymentCancelDialogProps {
  type?: "edit" | "cancel"
  isOpen: boolean
  onClose: () => void
  onConfirm: (type: "edit" | "cancel", reason?: IPaymentRejectionReason) => void
  paymentId: string
}

export function PaymentCancelDialog({
  type = "edit",
  isOpen,
  onClose,
  onConfirm,
}: PaymentCancelDialogProps) {
  const dialogContent = {
    edit: {
      title: "Reject for edit",
      description:
        "This payment will revert to Draft status. It can then be edited and re-submitted for approval.",
      confirmText: "Reject and revert to draft",
    },
    cancel: {
      title: "Reject and cancel",
      description: "Please select a reason for cancellation below.",
      confirmText: "Reject and cancel",
    },
  }

  const content = dialogContent[type]

  return (
    <Dialog onOpenChange={onClose} open={isOpen}>
      <DialogContent className="max-w-md" hideClose>
        <>
          <VisuallyHidden>
            <DialogTitle>{content.title}</DialogTitle>
            <DialogDescription>{content.description || null}</DialogDescription>
          </VisuallyHidden>

          <AlertBox severity="warning">
            <AlertTitle>{content.title}</AlertTitle>

            {content.description && (
              <AlertDescription>{content.description}</AlertDescription>
            )}
          </AlertBox>

          {type === "cancel" ? (
            <PaymentRejectionReason
              onCancel={onClose}
              onSubmit={(reason) => onConfirm("cancel", reason)}
            />
          ) : (
            <DialogFooter className="flex justify-start">
              <Button onClick={onClose} variant="link">
                Cancel
              </Button>

              <Button onClick={() => onConfirm("edit")}>
                {content.confirmText}
              </Button>
            </DialogFooter>
          )}
        </>
      </DialogContent>
    </Dialog>
  )
}
