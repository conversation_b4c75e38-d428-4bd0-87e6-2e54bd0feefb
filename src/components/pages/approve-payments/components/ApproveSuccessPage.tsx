import { CheckCircle } from "lucide-react"
import { <PERSON> } from "@tanstack/react-router"

import { But<PERSON> } from "@/components/ui/button"

interface PaymentSuccessPageProps {
  entityId: string
  approve?: boolean
}

export function PaymentSuccessPage({
  entityId,
  approve,
}: PaymentSuccessPageProps) {
  return (
    <>
      <div className="mx-auto flex max-w-md flex-col justify-center gap-2 pb-4 pt-4">
        <div className="mb-6">
          <CheckCircle
            className="h-20 w-20 text-primary"
            data-testid="success-icon"
          />
        </div>

        <h2 className="mb-4 text-2xl font-semibold">
          {approve
            ? "Payment approved successfully"
            : "Payment created successfully"}
        </h2>

        <div className="flex gap-4">
          <Button asChild className="px-4 py-2" variant="default">
            <Link
              params={{
                entityId,
              }}
              to={`/$entityId/payments`}
            >
              View Payments
            </Link>
          </Button>
        </div>
      </div>
    </>
  )
}
