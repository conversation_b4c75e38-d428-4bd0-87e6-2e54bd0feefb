import { toast } from "sonner"
import { useState } from "react"

import { usePaymentRejectionReasonsQuery } from "@/data/payments/payments.query"
import { cancelWithReasonMutation } from "@/data/payments/payments.mutation"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { TrailingSpinner } from "@/components/base/loading-spinner/TrailingSpinner"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

interface RejectPaymentModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  paymentId?: string
  onCancelSuccess?: () => void
}

export function RejectPaymentModal({
  open,
  onOpenChange,
  paymentId,
  onCancelSuccess,
}: RejectPaymentModalProps) {
  const [selectedReason, setSelectedReason] = useState("")
  const [isCancelling, setIsCancelling] = useState(false)

  const { data: rejectionReasons, isLoading: isLoadingReasons } =
    usePaymentRejectionReasonsQuery()
  const { mutateAsync: cancelPayment } = cancelWithReasonMutation()

  const handleCancelPayment = async () => {
    if (!paymentId || !selectedReason) {
      toast.error("Please select a reason for cancellation")
      return
    }

    const reason = rejectionReasons?.find((r) => r.key === selectedReason)

    if (!reason) {
      toast.error("Invalid cancellation reason")
      return
    }

    try {
      setIsCancelling(true)
      await cancelPayment({ paymentId, reason })
      // Toast is handled by the mutation
      onOpenChange(false)
      if (onCancelSuccess) onCancelSuccess()
    } catch (error) {
      console.error("Error cancelling payment:", error)
      // Error toast is handled by the mutation
    } finally {
      setIsCancelling(false)
      setSelectedReason("")
    }
  }

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-md">
        <DialogTitle className="text-md">Reject and cancel payment</DialogTitle>
        <DialogDescription className="text-sm">
          Please select a reason for cancellation below.
        </DialogDescription>

        <div className="my-4">
          <Select
            disabled={isLoadingReasons}
            onValueChange={setSelectedReason}
            value={selectedReason}
          >
            <SelectTrigger className="h-10 w-full">
              <TrailingSpinner isLoading={isLoadingReasons}>
                <SelectValue placeholder="Select reason" />
              </TrailingSpinner>
            </SelectTrigger>
            <SelectContent>
              {rejectionReasons?.map((reason) => (
                <SelectItem key={reason.key} value={reason.key}>
                  {reason.value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="mt-6 flex items-center justify-between">
          <Button onClick={() => onOpenChange(false)} variant="ghost">
            Go back
          </Button>
          <Button
            disabled={isCancelling || !selectedReason || isLoadingReasons}
            onClick={handleCancelPayment}
            variant="destructive"
          >
            {isCancelling ? (
              <>
                <LoadingSpinner className="mr-2" size="4" />
                Processing...
              </>
            ) : (
              "Cancel payment"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
