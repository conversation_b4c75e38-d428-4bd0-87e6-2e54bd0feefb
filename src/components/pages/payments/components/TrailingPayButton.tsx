import { useState } from "react"
import { Link, useLocation } from "@tanstack/react-router"
import { LayersIcon } from "@radix-ui/react-icons"
import { ArrowTopRightIcon } from "@radix-ui/react-icons"

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"

import { UploadBulkPaymentDialog } from "./UploadBulkPaymentDialog"

interface TrailingPayButtonProps {
  entityId: string
}

export function TrailingPayButton({ entityId }: TrailingPayButtonProps) {
  const [open, setOpen] = useState(false)
  const { href } = useLocation()
  const [bulkDialogOpen, setBulkDialogOpen] = useState(false)
  return (
    <>
      <Popover onOpenChange={setOpen} open={open}>
        <PopoverTrigger asChild>
          <Button className="py-2">
            <ArrowTopRightIcon className="h-4 w-4 opacity-70" />
            <span>Pay</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="end"
          className="w-52 rounded-lg border border-primary/20 p-2"
        >
          <Link
            params={{ entityId }}
            search={{
              fromRoute: href,
            }}
            to="/$entityId/payments/send"
          >
            <Button
              className="w-full justify-start px-1 font-normal"
              size="lg"
              variant="ghost"
            >
              <ArrowTopRightIcon className="mx-1 h-5 w-5 opacity-50" />
              Single payment
            </Button>
          </Link>
          <Button
            className="w-full justify-start px-1 font-normal"
            onClick={() => setBulkDialogOpen(true)}
            size="lg"
            variant="ghost"
          >
            <LayersIcon className="mx-1 h-5 w-5 opacity-50" />
            Bulk payment
          </Button>
        </PopoverContent>
      </Popover>
      <UploadBulkPaymentDialog
        onOpenChange={setBulkDialogOpen}
        open={bulkDialogOpen}
      />
    </>
  )
}
