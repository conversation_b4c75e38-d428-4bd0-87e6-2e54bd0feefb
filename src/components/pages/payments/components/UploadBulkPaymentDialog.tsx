import { toast } from "sonner"
import { useEffect, useRef, useState } from "react"
import { TriangleAlert, X } from "lucide-react"
import { useNavigate, useSearch } from "@tanstack/react-router"

import { queryClient } from "@/main"
import { queryKeys } from "@/lib/constants/query.constants"
import { uploadBulkPayment } from "@/data/payments/payments.api"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { FileUploadArea } from "@/components/base/FileUploadArea"

import { UploadAbortConfirmDialog } from "./UploadAbortConfirmDialog"
import { BulkPaymentAlert } from "./BulkPaymentAlert"

interface UploadBulkPaymentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function UploadBulkPaymentDialog({
  open,
  onOpenChange,
}: UploadBulkPaymentDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [showCloseDialog, setShowCloseDialog] = useState<
    "close" | "abort" | undefined
  >()

  const abortControllerRef = useRef<AbortController | null>(null)
  const fileUploadAreaRef = useRef<any>(null)

  const navigate = useNavigate()
  const search: any = useSearch({
    strict: false,
  })

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    setUploadProgress(0)
    setUploadError(null)

    // Create new AbortController
    abortControllerRef.current = new AbortController()

    uploadBulkPayment(
      { file },
      {
        signal: abortControllerRef.current.signal,
        onUploadProgress: (progress) => {
          setUploadProgress(progress.loaded / (progress.total ?? 1))
        },
      },
    )
      .then(() => {
        setUploadProgress(1)
        toast.success("Upload successful")
        onOpenChange(false)

        queryClient.invalidateQueries({
          predicate(query) {
            return (
              query.queryKey.at(0) === queryKeys.payment.list &&
              query.queryKey.at(2) === "bulk"
            )
          },
        })
        if (search.tab !== "bulk") {
          navigate({
            search: {
              ...search,
              tab: "bulk",
            },
          })
        }
      })
      .catch((error) => {
        setUploadError(error.message)
        toast.error("Upload failed")
      })
      .finally(() => {
        abortControllerRef.current = null
      })
    // Add your file processing logic here
  }

  function resetDialog() {
    setSelectedFile(null)
    setUploadProgress(0)
    setUploadError(null)
    setShowCloseDialog(undefined)
    abortControllerRef.current = null
    fileUploadAreaRef.current?.reset()
  }

  function handleAbort() {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    if (showCloseDialog === "close") {
      onOpenChange(false)
    } else {
      resetDialog()
    }
  }

  useEffect(() => {
    if (!open) {
      // reset dialog state
      resetDialog()
    }
  }, [open])

  function handleOpenChange(open: boolean) {
    if (!open && selectedFile) {
      setShowCloseDialog("close")
    } else {
      onOpenChange(open)
    }
  }

  function onFileDelete() {
    if (uploadError) {
      resetDialog()
    } else {
      setShowCloseDialog("abort")
    }
  }

  return (
    <Dialog onOpenChange={handleOpenChange} open={open}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Upload bulk payment file</DialogTitle>
          <DialogDescription>
            {" "}
            In order to perform a bulk payment you must upload a payment file.
          </DialogDescription>
        </DialogHeader>
        <BulkPaymentAlert
          error={uploadError as any}
          variant={uploadError ? "error" : "warning"}
        />

        <FileUploadArea
          acceptedFileTypes={[
            "text/csv",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          ]}
          className="mt-4 rounded-lg border-2 border-dashed shadow-sm"
          description="We accept CSV, XLSX, and XLS files"
          label="Drag and drop payment file or"
          onFileSelect={handleFileSelect}
          ref={fileUploadAreaRef}
        />

        {selectedFile && (
          <div className="mt-2 block w-full justify-center bg-foreground/5 p-2">
            <div className="m-0 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {uploadError && (
                  <TriangleAlert className="h-4 w-4 text-destructive" />
                )}

                <span className="my-auto text-sm text-foreground">
                  {selectedFile.name}
                </span>
              </div>
              <Button onClick={onFileDelete} size="icon" variant="ghost">
                <X />
              </Button>
            </div>
            {!uploadError && (
              <Progress
                className="mb-2 border-0"
                max={100}
                value={uploadProgress * 100}
              />
            )}
          </div>
        )}

        <UploadAbortConfirmDialog
          onAbort={handleAbort}
          onCancel={() => setShowCloseDialog(undefined)}
          open={showCloseDialog !== undefined}
          variant={showCloseDialog}
        />
      </DialogContent>
    </Dialog>
  )
}
