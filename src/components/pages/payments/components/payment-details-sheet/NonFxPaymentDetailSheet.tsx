import { useMemo, useState } from "react"
import { useNavigate } from "@tanstack/react-router"

import { ROLES } from "@/lib/constants/roles.constants"
import { useAuth } from "@/hooks/use-auth"
import {
  useDeleteDraftPaymentQuery,
  usePaymentApprovalDetailsQuery,
  useValidateSignatoryAmount,
} from "@/data/payments/payments.query"
import {
  approvePaymentMutation,
  submitPaymentWithVerificationMutation,
} from "@/data/payments/payments.mutation"
import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

import {
  PaymentDetailSheetBase,
  PaymentDetailSheetBaseProps,
} from "./PaymentDetailSheetBase"
import { RejectPaymentModal } from "../RejectPaymentModal"
import { RejectForEditModal } from "../RejectForEditModal"
import { NonFxPaymentControls } from "../payment-controls/NonFxPaymentControls"
import { ApprovalModal } from "../../../send-payments/components/modals/ApprovalModal"

export function NonFxPaymentDetailSheet({
  payment,
  entityId,
}: PaymentDetailSheetBaseProps) {
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [rejectForEditModalOpen, setRejectForEditModalOpen] = useState(false)
  const [rejectModalOpen, setRejectModalOpen] = useState(false)

  const navigate = useNavigate()

  const { data: approvalDetails } = usePaymentApprovalDetailsQuery(
    payment?.id || "",
  )
  const { data: signatoryValidation } = useValidateSignatoryAmount(
    Number(payment?.amount),
    payment?.currency as string,
  )

  const { mutateAsync: approvePayment } = approvePaymentMutation()

  const { mutateAsync: submitPayment } = submitPaymentWithVerificationMutation()

  const auth = useAuth()

  const isSelfApprover = signatoryValidation?.isSelfApproved
  const isAlreadyApproved = approvalDetails?.approvals.some(
    (approver) => approver.approverId === auth.user?.user_id,
  )

  const cantApproveMine =
    !signatoryValidation?.canApproveOwnSubmission &&
    approvalDetails?.submittedBy.id === auth.user?.user_id

  const { data: entityAccess } = useEntityAccessQuery(entityId)
  const { entity } = useLoaderData()

  const _entityAccess = useMemo(() => {
    if (!entityAccess) return null
    if (!entity.id) return null
    return entityAccess?.find(
      (access) => access.entityId?.toLowerCase() === entity.id?.toLowerCase(),
    )
  }, [entityAccess, entity])

  const isApproveButtonVisible = useMemo(() => {
    if (!payment) return false

    if (!_entityAccess) return false
    if (
      [ROLES.APPROVER, ROLES.ADMINISTRATOR].some((e) =>
        _entityAccess?.roles?.includes(e),
      )
    )
      return true
    return false
  }, [_entityAccess, payment])

  const startNonFxVerificationProcess = () => {
    setConfirmModalOpen(false)

    if (payment?.currentStatus === "Draft") {
      setOperationType("submit")
      verifyAndCompleteNonFxPayment()
    } else {
      setOperationType("approve")
      verifyAndCompleteNonFxPayment()
    }
  }

  const openNonFxApprovalConfirmation = () => {
    setConfirmModalOpen(true)
  }

  const submitNonFxDraftPayment = () => {
    if (
      payment?.currentStatus === "Draft" &&
      !approvalDetails?.isFinalApprover
    ) {
      setOperationType("submit")
      verifyAndCompleteNonFxPayment()
    } else {
      setConfirmModalOpen(true)
    }
  }

  const { mutate: deletePayment } = useDeleteDraftPaymentQuery(
    payment?.id || "",
  )

  const deleteNonFxDraftPayment = () => {
    deletePayment()
  }

  const [, setOperationType] = useState<"submit" | "approve" | null>(null)

  const verifyAndCompleteNonFxPayment = async () => {
    if (payment?.currentStatus === "Draft") {
      submitPayment(
        {
          paymentId: payment?.id || "",
        },
        {
          onSuccess: () => {
            if (approvalDetails?.isFinalApprover) {
              approvePayment(
                {
                  paymentId: payment?.id || "",
                },
                {
                  onSuccess: () => {
                    navigate({
                      to: "/$entityId/payments",
                      params: { entityId },
                    })
                  },
                },
              )
            }
          },
        },
      )
    } else {
      approvePayment(
        {
          paymentId: payment?.id || "",
        },
        {
          onSuccess: () => {
            navigate({ to: "/$entityId/payments", params: { entityId } })
          },
        },
      )
    }
  }
  const editOrRevertNonFxPayment = async () => {
    if (payment?.currentStatus === "Draft") {
      navigate({
        to: "/$entityId/payments/send",
        params: { entityId },
        search: { paymentId: payment.id },
      })
    } else {
      setRejectForEditModalOpen(true)
    }
  }

  const openNonFxCancellationModal = () => {
    setRejectModalOpen(true)
  }

  // This function is not used in NonFx payments, but kept for interface compatibility
  const unusedNonFxRateApproval = () => {}

  return (
    <>
      <PaymentDetailSheetBase entityId={entityId} payment={payment}>
        {payment && (
          <NonFxPaymentControls
            approvalDetails={approvalDetails}
            cantApproveMine={cantApproveMine}
            isAlreadyApproved={isAlreadyApproved}
            isApproveButtonVisible={isApproveButtonVisible}
            isSelfApprover={isSelfApprover}
            onApprove={openNonFxApprovalConfirmation}
            onDeleteDraft={deleteNonFxDraftPayment}
            onDraft={submitNonFxDraftPayment}
            onGetRateAndApprove={unusedNonFxRateApproval}
            onReject={openNonFxCancellationModal}
            onRejectForEdit={editOrRevertNonFxPayment}
            payment={payment}
            signatoryValidation={signatoryValidation}
          />
        )}
      </PaymentDetailSheetBase>

      {/* Non-FX specific modals */}
      <ApprovalModal
        onCancel={() => setConfirmModalOpen(false)}
        onConfirm={startNonFxVerificationProcess}
        onOpenChange={setConfirmModalOpen}
        open={confirmModalOpen}
      />

      <RejectForEditModal
        onOpenChange={setRejectForEditModalOpen}
        open={rejectForEditModalOpen}
        paymentId={payment?.id}
      />

      <RejectPaymentModal
        onOpenChange={setRejectModalOpen}
        open={rejectModalOpen}
        paymentId={payment?.id}
      />
    </>
  )
}
