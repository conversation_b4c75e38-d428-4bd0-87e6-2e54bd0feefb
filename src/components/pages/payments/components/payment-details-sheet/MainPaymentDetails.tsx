import { format } from "date-fns"

import { formatAccountNumber } from "@/lib/bank.utils"
import { IPaymentDetailsBackend } from "@/data/payments/payments.interface"
import { PayeeDetailsExpandable } from "@/components/pages/payees/components/PayeeDetailsExpandable"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

import { PaymentDetailRow } from "./PaymentDetailRow"

interface MainPaymentDetailsProps {
  payment?: IPaymentDetailsBackend
  amount?: number | null
  currency?: string
  isFx?: boolean
}

export function MainPaymentDetails({
  payment,
  amount,
  currency = "N/A",
  isFx,
}: MainPaymentDetailsProps) {
  return (
    <div className="mt-4 space-y-1 rounded-2xl bg-muted p-4">
      <div className="mb-6 mt-3">
        <div className="text-2xl font-semibold">
          {amount && <CurrencyText amount={amount} currency={currency} />}
        </div>
      </div>
      <PaymentDetailRow
        label="Payment date"
        value={
          payment?.valueDate
            ? format(new Date(payment.valueDate), "d MMM yyyy")
            : "N/A"
        }
      />
      <PaymentDetailRow
        label="From"
        value={
          payment?.fromAccount?.clientAccount?.accountName
            ? `${payment.fromAccount.clientAccount.accountName}${formatAccountNumber(payment.fromAccount.clientAccount.accountNumber) ?? ""}`
            : "N/A"
        }
      />
      <PaymentDetailRow label="To" value={payment?.toAccount?.accountName} />
      <PaymentDetailRow
        label=""
        value={
          payment?.toAccount && (
            <PayeeDetailsExpandable
              noBorder
              noCopy
              payee={payment?.toAccount}
            />
          )
        }
      />
      {isFx && (
        <>
          <PaymentDetailRow
            label="Fx Rate"
            value={
              payment?.fxTrade?.executedFxRate ? (
                <span>
                  {payment.remitterCurrency}:{payment.currency}{" "}
                  {payment.fxTrade?.executedFxRate.toFixed(6)}
                </span>
              ) : (
                "Set on final approval"
              )
            }
          />
          <PaymentDetailRow
            label="Payee receives"
            value={
              payment?.amount ? (
                <CurrencyText
                  amount={payment.amount}
                  currency={payment.currency}
                />
              ) : (
                "Set on final approval"
              )
            }
          />
        </>
      )}
    </div>
  )
}
