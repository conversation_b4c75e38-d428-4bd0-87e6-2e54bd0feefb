import { Label } from "@/components/ui/label"

interface IPaymentDetailRowProps {
  label: string
  value: string | React.ReactNode
  secondaryValue?: string
}

export function PaymentDetailRow({
  label,
  value,
  secondaryValue,
}: IPaymentDetailRowProps) {
  return (
    <div className="flex justify-between pb-2">
      <Label className="flex-none text-sm font-normal">{label}</Label>
      {typeof value === "string" ? (
        <p
          className="flex-none text-right text-sm font-light text-foreground/60"
          role="paragraph"
        >
          {value}
          {secondaryValue && (
            <span className="text-sm text-foreground/60">{secondaryValue}</span>
          )}
        </p>
      ) : (
        <div className="flex-none text-right text-sm font-light text-foreground/60">
          {value}
        </div>
      )}
    </div>
  )
}
