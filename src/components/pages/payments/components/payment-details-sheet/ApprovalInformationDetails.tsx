import { ReactNode } from "react"

import { formatDateTimeUTC } from "@/lib/date.utils"
import { IApprover, ISignatoryRule } from "@/data/payments/payments.interface"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

import { PaymentDetailRow } from "./PaymentDetailRow"

interface ApprovalDetails {
  signatoryAmountBand?: {
    signatoryRules?: ISignatoryRule[]
  }
  approvals?: IApprover[]
  submittedBy?: {
    id: string
  }
}

interface ApprovalInformationDetailsProps {
  signatoryMatrixEnabled?: boolean
  approvalDetails?: ApprovalDetails
  isLoadingApproval: boolean
}

export function ApprovalInformationDetails({
  signatoryMatrixEnabled,
  approvalDetails,
  isLoadingApproval,
}: ApprovalInformationDetailsProps) {
  if (!signatoryMatrixEnabled) {
    return null
  }

  const renderApprover = (approver: IApprover) => (
    <p className="mb-1" key={approver.approverId}>
      {approver.approverEmail || approver.approverDisplayName || "N/A"}
      {approver.approverLevel && (
        <span className="text-muted-foreground">
          {" "}
          ({approver.approverLevel.replace("Level", "")})
        </span>
      )}
      <span className="block">{formatDateTimeUTC(approver.approvedAt)}</span>
    </p>
  )

  return (
    <div className="mt-4 space-y-1 rounded-2xl bg-muted p-4">
      {isLoadingApproval ? (
        <div className="flex justify-center py-4">
          <LoadingSpinner size="6" />
        </div>
      ) : approvalDetails ? (
        <>
          <PaymentDetailRow
            label="Minimum signatories required"
            value={
              approvalDetails?.signatoryAmountBand?.signatoryRules && (
                <SignatoryRules
                  rules={approvalDetails?.signatoryAmountBand?.signatoryRules}
                />
              )
            }
          />
          <PaymentDetailRow
            label="Approved by"
            value={
              approvalDetails?.approvals && approvalDetails.approvals.length > 0
                ? approvalDetails.approvals.map((approval: IApprover) =>
                    renderApprover(approval),
                  )
                : "None"
            }
          />
        </>
      ) : (
        <>
          <PaymentDetailRow label="Minimum signatories required" value="N/A" />
          <PaymentDetailRow label="Approved by" value="None" />
        </>
      )}
    </div>
  )
}

export const SignatoryRules = ({
  rules,
}: {
  rules?: ISignatoryRule[]
}): ReactNode => {
  if (!rules || rules.length === 0) {
    return (
      <div className="flex items-center space-x-2">
        <span className="rounded-full border bg-white px-2 py-0.5 text-sm font-medium">
          1C
        </span>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-1">
      {rules.map((rule: ISignatoryRule, index) => {
        const levelCode = rule.approverLevel.replace("Level", "")
        let label = ""

        if (levelCode === "B") {
          label =
            rule.requiredCount === 1
              ? `1B`
              : `${rule.requiredCount}Bs or higher`
        } else if (levelCode === "C") {
          label =
            rule.requiredCount === 1
              ? `1C`
              : `${rule.requiredCount}Cs or higher`
        } else {
          label = `${rule.requiredCount}${levelCode}`
        }

        return (
          <div key={index}>
            {index > 0 && <span className="mr-1 text-lg font-bold">+</span>}
            <span
              className="rounded-lg border bg-white px-2 py-0.5 text-sm font-medium"
              key={index}
            >
              {label}
            </span>
          </div>
        )
      })}
    </div>
  )
}
