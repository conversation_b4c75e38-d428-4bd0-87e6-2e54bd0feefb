import { useState } from "react"
import { ArrowUpRight } from "lucide-react"

import {
  usePaymentApprovalDetailsQuery,
  useValidateSignatoryAmount,
} from "@/data/payments/payments.query"
import { IPaymentDetailsBackend } from "@/data/payments/payments.interface"

import { PaymentInformationDetails } from "./PaymentInformationDetails"
import { MainPaymentDetails } from "./MainPaymentDetails"
import { ApprovalInformationDetails } from "./ApprovalInformationDetails"
import { RejectPaymentModal } from "../RejectPaymentModal"
import { RejectForEditModal } from "../RejectForEditModal"
import PaymentStatusBadge from "../PaymentStatusBadge"

export interface PaymentDetailSheetBaseProps {
  payment?: IPaymentDetailsBackend
  entityId: string
  children?: React.ReactNode
}

export function PaymentDetailSheetBase({
  payment,
  children,
}: PaymentDetailSheetBaseProps) {
  const [rejectForEditModalOpen, setRejectForEditModalOpen] = useState(false)
  const [rejectModalOpen, setRejectModalOpen] = useState(false)

  const { data: approvalDetails, isLoading: isLoadingApproval } =
    usePaymentApprovalDetailsQuery(payment?.id || "")

  const { data: signatoryValidation } = useValidateSignatoryAmount(
    Number(payment?.amount),
    payment?.currency as string,
  )

  if (!payment) return null

  const status = payment?.currentStatus || "N/A"
  const amount = payment?.remitterSentAmount
  const currency = payment?.remitterCurrency || "N/A"
  const reference = payment?.reference || "N/A"
  const purpose = payment?.purpose || "N/A"
  const isFx =
    payment.currency !== payment.remitterCurrency && !!payment.remitterCurrency

  return (
    <>
      <div className="mt-8 flex items-center gap-3">
        <ArrowUpRight className="h-6 w-6 rounded-full bg-gray-100 p-1" />
        <span className="text-lg font-medium">Payment out</span>
        <div className="ml-auto flex items-center">
          <PaymentStatusBadge status={status} />
        </div>
      </div>

      {/* Child components for FX/Non-FX specific controls */}
      {children}

      {/* Main Payment Details */}
      <MainPaymentDetails
        amount={amount}
        currency={currency}
        isFx={isFx}
        payment={payment}
      />

      {/* Payment Information */}
      <PaymentInformationDetails
        approvalDetails={approvalDetails}
        payment={payment}
        purpose={purpose}
        reference={reference}
        signatoryMatrixEnabled={
          signatoryValidation?.signatoryMatrixEnabled || false
        }
      />

      {/* Approval Information */}
      <ApprovalInformationDetails
        approvalDetails={approvalDetails}
        isLoadingApproval={isLoadingApproval}
        signatoryMatrixEnabled={signatoryValidation?.signatoryMatrixEnabled}
      />

      {/* Modals for rejection */}
      <RejectForEditModal
        onOpenChange={setRejectForEditModalOpen}
        open={rejectForEditModalOpen}
        paymentId={payment?.id}
      />

      <RejectPaymentModal
        onOpenChange={setRejectModalOpen}
        open={rejectModalOpen}
        paymentId={payment?.id}
      />
    </>
  )
}
