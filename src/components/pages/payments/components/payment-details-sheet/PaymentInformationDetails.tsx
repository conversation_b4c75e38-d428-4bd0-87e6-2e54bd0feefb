import { format } from "date-fns"
import { Link } from "@tanstack/react-router"

import { formatDateTimeUTC } from "@/lib/date.utils"
import {
  IApprover,
  IPaymentApprovalDetails,
  IPaymentDetailsBackend,
} from "@/data/payments/payments.interface"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

import { PaymentDetailRow } from "./PaymentDetailRow"
interface PaymentInformationDetailsProps {
  payment?: IPaymentDetailsBackend
  approvalDetails?: IPaymentApprovalDetails
  reference: string
  purpose: string
  signatoryMatrixEnabled: boolean
}

function Approver({
  approver,
  signatoryMatrixEnabled,
}: {
  approver: any
  signatoryMatrixEnabled: boolean
}) {
  return (
    <div className="text-right">
      <p>
        {approver.userEmail || approver.userDisplayName || "N/A"}
        {approver.approverLevel && signatoryMatrixEnabled && (
          <span className="text-muted-foreground">
            {` (${approver.approverLevel.replace("Level", "")})`}
          </span>
        )}
      </p>
      <p>{approver.approvedAt && formatDateTimeUTC(approver.approvedAt)}</p>
    </div>
  )
}

export function PaymentInformationDetails({
  payment,
  approvalDetails,
  reference,
  purpose,
  signatoryMatrixEnabled,
}: PaymentInformationDetailsProps) {
  const { entity } = useLoaderData()
  return (
    <div className="mt-4 space-y-1 rounded-2xl bg-muted p-4">
      <PaymentDetailRow label="Payment code" value={payment?.code} />
      {payment?.fxTrade && (
        <PaymentDetailRow
          label="Related trade code"
          value={
            <Link
              className="text-primary underline hover:text-primary/80"
              params={{ entityId: entity.id! }}
              search={{ tradeId: payment?.fxTrade?.id }}
              to="/$entityId/trades"
            >
              {payment?.fxTrade?.code}
            </Link>
          }
        />
      )}
      <PaymentDetailRow
        label="Created by"
        value={
          approvalDetails?.submittedBy?.displayName ||
          approvalDetails?.submittedBy?.email ||
          "N/A"
        }
      />
      <PaymentDetailRow
        label="Created on"
        value={
          payment?.createdAt
            ? format(new Date(payment?.createdAt), "d MMM yyyy HH:mm")
            : "N/A"
        }
      />

      <PaymentDetailRow
        label="Payment type"
        value={payment?.type.toLocaleUpperCase()}
      />

      <PaymentDetailRow label="Payment reference" value={reference} />

      <PaymentDetailRow label="Purpose" value={purpose} />
      {payment?.feeOption && (
        <PaymentDetailRow label="Fee option" value={payment.feeOption} />
      )}
      {signatoryMatrixEnabled == false && (
        <PaymentDetailRow
          label="Approved by"
          value={
            <div key="approvers">
              {approvalDetails?.approvals.map(
                (approver: IApprover, index: number) => (
                  <Approver
                    approver={approver}
                    key={`approver-${index}`}
                    signatoryMatrixEnabled={signatoryMatrixEnabled}
                  />
                ),
              )}
            </div>
          }
        />
      )}
    </div>
  )
}
