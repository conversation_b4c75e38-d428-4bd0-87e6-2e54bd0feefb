import { useMemo, useState } from "react"
import { useNavigate } from "@tanstack/react-router"
import { useQueryClient } from "@tanstack/react-query"

import { ROLES } from "@/lib/constants/roles.constants"
import { queryKeys } from "@/lib/constants/query.constants"
import { useAuth } from "@/hooks/use-auth"
import {
  useDeleteDraftPaymentQuery,
  usePaymentApprovalDetailsQuery,
  useValidateSignatoryAmount,
} from "@/data/payments/payments.query"
import {
  approvePaymentMutation,
  approvePaymentWithFxEmbeddedMutation,
  submitPaymentWithVerificationMutation,
} from "@/data/payments/payments.mutation"
import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"

import {
  PaymentDetailSheetBase,
  PaymentDetailSheetBaseProps,
} from "./PaymentDetailSheetBase"
import { RejectPaymentModal } from "../RejectPaymentModal"
import { RejectForEditModal } from "../RejectForEditModal"
import { FxPaymentControls } from "../payment-controls/FxPaymentControls"
import { RateQuoteModal } from "../../../send-payments/components/modals/RateQuoteModal"
import { RateConfirmationModal } from "../../../send-payments/components/modals/rateConfirmationModal"

export function FxPaymentDetailSheet({
  payment,
  entityId,
}: PaymentDetailSheetBaseProps) {
  const [showRateConfirmation, setShowRateConfirmation] = useState(false)
  const [showRateQuote, setShowRateQuote] = useState(false)
  const [rejectForEditModalOpen, setRejectForEditModalOpen] = useState(false)
  const [rejectModalOpen, setRejectModalOpen] = useState(false)

  const navigate = useNavigate()
  const queryClient = useQueryClient()

  const { data: approvalDetails } = usePaymentApprovalDetailsQuery(
    payment?.id || "",
  )
  const { data: signatoryValidation } = useValidateSignatoryAmount(
    Number(payment?.amount),
    payment?.currency as string,
  )

  const { mutateAsync: approvePayment } = approvePaymentMutation()
  const { mutateAsync: approvePaymentWithFxEmbedded } =
    approvePaymentWithFxEmbeddedMutation()

  const { mutateAsync: submitPayment } = submitPaymentWithVerificationMutation()

  const auth = useAuth()

  const isSelfApprover = signatoryValidation?.isSelfApproved

  const isAlreadyApproved = approvalDetails?.approvals.some(
    (approver) => approver.approverId === auth.user?.user_id,
  )

  const cantApproveMine =
    !signatoryValidation?.canApproveOwnSubmission &&
    approvalDetails?.submittedBy.id === auth.user?.user_id

  const { data: entityAccess } = useEntityAccessQuery(entityId)
  const { entity } = useLoaderData()
  const _entityAccess = useMemo(() => {
    if (!entityAccess) return null
    if (!entity.id) return null
    return entityAccess?.find(
      (access) => access.entityId?.toLowerCase() === entity.id?.toLowerCase(),
    )
  }, [entityAccess, entity])

  const isApproveButtonVisible = useMemo(() => {
    if (!payment) return false
    if (!_entityAccess) return false
    if (
      [ROLES.APPROVER, ROLES.ADMINISTRATOR].some((e) =>
        _entityAccess?.roles?.includes(e),
      )
    )
      return true
    return false
  }, [_entityAccess, payment])

  const startFxRateAndVerificationProcess = () => {
    setOperationType("approve")
    verifyAndProcessFxPayment()
  }

  const initiateApprovalProcess = () => {
    setOperationType("approve")
    verifyAndProcessFxPayment()
  }

  const submitFxDraftPayment = () => {
    if (
      payment?.currentStatus === "Draft" &&
      !approvalDetails?.isFinalApprover
    ) {
      setOperationType("submit")
      verifyAndProcessFxPayment()
    }
  }

  const [, setOperationType] = useState<"submit" | "approve" | null>(null)

  const verifyAndProcessFxPayment = async () => {
    const dontShowAgain = JSON.parse(
      localStorage.getItem("dontShowRateConfirmationModal") || "false",
    )
    if (
      payment?.currentStatus === "Pending Signatory Approval" &&
      payment.currency !== payment.remitterCurrency
    ) {
      if (dontShowAgain) {
        setShowRateConfirmation(true)
        return
      }

      if (approvalDetails?.isFinalApprover) {
        proceedToFxRateQuote()
        return
      }

      approvePayment(
        {
          paymentId: payment?.id || "",
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({
              queryKey: [queryKeys.payment.byId, payment?.id],
            })
            queryClient.invalidateQueries({
              queryKey: [queryKeys.payment.approvalDetails, payment?.id],
            })
            navigate({
              to: "/$entityId/payments",
              params: { entityId },
            })
          },
        },
      )
      return
    }

    if (
      (payment?.currentStatus === "Draft" && isSelfApprover) ||
      (approvalDetails?.isFinalApprover &&
        payment?.currency !== payment?.remitterCurrency)
    ) {
      if (!dontShowAgain) {
        setShowRateConfirmation(true)
      }

      submitPayment(
        {
          paymentId: payment?.id || "",
        },
        {
          onSuccess: () => {
            if (approvalDetails?.isFinalApprover) {
              approvePayment(
                {
                  paymentId: payment?.id || "",
                },
                {
                  onSuccess: () => {
                    queryClient.invalidateQueries({
                      queryKey: [queryKeys.payment.byId, payment?.id],
                    })
                    queryClient.invalidateQueries({
                      queryKey: [
                        queryKeys.payment.approvalDetails,
                        payment?.id,
                      ],
                    })
                    navigate({
                      to: "/$entityId/payments",
                      params: { entityId },
                    })
                  },
                },
              )
            }
          },
        },
      )
      return
    }

    if (payment?.currentStatus === "Draft") {
      submitPayment(
        {
          paymentId: payment?.id || "",
        },
        {
          onSuccess: () => {
            if (approvalDetails?.isFinalApprover) {
              approvePayment(
                {
                  paymentId: payment?.id || "",
                },
                {
                  onSuccess: () => {
                    queryClient.invalidateQueries({
                      queryKey: [queryKeys.payment.byId, payment?.id],
                    })
                    queryClient.invalidateQueries({
                      queryKey: [
                        queryKeys.payment.approvalDetails,
                        payment?.id,
                      ],
                    })
                    navigate({
                      to: "/$entityId/payments",
                      params: { entityId },
                    })
                  },
                },
              )
            }
          },
        },
      )
    } else {
      approvePayment(
        {
          paymentId: payment?.id || "",
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({
              queryKey: [queryKeys.payment.byId, payment?.id],
            })
            queryClient.invalidateQueries({
              queryKey: [queryKeys.payment.approvalDetails, payment?.id],
            })
            navigate({ to: "/$entityId/payments", params: { entityId } })
          },
        },
      )
    }
  }

  const proceedToFxRateQuote = () => {
    setShowRateConfirmation(false)
    setShowRateQuote(true)
    if (
      payment?.currentStatus === "Draft" &&
      !approvalDetails?.isFinalApprover
    ) {
      submitPayment({
        paymentId: payment?.id || "",
      })
      return
    }
  }

  const cancelFxRateQuote = () => {
    setShowRateQuote(false)
  }

  const { mutate: deletePayment } = useDeleteDraftPaymentQuery(
    payment?.id || "",
  )

  const deleteFxDraftPayment = () => {
    deletePayment()
  }

  const acceptFxRateQuote = (
    fxQuoteRequestId: string,
    fxBrokeredQuoteId: string,
  ) => {
    setShowRateQuote(false)
    approvePaymentWithFxEmbedded(
      {
        paymentId: payment?.id || "",
        request: {
          fxBrokeredQuoteId: fxBrokeredQuoteId,
          fxQuoteRequestId: fxQuoteRequestId,
        },
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [queryKeys.payment.byId, payment?.id],
          })
          queryClient.invalidateQueries({
            queryKey: [queryKeys.payment.approvalDetails, payment?.id],
          })
          navigate({ to: "/$entityId/payments", params: { entityId } })
        },
      },
    )
  }

  const editOrRevertFxPayment = async () => {
    if (payment?.currentStatus === "Draft") {
      navigate({
        to: "/$entityId/payments/send",
        params: { entityId },
        search: { paymentId: payment.id },
      })
    } else {
      setRejectForEditModalOpen(true)
    }
  }

  const openFxCancellationModal = () => {
    setRejectModalOpen(true)
  }

  return (
    <>
      <PaymentDetailSheetBase entityId={entityId} payment={payment}>
        {payment && (
          <FxPaymentControls
            approvalDetails={approvalDetails}
            cantApproveMine={cantApproveMine}
            isAlreadyApproved={isAlreadyApproved}
            isApproveButtonVisible={isApproveButtonVisible}
            isSelfApprover={isSelfApprover}
            onApprove={initiateApprovalProcess}
            onDeleteDraft={deleteFxDraftPayment}
            onDraft={submitFxDraftPayment}
            onGetRateAndApprove={startFxRateAndVerificationProcess}
            onReject={openFxCancellationModal}
            onRejectForEdit={editOrRevertFxPayment}
            payment={payment}
            signatoryValidation={signatoryValidation}
          />
        )}
      </PaymentDetailSheetBase>

      <RateConfirmationModal
        isOpen={showRateConfirmation}
        onClose={() => setShowRateConfirmation(false)}
        onConfirm={proceedToFxRateQuote}
        onDontShowAgainChange={(dontShow: boolean) => {
          localStorage.setItem(
            "dontShowRateConfirmationModal",
            dontShow.toString(),
          )
        }}
      />

      {/* Ensure payment properties are defined before passing to RateQuoteModal */}
      {payment && (
        <RateQuoteModal
          currency={payment.remitterCurrency || ""}
          entityId={entityId}
          onCancel={cancelFxRateQuote}
          onConfirm={acceptFxRateQuote}
          onOpenChange={setShowRateQuote}
          open={showRateQuote}
          receiveAmount={payment.amount || 0}
          receiveCurrency={payment.currency || ""}
          sendAmount={payment.remitterSentAmount || 0}
        />
      )}

      <RejectForEditModal
        onOpenChange={setRejectForEditModalOpen}
        open={rejectForEditModalOpen}
        paymentId={payment?.id}
      />

      <RejectPaymentModal
        onOpenChange={setRejectModalOpen}
        open={rejectModalOpen}
        paymentId={payment?.id}
      />
    </>
  )
}
