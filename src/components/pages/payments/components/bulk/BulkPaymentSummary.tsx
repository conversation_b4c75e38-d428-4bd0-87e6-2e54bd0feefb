import { useMemo } from "react"
import { ChevronDownIcon } from "lucide-react"
import { Link } from "@tanstack/react-router"
import { InfoCircledIcon } from "@radix-ui/react-icons"

import { formatDate, formatDateTimeUTC } from "@/lib/date.utils"
import {
  IApprover,
  IBulkPaymentDetails,
} from "@/data/payments/payments.interface"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { TransactionDetailRow } from "@/components/pages/transactions/components/TransactionDetailRow"
import { SignatoryRules } from "@/components/pages/payments/components/payment-details-sheet/ApprovalInformationDetails"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { GetEntityResponseDto } from "@/client/onboarding/types.gen"

export function BulkPaymentSummary({
  data,
  entity,
}: {
  data: IBulkPaymentDetails
  entity: GetEntityResponseDto
}) {
  // Calculate payment status counts
  const statusCounts = useMemo(() => {
    if (!data?.bulkPaymentStatuses) return null

    const counts = {
      sent: 0,
      pending: 0,
      cancelled: 0,
      error: 0,
      total: data.totalRows || 0,
    }

    data.bulkPaymentStatuses.forEach((status) => {
      if (["Sent", "Processed", "Completed"].includes(status.status)) {
        counts.sent += status.count
      } else if (
        [
          "PendingApproval",
          "ValidationError",
          "Validating",
          "ReadyForReview",
          "ReadyToSubmission",
        ].includes(status.status)
      ) {
        counts.pending += status.count
      } else if (["Cancelled"].includes(status.status)) {
        counts.cancelled += status.count
      } else if (["Failed", "Error"].includes(status.status)) {
        counts.error += status.count
      }
    })

    return counts
  }, [data])

  const renderApprover = (approver: IApprover) => (
    <p className="mb-1" key={approver.approverId}>
      {approver.approverEmail || approver.approverDisplayName || "N/A"}
      {approver.approverLevel && (
        <span className="text-muted-foreground">
          {" "}
          ({approver.approverLevel.replace("Level", "")})
        </span>
      )}
      <span className="block">{formatDateTimeUTC(approver.approvedAt)}</span>
    </p>
  )

  return (
    <div className="mt-4 space-y-3 rounded-2xl bg-muted/80 p-4">
      <div className="flex flex-col gap-1">
        <div className="text-2xl font-semibold text-foreground">
          {data.totalAmount && (
            <CurrencyText
              amount={data.totalAmount}
              currency={data.totalAmountCurrency}
            />
          )}
        </div>
        {/* Collapsible estimate info */}
        <Collapsible>
          <CollapsibleTrigger asChild>
            <button
              className="flex items-center gap-1 text-xs font-medium text-foreground focus:outline-none"
              type="button"
            >
              <span className="inline-flex items-center gap-1">
                <InfoCircledIcon className="h-3 w-3" />
                This is an estimate
              </span>
              <ChevronDownIcon className="h-3 w-3 transition-transform data-[state=open]:rotate-180" />
            </button>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="mt-1 text-xs text-muted-foreground">
              The amount is based on the indicative rate at the time of
              submission of all currencies converted to GBP. The amount excludes
              fees.
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
      <div className="space-y-2">
        <TransactionDetailRow
          label="Payment file"
          value={
            <div className="flex flex-col items-end">
              <span className="max-w-[160px] truncate">{data.fileName}</span>
              <Link
                params={{
                  entityId: entity.id!,
                  bulkPaymentId: data.id,
                }}
                to="/$entityId/payments/bulk-payments/$bulkPaymentId"
              >
                <Button className="px-0" size="sm" variant="link">
                  View all payments
                </Button>
              </Link>
            </div>
          }
        />

        <TransactionDetailRow
          label="Total number of payments"
          value={statusCounts?.total?.toLocaleString() || "0"}
        />
        <TransactionDetailRow label="Created by" value={data.createdBy.email} />
        <TransactionDetailRow
          label="Created at"
          value={formatDate(data.createdAt)}
        />

        <TransactionDetailRow
          label="Minimum signatories required"
          value={
            data.signatoryAmountBand?.signatoryRules && (
              <SignatoryRules
                rules={data.signatoryAmountBand?.signatoryRules}
              />
            )
          }
        />

        <TransactionDetailRow
          label="Approved by"
          value={
            data.approvals && data.approvals.length > 0
              ? data.approvals.map((approval: IApprover) =>
                  renderApprover(approval),
                )
              : "None"
          }
        />
      </div>
    </div>
  )
}
