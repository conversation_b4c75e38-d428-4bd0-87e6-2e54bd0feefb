import { useMemo, useState } from "react"
import { Link, useNavigate } from "@tanstack/react-router"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import { useAuth0 } from "@auth0/auth0-react"

import { formatDate } from "@/lib/date.utils"
import { usePaymentApprovalDetailsQuery } from "@/data/payments/payments.query"
import {
  approveBulkPaymentMutation,
  bulkPaymentCancelMutation,
} from "@/data/payments/payments.mutation"
import { IBulkPaymentSummary } from "@/data/payments/payments.interface"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { BulkAccountSummaryTable } from "@/components/pages/bulk-payment/components/BulkAccountSummaryTable"
import { TrailingSpinner } from "@/components/base/loading-spinner/TrailingSpinner"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { AlertBox } from "@/components/base/alert-box"

interface BulkPaymentApprovePageProps {
  summary: IBulkPaymentSummary
  entityId: string
  bulkPaymentId: string
}

export function BulkPaymentApprovePage({
  summary,
  entityId,
  bulkPaymentId,
}: BulkPaymentApprovePageProps) {
  const navigate = useNavigate()
  const { data: approvalDetails } =
    usePaymentApprovalDetailsQuery(bulkPaymentId)

  const [dialogType, setDialogType] = useState<"approve" | "reject" | null>(
    null,
  )

  const { mutate: approveBulkPayment, isPending: isApproving } =
    approveBulkPaymentMutation()
  const { mutate: rejectBulkPayment, isPending: isRejecting } =
    bulkPaymentCancelMutation()

  const { user } = useAuth0()

  const isUserApprover = useMemo(() => {
    return summary?.approvers.some(
      (approver) => approver.approverEmail === user?.email,
    )
  }, [summary, user])

  const handleApprove = () => {
    setDialogType("approve")
  }

  const handleReject = () => {
    setDialogType("reject")
  }

  const handleConfirm = () => {
    if (dialogType === "approve") {
      approveBulkPayment(bulkPaymentId, {
        onSuccess: () => {
          setDialogType(null)
          navigate({
            to: "/$entityId/payments",
            params: { entityId },
            search: {
              tab: "bulk",
            },
            replace: true,
          })
        },
      })
    } else {
      rejectBulkPayment(bulkPaymentId, {
        onSuccess: () => {
          setDialogType(null)
          navigate({
            to: "/$entityId/payments",
            params: { entityId },
            search: {
              tab: "bulk",
            },
            replace: true,
          })
        },
      })
    }
  }

  return (
    <div className="content-container flex flex-col gap-4">
      <div>
        <h1 className="text-2xl font-semibold">Review bulk payment details</h1>
        <p className="pt-2 text-base font-light text-muted-foreground">
          Please review the summary of payments below for approval
        </p>
      </div>

      {/* File details */}
      <Card className="max-w-xl p-4">
        <Row label="File Name" value={summary.fileName} />
        <Row label="Submitted by" value={summary.createdBy?.email} />
        <Row label="Submitted on" value={formatDate(summary.createdAt)} />
        <Row
          label="Total number of payments"
          value={
            <div className="flex items-center gap-2">
              <span>{summary.totalPayments.toLocaleString()}</span>
              <Link
                params={{
                  entityId,
                  bulkPaymentId,
                }}
                to="/$entityId/payments/bulk-payments/$bulkPaymentId"
              >
                <Button className="p-0" size="sm" variant="link">
                  View all payments
                </Button>
              </Link>
            </div>
          }
        />
        <Row
          label="Total sum in GBP (excluding fees)"
          value={
            summary.totalAmount ? (
              <CurrencyText
                amount={summary.totalAmount}
                currency={summary.totalCurrency}
              />
            ) : (
              "N/A"
            )
          }
        />
      </Card>

      <BulkAccountSummaryTable accountSummaries={summary.accountSummaries} />

      {/* Signatories section */}
      <Card className="max-w-xl p-4">
        <Row
          label="Required signatories"
          value={
            summary.signatoryAmountBand?.signatoryRules.length ? (
              <div className="text-sm">
                {summary.signatoryAmountBand?.signatoryRules.map((rule) => (
                  <div key={rule.id}>
                    Any {rule.approverLevel} signatory ({rule.requiredCount})
                  </div>
                ))}
              </div>
            ) : (
              "Not required"
            )
          }
        />

        <Row
          label="Completed signatories"
          value={
            summary.approvers.length > 0 ? (
              <div className="text-sm">
                {summary.approvers.map((approver, index) => (
                  <div key={index}>
                    {approver.approverEmail} ({approver.approverLevel})
                  </div>
                ))}
              </div>
            ) : (
              "Not approved yet"
            )
          }
        />
      </Card>

      {isUserApprover ? (
        <AlertBox className="max-w-xl" severity="info">
          <div>
            You have already approved this bulk payment.
            <p>
              <Link
                params={{
                  entityId,
                  bulkPaymentId,
                }}
                to="/$entityId/payments/bulk-payments/$bulkPaymentId"
              >
                <Button className="p-0" size="sm" variant="link">
                  View all payments
                </Button>
              </Link>
            </p>
          </div>
        </AlertBox>
      ) : (
        <div className="mt-4 flex gap-4">
          <TrailingSpinner isLoading={isApproving || isRejecting}>
            <Button
              disabled={
                isApproving || isRejecting || !approvalDetails?.canApprove
              }
              onClick={handleApprove}
            >
              Approve bulk payment
            </Button>
          </TrailingSpinner>
          <TrailingSpinner isLoading={isRejecting || isApproving}>
            <Button
              disabled={isApproving || isRejecting}
              onClick={handleReject}
              variant="destructive-outline"
            >
              Reject and cancel
            </Button>
          </TrailingSpinner>
        </div>
      )}

      <Dialog onOpenChange={() => setDialogType(null)} open={!!dialogType}>
        <DialogContent>
          <VisuallyHidden>
            <DialogHeader>
              <DialogTitle>
                {dialogType === "approve"
                  ? "Confirm Approval"
                  : "Confirm Rejection"}
              </DialogTitle>
              <DialogDescription>
                Are you sure you want to{" "}
                {dialogType === "approve" ? "approve" : "reject and cancel"}{" "}
                this bulk payment?
              </DialogDescription>
            </DialogHeader>
          </VisuallyHidden>
          <AlertBox className="mt-4" severity="warning">
            {dialogType === "approve" ? (
              <p>Are you sure you want to approve this bulk payment?</p>
            ) : (
              <p>
                Are you sure you want to reject and cancel this bulk payment?
              </p>
            )}
          </AlertBox>
          <DialogFooter className="mt-4 flex justify-start gap-2">
            <Button onClick={() => setDialogType(null)} variant="link">
              Cancel
            </Button>
            <Button
              disabled={isApproving || isRejecting}
              onClick={handleConfirm}
              variant={dialogType === "approve" ? "default" : "destructive"}
            >
              {dialogType === "approve"
                ? isApproving
                  ? "Approving..."
                  : "Confirm Approval"
                : isRejecting
                  ? "Rejecting..."
                  : "Confirm Rejection"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

function Row({
  label,
  value,
}: {
  label: string
  value: string | React.ReactNode
}) {
  return (
    <div className="flex justify-between border-b border-dotted py-2">
      <span className="flex-none text-sm font-light text-foreground/80">
        {label}
      </span>
      {typeof value === "string" ? (
        <p className="text-right text-sm" role="paragraph">
          {value}
        </p>
      ) : (
        <div className="text-right text-sm">{value}</div>
      )}
    </div>
  )
}
