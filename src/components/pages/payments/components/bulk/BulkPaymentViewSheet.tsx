import { useMemo, useState } from "react"
import { LayersIcon } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import {
  useBulkPaymentByIdQuery,
  useBulkPaymentSummaryQuery,
} from "@/data/payments/payments.query"
import { bulkPaymentCancelMutation } from "@/data/payments/payments.mutation"
import {} from "@/data/payments/payments.interface"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet"
import { CancelPaymentFileDialog } from "@/components/pages/bulk-payment/components/CancelPaymentFileDialog"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

import { BulkPaymentSummary } from "./BulkPaymentSummary"
import { BulkPaymentPaymentsDetails } from "./BulkPaymentPaymentsDetails"
import { BulkPaymentActions } from "./BulkPaymentActions"
import { PaymentStatusBadge } from "../payment-table/PaymentStatusCell"

interface BulkPaymentViewSheetProps {
  id?: string
  entityId?: string
  onClose?: () => void
}

export function BulkPaymentViewSheet({
  id,
  onClose,
}: BulkPaymentViewSheetProps) {
  const { data, isLoading } = useBulkPaymentByIdQuery(id)
  const { entity } = useLoaderData()

  const { mutate: cancelBulkPayment } = bulkPaymentCancelMutation()
  const [cancelBulkPaymentId, setCancelBulkPaymentId] = useState<
    string | undefined
  >()

  const totalOutboundPayments = useMemo(() => {
    if (!data) return 0
    return data.outboundPaymentStatuses.reduce(
      (acc, curr) => acc + curr.count,
      0,
    )
  }, [data])

  const { data: summary } = useBulkPaymentSummaryQuery({
    bulkUploadId: data?.id,
    enabled: !!totalOutboundPayments,
  })

  const handleCancelFile = () => {
    if (cancelBulkPaymentId) {
      cancelBulkPayment(cancelBulkPaymentId, {
        onSuccess: () => {
          setCancelBulkPaymentId(undefined)
        },
      })
    }
  }

  return (
    <>
      <CancelPaymentFileDialog
        onConfirm={handleCancelFile}
        onOpenChange={() => setCancelBulkPaymentId(undefined)}
        open={!!cancelBulkPaymentId}
      />
      <Sheet
        onOpenChange={(open) => {
          if (!open) {
            onClose?.()
          }
        }}
        open={!!id}
      >
        <VisuallyHidden>
          <SheetTitle>Bulk payment details</SheetTitle>
          <SheetDescription>
            View the details of a bulk payment.
          </SheetDescription>
        </VisuallyHidden>
        <SheetContent className="w-[400px] sm:w-[540px]">
          {isLoading && (
            <div className="mt-1 flex h-96 w-full items-center justify-center">
              <LoadingSpinner size="8" />
            </div>
          )}
          {data && (
            <div className="space-y-4">
              <div className="my-3 flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <LayersIcon
                    className={`m-3 h-10 w-10 rounded-full bg-gray-100 p-1.5`}
                  />
                  <span className="text-lg font-medium">Bulk payment</span>
                </div>
                <PaymentStatusBadge
                  paymentType="BulkPayment"
                  status={data.currentStatus}
                />
              </div>
              {data && (
                <BulkPaymentActions
                  data={data}
                  entity={entity}
                  onCancel={() => setCancelBulkPaymentId?.(data.id)}
                  onConfirm={() => {
                    onClose?.()
                  }}
                  summary={summary}
                />
              )}

              <BulkPaymentSummary data={data} entity={entity} />

              {summary && <BulkPaymentPaymentsDetails summary={summary} />}
            </div>
          )}
        </SheetContent>
      </Sheet>
    </>
  )
}
