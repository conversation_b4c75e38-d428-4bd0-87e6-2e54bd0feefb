import { AlertTriangle } from "lucide-react"
import { ChevronDown } from "lucide-react"

import { IBulkPaymentSummary } from "@/data/payments/payments.interface"
import { IAccountSummary } from "@/data/payments/payments.interface"
import {
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Table,
  TableBody,
} from "@/components/ui/table"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyBadge } from "@/components/base/currency/CurrencyBadge"

export function BulkPaymentPaymentsDetails({
  summary,
}: {
  summary: IBulkPaymentSummary
}) {
  return (
    <Collapsible
      className="mt-4 space-y-3 rounded-2xl bg-muted/80 p-4"
      defaultOpen
    >
      <CollapsibleTrigger className="group flex w-full items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold">Account Summaries</h3>
        </div>
        <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200 group-data-[state=open]:rotate-180" />
      </CollapsibleTrigger>
      <CollapsibleContent>
        {summary && (
          <BulkPaymentAccountSummaryTable
            accountSummaries={summary.accountSummaries}
          />
        )}
      </CollapsibleContent>
    </Collapsible>
  )
}

function BulkPaymentAccountSummaryTable({
  accountSummaries,
}: {
  accountSummaries: IAccountSummary[]
}) {
  return (
    <div className="text-sm">
      {accountSummaries.map((account) => (
        <div className="mt-4" key={account.virtualIban}>
          <div className="flex items-center gap-2">
            <h2 className="font-semibold">{account.displayName}</h2>
            <span className="text-muted-foreground">{account.virtualIban}</span>
          </div>
          <Table className="-mx-2 text-xs">
            <TableHeader>
              <TableRow className="border-b text-muted-foreground">
                <TableHead>Currency</TableHead>
                <TableHead>Payment amount</TableHead>
                <TableHead>Payments</TableHead>
                <TableHead>Available balance</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {account.currencyAccounts.map((_account) => (
                <TableRow className="" key={_account.currency}>
                  <TableCell>
                    <div className="-ms-2 flex items-center gap-2">
                      <CurrencyBadge currency={_account.currency} />
                    </div>
                  </TableCell>

                  <TableCell>
                    <CurrencyText
                      amount={_account.paymentsAmount}
                      currency={_account.currency}
                    />
                  </TableCell>
                  <TableCell>
                    {_account.paymentsCount.toLocaleString()}
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center justify-end gap-1">
                      <CurrencyText
                        amount={_account.availableBalance}
                        currency={_account.currency}
                      />
                      {_account.availableBalance < _account.paymentsAmount && (
                        <AlertTriangle className="h-3 w-3 text-destructive" />
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ))}
    </div>
  )
}
