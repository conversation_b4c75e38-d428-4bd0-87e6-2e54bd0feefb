import { useMemo } from "react"
import { ArrowUpIcon, LayersIcon } from "lucide-react"
import { format } from "date-fns"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { IOutboundPayment } from "@/data/payments/payments.interface"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

// Helper component for displaying detail rows
function TransactionDetailRow({
  label,
  value,
  secondaryValue,
}: {
  label: string
  value: React.ReactNode
  secondaryValue?: string
}) {
  return (
    <div className="flex justify-between border-b pb-2">
      <span className="flex-none text-sm font-light text-foreground/60">
        {label}
      </span>
      {typeof value === "string" ? (
        <p className="text-right text-sm" role="paragraph">
          {value}
          {secondaryValue && (
            <span className="text-sm text-foreground/60">{secondaryValue}</span>
          )}
        </p>
      ) : (
        <div className="text-right text-sm">{value}</div>
      )}
    </div>
  )
}

interface BulkPaymentRowDetailSheetProps {
  payment?: IOutboundPayment
  onClose?: () => void
}

export function BulkPaymentRowDetailSheet({
  payment,
  onClose,
}: BulkPaymentRowDetailSheetProps) {
  // Format payment date
  const formattedDate = useMemo(() => {
    if (!payment?.valueDate) return "N/A"
    return format(new Date(payment.valueDate), "dd MMM yyyy")
  }, [payment])

  // Format account number for display
  const formatAccountDisplay = (
    account: string | null | undefined,
    name: string,
  ) => {
    if (!account) return name
    return `${name} (${account.substring(0, 4)})`
  }

  return (
    <Sheet
      onOpenChange={(open) => {
        if (!open) {
          onClose?.()
        }
      }}
      open={!!payment}
    >
      <VisuallyHidden>
        <SheetTitle>Payment details</SheetTitle>
        <SheetDescription>
          View the details of a payment in the bulk payment.
        </SheetDescription>
      </VisuallyHidden>
      <SheetContent className="w-[400px] sm:w-[540px]">
        {payment && (
          <>
            <div className="mb-6 mt-3 flex items-start">
              <div className="flex gap-2">
                <div className="flex h-14 w-14 items-center justify-center rounded-full bg-gray-100">
                  <ArrowUpIcon className="h-6 w-6 text-gray-500" />
                </div>
                <div className="flex h-14 w-14 items-center justify-center rounded-full bg-gray-100">
                  <LayersIcon className="h-6 w-6 text-gray-500" />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h2 className="text-xl font-medium text-gray-700">Payment out</h2>
              <div className="mt-2 text-3xl font-semibold">
                <div className="flex items-center gap-2">
                  <CurrencyText
                    amount={payment.amount}
                    currency={payment.currency}
                    displayModes={["amount"]}
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {payment.currentStatus && (
                <TransactionDetailRow
                  label="Status"
                  value={payment.currentStatus}
                />
              )}
              {payment.fileName && (
                <TransactionDetailRow
                  label="Payment file"
                  value={payment.fileName}
                />
              )}
              {payment.code && (
                <TransactionDetailRow label="Code" value={payment.code} />
              )}
              <TransactionDetailRow
                label="Payment date"
                value={formattedDate}
              />
              {payment.currencyAccount && (
                <TransactionDetailRow
                  label="From"
                  value={formatAccountDisplay(
                    payment.currencyAccount.clientAccount?.virtualIban ??
                      payment.currencyAccount.virtualIban,
                    payment.currencyAccount.clientAccount?.accountName ??
                      payment.currencyAccount.clientAccount?.accountName ??
                      "",
                  )}
                />
              )}
              {payment.payee && (
                <TransactionDetailRow
                  label="To"
                  value={payment.payee.accountName}
                />
              )}
              {payment.payee?.bank && (
                <TransactionDetailRow
                  label="Bank"
                  value={
                    <div className="flex flex-col items-end">
                      <span>{payment.payee.bank.name}</span>
                      {payment.payee.bank.swiftBic && (
                        <span className="text-xs text-muted-foreground">
                          {payment.payee.bank.swiftBic}
                        </span>
                      )}
                    </div>
                  }
                />
              )}
              {(payment.type || payment.fee) && (
                <TransactionDetailRow
                  label="Payment type / fee"
                  value={
                    <div className="flex items-center justify-end gap-1">
                      {payment.type && <span>{payment.type}</span>}
                      {payment.fee && (
                        <>
                          <span> / </span>
                          <CurrencyText
                            amount={payment.fee}
                            currency={payment.feeCurrency || payment.currency}
                            displayModes={["amount"]}
                          />
                        </>
                      )}
                    </div>
                  }
                />
              )}
              {payment.reference && (
                <TransactionDetailRow
                  label="Reference"
                  value={payment.reference}
                />
              )}
              {payment.purpose && (
                <TransactionDetailRow label="Purpose" value={payment.purpose} />
              )}
              {payment.createdAt && (
                <TransactionDetailRow
                  label="Created"
                  value={format(new Date(payment.createdAt), "dd MMM yyyy")}
                />
              )}
            </div>
          </>
        )}
      </SheetContent>
    </Sheet>
  )
}
