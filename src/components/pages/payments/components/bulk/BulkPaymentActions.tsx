import { useMemo, useState } from "react"
import { Trash2Icon } from "lucide-react"
import { useNavigate } from "@tanstack/react-router"

import { ROLES } from "@/lib/constants/roles.constants"
import { useCurrentUser } from "@/data/user"
import {
  approveBulkPaymentMutation,
  bulkPaymentCancelMutation,
  startBulkPaymentVerificationMutation,
  verifyBulkPaymentCodeMutation,
} from "@/data/payments/payments.mutation"
import { bulkPaymentSubmitMutation } from "@/data/payments/payments.mutation"
import {
  BulkPaymentStatusType,
  IBulkPaymentDetails,
  IBulkPaymentSummary,
} from "@/data/payments/payments.interface"
import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"
import { Dialog } from "@/components/ui/dialog"
import {
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { VerificationModal } from "@/components/pages/send-payments/components/modals/VerificationModal"
import { GetEntityResponseDto } from "@/client/onboarding/types.gen"

export function BulkPaymentActions({
  data,
  summary,
  onConfirm,
  onCancel,
  entity,
}: {
  data: IBulkPaymentDetails
  summary?: IBulkPaymentSummary
  onConfirm?: () => void
  onCancel?: () => void
  entity: GetEntityResponseDto
}) {
  const navigate = useNavigate()
  const { data: entityAccess } = useEntityAccessQuery(entity.id ?? "")
  const [dialogType, setDialogType] = useState<"approve" | "reject" | null>(
    null,
  )
  const [verifyModalOpen, setVerifyModalOpen] = useState(false)
  const [pendingAction, setPendingAction] = useState<
    "submit" | "approve" | null
  >(null)
  const [, setIsResending] = useState(false)

  const { data: currentUser } = useCurrentUser()

  const _entityAccess = useMemo(() => {
    if (!entityAccess) return null
    if (!entity.id) return null
    return entityAccess?.find(
      (access) => access.entityId?.toLowerCase() === entity.id?.toLowerCase(),
    )
  }, [entityAccess, entity])

  const isApproveButtonVisible = useMemo(() => {
    if (!data) return false
    if (data.currentStatus !== "PendingApproval") return false
    if (!_entityAccess) return false
    if (!currentUser) return false
    if (
      data.approvals.some(
        (approver) => approver.approverEmail === currentUser.email,
      )
    )
      return false
    if (
      [ROLES.APPROVER, ROLES.ADMINISTRATOR].some((e) =>
        _entityAccess?.roles?.includes(e),
      )
    )
      return true
    return false
  }, [_entityAccess, data, currentUser])

  const { mutate: submitBulkPayment, isPending: isSubmitting } =
    bulkPaymentSubmitMutation()
  const { mutate: approveBulkPayment, isPending: isApproving } =
    approveBulkPaymentMutation()
  const { mutate: rejectBulkPayment, isPending: isRejecting } =
    bulkPaymentCancelMutation()
  const { mutate: startVerification, isPending: isStartingVerification } =
    startBulkPaymentVerificationMutation()
  const { mutate: verifyCode, isPending: isVerifying } =
    verifyBulkPaymentCodeMutation()

  // Handler for SCA submit/approve
  function openVerificationModal(action: "submit" | "approve") {
    setPendingAction(action)
    setVerifyModalOpen(true)
    startVerification({
      bulkPaymentId: data.id,
      type: "Sms",
      operationType: action === "submit" ? "Submit" : "Approve",
    })
  }

  function handleConfirm(type: "review" | "submit" | "approve" | "reject") {
    if (type === "review") {
      onConfirm?.()
      navigate({
        to: "/$entityId/payments/bulk/$bulkPaymentId",
        params: {
          entityId: entity.id!,
          bulkPaymentId: data.id,
        },
        replace: true,
      })
    } else if (type === "submit") {
      if (summary?.isScaRequired) {
        openVerificationModal("submit")
      } else {
        submitBulkPayment(data.id, {
          onSuccess: () => {
            onConfirm?.()
            navigate({
              to: "/$entityId/payments",
              params: {
                entityId: entity.id ?? "",
              },
              replace: true,
            })
          },
        })
      }
    } else if (data.currentStatus == "PendingApproval") {
      if (type === "approve") {
        approveBulkPayment(data.id, {
          onSuccess: () => {
            setDialogType(null)
            onConfirm?.()
            navigate({
              to: "/$entityId/payments",
              params: { entityId: entity.id ?? "" },
              search: {
                tab: "bulk",
              },
              replace: true,
            })
          },
        })
      } else if (type === "reject") {
        rejectBulkPayment(data.id, {
          onSuccess: () => {
            setDialogType(null)
            onConfirm?.()
            navigate({
              to: "/$entityId/payments",
              params: { entityId: entity.id ?? "" },
              search: {
                tab: "bulk",
              },
              replace: true,
            })
          },
        })
      }
    }
  }

  // Handler for code verification
  function handleVerify(code: string) {
    if (pendingAction === "submit") {
      verifyCode(
        {
          bulkPaymentId: data.id,
          verificationCode: code,
          operationType: "Submit",
        },
        {
          onSuccess: () => {
            submitBulkPayment(data.id, {
              onSuccess: () => {
                setVerifyModalOpen(false)
                setPendingAction(null)
                onConfirm?.()
                navigate({
                  to: "/$entityId/payments",
                  params: { entityId: entity.id ?? "" },
                  search: { tab: "bulk" },
                  replace: true,
                })
              },
            })
          },
        },
      )
    } else if (pendingAction === "approve") {
      verifyCode(
        {
          bulkPaymentId: data.id,
          verificationCode: code,
          operationType: "Approve",
        },
        {
          onSuccess: () => {
            approveBulkPayment(data.id, {
              onSuccess: () => {
                setVerifyModalOpen(false)
                setPendingAction(null)
                onConfirm?.()
                navigate({
                  to: "/$entityId/payments",
                  params: { entityId: entity.id ?? "" },
                  search: { tab: "bulk" },
                  replace: true,
                })
              },
            })
          },
        },
      )
    }
  }

  // Handler for resending code
  function handleResendCode() {
    if (!pendingAction) return
    setIsResending(true)
    startVerification(
      {
        bulkPaymentId: data.id,
        type: "Sms",
        operationType: pendingAction === "submit" ? "Submit" : "Approve",
      },
      {
        onSettled: () => setIsResending(false),
      },
    )
  }

  const isEditable = useMemo(() => {
    if (data.currentStatus == "ReadyForReview") return true
    const totalOutboundPayments = data.outboundPaymentStatuses.reduce(
      (acc, curr) => acc + curr.count,
      0,
    )
    if (totalOutboundPayments > 0) return false
    const editableStatuses: BulkPaymentStatusType[] = [
      "Validating",
      "Uploaded",
      "ReadyForSubmission",
      "ReadyForReview",
    ]
    if (editableStatuses.includes(data.currentStatus)) return true
    return false
  }, [data])

  const isSubmitButtonVisible = useMemo(() => {
    const totalOutboundPayments = data.outboundPaymentStatuses.reduce(
      (acc, curr) => acc + curr.count,
      0,
    )
    if (totalOutboundPayments > 0 && data.currentStatus == "ReadyForSubmission")
      return true
    return false
  }, [data])

  return (
    <>
      <div className="m-3 flex items-center justify-start gap-2">
        {isEditable && (
          <div className="flex w-full justify-between gap-2">
            <Button onClick={() => handleConfirm("review")} variant="default">
              Review
            </Button>
            <Button
              disabled={!onCancel}
              onClick={onCancel}
              variant="link-muted"
            >
              <Trash2Icon className="h-4 w-4" />
              Cancel file
            </Button>
          </div>
        )}
        {isSubmitButtonVisible && (
          <Button
            disabled={
              isSubmitting ||
              isStartingVerification ||
              isVerifying ||
              isSubmitting
            }
            onClick={() => handleConfirm("submit")}
            variant="default"
          >
            Submit
          </Button>
        )}
        {isApproveButtonVisible && (
          <div className="flex w-full justify-between gap-2">
            <Button
              disabled={
                isStartingVerification ||
                isVerifying ||
                isApproving ||
                isRejecting
              }
              onClick={() => setDialogType("approve")}
              variant="default"
            >
              Approve
            </Button>
            <Button
              disabled={
                isStartingVerification ||
                isVerifying ||
                isApproving ||
                isRejecting
              }
              onClick={() => setDialogType("reject")}
              variant="link-muted"
            >
              <Trash2Icon className="h-4 w-4" />
              Reject
            </Button>
          </div>
        )}
      </div>
      <Dialog
        modal
        onOpenChange={() => setDialogType(null)}
        open={!!dialogType}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {dialogType === "approve"
                ? "Confirm Approval"
                : "Confirm Rejection"}
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to{" "}
              {dialogType === "approve" ? "approve" : "reject and cancel"} this
              bulk payment?
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="mt-4 flex justify-start gap-2">
            <Button onClick={() => setDialogType(null)} variant="link">
              Cancel
            </Button>
            <Button
              disabled={isApproving || isRejecting}
              onClick={() => dialogType && handleConfirm(dialogType)}
              variant={dialogType === "approve" ? "default" : "destructive"}
            >
              {dialogType === "approve"
                ? isApproving
                  ? "Approving..."
                  : "Confirm Approval"
                : isRejecting
                  ? "Rejecting..."
                  : "Confirm Rejection"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <VerificationModal
        onOpenChange={(open) => {
          setVerifyModalOpen(open)
          if (!open) setPendingAction(null)
        }}
        onResendCode={handleResendCode}
        onVerify={handleVerify}
        open={verifyModalOpen}
      />
    </>
  )
}
