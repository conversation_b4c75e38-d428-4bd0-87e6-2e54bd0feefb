import { IPaymentDetailsBackend } from "@/data/payments/payments.interface"

export interface PaymentControlsProps {
  payment: IPaymentDetailsBackend
  approvalDetails?: {
    isFinalApprover?: boolean
    canApprove?: boolean
  }
  signatoryValidation?: {
    canApproveOwnSubmission: boolean
  }
  isApproveButtonVisible?: boolean
  cantApproveMine?: boolean
  isSelfApprover?: boolean
  isAlreadyApproved?: boolean
  onApprove: () => void
  onDraft: () => void
  onGetRateAndApprove: () => void
  onRejectForEdit: () => void
  onReject: () => void
  onDeleteDraft: () => void
}
