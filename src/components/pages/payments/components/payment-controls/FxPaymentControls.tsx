import { TrashIcon } from "lucide-react"

import { CustomerPaymentStatus } from "@/data/payments/payments.interface"
import { But<PERSON> } from "@/components/ui/button"

import { PaymentControlsProps } from "./types"
export function FxPaymentControls({
  payment,
  approvalDetails,
  isAlreadyApproved,
  cantApproveMine,
  isApproveButtonVisible,
  onApprove,
  onGetRateAndApprove,
  onRejectForEdit,
  onReject,
  onDeleteDraft,
}: PaymentControlsProps) {
  // Create a reusable Cancel payment button component
  const CancelPaymentButton = () => (
    <Button
      className="flex items-center gap-2"
      onClick={onReject}
      variant="link-muted"
    >
      <TrashIcon className="h-4 w-4" />
      Cancel payment
    </Button>
  )

  // Create a reusable Delete Draft Payment button component
  const DeleteDraftPaymentButton = () => (
    <Button onClick={onDeleteDraft} variant="link-muted">
      <TrashIcon className="h-4 w-4" />
      Delete draft payment
    </Button>
  )

  // Don't render any buttons for cancelled, processing, sent, rejected, or failed payments
  if (
    payment.currentStatus === CustomerPaymentStatus.Cancelled ||
    payment.currentStatus === CustomerPaymentStatus.Processing ||
    payment.currentStatus === CustomerPaymentStatus.Sent ||
    payment.currentStatus === CustomerPaymentStatus.Rejected ||
    payment.currentStatus === CustomerPaymentStatus.InsufficientBalance
  ) {
    return null
  }

  // For Draft status, show Edit and Cancel payment buttons
  if (payment.currentStatus === CustomerPaymentStatus.Draft) {
    return (
      <div className="mt-6 flex w-full justify-between gap-2">
        <Button className="w-fit" onClick={onRejectForEdit}>
          Edit
        </Button>
        <DeleteDraftPaymentButton />
      </div>
    )
  }

  // For Pending, Held, or Error status, only show Cancel payment button
  if (
    payment.currentStatus === CustomerPaymentStatus.Pending ||
    payment.currentStatus === CustomerPaymentStatus.Held ||
    payment.currentStatus === CustomerPaymentStatus.InsufficientBalance
  ) {
    return (
      <div className="mt-6 flex w-2/3 gap-2">
        <CancelPaymentButton />
      </div>
    )
  }

  // For Pending approval status, show Get rate and approve, Reject for edit, and Reject buttons
  if (
    payment.currentStatus === CustomerPaymentStatus.PendingSignatoryApproval
  ) {
    return (
      <div className="mt-6 flex w-full justify-between gap-2">
        <div className="flex gap-2">
          {isApproveButtonVisible &&
            !isAlreadyApproved &&
            approvalDetails?.isFinalApprover &&
            !cantApproveMine &&
            approvalDetails?.canApprove && (
              <Button className="w-fit" onClick={onGetRateAndApprove}>
                Get rate and approve
              </Button>
            )}

          {isApproveButtonVisible &&
            !isAlreadyApproved &&
            !approvalDetails?.isFinalApprover &&
            !cantApproveMine &&
            approvalDetails?.canApprove && (
              <Button className="w-fit" onClick={onApprove}>
                Approve
              </Button>
            )}

          <Button className="w-fit" onClick={onRejectForEdit} variant="outline">
            Reject for edit
          </Button>
        </div>

        <CancelPaymentButton />
      </div>
    )
  }
}
