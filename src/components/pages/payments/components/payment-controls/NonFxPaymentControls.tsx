import { TrashIcon } from "lucide-react"

import { CustomerPaymentStatus } from "@/data/payments/payments.interface"
import { But<PERSON> } from "@/components/ui/button"

import { PaymentControlsProps } from "./types"
export function NonFxPaymentControls({
  payment,
  approvalDetails,
  isAlreadyApproved,
  isApproveButtonVisible,
  cantApproveMine,
  onApprove,
  onRejectForEdit,
  onReject,
  onDeleteDraft,
}: PaymentControlsProps) {
  // Create a reusable Cancel payment button component
  const CancelPaymentButton = () => (
    <Button
      className="flex items-center gap-2"
      onClick={onReject}
      variant="link-muted"
    >
      <TrashIcon className="h-4 w-4" />
      Cancel payment
    </Button>
  )

  // Create a reusable Delete Draft Payment button component
  const DeleteDraftPaymentButton = () => (
    <Button onClick={onDeleteDraft} variant="link-muted">
      <TrashIcon className="h-4 w-4" />
      Delete draft payment
    </Button>
  )

  // Don't render any buttons for cancelled, processing, sent, rejected, or failed payments
  if (
    payment.currentStatus === CustomerPaymentStatus.Cancelled ||
    payment.currentStatus === CustomerPaymentStatus.Processing ||
    payment.currentStatus === CustomerPaymentStatus.Sent ||
    payment.currentStatus === CustomerPaymentStatus.Rejected ||
    payment.currentStatus === CustomerPaymentStatus.InsufficientBalance
  ) {
    return null
  }

  // For Draft status, show Edit and Cancel payment buttons
  if (payment.currentStatus === CustomerPaymentStatus.Draft) {
    return (
      <div className="mt-6 flex w-full justify-between gap-2">
        <Button className="w-fit" onClick={onRejectForEdit}>
          Edit
        </Button>
        <DeleteDraftPaymentButton />
      </div>
    )
  }

  // For Error status, only show Cancel payment button
  if (
    payment.currentStatus === CustomerPaymentStatus.InsufficientBalance ||
    payment.currentStatus === CustomerPaymentStatus.Pending
  ) {
    return (
      <div className="full mt-6 flex justify-end gap-2">
        <CancelPaymentButton />
      </div>
    )
  }

  // For Pending approval status, show Approve, Reject for edit, and Reject buttons
  if (
    payment.currentStatus === CustomerPaymentStatus.PendingSignatoryApproval
  ) {
    return (
      <div className="mt-6 flex w-full justify-between gap-2">
        <div className="flex gap-2">
          {isApproveButtonVisible &&
            !isAlreadyApproved &&
            !cantApproveMine &&
            approvalDetails?.canApprove && (
              <Button className="fw-fit" onClick={onApprove}>
                Approve
              </Button>
            )}

          <Button className="w-fit" onClick={onRejectForEdit} variant="outline">
            Reject for edit
          </Button>
        </div>

        <CancelPaymentButton />
      </div>
    )
  }
}
