import {
  CircleCheckIcon,
  CircleXIcon,
  InfoIcon,
  TriangleAlertIcon,
} from "lucide-react"

import { cn } from "@/lib/utils"
interface PaymentStatusBadgeProps {
  status: string
  className?: string
}

export function PaymentStatusBadge({
  status,
  className,
}: PaymentStatusBadgeProps) {
  const getStatusVariant = (status: string) => {
    if (
      ["pending", "validating", "readytoprocess", "processing"].some((v) =>
        status?.toLowerCase().includes(v),
      )
    ) {
      return "pending"
    }
    if (
      ["completed", "success", "sent"].some((v) =>
        status?.toLowerCase().includes(v),
      )
    ) {
      return "success"
    }
    if (
      ["error", "fail", "cancel", "rejected"].some((v) =>
        status?.toLowerCase().includes(v),
      )
    ) {
      return "error"
    }
    return "info"
  }

  const getDisplayStatus = (status: string) => {
    if (status?.toLowerCase().includes("cancel")) {
      return "Cancelled"
    }
    return status
  }

  const variants = {
    pending: "bg-warning/5 text-warning border-warning/50 stroke-warning/50",
    success: "bg-success/10 text-success border-success/50 stroke-success/50",
    error:
      "bg-destructive/10 text-destructive border-destructive/50 stroke-destructive/50",
    info: "bg-info/10 text-info border-info/50 stroke-info/50",
  }

  const statusVariant = getStatusVariant(status)
  const displayStatus = getDisplayStatus(status)

  return (
    <span
      className={cn(
        "flex flex-none items-center gap-1 text-nowrap rounded-lg border px-2 py-1 text-xs",
        variants[statusVariant],
        className,
      )}
    >
      {statusVariant === "pending" && <TriangleAlertIcon className="h-3 w-3" />}
      {statusVariant === "success" && <CircleCheckIcon className="h-3 w-3" />}
      {statusVariant === "error" && <CircleXIcon className="h-3 w-3" />}
      {statusVariant === "info" && <InfoIcon className="h-3 w-3" />}
      {displayStatus}
    </span>
  )
}
