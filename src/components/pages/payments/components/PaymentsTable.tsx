import { useEffect, useState } from "react"
import { Info } from "lucide-react"
import { format } from "date-fns"
import { Link, useNavigate, useSearch } from "@tanstack/react-router"

import { IOutboundPayment, Payment } from "@/data/payments/payments.interface"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

import { PaymentTablePayeeCell } from "./payment-table/PaymentTablePayeeCell"
import { PaymentTableFromCell } from "./payment-table/PaymentTableFromCell"
import { PaymentTableDetailsCell } from "./payment-table/PaymentTableDetailsCell"
import { PaymentStatusCell } from "./payment-table/PaymentStatusCell"
import { BulkPaymentViewSheet } from "./bulk/BulkPaymentViewSheet"
import { BulkPaymentRowDetailSheet } from "./bulk/BulkPaymentRowDetailSheet"

type DefaultColumnKeys =
  | "date"
  | "paymentCode"
  | "payee"
  | "amount"
  | "fromAccount"
  | "status"
  | "details"

type SortDirection = "asc" | "desc" | "none"
type SortField = "date" | "payee" | "amount" | "fromAccount" | "status"

interface SortState {
  field: SortField | null
  direction: SortDirection
}

/**
 *
 * @deprecated Soon to be deprecated. Use ./table/{bulk,single}
 */
export default function PaymentsTable({
  payments = [],
  entityId,
  variant,
  bulkUploadId,
  filter,
  onPaymentClick,
}: {
  payments?: Payment[]
  entityId: string
  variant: "default" | "bulk"
  bulkUploadId?: string
  filter?: string
  onPaymentClick?: (payment: IOutboundPayment) => void
}) {
  const [columnsVisibility, setColumnsVisibility] = useState({
    date: true,
    paymentCode: variant == "default",
    payee: true,
    amount: variant == "default",
    fromAccount: true,
    status: true,
    details: variant == "bulk",
  })
  const [sort, setSort] = useState<SortState>({
    field: null,
    direction: "none",
  })
  const search: any = useSearch({
    strict: false,
  })

  const navigate = useNavigate()

  const [bulkPaymentId, setBulkPaymentId] = useState<string | undefined>(
    search.bulkPaymentId,
  )

  useEffect(() => {
    if (bulkPaymentId) {
      navigate({
        search: {
          ...search,
          bulkPaymentId: bulkPaymentId,
        },
        replace: true,
      })
    } else {
      const s = { ...search }
      delete s.bulkPaymentId
      navigate({
        search: {
          ...s,
        },
        replace: true,
      })
    }
  }, [bulkPaymentId, navigate, search])

  const [paymentDetails, setPaymentDetails] = useState<
    IOutboundPayment | undefined
  >()

  const toggleColumnVisibility = (column: DefaultColumnKeys) => {
    setColumnsVisibility((prev) => ({
      ...prev,
      [column]: !prev[column],
    }))
  }

  useEffect(() => {
    if (variant === "bulk" && columnsVisibility.paymentCode) {
      if (columnsVisibility.paymentCode) {
        toggleColumnVisibility("paymentCode")
      }
      if (columnsVisibility.amount) {
        toggleColumnVisibility("amount")
      }
      if (!columnsVisibility.details) {
        toggleColumnVisibility("details")
      }
    }
    if (variant === "default") {
      if (!columnsVisibility.paymentCode) {
        toggleColumnVisibility("paymentCode")
      }
      if (!columnsVisibility.amount) {
        toggleColumnVisibility("amount")
      }
      if (columnsVisibility.details) {
        toggleColumnVisibility("details")
      }
    }
  }, [
    columnsVisibility.amount,
    columnsVisibility.details,
    columnsVisibility.paymentCode,
    variant,
  ])

  const sortData = (data: Payment[]) => {
    if (!sort.field || sort.direction === "none") return data

    return [...data].sort((a, b) => {
      const getValue = (payment: Payment) => {
        switch (sort.field) {
          case "date":
            return new Date(payment.createdAt).getTime()
          case "payee":
            return payment.paymentType === "OutboundPayment"
              ? payment.payee.accountName
              : ""
          case "amount":
            return payment.paymentType === "OutboundPayment"
              ? payment.amount
              : ""
          case "fromAccount":
            return payment.paymentType === "OutboundPayment"
              ? payment.currencyAccount?.clientAccount?.accountName
              : ""
          case "status":
            return payment.currentStatus
          default:
            return ""
        }
      }

      const aValue = getValue(a)
      const bValue = getValue(b)

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sort.direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return sort.direction === "asc"
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })
  }

  const toggleSort = (field: SortField) => {
    setSort((prev) => ({
      field,
      direction:
        prev.field === field
          ? prev.direction === "asc"
            ? "desc"
            : prev.direction === "desc"
              ? "none"
              : "asc"
          : "asc",
    }))
  }

  const sortedPayments = sortData(payments)

  const handleRowClick = (payment: Payment) => {
    if (payment.paymentType === "OutboundPayment" && bulkUploadId) {
      setPaymentDetails(payment as IOutboundPayment)
    } else if (payment.paymentType === "OutboundPayment" && onPaymentClick) {
      onPaymentClick(payment as IOutboundPayment)
    }

    if (payment.paymentType === "BulkPayment") {
      setBulkPaymentId(payment.id)
    }
  }

  if (payments.length === 0) {
    if (bulkUploadId) {
      return (
        <div className="flex h-full min-h-80 w-full items-center justify-center">
          <div className="flex flex-col items-center justify-center gap-4">
            <h2 className="text-lg font-medium">No payments found</h2>
            <>
              <p className="text-sm text-muted-foreground">
                {filter === "all" ? (
                  <>
                    Looks like bulk payment file is not processed yet. Please
                    check bulk status and process the file.
                  </>
                ) : (
                  "No payments found for the selected filter."
                )}
              </p>
              <Button size="sm" variant="outline">
                <Link
                  params={{
                    entityId: entityId,
                    bulkPaymentId: bulkUploadId,
                  }}
                  to="/$entityId/payments/bulk/$bulkPaymentId"
                >
                  Check bulk status
                </Link>
              </Button>
            </>
          </div>
        </div>
      )
    }

    return (
      <div className="flex h-full min-h-80 w-full items-center justify-center">
        <div className="flex flex-col items-center justify-center gap-4">
          <h2 className="text-lg font-medium">No payments found</h2>
          <p className="text-sm text-muted-foreground">
            {filter === "all"
              ? "No payments found"
              : "No payments found for the selected filter."}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        {/* Table Header */}
        <TableHeader>
          <TableRow className="border-none">
            {columnsVisibility.date && (
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                <Button
                  className="h-8 whitespace-nowrap p-0 text-xs font-bold hover:bg-transparent"
                  onClick={() => toggleSort("date")}
                  variant="ghost"
                >
                  Date
                </Button>
              </TableHead>
            )}
            {columnsVisibility.paymentCode && (
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                Code
              </TableHead>
            )}
            {columnsVisibility.payee && (
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                <Button
                  className="h-8 p-0 text-xs font-bold hover:bg-transparent"
                  onClick={() => toggleSort("payee")}
                  variant="ghost"
                >
                  {variant === "bulk" ? "File" : "Payee"}
                </Button>
              </TableHead>
            )}
            {columnsVisibility.amount && (
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                <Button
                  className="h-8 whitespace-nowrap p-0 text-xs font-bold hover:bg-transparent"
                  onClick={() => toggleSort("amount")}
                  variant="ghost"
                >
                  Amount
                </Button>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="ml-2 inline-flex h-5 w-5 items-center justify-center rounded-full text-sm">
                      <Info aria-label="Amount info" className="h-4 w-4" />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent className="border-1 border-border bg-background text-muted-foreground shadow-md">
                    Amount to be paid in the selected currency
                  </TooltipContent>
                </Tooltip>
              </TableHead>
            )}
            {columnsVisibility.fromAccount && (
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                <Button
                  className="h-8 whitespace-nowrap p-0 text-xs font-bold hover:bg-transparent"
                  onClick={() => toggleSort("fromAccount")}
                  variant="ghost"
                >
                  From Account
                </Button>
              </TableHead>
            )}
            {columnsVisibility.status && (
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                <Button
                  className="h-8 whitespace-nowrap p-0 text-xs font-bold hover:bg-transparent"
                  onClick={() => toggleSort("status")}
                  variant="ghost"
                >
                  Status
                </Button>
              </TableHead>
            )}
            {columnsVisibility.details && (
              <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                Progress
              </TableHead>
            )}
          </TableRow>
        </TableHeader>

        {/* Table Body */}
        <TableBody>
          {sortedPayments.map((payment, index) => (
            <TableRow
              className="cursor-pointer hover:bg-muted/5"
              key={`${payment.id}-${index}`}
              onClick={() => {
                handleRowClick(payment)
              }}
            >
              {columnsVisibility.date && (
                <TableCell className="py-6">
                  {format(new Date(payment.createdAt), "dd MMM yyyy")}
                </TableCell>
              )}
              {columnsVisibility.paymentCode && (
                <TableCell>
                  {payment.paymentType === "OutboundPayment"
                    ? payment.code
                    : "-"}
                </TableCell>
              )}
              {columnsVisibility.payee && (
                <PaymentTablePayeeCell payment={payment} />
              )}
              {columnsVisibility.amount && (
                <TableCell>
                  {payment.paymentType === "OutboundPayment" ? (
                    <CurrencyText
                      amount={payment.amount}
                      className="font-medium"
                      currency={payment.currency}
                    />
                  ) : (
                    <span>-</span>
                  )}
                </TableCell>
              )}
              {columnsVisibility.fromAccount && (
                <PaymentTableFromCell payment={payment} />
              )}
              {columnsVisibility.status && (
                <PaymentStatusCell payment={payment} />
              )}
              {columnsVisibility.details && (
                <PaymentTableDetailsCell payment={payment} />
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <BulkPaymentViewSheet
        id={bulkPaymentId}
        onClose={() => setBulkPaymentId(undefined)}
      />
      <BulkPaymentRowDetailSheet
        onClose={() => setPaymentDetails(undefined)}
        payment={paymentDetails}
      />
    </div>
  )
}
