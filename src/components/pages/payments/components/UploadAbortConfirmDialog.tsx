import { VisuallyHidden } from "@radix-ui/react-visually-hidden" // Import this

import {
  Dialog,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog"
import { DialogContent } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import {
  AlertBox,
  AlertDescription,
  AlertTitle,
} from "@/components/base/alert-box"

interface UploadAbortConfirmDialogProps {
  open: boolean
  variant?: "close" | "abort"
  onCancel: () => void
  onAbort: () => void
}

export function UploadAbortConfirmDialog({
  open,
  onCancel,
  onAbort,
  variant,
}: UploadAbortConfirmDialogProps) {
  return (
    <Dialog open={open}>
      <DialogContent className="block max-w-sm" hideClose>
        <VisuallyHidden>
          <DialogTitle>
            {variant === "close" ? "Close Window" : "Abort Upload"}
          </DialogTitle>
          <DialogDescription>
            {variant === "close"
              ? "Are you sure you want to close the window?"
              : "Are you sure you want to abort the upload?"}
          </DialogDescription>
        </VisuallyHidden>
        <AlertBox severity="warning">
          <AlertTitle>
            {variant === "close"
              ? "Are you sure you want to close the window?"
              : "Are you sure you want to abort the upload?"}
          </AlertTitle>
          <AlertDescription>
            {variant === "close"
              ? "This will stop your file upload."
              : "This will stop your file upload and delete the file you are uploading."}
          </AlertDescription>
        </AlertBox>
        <DialogFooter className="mt-4 !flex !flex-row sm:!flex-row sm:!justify-between sm:space-x-2">
          <Button className="flex" onClick={onCancel} variant="link">
            {variant === "close" ? "Leave open" : "Cancel"}
          </Button>
          <Button className="flex" onClick={onAbort}>
            {variant === "close" ? "Close upload window" : "Abort upload"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
