import { useState } from "react"

import { revertPaymentToDraftMutation } from "@/data/payments/payments.mutation"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

interface RejectForEditModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  paymentId?: string
  onRevertSuccess?: () => void
}

export function RejectForEditModal({
  open,
  onOpenChange,
  paymentId,
  onRevertSuccess,
}: RejectForEditModalProps) {
  const [isReverting, setIsReverting] = useState(false)
  const { mutateAsync: revertPaymentToDraft } = revertPaymentToDraftMutation()

  const handleRejectForEdit = async () => {
    if (!paymentId) return

    try {
      setIsReverting(true)
      await revertPaymentToDraft(paymentId)
      onOpenChange(false)
      if (onRevertSuccess) onRevertSuccess()
    } catch (error) {
      console.error("Error reverting payment to draft:", error)
    } finally {
      setIsReverting(false)
    }
  }

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-md">
        <DialogTitle className="text-md">Reject for edit</DialogTitle>
        <DialogDescription className="text-sm">
          This payment will revert to &quot;Draft&quot; status. It can then be
          edited and re-submitted for approval.
        </DialogDescription>
        <div className="mt-6 flex justify-end gap-4">
          <Button onClick={() => onOpenChange(false)} variant="outline">
            Cancel
          </Button>
          <Button disabled={isReverting} onClick={handleRejectForEdit}>
            {isReverting ? (
              <>
                <LoadingSpinner className="mr-2" size="4" />
                Processing...
              </>
            ) : (
              "Reject and revert to draft"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
