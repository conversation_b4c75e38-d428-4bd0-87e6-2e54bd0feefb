import { describe, expect, it, vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { TrailingPayButton } from "@/components/pages/payments/components/TrailingPayButton"

// Mock the Popover components
vi.mock("@/components/ui/popover", () => ({
  Popover: ({ children }: any) => <div data-testid="popover">{children}</div>,
  PopoverTrigger: ({ children }: any) => (
    <div data-testid="trigger">{children}</div>
  ),
  PopoverContent: ({ children }: any) => (
    <div data-testid="content">{children}</div>
  ),
}))

// Mock the dialog
vi.mock(
  "@/components/pages/payments/components/UploadBulkPaymentDialog",
  () => ({
    UploadBulkPaymentDialog: ({ open }: any) => (
      <div data-open={open} data-testid="bulk-dialog">
        Mock Dialog
      </div>
    ),
  }),
)

// Mock router hooks
vi.mock("@tanstack/react-router", () => ({
  useLocation: () => ({ href: "/test-route" }),
  Link: ({ children }: any) => <div>{children}</div>,
}))

describe("TrailingPayButton", () => {
  it("renders pay button", () => {
    render(<TrailingPayButton entityId="test-entity" />)
    const payElements = screen.getAllByText(/pay/i)
    expect(payElements.length).toBeGreaterThan(0)
    expect(payElements[0]).toBeInTheDocument()
  })

  it("renders popover content", () => {
    render(<TrailingPayButton entityId="test-entity" />)
    expect(screen.getByText("Single payment")).toBeInTheDocument()
    expect(screen.getByText("Bulk payment")).toBeInTheDocument()
  })

  it("opens bulk payment dialog when bulk payment button is clicked", () => {
    render(<TrailingPayButton entityId="test-entity" />)

    const bulkPaymentButton = screen.getByText("Bulk payment")
    fireEvent.click(bulkPaymentButton)

    const dialog = screen.getByTestId("bulk-dialog")
    expect(dialog).toHaveAttribute("data-open", "true")
  })

  it("maintains dialog state when reopening", () => {
    render(<TrailingPayButton entityId="test-entity" />)

    // Click bulk payment
    fireEvent.click(screen.getByText("Bulk payment"))

    // Dialog should be open
    expect(screen.getByTestId("bulk-dialog")).toHaveAttribute(
      "data-open",
      "true",
    )
  })
})
