import { formatAccountNumber } from "@/lib/bank.utils"
import { IBulkPayment } from "@/data/payments/payments.interface"

export default function FromAccount({ payment }: { payment: IBulkPayment }) {
  const hasCurrencyAccounts =
    "currencyAccounts" in payment &&
    Array.isArray(payment?.currencyAccounts) &&
    payment?.currencyAccounts.length > 1

  if (!hasCurrencyAccounts) return null

  return (
    <div className="">
      <span className="font-normal">
        {hasCurrencyAccounts
          ? `${hasCurrencyAccounts} accounts`
          : payment.currencyAccounts[0].accountName}
      </span>

      <div className="flex gap-2">
        {hasCurrencyAccounts &&
          payment?.currencyAccounts.map((account, i) => (
            <p className="text-xs text-muted-foreground" key={i}>
              {formatAccountNumber(account.virtualIban)}
            </p>
          ))}
      </div>
    </div>
  )
}
