import { Check<PERSON><PERSON><PERSON>, Clock, Refresh<PERSON>cw, XCircle } from "lucide-react"

import {
  BULK_PAYMENT_PAYMENT_STATUS,
  IBulkPayment,
  TBulkPaymentPaymentStatus,
} from "@/data/payments/payments.interface"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const getIcon = (status: TBulkPaymentPaymentStatus) => {
  switch (status) {
    case "Sent":
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case "Processing":
      return <RefreshCcw className="h-4 w-4 text-info-foreground" />
    case "Pending":
      return <Clock className="h-4 w-4 text-info" />
    case "Rejected":
      return <XCircle className="h-4 w-4 text-destructive" />
  }
}

function getStatus(payment: IBulkPayment, status: TBulkPaymentPaymentStatus) {
  const _status = payment.paymentStatuses.find((s) => s.status === status)

  return {
    icon: getIcon(status),
    count: _status?.count ?? 0,
    label: status,
  }
}

export interface ProgressProps {
  payment: IBulkPayment
}

export default function Progress({ payment }: ProgressProps) {
  return (
    <div className="inline-flex gap-x-4">
      {BULK_PAYMENT_PAYMENT_STATUS.map((_status) => {
        const status = getStatus(payment, _status)

        return (
          <Tooltip key={_status}>
            <TooltipTrigger>
              <span className="flex items-center justify-end gap-1">
                {status.icon} {status.count}
              </span>
            </TooltipTrigger>
            <TooltipContent className="bg-foreground text-xs text-background">
              {status.label}
            </TooltipContent>
          </Tooltip>
        )
      })}
    </div>
  )
}
