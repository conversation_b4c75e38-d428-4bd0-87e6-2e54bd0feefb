import { cn } from "@/lib/utils"
import { IBulkPayment } from "@/data/payments/payments.interface"
import DataTable from "@/components/base/data-table"

import { file, date, fromAccount, progress, status } from "./Bulk.columns"
import usePaymentsTable from "../table.hook"
import { PaymentsTableProps } from "../interface"
import { BulkPaymentViewSheet } from "../../bulk/BulkPaymentViewSheet"

function useBulkTable(props: PaymentsTableProps) {
  const {
    paymentsData,
    isLoading,
    currentPage,
    sort,
    setPageSize,
    setCurrentPage,
    setSort,
    sortData,
    handleNext,
    handlePrev,
    setSelectedPayment,
  } = usePaymentsTable<IBulkPayment>("bulk", props)

  const sortedPayments = sortData(
    (paymentsData?.data as IBulkPayment[]) ?? [],
    (payment) => {
      switch (sort.field) {
        case "date":
          return new Date(payment.createdAt).getTime()
        case "fileName":
          return payment.fileName
        case "fromAccount":
          return payment.currencyAccounts
        case "currentStatus":
          return payment.currentStatus
        default:
          return ""
      }
    },
  )

  function handleRowClick(payment?: IBulkPayment) {
    return setSelectedPayment({ bulkPaymentId: payment?.id })
  }

  return {
    currentPage,
    sortedPayments,
    isLoading,
    totalItems: paymentsData?.totalCount,
    onNext: handleNext,
    onPrev: handlePrev,
    setPageSize,
    setCurrentPage,
    setSort,
    handleRowClick,
  }
}

export default function BulkTable(props: PaymentsTableProps) {
  const {
    currentPage,
    sortedPayments,
    isLoading,
    totalItems,
    onNext,
    onPrev,
    setPageSize,
    setCurrentPage,
    setSort,
    handleRowClick,
  } = useBulkTable(props)

  return (
    <div className="overflow-x-auto">
      <DataTable
        aria-label="Bulk table"
        columns={[date, file, fromAccount, status, progress]}
        containerClassName="pr-2"
        currentPage={currentPage}
        data={sortedPayments}
        getRowKey={({ id }) => id}
        loading={isLoading}
        loadingRows={12}
        onNext={onNext}
        onPageChange={setCurrentPage}
        onPageSizeChange={setPageSize}
        onPrevious={onPrev}
        onRowClick={handleRowClick}
        onSort={(field, direction) => setSort({ field, direction })}
        pagination={{ showFirstLast: false, className: cn("pr-4") }}
        sortable
        stickyHeader
        totalItems={totalItems}
      />

      <BulkPaymentViewSheet
        id={props.queryParams?.bulkPaymentId}
        onClose={handleRowClick}
      />
    </div>
  )
}
