import { format } from "date-fns"

import { cn } from "@/lib/utils"
import { IBulkPayment } from "@/data/payments/payments.interface"
import { column } from "@/components/base/data-table"

import Progress from "./components/Cell.Progress"
import FromAccount from "./components/Cell.FromAccount"
import File from "./components/Cell.File"
import PaymentStatusBadge from "../../PaymentStatusBadge"

function CreatedAt({ date }: { date: string }) {
  const formattedDate = format(new Date(date), "dd MMM yyyy")
  return <>{formattedDate && formattedDate}</>
}

export const date = column.date<IBulkPayment>("createdAt", "Date", {
  render: (date) => <CreatedAt date={date} />,
  headerClassName: cn("w-[150px]"),
})

export const file = column.custom<IBulkPayment>(
  null,
  "File",
  (payment) => {
    if (!payment) return null

    return <File payment={payment} />
  },
  {
    key: "fileName",
    accessor: (payment) => payment,
    sortable: true,
    headerClassName: cn("min-w-[150px]"),
  },
)

export const fromAccount = column.custom<IBulkPayment>(
  null,
  "From Account",
  (payment) => {
    if (!payment) return <>-</>

    return <FromAccount payment={payment} />
  },
  {
    key: "currencyAccounts",
    accessor: (payment) => payment,
    headerClassName: cn("w-[200px]"),
  },
)

export const status = column.custom<IBulkPayment>(
  null,
  "Status",
  (status) => {
    if (!status) return null

    return <PaymentStatusBadge status={status} />
  },
  {
    key: "currentStatus",
    accessor: ({ currentStatus }) => currentStatus,
    headerClassName: cn("w-[180px]"),
    sortable: true,
  },
)

export const progress = column.custom<IBulkPayment>(
  null,
  "Progress",
  (payment) => {
    if (!payment) return null

    return <Progress payment={payment} />
  },
  {
    key: "paymentStatuses",
    accessor: (payment) => payment,
    headerClassName: cn("w-[300px]"),
  },
)
