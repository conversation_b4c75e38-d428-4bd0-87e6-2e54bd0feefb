import { Layers3Icon } from "lucide-react"

import { formatAccountNumber } from "@/lib/bank.utils"
import { IOutboundPayment } from "@/data/payments/payments.interface"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export default function Payee({ payment }: { payment: IOutboundPayment }) {
  return (
    <div className="inline-flex flex-col">
      <span className="flex items-center gap-1">
        {payment.bulkPaymentUploadId && (
          <Tooltip>
            <TooltipTrigger>
              <Layers3Icon className="h-4 w-4" />
            </TooltipTrigger>
            <TooltipContent className="bg-foreground text-xs text-background">
              <span>Bulk Payment Related</span>
            </TooltipContent>
          </Tooltip>
        )}
        {payment.payee.accountName}
      </span>
      <span className="line-clamp-1 hidden text-start text-xs text-muted-foreground md:inline-flex">
        {`${
          (payment.payee.bank?.name || "N/A").length > 20
            ? (payment.payee.bank?.name || "N/A").substring(0, 20)
            : payment.payee.bank?.name || "N/A"
        } ${formatAccountNumber(
          payment.payee.accountNumber ?? payment.payee.iban,
        )}`}
      </span>
    </div>
  )
}
