import { formatAccountNumber } from "@/lib/bank.utils"
import { IOutboundPayment } from "@/data/payments/payments.interface"

export default function FromAccount({
  payment,
}: {
  payment: IOutboundPayment
}) {
  return (
    <div>
      {payment.currencyAccount?.clientAccount?.accountName || "-"}
      <div className="text-xs text-muted-foreground">
        {formatAccountNumber(
          payment.currencyAccount?.clientAccount?.virtualIban,
        )}
      </div>
    </div>
  )
}
