import { useState } from "react"
import { <PERSON> } from "@tanstack/react-router"

import { cn } from "@/lib/utils"
import { IOutboundPayment } from "@/data/payments/payments.interface"
import { Button } from "@/components/ui/button"
import DataTable from "@/components/base/data-table"

import {
  amount,
  date,
  fromAccount,
  payee,
  paymentsCode,
  status,
} from "./Single.columns"
import usePaymentsTable from "../table.hook"
import { PaymentsTableProps } from "../interface"
import { PaymentDetailSheet } from "../../PaymentDetailSheet"
import { BulkPaymentRowDetailSheet } from "../../bulk/BulkPaymentRowDetailSheet"

function useSingleTable(props: PaymentsTableProps) {
  const {
    paymentsData,
    isLoading,
    currentPage,
    sort,
    setPageSize,
    setCurrentPage,
    setSort,
    sortData,
    handleNext,
    handlePrev,
    setSelectedPayment,
  } = usePaymentsTable<IOutboundPayment>("default", props)

  const [paymentDetails, setPaymentDetails] = useState<
    IOutboundPayment | undefined
  >()

  const sortedPayments = sortData(
    (paymentsData?.data as IOutboundPayment[]) ?? [],
    (payment) => {
      switch (sort.field) {
        case "date":
          return new Date(payment.createdAt).getTime()
        case "payee":
          return payment.payee.accountName
        case "amount":
          return payment.amount
        case "fromAccount":
          return payment.currencyAccount?.clientAccount?.accountName
        case "currentStatus":
          return payment.currentStatus
        default:
          return ""
      }
    },
  )

  function handleRowClick(payment?: IOutboundPayment) {
    if (props.bulkUploadId) {
      return setPaymentDetails(payment)
    }

    return setSelectedPayment({ paymentId: payment?.id })
  }

  function handleCloseBulkDetailSheet() {
    setPaymentDetails(undefined)
  }

  return {
    currentPage,
    sortedPayments,
    isLoading,
    totalItems: paymentsData?.totalCount,
    paymentDetails,
    onNext: handleNext,
    onPrev: handlePrev,
    setPageSize,
    setCurrentPage,
    setSort,
    setSelectedPayment,
    handleRowClick,
    handleCloseBulkDetailSheet,
  }
}

export default function SingleTable(props: PaymentsTableProps) {
  const {
    currentPage,
    sortedPayments,
    isLoading,
    totalItems,
    paymentDetails,
    onNext,
    onPrev,
    setPageSize,
    setCurrentPage,
    setSort,
    setSelectedPayment,
    handleRowClick,
    handleCloseBulkDetailSheet,
  } = useSingleTable(props)

  if (props.bulkUploadId) {
    return (
      <div className="flex h-full min-h-80 w-full items-center justify-center">
        <div className="flex flex-col items-center justify-center gap-4">
          <h2 className="text-lg font-medium">No payments found</h2>
          <>
            <p className="text-sm text-muted-foreground">
              {props.filter === "all" ? (
                <>
                  Looks like bulk payment file is not processed yet. Please
                  check bulk status and process the file.
                </>
              ) : (
                "No payments found for the selected filter."
              )}
            </p>
            <Button size="sm" variant="outline">
              <Link
                params={{
                  entityId: props.entityId,
                  bulkPaymentId: props.bulkUploadId,
                }}
                to="/$entityId/payments/bulk/$bulkPaymentId"
              >
                Check bulk status
              </Link>
            </Button>
          </>
        </div>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <DataTable
        aria-label="Outbound table"
        columns={[date, paymentsCode, payee, amount, fromAccount, status]}
        containerClassName="pr-2"
        currentPage={currentPage}
        data={sortedPayments}
        getRowKey={({ id }) => id}
        loading={isLoading}
        loadingRows={12}
        maxHeight="sm"
        onNext={onNext}
        onPageChange={setCurrentPage}
        onPageSizeChange={setPageSize}
        onPrevious={onPrev}
        onRowClick={handleRowClick}
        onSort={(field, direction) => setSort({ field, direction })}
        pagination={{ showFirstLast: false, className: cn("pr-4") }}
        sortable
        stickyHeader
        totalItems={totalItems}
      />

      <PaymentDetailSheet
        entityId={props.entityId}
        onOpenChange={(v) => !v && setSelectedPayment()}
        open={!!props.queryParams?.paymentId}
        paymentId={props.queryParams?.paymentId}
      />

      <BulkPaymentRowDetailSheet
        onClose={handleCloseBulkDetailSheet}
        payment={paymentDetails}
      />
    </div>
  )
}
