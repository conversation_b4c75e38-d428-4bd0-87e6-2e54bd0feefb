import { format } from "date-fns"

import { cn } from "@/lib/utils"
import { IOutboundPayment } from "@/data/payments/payments.interface"
import { column } from "@/components/base/data-table"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

import Payee from "./components/Cell.Payee"
import FromAccount from "./components/Cell.FromAccount"
import PaymentStatusBadge from "../../PaymentStatusBadge"

function CreatedAt({ date }: { date: string }) {
  const formattedDate = format(new Date(date), "dd MMM yyyy")
  return <>{formattedDate && formattedDate}</>
}

export const payee = column.custom<IOutboundPayment>(
  null,
  "Payee",
  (payment) => {
    if (!payment) return null

    return <Payee payment={payment} />
  },
  {
    key: "payee",
    accessor: (payment) => payment,
    sortable: true,
    headerClassName: cn("w-[200px]"),
  },
)

export const fromAccount = column.custom<IOutboundPayment>(
  null,
  "From Account",
  (payment) => {
    if (!payment) return null

    return <FromAccount payment={payment} />
  },
  {
    key: "currencyAccount",
    accessor: (payment) => payment,
    headerClassName: cn("w-[200px]"),
  },
)

export const amount = column.custom<IOutboundPayment>(
  null,
  "Amount",
  (data) => {
    if (!data) return null

    return (
      <div className="flex flex-col">
        <CurrencyText
          amount={data.amount}
          className="max-w-[150px] truncate"
          currency={data.currency}
          displayModes={["amount"]}
        />

        <span className="line-clamp-1 hidden text-start text-xs text-muted-foreground md:inline-flex">
          {data.currency}
        </span>
      </div>
    )
  },
  {
    key: "amount",
    accessor: ({ amount, currency }) => ({ amount, currency }),
    headerClassName: cn("w-[150px]"),
    sortable: true,
  },
)

export const date = column.date<IOutboundPayment>("createdAt", "Date", {
  render: (date) => <CreatedAt date={date} />,
  headerClassName: cn("w-[100px]"),
})

export const paymentsCode = column.text<IOutboundPayment>("code", "Code", {
  headerClassName: cn("w-[130px]"),
})

export const status = column.custom<IOutboundPayment>(
  null,
  "Status",
  (status: string) => {
    if (!status) return null

    return <PaymentStatusBadge status={status} />
  },
  {
    key: "currentStatus",
    accessor: ({ currentStatus }) => currentStatus,
    headerClassName: cn("w-[240px]"),
    sortable: true,
  },
)
