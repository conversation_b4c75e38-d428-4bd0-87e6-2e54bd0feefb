import { toast } from "sonner"
import { useCallback, useEffect, useState } from "react"
import { useNavigate } from "@tanstack/react-router"

import { usePaymentsListQuery } from "@/data/payments/payments.query"
import { SortState } from "@/components/base/data-table"

import { PaymentsTableProps } from "./interface"

interface UsePaymentsTableProps<T> extends PaymentsTableProps {
  onPaymentClick?: (payment: T) => void
}

export default function usePaymentsTable<T>(
  variant: "bulk" | "default",
  props: UsePaymentsTableProps<T>,
) {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [sort, setSort] = useState<SortState>({
    field: null,
    direction: "none",
  })

  const {
    data: paymentsData,
    isLoading,
    isError,
    isPlaceholderData,
  } = usePaymentsListQuery({
    entityId: props.entityId,
    variant: variant,
    pageSize,
    pageNumber: currentPage,
    currentStatus: props.filter,
    payeeName: props.searchQuery,
    bulkUploadId: props.bulkUploadId,
  })

  useEffect(() => {
    if (isError) {
      toast.error("Failed to fetch payments")
    }
  }, [isError])

  const navigate = useNavigate({
    from: "/$entityId/payments",
  })

  const setSelectedPayment = useCallback(
    (payment?: { paymentId?: string; bulkPaymentId?: string }) => {
      const {
        paymentId: _paymentId,
        bulkPaymentId: _bulkPaymentId,
        ...restParams
      } = props.queryParams || {}

      if (payment?.paymentId) {
        return navigate({
          search: { ...restParams, paymentId: payment.paymentId },
          replace: true,
        })
      }

      if (payment?.bulkPaymentId) {
        return navigate({
          search: { ...restParams, bulkPaymentId: payment.bulkPaymentId },
          replace: true,
        })
      }

      navigate({
        search: { ...restParams },
        replace: true,
      })
    },
    [navigate, props.queryParams],
  )

  function sortData(data: T[], fields: (payment: T) => unknown) {
    if (!sort.field || sort.direction === "none") return data

    return [...data].sort((a, b) => {
      const aValue = fields(a)
      const bValue = fields(b)

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sort.direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return sort.direction === "asc"
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })
  }

  function handleNext() {
    if (!isPlaceholderData && paymentsData?.pageNumber) {
      setCurrentPage(paymentsData.pageNumber + 1)
    }
  }

  function handlePrev() {
    if (!isPlaceholderData && paymentsData?.pageNumber) {
      setCurrentPage(paymentsData.pageNumber - 1)
    }
  }

  return {
    currentPage,
    pageSize,
    sort,
    paymentsData,
    isLoading,
    isError,
    isPlaceholderData,
    setCurrentPage,
    setPageSize,
    setSort,
    sortData,
    handleNext,
    handlePrev,
    setSelectedPayment,
  }
}
