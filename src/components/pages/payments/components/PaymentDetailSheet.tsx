import { useMemo } from "react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { usePaymentQuery } from "@/data/payments/payments.query"
import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

import { NonFxPaymentDetailSheet } from "./payment-details-sheet/NonFxPaymentDetailSheet"
import { FxPaymentDetailSheet } from "./payment-details-sheet/FxPaymentDetailSheet"

interface PaymentDetailSheetProps {
  paymentId?: string
  open: boolean
  onOpenChange: (open: boolean) => void
  entityId: string
}

export function PaymentDetailSheet({
  paymentId,
  open,
  onOpenChange,
  entityId,
}: PaymentDetailSheetProps) {
  const { data: payment, isLoading: isLoadingPayment } =
    usePaymentQuery(paymentId)

  const isFxPayment = useMemo(() => {
    if (!payment) return false
    return (
      payment.currency !== payment.remitterCurrency &&
      !!payment.remitterCurrency
    )
  }, [payment])

  return (
    <Sheet onOpenChange={onOpenChange} open={open}>
      <VisuallyHidden>
        <SheetTitle>Payment details</SheetTitle>
        <SheetDescription>View payment details</SheetDescription>
      </VisuallyHidden>
      <SheetContent className="flex flex-col gap-4 overflow-y-auto sm:max-w-md md:max-w-lg lg:max-w-xl">
        {isLoadingPayment ? (
          <div className="flex h-96 items-center justify-center">
            <LoadingSpinner size="8" />
          </div>
        ) : (
          <>
            {isFxPayment ? (
              <FxPaymentDetailSheet entityId={entityId} payment={payment} />
            ) : (
              <NonFxPaymentDetailSheet entityId={entityId} payment={payment} />
            )}
          </>
        )}
      </SheetContent>
    </Sheet>
  )
}
