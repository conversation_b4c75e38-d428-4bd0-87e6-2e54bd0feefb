import { Layers3Icon } from "lucide-react"

import { formatAccountNumber } from "@/lib/bank.utils"
import {
  IBulkPayment,
  IOutboundPayment,
  Payment,
} from "@/data/payments/payments.interface"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { TableCell } from "@/components/ui/table"

export function PaymentTablePayeeCell({ payment }: { payment: Payment }) {
  if (payment.paymentType === "OutboundPayment") {
    const outboundPayment = payment as IOutboundPayment
    return (
      <TableCell>
        <div className="font-normal">
          <span className="flex items-center gap-1">
            {outboundPayment.bulkPaymentUploadId && (
              <Tooltip>
                <TooltipTrigger>
                  <Layers3Icon className="h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent className="bg-foreground text-xs text-background">
                  <span>Bulk Payment Related</span>
                </TooltipContent>
              </Tooltip>
            )}
            {outboundPayment.payee.accountName}
          </span>
          <div className="text-xs text-muted-foreground">
            {`${
              (outboundPayment.payee.bank?.name || "N/A").length > 20
                ? (outboundPayment.payee.bank?.name || "N/A").substring(0, 20)
                : outboundPayment.payee.bank?.name || "N/A"
            } ${formatAccountNumber(
              outboundPayment.payee.accountNumber ?? outboundPayment.payee.iban,
            )}`}
            {/* {outboundPayment.payee.bank.name}{" "}
            {formatAccountNumber(
              outboundPayment.payee.accountNumber ?? outboundPayment.payee.iban,
            )} */}
          </div>
        </div>
      </TableCell>
    )
  }
  const bulkPayment = payment as IBulkPayment
  return (
    <TableCell>
      <div className="font-normal">
        <span className="flex items-center gap-1">
          <Layers3Icon className="h-4 w-4" />

          {"Bulk Payment"}
        </span>
        <div className="text-xs text-muted-foreground">
          {bulkPayment.fileName}
        </div>
      </div>
    </TableCell>
  )
}
