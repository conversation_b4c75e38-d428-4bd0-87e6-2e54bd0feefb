import { useMemo } from "react"
import { CheckCircle, Clock, RefreshCcw, XCircle } from "lucide-react"

import {
  BULK_PAYMENT_PAYMENT_STATUS,
  IBulkPayment,
  Payment,
  TBulkPaymentPaymentStatus,
} from "@/data/payments/payments.interface"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { TableCell } from "@/components/ui/table"

interface PaymentTableDetailsCellProps {
  payment: Payment
}

const getIcon = (status: TBulkPaymentPaymentStatus) => {
  switch (status) {
    case "Sent":
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case "Processing":
      return <RefreshCcw className="h-4 w-4 text-info-foreground" />
    case "Pending":
      return <Clock className="h-4 w-4 text-info" />
    case "Rejected":
      return <XCircle className="h-4 w-4 text-destructive" />
  }
}

export const PaymentTableDetailsCell = ({
  payment,
}: PaymentTableDetailsCellProps) => {
  function getStatus(status: TBulkPaymentPaymentStatus) {
    const _p = payment as IBulkPayment
    const _s = _p.paymentStatuses.find((s) => s.status === status)
    return {
      icon: getIcon(status),
      count: _s?.count ?? 0,
      label: status,
    }
  }

  const showStatusItems = useMemo(() => {
    if (payment.paymentType === "OutboundPayment") return

    return payment.paymentStatuses.some((s) => s.count > 0)
  }, [payment])

  if (payment.paymentType == "OutboundPayment") return <TableCell></TableCell>

  if (!showStatusItems) return <TableCell></TableCell>

  return (
    <TableCell>
      <div className="grid grid-cols-2 gap-2">
        {BULK_PAYMENT_PAYMENT_STATUS.map((status) => {
          const _s = getStatus(status)
          return (
            <Tooltip key={status}>
              <TooltipTrigger>
                <span className="flex items-center gap-1">
                  {_s.icon} {_s.count}
                </span>
              </TooltipTrigger>
              <TooltipContent className="bg-foreground text-xs text-background">
                {_s.label}
              </TooltipContent>
            </Tooltip>
          )
        })}
      </div>
    </TableCell>
  )
}
