import { useMemo } from "react"
import {
  CircleCheckIcon,
  CircleXIcon,
  InfoIcon,
  TriangleAlertIcon,
} from "lucide-react"

import { cn } from "@/lib/utils"
import {
  BULK_PAYMENT_STATUS_MAP,
  BulkPaymentStatusType,
  Payment,
} from "@/data/payments/payments.interface"
import { TableCell } from "@/components/ui/table"

const variants = {
  pending: "bg-warning/5 text-warning border-warning/50 stroke-warning/50",
  success: "bg-success/10 text-success border-success/50 stroke-success/50",
  error:
    "bg-destructive/10 text-destructive border-destructive/50 stroke-destructive/50",
  info: "bg-info/10 text-info border-info/50 stroke-info/50",
}

type PaymentStatus = keyof typeof variants

export const PaymentStatusCell = ({ payment }: { payment: Payment }) => {
  return (
    <TableCell>
      <PaymentStatusBadge
        paymentType={payment.paymentType}
        status={payment.currentStatus}
      />
    </TableCell>
  )
}

export const PaymentStatusBadge = ({
  status: currentStatus,
  paymentType,
}: {
  status: string
  paymentType: "BulkPayment" | "OutboundPayment"
}) => {
  const variant: PaymentStatus = useMemo(() => {
    if (
      ["pending", "validating", "readytoprocess", "processing"].some((v) =>
        currentStatus?.toLowerCase().includes(v),
      )
    ) {
      return "pending"
    }
    if (
      ["completed", "success", "sent"].some((v) =>
        currentStatus?.toLowerCase().includes(v),
      )
    ) {
      return "success"
    }
    if (
      ["error", "fail", "cancel", "rejected", "insufficient balance"].some(
        (v) => currentStatus?.toLowerCase().includes(v),
      )
    ) {
      return "error"
    }
    return "info"
  }, [currentStatus])

  const status = useMemo(() => {
    if (paymentType === "BulkPayment") {
      return (
        BULK_PAYMENT_STATUS_MAP[currentStatus as BulkPaymentStatusType] ??
        currentStatus
      )
    }
    return currentStatus
  }, [currentStatus, paymentType])

  return (
    <div className="flex flex-none items-center">
      <span
        className={cn(
          "flex flex-none items-center gap-1 text-nowrap rounded-lg border px-2 py-1 text-xs",
          variants[variant],
        )}
      >
        {variant === "pending" && <TriangleAlertIcon className="h-3 w-3" />}
        {variant === "success" && <CircleCheckIcon className="h-3 w-3" />}
        {variant === "error" && <CircleXIcon className="h-3 w-3" />}
        {variant === "info" && <InfoIcon className="h-3 w-3" />}
        {status}
      </span>
    </div>
  )
}
