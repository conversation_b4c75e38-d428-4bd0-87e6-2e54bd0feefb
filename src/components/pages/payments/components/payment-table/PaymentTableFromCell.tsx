import { formatAccountNumber } from "@/lib/bank.utils"
import {
  IBulkPayment,
  IOutboundPayment,
  Payment,
} from "@/data/payments/payments.interface"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { TableCell } from "@/components/ui/table"

export function PaymentTableFromCell({ payment }: { payment: Payment }) {
  if (payment.paymentType === "OutboundPayment") {
    const outboundPayment = payment as IOutboundPayment
    return (
      <TableCell>
        <div className="font-normal">
          {outboundPayment.currencyAccount?.clientAccount?.accountName || "-"}
          <div className="text-xs text-muted-foreground">
            {formatAccountNumber(
              outboundPayment.currencyAccount?.clientAccount?.virtualIban,
            )}
          </div>
        </div>
      </TableCell>
    )
  }
  const bulkPayment = payment as IBulkPayment

  const hasCurrencyAccounts =
    "currencyAccounts" in bulkPayment &&
    Array.isArray(bulkPayment?.currencyAccounts) &&
    bulkPayment?.currencyAccounts.length

  if (!hasCurrencyAccounts) return <TableCell></TableCell>

  return (
    <TableCell>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="font-normal underline">
            {hasCurrencyAccounts && hasCurrencyAccounts > 1
              ? `${hasCurrencyAccounts} accounts`
              : bulkPayment?.currencyAccounts[0]?.accountName}
          </span>
        </TooltipTrigger>
        <TooltipContent className="border-1 border-border bg-background shadow-md">
          <div className="flex flex-col gap-2">
            {hasCurrencyAccounts &&
              bulkPayment?.currencyAccounts.map((account, i) => (
                <div className={i !== 0 ? "border-t pt-2" : ""} key={i}>
                  <div className="flex flex-col gap-2">
                    <p className="text-sm font-medium text-foreground">
                      {account.accountName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatAccountNumber(account.virtualIban)}
                    </p>
                  </div>
                </div>
              ))}
          </div>
        </TooltipContent>
      </Tooltip>
    </TableCell>
  )
}
