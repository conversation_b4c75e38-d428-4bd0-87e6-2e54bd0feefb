import { Download } from "lucide-react"

import { downloadBulkPaymentTemplate } from "@/data/payments/payments.api"
import { AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertBox } from "@/components/base/alert-box"

const alertVariants = {
  default: {
    title: "Payment file template required",
    description: "A payment file template must be used to import data.",
  },
  "data-header-columns": {
    title: "The data header columns are not correct",
    description:
      "Please make sure the file has the same columns as the payment file template below.",
  },
  "file-format": {
    title: "The file format is not correct",
    description:
      "Please make sure the file is CSV format and has the same columns as the payment file template below.",
  },
} as const

export type BulkPaymentErrorType = keyof typeof alertVariants

interface BulkPaymentAlertProps {
  variant: "error" | "warning"
  error: BulkPaymentErrorType | null
}

export function BulkPaymentAlert({ error, variant }: BulkPaymentAlertProps) {
  const title = alertVariants[error ?? "default"]?.title
  const description = alertVariants[error ?? "default"]?.description

  return (
    <AlertBox
      className="rounded-lg border bg-muted/40 px-4 py-3"
      severity={variant}
    >
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>
        {description}
        <p>
          <button
            className="mt-2 flex items-center gap-1 text-green-700 hover:underline"
            onClick={async () => {
              await downloadBulkPaymentTemplate()
            }}
          >
            <Download className="h-4 w-4" />
            Download payment file template
          </button>
        </p>
      </AlertDescription>
    </AlertBox>
  )
}
