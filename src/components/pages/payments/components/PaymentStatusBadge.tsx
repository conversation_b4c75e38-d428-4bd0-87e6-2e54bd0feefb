import { useMemo } from "react"
import { CircleDashed, Clock5, Octagon<PERSON> } from "lucide-react"

import {
  BULK_PAYMENT_STATUS_MAP,
  BulkPaymentStatusType,
} from "@/data/payments/payments.interface"
import StatusBadge, {
  StatusVariant,
} from "@/components/pages/_components/status-badge/StatusBadge"

const PAYMENT_STATUS_KEYWORDS = {
  pending: ["pending", "validating", "readytoprocess", "processing"],
  success: ["completed", "success", "sent"],
  error: ["error", "fail", "cancel", "rejected", "insufficient balance"],
} as const

// Helper function to determine variant based on payment status
const getPaymentVariant = (status: string): StatusVariant => {
  if (!status) return "info"
  if (status === "Pending") return "warning"
  if (status === "Insufficient Funds") return "error"
  if (status === "ReadyForReview" || status === "ReadyForSubmission")
    return "info"

  const lowerStatus = status.toLowerCase()

  for (const [variant, keywords] of Object.entries(PAYMENT_STATUS_KEYWORDS)) {
    if (keywords.some((keyword) => lowerStatus.includes(keyword))) {
      if (variant === "pending") return "warning"

      return variant as StatusVariant
    }
  }

  return "neutral"
}

// Helper function to get display status for payments
const getPaymentDisplayStatus = (status: string): string => {
  return BULK_PAYMENT_STATUS_MAP[status as BulkPaymentStatusType] ?? status
}

interface PaymentStatusBadgeProps {
  status: string
  className?: string
}

export default function PaymentStatusBadge({
  status,
  className,
}: PaymentStatusBadgeProps) {
  const variant = useMemo(() => getPaymentVariant(status), [status])

  const displayStatus = useMemo(() => getPaymentDisplayStatus(status), [status])

  const icon = useMemo(() => {
    const icons = {
      error: OctagonX,
      warning: Clock5,
      neutral: CircleDashed,
    } as const

    return variant in icons ? icons[variant as keyof typeof icons] : undefined
  }, [variant])

  return (
    <StatusBadge
      className={className}
      icon={icon}
      text={displayStatus}
      variant={variant}
    />
  )
}
