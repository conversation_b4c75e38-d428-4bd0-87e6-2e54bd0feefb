import { describe, expect, it, Mock, vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"

import { TestRouter } from "@/tests/utils/TestRouter"
import {
  usePaymentQuery,
  usePaymentsListQuery,
} from "@/data/payments/payments.query"
import { Payment } from "@/data/payments/payments.interface"
import { PaymentsListingPage } from "@/components/pages/payments/Page"

// Mock router hooks
vi.mock("@tanstack/react-router", async (importOriginal) => {
  const actual = await importOriginal()

  // Create a safe version of actual to spread
  const safeActual = actual as Record<string, unknown>

  return {
    ...safeActual,
    useSearch: vi.fn(() => ({
      tab: "default",
      filter: "All",
      page: 1,
      from: "/",
    })),
  }
})

// Mock the queries
vi.mock("@/data/payments/payments.query", () => ({
  usePaymentsListQuery: vi.fn(() => ({
    isLoading: false,
    data: {
      data: [],
      totalCount: 0,
      pageNumber: 1,
      pageSize: 10,
      isFirstPage: true,
      isLastPage: true,
    },
  })),
  usePaymentQuery: vi.fn(() => ({
    isLoading: false,
    data: [],
  })),
}))

// Mock child components
vi.mock("@/components/pages/payments/components/PaymentsTable", () => ({
  default: ({ payments }: { payments: Payment[] }) => (
    <div data-testid="payments-table">
      {payments?.map((payment) => (
        <div data-testid="payment-row" key={payment.id}>
          {payment.paymentType}
        </div>
      ))}
    </div>
  ),
}))

vi.mock("@/components/pages/payments/components/TrailingPayButton", () => ({
  TrailingPayButton: () => <button>Pay</button>,
}))

vi.mock("@/components/pages/payments/components/ButtonGroup", () => ({
  ButtonGroup: ({ setActiveFilter }: any) => (
    <div data-testid="button-group">
      <button onClick={() => setActiveFilter("All")}>All</button>
      <button onClick={() => setActiveFilter("Pending")}>Pending</button>
      <button onClick={() => setActiveFilter("Sent")}>Sent</button>
      <button onClick={() => setActiveFilter("Failed")}>Failed</button>
    </div>
  ),
}))

// Mock PaymentDetailSheet component
vi.mock("@/components/pages/payments/components/PaymentDetailSheet", () => ({
  PaymentDetailSheet: ({ payment, open }: any) => (
    <div data-testid="payment-detail-sheet">
      {open && <div data-testid="payment-details">{payment?.id}</div>}
    </div>
  ),
}))

describe("PaymentsPage", () => {
  it("should render loading state", () => {
    ;(usePaymentsListQuery as any).mockReturnValue({
      isLoading: true,
      data: null,
    })

    render(<PaymentsListingPage entityId="1" />, { wrapper: TestRouter })

    expect(screen.getByLabelText("Loading")).toBeInTheDocument()
  })

  it("should render payments when data is loaded", () => {
    const mockPayments = [
      {
        id: "1",
        paymentType: "OutboundPayment",
        code: "PAY001",
        type: "Standard",
        valueDate: "2023-01-01",
        payee: {
          id: "payee1",
          accountName: "John Doe",
          accountNumber: "*********",
          iban: "**********************",
          type: "Individual",
          bank: {
            name: "Test Bank",
            nationalId: "12345",
            nationalIdType: "BIC",
            swiftBic: "TESTBIC1",
            country: {
              name: "United Kingdom",
              formalName: "United Kingdom",
              codeIso2: "GB",
              codeIso3: "GBR",
              codeIso3Numeric: "826",
              phoneCode: "44",
              ibanLength: 22,
              ibanRegex: null,
              ibanSupported: true,
              accountNumberType: "IBAN",
              nationalIdType: "Swift/BIC",
              isSepaCountry: true,
              paymentPurposeCodeRequired: false,
            },
          },
        },
        currencyAccount: {
          id: "account1",
          accountName: "Main Account",
          virtualIban: "**********************",
        },
        currentStatus: "Pending",
        createdAt: "2023-01-01T00:00:00Z",
        amount: 100,
        currency: "GBP",
      },
    ]

    ;(usePaymentsListQuery as Mock).mockReturnValue({
      isLoading: false,
      data: {
        data: mockPayments,
        totalCount: 1,
        pageNumber: 1,
        pageSize: 10,
        isFirstPage: true,
        isLastPage: true,
      },
    })
    ;(usePaymentQuery as Mock).mockReturnValue({
      isLoading: false,
      data: null,
    })

    render(<PaymentsListingPage entityId="1" />, {
      wrapper: TestRouter,
    })

    expect(screen.getByLabelText("Outbound table")).toBeInTheDocument()
    expect(screen.queryByLabelText("Loading")).not.toBeInTheDocument()
  })

  it("should handle pagination correctly", () => {
    // Setup mock data for pagination
    const mockPayments = [
      {
        id: "1",
        paymentType: "OutboundPayment",
        code: "PAY001",
        currentStatus: "Pending",
        createdAt: "2023-01-01T00:00:00Z",
        amount: 100,
        currency: "GBP",
        valueDate: "2023-01-01",
        payee: {
          id: "payee1",
          accountName: "John Doe",
          accountNumber: "*********",
          iban: "**********************",
          type: "Individual",
          bank: {
            name: "Test Bank",
            nationalId: null,
            nationalIdType: null,
            swiftBic: "TESTBIC1",
            country: {
              name: "United Kingdom",
              formalName: "United Kingdom",
              codeIso2: "GB",
              codeIso3: "GBR",
              codeIso3Numeric: "826",
              phoneCode: "44",
              ibanLength: 22,
              ibanRegex: null,
              ibanSupported: true,
              accountNumberType: "IBAN",
              nationalIdType: "Swift/BIC",
              isSepaCountry: true,
              paymentPurposeCodeRequired: false,
            },
          },
        },
        currencyAccount: {
          id: "account1",
          accountName: "Main Account",
          virtualIban: "**********************",
        },
      },
    ]

    // Setup mock implementation that will update when pagination changes
    let currentPage = 2

    ;(usePaymentsListQuery as Mock).mockReturnValue({
      isLoading: false,
      data: {
        data: mockPayments,
        totalCount: 20, // Total of 20 items, so 2 pages with pageSize 10
        pageNumber: currentPage,
        pageSize: 10,
        isFirstPage: currentPage === 1,
        isLastPage: currentPage === 2,
      },
    })

    const { rerender } = render(<PaymentsListingPage entityId="1" />, {
      wrapper: TestRouter,
    })

    // Click next page button
    fireEvent.click(screen.getByLabelText("Next"))

    // Update the mock page number for the next render
    currentPage = 2

    // Rerender to simulate the component updating after the state change
    rerender(<PaymentsListingPage entityId="1" />)

    // Check page has been updated

    // Previous button should now be enabled
    // expect(screen.getByLabelText("Previous")).not.toBeDisabled()

    // Next button should be disabled on last page
    // expect(screen.getByLabelText("Next")).toBeDisabled()
  })
})
