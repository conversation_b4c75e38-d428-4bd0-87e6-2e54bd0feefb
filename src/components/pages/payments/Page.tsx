import { useMemo, useState } from "react"
import { ArrowUpRightIcon, Layers, Search } from "lucide-react"
import { useDebounce } from "@uidotdev/usehooks"
import { useSearch, useNavigate } from "@tanstack/react-router"

import { PaymentsSearchFilterKeys } from "@/routes/_auth/$entityId/payments"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"

import { TrailingPayButton } from "./components/TrailingPayButton"
import { SingleTable } from "./components/table/single"
import { BulkTable } from "./components/table/bulk"
import FilterTabs from "../_components/filter-tabs"

export function PaymentsListingPage({
  entityId,
  bulkUploadId,
}: {
  entityId: string
  bulkUploadId?: string
}) {
  const [searchQuery, setSearchQuery] = useState("")

  const searchParams = useSearch({
    from: "/_auth/$entityId/payments",
  })

  const navigate = useNavigate({ from: "/$entityId/payments" })

  const variants = useMemo(
    () => (bulkUploadId ? ["default"] : ["default", "bulk"]),
    [bulkUploadId],
  )

  const variant = useMemo(
    () =>
      variants.includes(searchParams?.tab ?? "")
        ? (searchParams.tab as "default" | "bulk")
        : "default",
    [searchParams.tab, variants],
  )

  const setVariant = (variant: "default" | "bulk") => {
    navigate({
      search: { ...searchParams, tab: variant, filter: "All" },
    })
  }

  const filters = useMemo(
    () =>
      variant === "default"
        ? ["All", "Draft", "Pending", "Sent", "Failed"]
        : ["All", "Validating", "Ready", "Pending", "Processed", "Cancelled"],
    [variant],
  )

  const activeFilter = useMemo(
    () => filters.find((filter) => filter === searchParams.filter) ?? "All",
    [searchParams.filter, filters],
  )

  const searchQueryDebounced = useDebounce(searchQuery, 500)

  const setActiveFilter = (filter: PaymentsSearchFilterKeys) => {
    navigate({ search: { ...searchParams, filter } })
  }

  return (
    <div className="flex flex-col gap-4 px-4">
      {variants.length > 1 && (
        <Tabs
          className="border-none bg-background"
          onValueChange={(value) => {
            if (value === "default") {
              setVariant("default")
            } else {
              setVariant("bulk")
            }
          }}
          value={variant}
        >
          <TabsList className="rounded-full border-none bg-background">
            <TabsTrigger
              className="border-none data-[state=active]:rounded-full data-[state=active]:bg-muted"
              value="default"
            >
              <ArrowUpRightIcon className="mr-2 h-4 w-4" />
              Single
            </TabsTrigger>
            <TabsTrigger
              className="border-none data-[state=active]:rounded-full data-[state=active]:bg-muted"
              value="bulk"
            >
              <Layers className="mr-2 h-4 w-4" />
              Bulk
            </TabsTrigger>
          </TabsList>
        </Tabs>
      )}

      <div className="rounded-lg bg-background py-4">
        {/* Header */}
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="relative w-60">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                className="w-full rounded-xl pl-9 shadow-none"
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search"
                type="text"
                value={searchQuery}
              />
            </div>
            <div className="flex space-x-2">
              <FilterTabs
                active={activeFilter}
                items={filters}
                setActive={(filter) => {
                  setActiveFilter(filter as PaymentsSearchFilterKeys)
                }}
              />
            </div>
          </div>
          {!bulkUploadId && <TrailingPayButton entityId={entityId} />}
        </div>

        {variant === "default" && (
          <SingleTable
            bulkUploadId={bulkUploadId}
            entityId={entityId}
            filter={activeFilter}
            queryParams={searchParams}
            searchQuery={searchQueryDebounced}
          />
        )}

        {variant === "bulk" && (
          <BulkTable
            entityId={entityId}
            filter={activeFilter}
            queryParams={searchParams}
            searchQuery={searchQueryDebounced}
          />
        )}
      </div>
    </div>
  )
}
