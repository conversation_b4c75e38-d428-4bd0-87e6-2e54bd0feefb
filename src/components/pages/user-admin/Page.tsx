import { useUserAdminLoaderData } from "@/hooks/useUserAdminLoaderData"
import { useRolePermissions } from "@/hooks/useRolePermissions"

import { Users } from "./components/Users/<USER>"
import { RolesandPermissions } from "./components/RolesAndPermissions/Page"
import { ApproverSignatoriesAndRules } from "./components/Approver/Page"
import FilterTabs from "../_components/filter-tabs"

export function UserAdminPage() {
  const { entity, entities } = useUserAdminLoaderData()
  const { getPermission } = useRolePermissions()

  // Check if current user is an admin for this entity
  const isAdmin = getPermission("UserManagement.Add")

  // Check if entity is a Client (only show Approvers tab for Client entities)
  const isClientEntity = entities?.some(
    (access) =>
      access.entityId?.toLowerCase() === entity.id?.toLowerCase() &&
      access.entityStatus === "Client",
  )

  const tabItems: string[] = ["Users", "Roles and permissions"]

  if (isAdmin && isClientEntity) {
    tabItems.push("Approvers, signatories and rules")
  }

  return (
    <div
      aria-label="User admin page"
      className="flex flex-1 flex-col gap-4 px-4"
    >
      <FilterTabs items={tabItems}>
        <FilterTabs.Item aria-label="Users page" value={tabItems[0]}>
          <Users />
        </FilterTabs.Item>

        <FilterTabs.Item
          aria-label="Roles and permissions page"
          value={tabItems[1]}
        >
          <RolesandPermissions />
        </FilterTabs.Item>

        {isAdmin && isClientEntity && (
          <FilterTabs.Item aria-label="Approver page" value={tabItems[2]}>
            <ApproverSignatoriesAndRules />
          </FilterTabs.Item>
        )}
      </FilterTabs>
    </div>
  )
}
