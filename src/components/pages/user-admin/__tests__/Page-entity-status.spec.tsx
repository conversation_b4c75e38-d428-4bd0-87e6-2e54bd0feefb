import { vi, describe, it, expect } from "vitest"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { OnboardingUserAdminProvider } from "@/contexts/OnboardingUserAdminContext"
import { UserAdminPage } from "@/components/pages/user-admin/Page"

// Mock the role permissions hook
const mockGetPermission = vi.fn()
vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(() => ({
    getPermission: mockGetPermission,
  })),
}))

// Mock the user admin components
vi.mock("@/components/pages/user-admin/components/Users/<USER>", () => ({
  Users: () => <div>Users Component</div>,
}))

vi.mock(
  "@/components/pages/user-admin/components/RolesAndPermissions/Page",
  () => ({
    RolesandPermissions: () => <div>Roles and Permissions Component</div>,
  }),
)

vi.mock("@/components/pages/user-admin/components/Approver/Page", () => ({
  ApproverSignatoriesAndRules: () => <div>Approvers Component</div>,
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

describe("UserAdminPage Entity Status", () => {
  it("shows Approvers tab for Client entity with admin user", () => {
    // Mock admin permissions
    mockGetPermission.mockReturnValue(true)
    const mockLoaderData = {
      entityId: "test-entity",
      entity: { id: "test-entity" },
      entities: [
        {
          entityId: "test-entity",
          entityStatus: "Client", // Client status
          roles: ["Administrator"],
        },
      ],
      user: { id: "user-1" },
    }

    const clientWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        <OnboardingUserAdminProvider loaderData={mockLoaderData}>
          {children}
        </OnboardingUserAdminProvider>
      </QueryClientProvider>
    )

    render(<UserAdminPage />, { wrapper: clientWrapper })

    expect(screen.getByText("Users")).toBeInTheDocument()
    expect(screen.getByText("Roles and permissions")).toBeInTheDocument()
    expect(
      screen.getByText("Approvers, signatories and rules"),
    ).toBeInTheDocument()
  })

  it("hides Approvers tab for ReadyForOnboarding entity even with admin user", () => {
    // Mock admin permissions
    mockGetPermission.mockReturnValue(true)
    const mockLoaderData = {
      entityId: "test-entity",
      entity: { id: "test-entity" },
      entities: [
        {
          entityId: "test-entity",
          entityStatus: "ReadyForOnboarding", // Not Client status
          roles: ["Administrator"],
        },
      ],
      user: { id: "user-1" },
    }

    const onboardingWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        <OnboardingUserAdminProvider loaderData={mockLoaderData}>
          {children}
        </OnboardingUserAdminProvider>
      </QueryClientProvider>
    )

    render(<UserAdminPage />, { wrapper: onboardingWrapper })

    expect(screen.getByText("Users")).toBeInTheDocument()
    expect(screen.getByText("Roles and permissions")).toBeInTheDocument()
    expect(
      screen.queryByText("Approvers, signatories and rules"),
    ).not.toBeInTheDocument()
  })

  it("hides Approvers tab for Onboarding entity even with admin user", () => {
    // Mock admin permissions
    mockGetPermission.mockReturnValue(true)
    const mockLoaderData = {
      entityId: "test-entity",
      entity: { id: "test-entity" },
      entities: [
        {
          entityId: "test-entity",
          entityStatus: "Onboarding", // Not Client status
          roles: ["Administrator"],
        },
      ],
      user: { id: "user-1" },
    }

    const onboardingWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        <OnboardingUserAdminProvider loaderData={mockLoaderData}>
          {children}
        </OnboardingUserAdminProvider>
      </QueryClientProvider>
    )

    render(<UserAdminPage />, { wrapper: onboardingWrapper })

    expect(screen.getByText("Users")).toBeInTheDocument()
    expect(screen.getByText("Roles and permissions")).toBeInTheDocument()
    expect(
      screen.queryByText("Approvers, signatories and rules"),
    ).not.toBeInTheDocument()
  })

  it("hides Approvers tab for Client entity with non-admin user", () => {
    // Mock non-admin permissions
    mockGetPermission.mockReturnValue(false)

    const mockLoaderData = {
      entityId: "test-entity",
      entity: { id: "test-entity" },
      entities: [
        {
          entityId: "test-entity",
          entityStatus: "Client", // Client status
          roles: ["Viewer"], // Non-admin role
        },
      ],
      user: { id: "user-1" },
    }

    const clientWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        <OnboardingUserAdminProvider loaderData={mockLoaderData}>
          {children}
        </OnboardingUserAdminProvider>
      </QueryClientProvider>
    )

    render(<UserAdminPage />, { wrapper: clientWrapper })

    expect(screen.getByText("Users")).toBeInTheDocument()
    expect(screen.getByText("Roles and permissions")).toBeInTheDocument()
    // Should not show Approvers tab for non-admin users, even on Client entities
    expect(
      screen.queryByText("Approvers, signatories and rules"),
    ).not.toBeInTheDocument()
  })
})
