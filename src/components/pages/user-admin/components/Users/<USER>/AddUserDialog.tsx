import { UserPlus } from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"

export function AddUserDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="py-2">
          <UserPlus className="size-4" />
          <span>Add user</span>
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add user</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )
}
