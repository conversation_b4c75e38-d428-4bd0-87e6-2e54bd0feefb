import React, { <PERSON>, PropsWithChildren } from "react"
import { CircleCheckIcon, Clock5 } from "lucide-react"
import { toLower } from "lodash-es"
import { ReactNode } from "@tanstack/react-router"
import { QuestionMarkCircledIcon } from "@radix-ui/react-icons"

import { cn } from "@/lib/utils"
import { useUserAdminLoaderData } from "@/hooks/useUserAdminLoaderData"
import { useToggle } from "@/hooks/use-toggle"
import { useModal } from "@/hooks/use-modal"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { RemoveUser } from "@/components/pages/_components/remove-user"
import { ShowChildren } from "@/components/base/show-children"

import { getInitials } from "./Users.table.columns"
import { User } from "../Users.hook"

interface UsersDetailsProps {
  user: User
  isOpen: boolean
  onOpenChange?: (open: boolean) => void
  onSheetClose?: () => void
}

interface InfoRowProps extends PropsWithChildren {
  label: string | ReactNode
  value?: string
  className?: string
  containerClassName?: string
}

const InfoRow = ({
  label,
  value,
  className,
  containerClassName,
  children,
}: InfoRowProps) => (
  <>
    <div
      className={cn(
        "flex items-start justify-between py-1 first:pt-0",
        containerClassName,
      )}
    >
      {React.isValidElement(label) ? (
        label
      ) : (
        <span className="text-sm font-medium text-foreground">{label}</span>
      )}

      <ShowChildren when={!!children}>
        {Children.map(
          children,
          (child, i) =>
            child &&
            (React.isValidElement(child) ? (
              child
            ) : (
              <span
                className="text-right text-sm text-muted-foreground"
                key={i}
              >
                {child}
              </span>
            )),
        )}
      </ShowChildren>

      <ShowChildren when={!!value}>
        <span
          className={cn("text-right text-sm text-muted-foreground", className)}
        >
          {value}
        </span>
      </ShowChildren>
    </div>
  </>
)

function useUsersDetails(props: UsersDetailsProps) {
  const [remove, { toggle }] = useToggle()
  const { entityId } = useUserAdminLoaderData()
  const {
    visible: isModalOpen,
    show: showModal,
    close: closeModal,
  } = useModal()

  const currentEntity = props.user.entities?.find(({ id }) => id === entityId)
  const hasCurrentEntity = !!currentEntity

  const currentEntityRoles =
    (hasCurrentEntity &&
      currentEntity.roles
        ?.map(
          ({ role }) =>
            `${role}${currentEntity.approverLevel ? ` (${currentEntity.approverLevel})` : ""}`,
        )
        .join(",")) ??
    ""

  return {
    ...props,
    remove,
    currentEntity,
    entityId,
    hasCurrentEntity,
    currentEntityRoles,
    isModalOpen,
    toggle,
    showModal,
    closeModal,
  }
}

export default function UsersDetails(props: UsersDetailsProps) {
  const {
    isOpen,
    user,
    currentEntity,
    currentEntityRoles,
    entityId,
    onOpenChange,
    onSheetClose,
  } = useUsersDetails(props)

  return (
    <>
      <Sheet onOpenChange={onOpenChange} open={isOpen}>
        <SheetContent className="p-6 pt-16">
          <SheetTitle className="sr-only">Payee Details</SheetTitle>
          <SheetDescription className="sr-only">
            View user details
          </SheetDescription>

          <SheetHeader className="">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <ShowChildren when={!!user?.displayName}>
                  <Avatar className="h-8 w-8 bg-foreground">
                    <AvatarFallback
                      className="bg-foreground text-sm text-background"
                      data-testid="avatar-fallback"
                    >
                      {getInitials(user?.displayName ?? "")}
                    </AvatarFallback>
                  </Avatar>
                </ShowChildren>

                <SheetTitle className="text-xl font-semibold">
                  {user?.displayName}
                </SheetTitle>
              </div>
              <div className="">
                <Badge
                  className={cn("gap-x-1 rounded-lg px-1.5 font-normal", {
                    "border-success/40 bg-success/5 text-success":
                      user?.status === "Active",
                    "border-warning/40 bg-warning/5 text-warning":
                      user?.status === "Pending",
                  })}
                  variant="outline"
                >
                  {user?.status === "Active" ? (
                    <CircleCheckIcon className="size-3" />
                  ) : (
                    <Clock5 className="size-3" />
                  )}

                  <span className="line-clamp-1">{user?.status}</span>
                </Badge>
              </div>
            </div>
          </SheetHeader>

          <div className="mt-6 flex flex-col gap-y-6">
            <RemoveUser
              email={user.email}
              entityId={entityId}
              entityName={currentEntity?.displayName}
              onRemoveSuccess={onSheetClose}
              primaryText="Remove user"
            />

            <div className="space-y-0 rounded-2xl bg-muted/50 p-4">
              <InfoRow
                label={
                  <Tooltip>
                    <div className="flex items-center justify-center">
                      <span className="text-sm font-medium text-foreground">
                        Role (Signatory type)
                      </span>
                      <TooltipTrigger asChild>
                        <QuestionMarkCircledIcon className="ml-1 size-4 text-muted-foreground" />
                      </TooltipTrigger>
                    </div>
                    <TooltipContent>
                      <span className="font-medium text-foreground">
                        Permission with signatory type
                      </span>
                    </TooltipContent>
                  </Tooltip>
                }
                value={currentEntityRoles || ""}
              />
              <InfoRow label="Email" value={user?.email} />
              <InfoRow label="Phone" value={user?.phoneNumber} />
              <InfoRow containerClassName="flex-col" label="Current entities">
                <div className="mt-2 flex flex-wrap gap-1">
                  {user?.entities?.map(({ id, displayName, approverLevel }) => (
                    <Badge key={id} variant="outline">
                      <span className="text-xs font-medium text-muted-foreground">
                        {toLower(displayName)}&nbsp;
                        {approverLevel ? ` (${approverLevel})` : ""}
                      </span>
                    </Badge>
                  ))}
                </div>
              </InfoRow>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}
