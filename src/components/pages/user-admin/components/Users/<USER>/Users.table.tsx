import DataTable from "@/components/base/data-table"

import UsersDetails from "./UsersDetails"
import { email, name, roles, status } from "./Users.table.columns"
import { AddUserDialog } from "./AddUserDialog"
import { useUsers } from "../Users.hook"

export default function Table() {
  const { data, isLoading, user, isSheetOpen, sheetToggler, handleRowClick } =
    useUsers()

  return (
    <>
      <DataTable
        columns={[name, email, roles, status]}
        containerClassName="pr-2"
        data={data}
        emptyState={{
          title: "No users found",
          description: "There are no users to display",
          action: <AddUserDialog />,
        }}
        loading={isLoading}
        onRowClick={({ id }) => handleRowClick(id)}
        pagination
        stickyHeader
      />

      <UsersDetails
        isOpen={isSheetOpen}
        onOpenChange={sheetToggler.toggle}
        user={user}
      />
    </>
  )
}
