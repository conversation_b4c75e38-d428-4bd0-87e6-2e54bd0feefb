import { toast } from "sonner"
import { useMemo, useState } from "react"
import { useNavigate, useSearch, useLocation } from "@tanstack/react-router"

import { UserAdminFilterKeys } from "@/routes/_auth/$entityId/user-admin"
import { useUserAdminLoaderData } from "@/hooks/useUserAdminLoaderData"
import { useToggle } from "@/hooks/use-toggle"
import { useGetUserQry } from "@/data/user"
import { useGetAllUsersQry } from "@/data/entity/entity.query"
import {
  GetSpecificUserEntityDto,
  UserLoginStatus,
} from "@/client/onboarding/types.gen"

export interface UserRow {
  id: string
  name: string
  email: string
  roles: string
  status: UserLoginStatus
}

export interface User {
  roles: string
  id?: string
  email?: string
  phoneNumber?: string
  status?: UserLoginStatus
  entities?: Array<GetSpecificUserEntityDto>
  displayName?: string
}

export function useUsers() {
  const [searchQuery, setSearchQuery] = useState("")
  const searchParams = useSearch({ strict: false })
  const location = useLocation()
  const { entityId, entity } = useUserAdminLoaderData()

  // Determine if we're in onboarding context or main entity context
  const isOnboardingContext = location.pathname.includes("/onboarding/")
  const navigate = useNavigate({
    from: isOnboardingContext
      ? "/onboarding/$entityId"
      : "/$entityId/user-admin",
  })
  const { data: allUsers = [], isLoading } = useGetAllUsersQry(entityId)
  const {
    data: currentUser,
    isLoading: currentUserIsLoading,
    trigger,
  } = useGetUserQry()
  const [isSheetOpen, sheetToggler] = useToggle()

  const entityName = entity?.legalEntity?.name || "the entity"

  const variants = useMemo(() => ["default"], [])

  const variant = useMemo(() => {
    return variants.includes(searchParams.tab!)
      ? (searchParams.tab as "default")
      : "default"
  }, [searchParams.tab, variants])

  const filters = useMemo(() => {
    if (variant === "default") {
      return ["All", "Active", "Pending"]
    }
    return ["All", "Active", "Pending"]
  }, [variant])

  const activeFilter = useMemo(() => {
    return filters.find((filter) => filter === searchParams.filter) ?? "All"
  }, [searchParams.filter, filters])

  const data: UserRow[] = allUsers
    .map((user) => ({
      id: user.id ?? "",
      name: user.displayName ?? "",
      email: user.email ?? "",
      roles: user.entityAccess?.roles?.map(({ name }) => name).join(", ") ?? "",
      status: user.status!,
    }))
    .filter(({ status }) => activeFilter === "All" || status === activeFilter)

  const user = useMemo(() => {
    const current = data.filter(({ id }) => id === currentUser?.id)?.[0]

    return {
      ...currentUser,
      roles: current?.roles,
    } as User
  }, [currentUser, data])

  function setActiveFilter(filter: UserAdminFilterKeys) {
    if (isOnboardingContext) {
      // In onboarding context, preserve the view parameter and update filter
      navigate({
        search: {
          ...searchParams,
          view: "user-admin",
          tab: searchParams.tab ?? "default",
          filter,
        },
      })
    } else {
      // In main entity context, use original navigation
      navigate({
        search: {
          ...searchParams,
          tab: searchParams.tab ?? "default",
          filter,
        } as any,
      })
    }
  }

  function handleRowClick(userId: string) {
    trigger(userId, {
      onError: () => {
        toast.error("User not found")
      },
      onSuccess: () => {
        sheetToggler.on()
      },
    })
  }

  return {
    entityId,
    entityName,
    data,
    isLoading,
    searchQuery,
    activeFilter,
    filters,
    user,
    currentUserIsLoading,
    isSheetOpen,
    sheetToggler,
    setSearchQuery,
    setActiveFilter,
    handleRowClick,
  }
}
