import { UserPlus } from "lucide-react"

import { UserAdminFilterKeys } from "@/routes/_auth/$entityId/user-admin"
import { cn } from "@/lib/utils"
import FilterTabs from "@/components/pages/_components/filter-tabs"
import { AddUserWizard } from "@/components/pages/_components/add-user-wizard"

import { useUsers } from "./Users.hook"
import UsersTable from "./components/Users.table"

function Header() {
  const { entityId, entityName, activeFilter, filters, setActiveFilter } =
    useUsers()

  return (
    <div className="mb-4 flex items-center justify-between">
      <FilterTabs
        active={activeFilter}
        items={filters}
        setActive={(filter) => setActiveFilter(filter as UserAdminFilterKeys)}
      />

      <AddUserWizard
        Button={{ icon: <UserPlus />, className: cn("px-4") }}
        entityId={entityId}
        entityName={entityName}
      />
    </div>
  )
}

export function Users() {
  console.log('render Users');
  
  return (
    <div className="py-4">
      <Header />
      <UsersTable />
    </div>
  )
}
