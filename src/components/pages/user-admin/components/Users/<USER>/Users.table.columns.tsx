import { useMemo } from "react"
import { Clock5 } from "lucide-react"

import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { StatusVariant } from "@/components/pages/_components/status-badge/StatusBadge"
import StatusBadge from "@/components/pages/_components/status-badge"
import { column } from "@/components/base/data-table"
import CopyToClipboard from "@/components/base/copy-to-clipboard"
import { UserLoginStatus } from "@/client/onboarding/types.gen"

export function getInitials(name: string): string {
  if (!name) return "N/A"

  const words = name.trim().split(/\s+/) // Split by spaces
  const initials = words
    .slice(0, 1) // Take only the first two words
    .map((word) => word[0].toUpperCase()) // Extract first letter and uppercase it
    .join("") // Join the initials

  return initials || "N/A"
}

function Name({ name }: { name: string }) {
  return (
    <>
      {name && (
        <div className="flex items-center gap-x-2">
          <Avatar className="size-6 overflow-hidden rounded-full bg-slate-200">
            <AvatarFallback
              className="bg-avatar text-sm text-white"
              data-testid="avatar-fallback"
            >
              {getInitials(name)}
            </AvatarFallback>
          </Avatar>
          <span className="line-clamp-1 text-sm">{name}</span>
        </div>
      )}
    </>
  )
}

function Email({ email }: { email: string }) {
  return (
    <div className="flex items-center gap-1">
      <div className="flex max-w-60">
        <span className="truncate text-sm">{email}</span>
      </div>

      <CopyToClipboard className="size-5 p-0 [&_svg]:size-2.5" text={email} />
    </div>
  )
}

function Roles({ rawRoles }: { rawRoles: string }) {
  const roles = rawRoles.split(",")

  return (
    <div className="flex flex-wrap gap-1">
      {roles.map((role, i) => (
        <StatusBadge key={i} variant="neutral">
          {role}
        </StatusBadge>
      ))}
    </div>
  )
}

function Status({ status }: { status: UserLoginStatus }) {
  const statusToVariant = useMemo(() => {
    const variants = {
      Active: "success",
      Pending: "warning",
    }

    return status in variants
      ? (variants[status as keyof typeof variants] as StatusVariant)
      : undefined
  }, [status])

  const icon = useMemo(() => {
    const icons = {
      Pending: Clock5,
    } as const

    return status in icons ? icons[status as keyof typeof icons] : undefined
  }, [status])

  return <StatusBadge icon={icon} text={status} variant={statusToVariant} />
}

export const name = column.custom("name", "Name", (value) => (
  <Name name={value} />
))

export const email = column.custom(
  "email",
  "Email",
  (value) => <Email email={value} />,
  { headerClassName: cn("w-[300px]") },
)

export const roles = column.custom(
  "roles",
  "Role(s)",
  (value) => <Roles rawRoles={value} />,
  { headerClassName: "w-[200px]" },
)

export const status = column.custom(
  "status",
  "Status",
  (value) => <Status status={value} />,
  { headerClassName: "w-[100px]" },
)
