import { useState, useEffect } from "react"

import {
  SignatoryRule,
  PaymentTiersDialogProps,
} from "@/data/user-admin/user-admin.types"
import {
  usePostSignatoryMatrixMutation,
  useUpdateSignatoryMatrixMutation,
} from "@/data/user-admin/user-admin.mutation"
import {
  ApproverLevel,
  CreateSignatoryMatrixRequest,
} from "@/data/user-admin/user-admin.interface"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { Stepper, Step } from "@/components/base/triggers/Stepper"

import { SignatoryRulesStep } from "./SignatoryRulesStep"
import { PaymentTiersStep } from "./PaymentTiersStep"

const STEPS = {
  PAYMENT_TIERS: 0,
  SIGNATORY_RULES: 1,
} as const

type StepType = (typeof STEPS)[keyof typeof STEPS]

const CURRENCY = "GBP" as const

export function PaymentTiersDialog({
  open,
  onOpenChange,
  tiers,
  tierErrors,
  onTierEndChange,
  onAddTier,
  onRemoveTier,
  canAddTier,
  signatoryRules,
  setSignatoryRules,
  mode,
  signatoryMatrix,
}: PaymentTiersDialogProps) {
  const [activeStep, setActiveStep] = useState<StepType>(STEPS.PAYMENT_TIERS)
  const [maxStep, setMaxStep] = useState<StepType>(STEPS.PAYMENT_TIERS)

  const { mutate: postSignatoryMatrix } = usePostSignatoryMatrixMutation()
  const { mutate: updateSignatoryMatrix } = useUpdateSignatoryMatrixMutation()

  // Reset active step when dialog opens
  useEffect(() => {
    if (open) {
      setActiveStep(STEPS.PAYMENT_TIERS)
      setMaxStep(STEPS.PAYMENT_TIERS)
    }
  }, [open])

  const handleSignatoryRuleChange = (
    tierIdx: number,
    type: keyof SignatoryRule,
    value: number,
  ) => {
    setSignatoryRules((prev) => {
      const updated = [...prev]
      updated[tierIdx] = { ...updated[tierIdx], [type]: value }
      return updated
    })
  }

  const canGoNext = () => {
    return (
      tiers.length > 0 &&
      tiers.every((tier) => tier.end) &&
      tierErrors.every((err) => !err)
    )
  }

  const handleNext = () => {
    if (activeStep === STEPS.PAYMENT_TIERS && canGoNext()) {
      setActiveStep(STEPS.SIGNATORY_RULES)
      if (maxStep < STEPS.SIGNATORY_RULES) setMaxStep(STEPS.SIGNATORY_RULES)
    }
  }

  const handleBack = () => {
    if (activeStep > STEPS.PAYMENT_TIERS) {
      setActiveStep((prev) => (prev - 1) as StepType)
    }
  }

  const handleStepChange = (step: number) => {
    setActiveStep(step as StepType)
  }

  const createSignatoryRules = (index: number, isUpdate: boolean) => {
    const rules = [
      {
        ...(isUpdate && {
          id: signatoryMatrix?.signatoryAmountBands[index]?.signatoryRules[0]
            ?.id,
        }),
        approverLevel: ApproverLevel.LevelA,
        requiredCount: signatoryRules[index].A,
      },
      {
        ...(isUpdate && {
          id: signatoryMatrix?.signatoryAmountBands[index]?.signatoryRules[1]
            ?.id,
        }),
        approverLevel: ApproverLevel.LevelB,
        requiredCount: signatoryRules[index].B,
      },
      {
        ...(isUpdate && {
          id: signatoryMatrix?.signatoryAmountBands[index]?.signatoryRules[2]
            ?.id,
        }),
        approverLevel: ApproverLevel.LevelC,
        requiredCount: signatoryRules[index].C,
      },
    ]

    return rules.filter((rule) => rule.requiredCount > 0)
  }

  const createSignatoryMatrixRequest = (
    isUpdate: boolean = false,
  ): CreateSignatoryMatrixRequest => {
    const baseRequest: CreateSignatoryMatrixRequest = {
      currency: CURRENCY,
      enabled: true,
      signatoryAmountBands: tiers.map((tier, index) => ({
        maximumAmount: Number(tier.end),
        ...(isUpdate &&
          signatoryMatrix?.signatoryAmountBands[index]?.id && {
            id: signatoryMatrix.signatoryAmountBands[index].id,
          }),
        signatoryRules: createSignatoryRules(index, isUpdate),
      })),
    }

    if (isUpdate && signatoryMatrix?.id) {
      return {
        ...baseRequest,
        id: signatoryMatrix.id,
      }
    }

    return baseRequest
  }

  const handleSave = () => {
    const request = createSignatoryMatrixRequest()
    postSignatoryMatrix(request)
    onOpenChange(false)
  }

  const handleUpdate = () => {
    const request = createSignatoryMatrixRequest(true)
    updateSignatoryMatrix({
      id: signatoryMatrix?.id || "",
      payload: request,
    })
    onOpenChange(false)
  }

  const renderDialogHeader = () => (
    <DialogHeader className="flex flex-row justify-center">
      <DialogTitle>Payment tiers and signatories</DialogTitle>
      <DialogDescription className="hidden" />
    </DialogHeader>
  )

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="max-w-2xl overflow-hidden rounded-2xl">
        {renderDialogHeader()}
        <div className="min-h-[350px]">
          <Stepper
            buttonTextSize="text-sm"
            onStepChange={handleStepChange}
            value={activeStep}
          >
            <Step title="Payment tiers">
              <PaymentTiersStep
                canAddTier={canAddTier}
                canGoNext={canGoNext}
                currency={signatoryMatrix?.currency}
                handleNext={handleNext}
                onAddTier={onAddTier}
                onOpenChange={onOpenChange}
                onRemoveTier={onRemoveTier}
                onTierEndChange={onTierEndChange}
                tierErrors={tierErrors}
                tiers={tiers}
              />
            </Step>
            <Step
              disabled={maxStep < STEPS.SIGNATORY_RULES}
              title="Signatory rules"
            >
              <SignatoryRulesStep
                handleBack={handleBack}
                handleSave={mode === "update" ? handleUpdate : handleSave}
                onOpenChange={onOpenChange}
                onSignatoryRuleChange={handleSignatoryRuleChange}
                signatoryRules={signatoryRules}
                tiers={tiers}
              />
            </Step>
          </Stepper>
        </div>
      </DialogContent>
    </Dialog>
  )
}
