import { useState } from "react"
import { Ch<PERSON><PERSON>Down, ChevronUp, Search, Clock } from "lucide-react"

import { cn } from "@/lib/utils"
import { useUsersQuery } from "@/data/user-admin/user-admin.query"
import { Input } from "@/components/ui/input"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { ButtonGroup } from "@/components/pages/payments/components/ButtonGroup"
import { BasicPagination } from "@/components/base/pagination/BasicPagination"

interface SignatoryListAccordionProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

const FILTERS = ["All", "Active", "Pending", "Inactive"]

function groupByLevel(approvers: any) {
  return ["LevelA", "LevelB", "LevelC"].map((level) => {
    const users = approvers.filter((a: any) => a.approverLevel === level)
    return { level: level.replace("Level", ""), users }
  })
}

function getStatusClass(status: string) {
  if (status === "Pending") return cn("font-normal text-muted-foreground")

  if (status === "Inactive")
    return cn("font-normal text-muted-foreground line-through")

  return cn("font-medium text-foreground")
}

export function SignatoryListAccordion({
  open: controlledOpen,
  onOpenChange,
}: SignatoryListAccordionProps) {
  const [open, setOpen] = useState(controlledOpen ?? false)
  const [activeFilter, setActiveFilter] = useState(FILTERS[0])
  const [search, setSearch] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 100

  const handleOpenChange = (val: boolean) => {
    setOpen(val)
    onOpenChange?.(val)
  }

  const { data: users } = useUsersQuery({
    pageNumber: currentPage,
    pageSize,
    status: activeFilter !== "All" ? activeFilter : undefined,
    displayName: search.trim() || undefined,
  })

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter)
    setCurrentPage(1) // Reset to first page when filter changes
  }

  const handleSearchChange = (value: string) => {
    setSearch(value)
    setCurrentPage(1) // Reset to first page when search changes
  }

  return (
    <Collapsible onOpenChange={handleOpenChange} open={open}>
      <div className="flex items-center gap-4">
        <h2 className="text-lg font-semibold">Signatory list</h2>
        <CollapsibleTrigger asChild>
          <Button
            className="flex items-center text-sm text-primary underline underline-offset-2 hover:text-primary/80 focus:outline-none"
            type="button"
            variant="link"
          >
            {open ? "Close" : "View"}
            {open ? (
              <ChevronUp className="ml-1 h-4 w-4 text-primary" />
            ) : (
              <ChevronDown className="ml-1 h-4 w-4 text-primary" />
            )}
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent>
        <>
          <div className="my-4 flex flex-col gap-2 md:flex-row md:items-center md:gap-4">
            <div className="relative w-60">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                className="w-full rounded-xl bg-background pl-9 shadow-none"
                onChange={(e) => handleSearchChange(e.target.value)}
                placeholder="Search"
                value={search}
              />
            </div>
            <ButtonGroup
              active={activeFilter}
              items={FILTERS}
              setActive={handleFilterChange}
            />
          </div>
          <div className="min-h-[200px] rounded-xl p-6">
            {users?.data &&
              groupByLevel(users?.data).map(({ level, users }) => (
                <div className="mb-8" key={level}>
                  <div className="mb-2 flex items-center gap-2">
                    <span className="text-sm font-bold">{level}</span>
                    <span className="text-sm font-normal text-muted-foreground">
                      ({users.length} signatories)
                    </span>
                  </div>
                  <hr className="mb-3" />
                  <div className="grid grid-cols-1 gap-y-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                    {users.map((user: any) => (
                      <div
                        className={cn(
                          getStatusClass(user.status),
                          "flex items-center gap-1 text-sm font-normal",
                        )}
                        key={user.id}
                      >
                        {user.displayName}
                        {user.status === "Pending" && (
                          <Clock className="h-4 w-4 text-orange-400" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
          </div>
          {users?.data && users?.data.length > 0 && (
            <BasicPagination
              data={users}
              onNext={() => setCurrentPage(users.pageNumber + 1)}
              onPrevious={() => setCurrentPage(users.pageNumber - 1)}
              pageSize={users.pageSize}
              totalItems={users.totalCount}
            />
          )}
        </>
      </CollapsibleContent>
    </Collapsible>
  )
}
