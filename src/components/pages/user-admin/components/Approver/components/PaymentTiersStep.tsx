import { Trash2, <PERSON>P<PERSON> } from "lucide-react"

import { cn } from "@/lib/utils"
import { Currency } from "@/lib/constants/currency.constants"
import { DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyEnter } from "@/components/base/currency/CurrencyEnter"

interface Tier {
  start: number
  end: number
}

interface PaymentTiersStepProps {
  tiers: Tier[]
  currency: string | undefined
  tierErrors: string[]
  onTierEndChange: (index: number, value: number) => void

  onAddTier: () => void
  onRemoveTier: (index: number) => void
  canAddTier: boolean
  onOpenChange: (open: boolean) => void
  handleNext: () => void
  canGoNext: () => boolean
}

export function PaymentTiersStep({
  tiers,
  currency,
  tierErrors,
  onTierEndChange,
  onAddTier,
  onRemoveTier,
  canAddTier,
  onOpenChange,
  handleNext,
  canGoNext,
}: PaymentTiersStepProps) {
  return (
    <>
      <div className="mt-6 flex flex-col gap-4">
        <p className="text-sm text-muted-foreground">
          Add up to a total of 5 payment tiers in {currency}
        </p>
        {tiers.map((tier, idx) => (
          <div className="flex items-center gap-2" key={idx}>
            <CurrencyText
              amount={tier.start + (idx === 0 ? 0 : 1)}
              className="min-w-[80px] text-sm text-muted-foreground"
              currency={currency ?? "GBP"}
              displayModes={["amount"]}
            />
            <span className="mx-2 text-sm text-muted-foreground">to</span>
            <CurrencyEnter
              className={cn(
                "w-1/2 text-sm",
                tierErrors[idx] && "border-red-500",
              )}
              currency={(currency ?? "GBP") as Currency}
              onChange={(val) => {
                if (tiers[idx + 1]) {
                  tiers[idx + 1].start = val
                }
                onTierEndChange(idx, val === 0 ? 0 : val)
              }}
              placeholder="Enter amount"
              value={tier.end ? Number(tier.end) : undefined}
            />

            {idx > 0 && (
              <Button
                className="text-primary"
                onClick={() => onRemoveTier(idx)}
                type="button"
                variant="link-muted"
              >
                <Trash2 className="h-5 w-5" />
              </Button>
            )}
          </div>
        ))}
      </div>
      <Button
        className="mt-4 flex pl-0 text-sm text-primary underline disabled:opacity-50"
        disabled={!canAddTier}
        onClick={onAddTier}
        type="button"
        variant="link-muted"
      >
        <CirclePlus className="h-5 w-5" />
        Add another tier
      </Button>
      <DialogFooter className="mt-8 flex flex-row justify-end gap-4">
        <Button
          onClick={() => onOpenChange(false)}
          type="button"
          variant="link-muted"
        >
          Cancel
        </Button>
        <Button
          disabled={!canGoNext()}
          onClick={handleNext}
          type="button"
          variant="default"
        >
          Next
        </Button>
      </DialogFooter>
    </>
  )
}
