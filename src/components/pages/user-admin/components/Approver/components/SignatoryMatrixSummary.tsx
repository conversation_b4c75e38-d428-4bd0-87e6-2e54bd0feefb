import React, { useState } from "react"
import { QuestionMarkCircledIcon } from "@radix-ui/react-icons"

import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

interface SignatoryMatrixSummaryProps {
  signatoryMatrix: any
  onEdit: () => void
  onToggleEnabled?: (enabled: boolean) => void
}

export const SignatoryMatrixSummary: React.FC<SignatoryMatrixSummaryProps> = ({
  signatoryMatrix,
  onEdit,
  onToggleEnabled,
}) => {
  const [enabled, setEnabled] = useState(!!signatoryMatrix.enabled)

  const handleSwitch = (checked: boolean) => {
    setEnabled(checked)
    if (onToggleEnabled) onToggleEnabled(checked)
  }

  return (
    <>
      <div className="mb-4 flex items-center gap-3">
        <Switch
          checked={enabled}
          id="signatory-requirements-switch"
          onCheckedChange={handleSwitch}
        />
        <Label
          className="text-sm font-normal"
          htmlFor="signatory-requirements-switch"
        >
          Signatories required for payments
        </Label>
      </div>
      {enabled && (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full border-separate border-spacing-y-2 text-sm">
              <thead>
                <tr>
                  <th className="px-4 py-2 text-left font-semibold">
                    Payment tier ({signatoryMatrix.currency})
                  </th>
                  <th className="px-4 py-2 text-left font-semibold">
                    Signatory rule{" "}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="inline-flex cursor-pointer items-center">
                          <QuestionMarkCircledIcon className="inline h-4 w-4 text-muted-foreground" />
                        </span>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        Minimum number of signatories required for each payment
                        tier
                      </TooltipContent>
                    </Tooltip>
                  </th>
                </tr>
              </thead>
              <tbody>
                {signatoryMatrix.signatoryAmountBands.map(
                  (band: any, idx: number) => {
                    const start =
                      idx === 0
                        ? 0
                        : Number(
                            signatoryMatrix.signatoryAmountBands[idx - 1]
                              .maximumAmount,
                          )
                    const end = Number(band.maximumAmount)
                    return (
                      <tr key={idx}>
                        <td className="px-4 py-2">
                          <CurrencyText
                            amount={start + (idx === 0 ? 0 : 1)}
                            currency={signatoryMatrix.currency}
                            displayModes={["amount"]}
                          />{" "}
                          <>
                            <span className="mx-1">to</span>{" "}
                            <CurrencyText
                              amount={end}
                              currency={signatoryMatrix.currency}
                              displayModes={["amount"]}
                            />
                          </>
                        </td>
                        <td className="px-4 py-2">
                          {band.signatoryRules.map(
                            (rule: any, rIdx: number) => {
                              const levelCode = rule.approverLevel.replace(
                                "Level",
                                "",
                              )
                              let label = ""

                              if (levelCode === "B") {
                                label =
                                  rule.requiredCount === 1
                                    ? `1B or higher`
                                    : `${rule.requiredCount}Bs or higher`
                              } else if (levelCode === "C") {
                                label =
                                  rule.requiredCount === 1
                                    ? `1C or higher`
                                    : `${rule.requiredCount}Cs or higher`
                              } else {
                                label = `${rule.requiredCount}${levelCode}`
                              }

                              return (
                                <React.Fragment key={rIdx}>
                                  {rIdx > 0 && (
                                    <span className="mx-2 font-bold">+</span>
                                  )}
                                  <span className="inline-block rounded border bg-white px-2 py-0.5">
                                    {label}
                                  </span>
                                </React.Fragment>
                              )
                            },
                          )}
                        </td>
                      </tr>
                    )
                  },
                )}
              </tbody>
            </table>
          </div>
          <Button onClick={onEdit} variant="outline">
            Edit tiers and signatory rules
          </Button>
        </>
      )}
    </>
  )
}
