import { Tier } from "@/data/user-admin/user-admin.types"
import { <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

interface SignatoryRulesStepProps {
  handleBack: () => void
  handleSave: () => void
  tiers: Tier[]
  signatoryRules: { A: number; B: number; C: number }[]
  onSignatoryRuleChange: (
    tierIdx: number,
    type: "A" | "B" | "C",
    value: number,
  ) => void
  onOpenChange: (open: boolean) => void
}

export function SignatoryRulesStep({
  handleSave,
  tiers,
  signatoryRules,
  onSignatoryRuleChange,
  onOpenChange,
}: SignatoryRulesStepProps) {
  return (
    <>
      <div className="flex flex-col gap-6">
        <div className="mt-6 text-sm text-muted-foreground">
          Enter the minimum signatory requirements per GBP tier.
        </div>
        <div className="flex flex-col gap-4">
          {tiers.map((tier, idx) => (
            <div className="flex items-center gap-6" key={idx}>
              <span className="w-1/3 text-sm text-foreground">
                <CurrencyText
                  amount={tier.start}
                  currency="GBP"
                  displayModes={["amount"]}
                />
                {" - "}
                {tier.end === 0 ? (
                  "..."
                ) : (
                  <CurrencyText
                    amount={tier.end}
                    currency="GBP"
                    displayModes={["amount"]}
                  />
                )}
              </span>
              {["A", "B", "C"].map((type) => (
                <div className="flex items-center gap-1" key={type}>
                  <Button
                    className="flex h-4 w-4 items-center justify-center rounded-full border p-3 text-sm disabled:opacity-50"
                    disabled={
                      signatoryRules[idx][type as "A" | "B" | "C"] === 0
                    }
                    onClick={() =>
                      onSignatoryRuleChange(
                        idx,
                        type as "A" | "B" | "C",
                        Math.max(
                          0,
                          signatoryRules[idx][type as "A" | "B" | "C"] - 1,
                        ),
                      )
                    }
                    type="button"
                    variant="secondary"
                  >
                    –
                  </Button>
                  <span className="w-4 text-center text-sm text-foreground">
                    {signatoryRules[idx][type as "A" | "B" | "C"]}
                  </span>
                  <span className="text-sm text-foreground">{type}</span>
                  <Button
                    className="flex h-4 w-4 items-center justify-center rounded-full border p-3"
                    onClick={() =>
                      onSignatoryRuleChange(
                        idx,
                        type as "A" | "B" | "C",
                        signatoryRules[idx][type as "A" | "B" | "C"] + 1,
                      )
                    }
                    type="button"
                    variant="secondary"
                  >
                    +
                  </Button>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
      <DialogFooter className="mt-8 flex flex-row justify-between gap-4">
        <Button
          onClick={() => onOpenChange(false)}
          type="button"
          variant="link-muted"
        >
          Cancel
        </Button>
        <Button onClick={handleSave} type="button" variant="default">
          Save
        </Button>
      </DialogFooter>
    </>
  )
}
