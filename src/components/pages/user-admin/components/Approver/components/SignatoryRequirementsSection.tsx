import { AlertTriangle } from "lucide-react"

import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface SignatoryRequirementsSectionProps {
  onSetupClick: () => void
  signatoryRequirements: boolean
  onSignatoryRequirementsChange: (checked: boolean) => void
}

export function SignatoryRequirementsSection({
  onSetupClick,
  signatoryRequirements,
  onSignatoryRequirementsChange,
}: SignatoryRequirementsSectionProps) {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-3">
        <Switch
          checked={signatoryRequirements}
          id="signatory-requirements-switch"
          onCheckedChange={onSignatoryRequirementsChange}
        />
        <Label
          className="text-sm font-normal"
          htmlFor="signatory-requirements-switch"
        >
          Signatories required for payments
        </Label>
      </div>

      {signatoryRequirements && (
        <>
          <Card className="mt-4 w-5/12">
            <div className="flex items-start gap-2 p-6">
              <AlertTriangle className="h-6 w-6 flex-shrink-0 text-warning" />
              <div className="flex flex-col gap-2">
                <p className="text-sm font-semibold">
                  Payment tiers and signatory rules are required{" "}
                </p>
                <p className="text-sm text-muted-foreground">
                  No payments are allowed without these tiers and rules.
                </p>
              </div>
            </div>
          </Card>
          <Button className="w-3/12" onClick={onSetupClick}>
            Set up tiers and signatory rules{" "}
          </Button>
        </>
      )}
    </div>
  )
}
