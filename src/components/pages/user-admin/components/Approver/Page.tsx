import { useState, useEffect } from "react"
import { QuestionMarkCircledIcon } from "@radix-ui/react-icons"

import type { Tier } from "@/data/user-admin/user-admin.types"

import { queryClient } from "@/main"
import { useSignatoryMatrixQuery } from "@/data/user-admin/user-admin.query"
import {
  useUpdateClientMutation,
  useUpdateSignatoryMatrixMutation,
} from "@/data/user-admin/user-admin.mutation"
import { useValidateSignatoryAmount } from "@/data/payments/payments.query"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

import { SignatoryRequirementsSection } from "./components/SignatoryRequirementsSection"
import { SignatoryMatrixSummary } from "./components/SignatoryMatrixSummary"
import { SignatoryListAccordion } from "./components/SignatoryListAccordion"
import { PaymentTiersDialog } from "./components/PaymentTiersDialog"

export function ApproverSignatoriesAndRules() {
  const [localCanApproveOwn, setLocalCanApproveOwn] = useState<
    boolean | undefined
  >(undefined)
  const [signatoryRequirements, setSignatoryRequirements] = useState(false)
  const [showTiersDialog, setShowTiersDialog] = useState(false)
  const [tiers, setTiers] = useState<Tier[]>([{ start: 0, end: 0 }])
  const [tierErrors, setTierErrors] = useState<string[]>([])
  const [signatoryRules, setSignatoryRules] = useState<
    { A: number; B: number; C: number }[]
  >([])

  const { data: signatoryMatrix } = useSignatoryMatrixQuery()
  const { mutate: updateClient } = useUpdateClientMutation()

  useEffect(() => {
    if (signatoryMatrix?.signatoryAmountBands) {
      signatoryMatrix.signatoryAmountBands.sort(
        (a, b) => a.maximumAmount - b.maximumAmount,
      )
    }
  }, [signatoryMatrix])

  // Todo: this is totally wrong we should no call this endpoint to get canApproveOwnSubmission
  // it should call get config
  const { data: signatoryValidation } = useValidateSignatoryAmount(
    1000000,
    "GBP",
  )

  const { mutate: updateSignatoryMatrix } = useUpdateSignatoryMatrixMutation()

  useEffect(() => {
    setLocalCanApproveOwn(signatoryValidation?.canApproveOwnSubmission)
  }, [signatoryValidation?.canApproveOwnSubmission])

  const handleTierEndChange = (index: number, value: number) => {
    setTiers((prev) => {
      const newTiers = [...prev]
      let error = ""
      const currentValue = value
      const prevValue = index > 0 ? newTiers[index - 1].end : 0
      if (
        value &&
        !isNaN(currentValue) &&
        index > 0 &&
        (!newTiers[index - 1].end || currentValue <= prevValue)
      ) {
        error = `Value must be greater than ${prevValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
      }
      newTiers[index].end = value
      setTierErrors((prevErrors) => {
        const newErrors = [...prevErrors]
        newErrors[index] = error
        return newErrors
      })
      return newTiers
    })
  }

  const handleAddTier = () => {
    if (tiers.length < 5) {
      const lastEnd = tiers[tiers.length - 1].end
      setTiers([...tiers, { start: isNaN(lastEnd) ? 0 : lastEnd, end: 0 }])
      setSignatoryRules((prev) => [...prev, { A: 0, B: 0, C: 0 }])
    }
  }

  const handleRemoveTier = (index: number) => {
    setTiers((prev) => prev.filter((_, i) => i !== index))
  }

  const canAddTier =
    tiers.length < 5 &&
    tiers[tiers.length - 1].end !== 0 &&
    !tierErrors[tiers.length - 1]

  useEffect(() => {
    if (!showTiersDialog) {
      setTiers([{ start: 0, end: 0 }])
      setTierErrors([])
    }
  }, [showTiersDialog])

  function prepopulateTiersAndRulesFromMatrix(signatoryMatrix: any) {
    const bands = signatoryMatrix.signatoryAmountBands
    const newTiers = bands.map((band: any, idx: number) => {
      const start = idx === 0 ? 0 : Number(bands[idx - 1].maximumAmount)
      return {
        start,
        end: band.maximumAmount,
      }
    })
    setTiers(newTiers)
    const newSignatoryRules = bands.map((band: any) => ({
      A:
        band.signatoryRules.find((r: any) => r.approverLevel === "LevelA")
          ?.requiredCount || 0,
      B:
        band.signatoryRules.find((r: any) => r.approverLevel === "LevelB")
          ?.requiredCount || 0,
      C:
        band.signatoryRules.find((r: any) => r.approverLevel === "LevelC")
          ?.requiredCount || 0,
    }))
    setSignatoryRules(newSignatoryRules)
    setShowTiersDialog(true)
  }

  // todo: wrong use of signatory-validation
  const handleCanApproveOwnChange = (checked: boolean) => {
    setLocalCanApproveOwn(checked)
    updateClient(checked, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["signatory-validation", 1000000, "GBP", false],
        })
      },
      onError: () => {
        setLocalCanApproveOwn(signatoryValidation?.canApproveOwnSubmission)
      },
    })
  }

  const handleSignatoryRequirementsChange = (checked: boolean) => {
    if (signatoryMatrix) {
      updateSignatoryMatrix({
        id: signatoryMatrix.id,
        payload: {
          ...signatoryMatrix,
          enabled: checked,
        },
      })
    } else {
      setSignatoryRequirements(checked)
    }
  }

  return (
    <>
      <div className="mt-4 space-y-1 rounded-2xl bg-muted p-6">
        <h2 className="mb-6 text-lg font-semibold">Approvers</h2>
        <div className="flex items-center gap-3">
          <Switch
            checked={!!localCanApproveOwn}
            id="submitter-approve-switch"
            onCheckedChange={handleCanApproveOwnChange}
          />
          <Label
            className="text-sm font-normal"
            htmlFor="submitter-approve-switch"
          >
            Submitter can approve own payments
          </Label>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="inline-flex cursor-pointer items-center">
                <QuestionMarkCircledIcon className="inline h-4 w-4 text-muted-foreground" />
              </span>
            </TooltipTrigger>
            <TooltipContent side="top">
              Allowing the submitter to approve their own payments may reduce
              oversight.
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
      <div className="mt-8 space-y-1 rounded-2xl bg-muted p-6">
        <h2 className="mb-6 text-lg font-semibold">Signatory requirements</h2>
        {!signatoryMatrix && (
          <SignatoryRequirementsSection
            onSetupClick={() => setShowTiersDialog(true)}
            onSignatoryRequirementsChange={handleSignatoryRequirementsChange}
            signatoryRequirements={signatoryRequirements}
          />
        )}
        {signatoryMatrix && (
          <SignatoryMatrixSummary
            onEdit={() => {
              prepopulateTiersAndRulesFromMatrix(signatoryMatrix)
            }}
            onToggleEnabled={(enabled) => {
              handleSignatoryRequirementsChange(enabled)
            }}
            signatoryMatrix={signatoryMatrix}
          />
        )}
      </div>
      <div className="mt-8 space-y-1 rounded-2xl bg-muted p-6">
        <SignatoryListAccordion />
      </div>
      {/* Payment Tiers Dialog */}
      <PaymentTiersDialog
        canAddTier={canAddTier}
        mode={signatoryMatrix ? "update" : "create"}
        onAddTier={handleAddTier}
        onOpenChange={setShowTiersDialog}
        onRemoveTier={handleRemoveTier}
        onTierEndChange={handleTierEndChange}
        open={showTiersDialog}
        setSignatoryRules={setSignatoryRules}
        signatoryMatrix={signatoryMatrix}
        signatoryRules={signatoryRules}
        tierErrors={tierErrors}
        tiers={tiers}
      />
    </>
  )
}
