import React from "react"
import { Check } from "lucide-react"

import { useRolesAndPermissionsQuery } from "@/data/user-admin/user-admin.query"
import { TabsContent } from "@/components/ui/tabs"
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table"

const roleLabels = [
  {
    main: "Admin",
    sub: "(Master user)",
  },
  { main: "Approver" },
  { main: "Submitter" },
  { main: "Viewer" },
]

const check = (
  <div className="relative mx-auto h-5 w-5 justify-center rounded-full bg-[#C8EACD] text-muted-foreground">
    <Check
      aria-label="Allowed"
      className="absolute left-1/2 top-1/2 h-4 w-4 -translate-x-1/2 -translate-y-1/2"
    />
  </div>
)

const roleOrder = ["Administrator", "Approver", "Submitter", "Viewer"]

export function RolesandPermissions() {
  const { data: rolesAndPermissions } = useRolesAndPermissionsQuery()
  return (
    <TabsContent aria-label="Roles and permissions page" value="roles">
      <Table>
        <TableHeader>
          <TableRow className="border-none">
            <TableHead className="w-1/3 bg-background text-base font-semibold">
              &nbsp;
            </TableHead>
            {roleLabels.map((role) => (
              <TableHead
                className="bg-background text-center align-bottom text-base font-semibold"
                key={role.main}
              >
                {role.main}
              </TableHead>
            ))}
          </TableRow>
          <TableRow className="border-none">
            <TableHead className="bg-background">&nbsp;</TableHead>
            <TableHead className="bg-background text-center text-xs font-normal text-muted-foreground">
              {roleLabels[0].sub}
            </TableHead>
            <TableHead className="bg-background" />
            <TableHead className="bg-background" />
            <TableHead className="bg-background" />
          </TableRow>
        </TableHeader>
        <TableBody>
          {rolesAndPermissions?.map((section) => (
            <React.Fragment key={section.category}>
              <TableRow className="border-none">
                <TableCell
                  className="px-0 py-3 text-base font-semibold"
                  colSpan={5}
                >
                  {section.category}
                </TableCell>
              </TableRow>
              {section.permissions.map((perm) => (
                <TableRow key={perm.permission}>
                  <TableCell className="px-0 py-2 text-sm">
                    {perm.displayName}
                  </TableCell>
                  {roleOrder.map((role) => (
                    <TableCell className="text-center align-middle" key={role}>
                      {perm.roleAccess?.[role as keyof typeof perm.roleAccess]
                        ? check
                        : null}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
    </TabsContent>
  )
}
