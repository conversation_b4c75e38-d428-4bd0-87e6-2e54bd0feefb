import { formatDate } from "@/lib/date.utils"
import { useRolePermissions } from "@/hooks/useRolePermissions"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { FormLayout } from "@/components/base/form/form"

import { useReviewDetails } from "./ReviewForm.hook"
import Confirmation from "./Confirmation"
import { Section } from "./components/Section"
import { Field } from "./components/Field"
import { FormStepId } from "../FormSteps"
import { GoBack } from "../common/GoBack"

export interface ReviewFormProps {
  onNext?: (payload: any) => void
  onBack?: (step?: number) => void
}

export function ReviewForm(props: ReviewFormProps) {
  const {
    isFetching,
    isSuccess,
    isError,
    form,
    termsAndConditions,
    handleSubmit,
    handleEditSection,
  } = useReviewDetails(props)
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")

  if (isFetching || !isSuccess) {
    return (
      <div className="mt-1 flex h-96 w-full items-center justify-center">
        <LoadingSpinner size="8" />
      </div>
    )
  }

  return (
    <div
      aria-label="Application in review"
      className="container flex max-w-2xl flex-col gap-y-8 px-4"
    >
      {props.onBack && <GoBack onBack={props.onBack} />}

      <FormLayout
        className="mx-0 flex max-w-full flex-col gap-y-7"
        title="Application in review"
      >
        <p className="text-muted-foreground">
          Please review the data collected throughout the onboarding form.
        </p>
        {/* Entity details */}
        <Section
          disabled={!permission}
          onEdit={() => handleEditSection(FormStepId.entityDetailsForm)}
          stepNumber={1}
          title="Entity details"
        >
          <Field label="Entity name" value={form.entityDetails.entityName} />
          <Field
            label="Trading name(s)"
            value={form.entityDetails.tradingNames}
          />
          <Field
            label="Entity type"
            value={form.entityDetails?.entityType?.display || ""}
          />
          <Field
            label="Registered address"
            value={form.entityDetails.address}
          />
          <Field
            label="Trading address"
            value={form.entityDetails.tradingAddress}
          />
          <Field
            label="Listed on stock exchange"
            value={form.entityDetails.stockExchange.display ?? ""}
          />
          <Field
            label="Purpose of entity"
            value={form.entityDetails.purposeOfEntity?.display || ""}
          />
          <Field
            label="Date of incorporation"
            value={
              form.entityDetails.dateOfIncorporation
                ? formatDate(form.entityDetails.dateOfIncorporation)
                : ""
            }
          />
          <Field
            label="Registration jurisdiction"
            value={form.entityDetails.jurisdiction?.name || ""}
          />
          <Field
            label="Company number"
            value={form.entityDetails.registrationNumber}
          />
        </Section>

        {/* Further details */}
        <Section
          disabled={!permission}
          onEdit={() => handleEditSection(FormStepId.furtherDetailsForm)}
          stepNumber={2}
          title="Further details"
        >
          <Field label="Regulation" value={form.furtherDetails.regulation} />
          <Field label="Website" value={form.furtherDetails.website} />
          <Field
            label="Nature of business"
            value={form.furtherDetails.natureOfBusiness}
          />
          <Field label="Phone number" value={form.furtherDetails.phoneNumber} />
          <Field
            label="Company register file"
            value={form.furtherDetails.documentsToUpload}
          />
        </Section>

        {/* Ownership and control */}
        <Section
          disabled={!permission}
          onEdit={() => handleEditSection(FormStepId.ownershipAndControlForm)}
          stepNumber={3}
          title="Ownership and control"
        >
          <Field
            label="Directors file"
            value={form.ownershipControl.directorsDocuments}
          />
          <Field
            label="Structure chart"
            value={form.ownershipControl.structureChartDocuments}
          />
        </Section>

        {/* Financial information */}
        <Section
          disabled={!permission}
          onEdit={() => handleEditSection(FormStepId.financialInformationForm)}
          stepNumber={4}
          title="Financial information"
        >
          <Field
            label="Source Of Funds"
            value={form.financialInformation.sourceOfFunds}
          />
          <Field
            label="Financial documents"
            value={form.financialInformation.financialStatements}
          />
        </Section>

        {/* Transaction activity */}
        <Section
          disabled={!permission}
          onEdit={() => handleEditSection(FormStepId.transactionActivityForm)}
          stepNumber={5}
          title="Transaction activity"
        >
          <Field
            label="Transaction currencies"
            value={form.transactionActivity.expectedTransactionCurrencies}
          />
          <Field
            label="Transaction jurisdictions"
            value={form.transactionActivity?.transactionJurisdictions}
          />
          <Field
            label="Number of inbound transactions per month"
            value={`${form.transactionActivity?.activityBaseCurrency} ${form.transactionActivity?.monthlyTransactionsInbound}`}
          />
          <Field
            label="Number of outbound transactions per month"
            value={`${form.transactionActivity?.activityBaseCurrency} ${form.transactionActivity?.monthlyTransactionsOutbound}`}
          />
          <Field
            label="Average value of inbound transactions per month"
            value={`${form.transactionActivity?.activityBaseCurrency} ${form.transactionActivity?.monthlyIncomingTransactionsValue}`}
          />
          <Field
            label="Average value of outbound transactions per month"
            value={`${form.transactionActivity?.activityBaseCurrency} ${form.transactionActivity?.monthlyOutgoingTransactionsValue}`}
          />
          <Field
            label="Value of your largest expected transaction"
            value={`${form.transactionActivity?.activityBaseCurrency} ${form.transactionActivity?.maximumTransactionSize}`}
          />
          <Field
            label="Transaction activity type"
            value={form.transactionActivity?.transactionActivityType}
          />
        </Section>

        {/* Users & settings */}
        <Section
          disabled={!permission}
          onEdit={() => handleEditSection(FormStepId.usersAndSignatoriesForm)}
          stepNumber={6}
          title="Users and signatories"
        >
          {form.usersAndSignatories.users.map((user, i) => (
            <Field key={i} label={user.name} value={user.email} />
          ))}
        </Section>

        {/* Applicant details */}
        <Section
          onEdit={() => handleEditSection(FormStepId.applicantDetailsForm)}
          stepNumber={7}
          title="Applicant details"
        >
          <Field label="First name" value={form.applicantDetails.firstName} />
          <Field label="Last name" value={form.applicantDetails.lastName} />
          <Field label="Date of birth" value={form.applicantDetails.dob} />
          <Field
            label="Nationality"
            value={form.applicantDetails.nationality}
          />
          <Field
            label="Address"
            value={form.applicantDetails.residentialAddress}
          />
          <Field
            label="Proof of identity"
            value={form.applicantDetails.ProofOfIdentityDocs}
          />
          <Field
            label="Proof of address"
            value={form.applicantDetails.ProofOfAddressDocs}
          />
        </Section>

        {permission && (
          <Confirmation
            form={form}
            isError={isError}
            onComplete={handleSubmit}
            termsAndConditions={termsAndConditions}
          />
        )}
      </FormLayout>
    </div>
  )
}
