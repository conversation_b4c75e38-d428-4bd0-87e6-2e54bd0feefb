import { use<PERSON><PERSON>back, useReducer } from "react"
import { Alert<PERSON>riangle } from "lucide-react"

import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { LatestTermsAndConditions } from "@/client/onboarding/types.gen"

import { useReviewDetails } from "./ReviewForm.hook"
import PdfViewer from "./PdfViewer"
import ShowEntityChildren from "../common/ShowEntityChildren"

interface CheckboxProps {
  checked: boolean
  onChange?: (val: boolean) => void
  entitiyName?: string
}

function AuthorisedCheckbox({ checked, onChange, entitiyName }: CheckboxProps) {
  return (
    <div className="flex gap-x-3">
      <Checkbox
        checked={checked}
        className="mt-0.5"
        id="authorised"
        onCheckedChange={onChange}
      />
      <Label className="text-sm font-normal" htmlFor="authorised">
        I confirm that I am fully authorised to submit this application and to
        accept the Argentex Terms and Conditions on behalf of
        <span className="text-rose-600"> {entitiyName || "the applicant"}</span>
      </Label>
    </div>
  )
}

interface TCCheckboxProps extends CheckboxProps {
  checked: boolean
  termsAndConditions: LatestTermsAndConditions[]
  onChange?: (val: boolean) => void
}

function TCCheckbox({
  checked,
  termsAndConditions,
  onChange,
}: TCCheckboxProps) {
  return (
    <div className="flex gap-x-3">
      <Checkbox
        checked={checked}
        className="mt-0.5"
        id="terms"
        onCheckedChange={onChange}
      />
      <Label className="text-sm font-normal" htmlFor="terms">
        I agree to the following terms and conditions:
        <div className="flex gap-x-4">
          {termsAndConditions.map((doc) => (
            <PdfViewer
              data={doc as LatestTermsAndConditions}
              key={doc.documentId}
            />
          ))}
        </div>
      </Label>
    </div>
  )
}

interface ConfirmationProps {
  isError: boolean
  termsAndConditions: LatestTermsAndConditions[]
  onComplete: () => void
  form: ReturnType<typeof useReviewDetails>["form"]
}

export default function Confirmation({
  isError,
  termsAndConditions,
  onComplete,
  form,
}: ConfirmationProps) {
  const [state, dispatch] = useReducer(
    (
      prev: { authorised: boolean; tc: boolean },
      next: Partial<typeof prev>,
    ) => ({
      ...prev,
      ...next,
    }),
    { authorised: false, tc: false },
  )

  const isEnabled = useCallback(() => state.authorised && state.tc, [state])

  return (
    <ShowEntityChildren when="onboarding">
      <div className="mt-8 space-y-8">
        {isError && (
          <Alert className="border-red-200 bg-red-50" variant="destructive">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <AlertTitle className="text-red-600">
              Your form is incomplete
            </AlertTitle>
            <AlertDescription className="text-red-600">
              Please make sure all the sections shown with incomplete above are
              filled correctly.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col gap-3">
          <AuthorisedCheckbox
            checked={state.authorised}
            entitiyName={form?.entityDetails?.entityName}
            onChange={(val) => dispatch({ authorised: val })}
          />
          <TCCheckbox
            checked={state.tc}
            onChange={(val) => dispatch({ tc: val })}
            termsAndConditions={termsAndConditions}
          />
        </div>

        <div className="flex gap-4">
          <Button
            className="bg-teal-600 px-6 text-white hover:bg-teal-700"
            disabled={!isEnabled()}
            onClick={onComplete}
            variant="default"
          >
            Confirm and submit
          </Button>
        </div>
      </div>
    </ShowEntityChildren>
  )
}
