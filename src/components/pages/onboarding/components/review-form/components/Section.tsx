import { useState } from "react"
import { ChevronDown, ChevronUp, EditIcon } from "lucide-react"

import { Button } from "@/components/ui/button"

import ShowEntityChildren from "../../common/ShowEntityChildren"

interface SectionProps {
  title: string
  stepNumber: number
  children: React.ReactNode
  onEdit?: () => void
  disabled?: boolean
}

export function Section({
  title,
  stepNumber,
  children,
  onEdit,
  disabled,
}: SectionProps) {
  const [isExpanded, setIsExpanded] = useState(true)

  return (
    <div className="rounded-lg bg-white">
      <div className="flex items-center justify-between border-b border-gray-200 p-4">
        <button
          className="flex w-full items-center justify-between text-lg/normal font-semibold"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <span>
            {stepNumber}. {title}
          </span>
          <span className="text-muted-foreground">
            {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </span>
        </button>
      </div>

      {isExpanded && (
        <>
          <div className="p-4 px-0">{children}</div>
          <ShowEntityChildren when="onboarding">
            <div className="pb-4 pl-1">
              {!disabled && (
                <Button
                  className="text-teal-600 hover:bg-transparent hover:text-teal-700"
                  onClick={onEdit}
                  size="sm"
                  variant="link"
                >
                  <EditIcon className="mr-1" size={16} />
                  Edit {title.toLowerCase()}
                </Button>
              )}
            </div>
          </ShowEntityChildren>
        </>
      )}
    </div>
  )
}
