import { toast } from "sonner"
import { Document, Page, pdfjs } from "react-pdf"
import "react-pdf/dist/Page/TextLayer.css"
import "react-pdf/dist/Page/AnnotationLayer.css"
import { useState } from "react"
import workerSrc from "pdfjs-dist/build/pdf.worker.min.mjs?worker&url"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { cn } from "@/lib/utils"
import { useModal } from "@/hooks/use-modal"
import { useDownloadTermsAndConditionsQry } from "@/data/onboarding/review.query"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { LatestTermsAndConditions } from "@/client/onboarding/types.gen"

pdfjs.GlobalWorkerOptions.workerSrc = workerSrc

interface PdfViewerProps {
  data?: LatestTermsAndConditions
}

export default function PdfViewer({ data }: PdfViewerProps) {
  const [numPages, setNumPages] = useState<number | null>(null)
  const { visible: open, show, close } = useModal()
  const {
    trigger,
    isLoading,
    data: pdfData,
  } = useDownloadTermsAndConditionsQry()

  const handleOpen = (isOpen: boolean) => {
    if (isOpen) {
      show()
      if (!data?.termsAndConditionsId) return
      trigger([data.termsAndConditionsId!], {
        onError: () => {
          toast.error(`Unable to download document`)
        },
      })
      return
    }
    close()
  }

  function download() {
    const type = pdfData?.type.split("/")[1] // pdf
    const href = window.URL.createObjectURL(pdfData as Blob)
    const link = document.createElement("a")
    link.href = href
    link.setAttribute("download", `${data?.documentName}.${type}`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages)
  }

  function renderLoading() {
    return (
      <div className="mt-1 flex h-96 w-full items-center justify-center">
        <LoadingSpinner size="8" />
      </div>
    )
  }

  return (
    <div>
      <Dialog modal onOpenChange={handleOpen} open={open}>
        <DialogTrigger asChild>
          <Button className={cn(["py-0 pl-0 pr-0"])} variant="link">
            {data?.documentName}
          </Button>
        </DialogTrigger>

        <DialogPortal>
          <DialogContent className="min-w-max">
            <VisuallyHidden>
              <DialogDescription className="hidden"></DialogDescription>
              <DialogTitle> {data?.documentName}</DialogTitle>
            </VisuallyHidden>

            {isLoading ? (
              renderLoading()
            ) : (
              <>
                {pdfData && (
                  <div className="border-1 m-5 max-h-[40rem] min-w-max overflow-x-scroll overflow-y-scroll border">
                    <Document
                      file={pdfData}
                      onLoadSuccess={onDocumentLoadSuccess}
                    >
                      {Array.from(new Array(numPages), (_, index) => (
                        <Page
                          key={`page_${index + 1}`}
                          pageNumber={index + 1}
                        />
                      ))}
                    </Document>
                  </div>
                )}
              </>
            )}

            <DialogFooter className="">
              <Button
                onClick={() => {
                  download()
                }}
                type="button"
                variant="link"
              >
                Download as PDF
              </Button>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  )
}
