import { useEffect } from "react"

import { useToggle } from "@/hooks/use-toggle"
import { useGetTermsAndConditionsSuspenseQry } from "@/data/onboarding/review.query"
import { useSubmitOnboardingMutation } from "@/data/onboarding/review.mutation"
import { useGlobalStore } from "@/data/global/global.store"
import { LatestTermsAndConditions } from "@/client/onboarding/types.gen"

import { ReviewFormProps } from "./ReviewForm"
import { useValidateSteps } from "../hooks/useValidateSteps"
import { FormStepId } from "../FormSteps"

export function useReviewDetails(props: ReviewFormProps) {
  const { isFetching, isSuccess, form, steps } = useValidateSteps()
  const { data: termsAndConditions } = useGetTermsAndConditionsSuspenseQry()
  const { mutate: submitOnboarding } = useSubmitOnboardingMutation()
  const [isError, toggle] = useToggle()

  const handleEditSection = (step: FormStepId) => {
    // Let the parent stepper component handle navigation
    if (props.onBack) {
      props.onBack(step)
    }
  }

  const handleSubmit = () => {
    const isValid = Object.values(steps).every(Boolean)

    if (!isValid) {
      toggle.on()
      return
    }

    toggle.off()
    submitOnboarding()
  }

  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)

  useEffect(() => {
    setFormSeen({
      ...formSeenState,
      reviewForm: true,
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    isFetching,
    isSuccess,
    form,
    isError,
    termsAndConditions: termsAndConditions as LatestTermsAndConditions[],
    handleEditSection,
    handleSubmit,
  }
}
