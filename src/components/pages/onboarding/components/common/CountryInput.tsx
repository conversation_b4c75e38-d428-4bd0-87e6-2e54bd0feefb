import { useMemo } from "react"

import { cn } from "@/lib/utils"
import { Country } from "@/lib/constants/country.constants"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { SimpleAutoComplete } from "@/components/base/form/autocomplete"
import { CountryFlag } from "@/components/base/country/CountryFlag"
import { GetCountryListDto } from "@/client/onboarding/types.gen"

interface CountryInputProps {
  id?: string
  value: string
  onChange: (val: string | undefined) => void
  disabled?: boolean
}

export function useCountryInput(
  countryName: string,
  filter?: (country: GetCountryListDto) => boolean,
) {
  const { staticDataCountries: countries } = useLoaderData()

  const flag = useMemo(() => {
    const defaultFilter = (country: GetCountryListDto) =>
      country.name === countryName

    const country = countries.find(filter ?? defaultFilter)

    if (country) return country.code
  }, [countries, countryName, filter])

  return {
    countries,
    flag,
  }
}

export default function CountryInput(props: CountryInputProps) {
  const { countries, flag } = useCountryInput(props.value)

  return (
    <div className="relative flex flex-1 items-center [&>div]:flex-1">
      <div className="absolute left-4">
        <CountryFlag country={flag as Country} />
      </div>
      <SimpleAutoComplete
        data-testid="country-input"
        disabled={props.disabled}
        emptyMessage="No country found"
        id={props.id}
        inputClassName={cn("h-9 w-full py-6 pl-10 pr-10")}
        items={
          countries?.map((country) => ({
            value: country.name!,
            label: country.name!,
          })) ?? []
        }
        onSelectedValueChange={props.onChange}
        placeholder="Select a country"
        selectedValue={props.value}
      />
    </div>
  )
}
