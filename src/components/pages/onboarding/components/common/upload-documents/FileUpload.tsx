import { toast } from "sonner"
import { useDropzone } from "react-dropzone"
import { useRef, useCallback } from "react"
import { <PERSON>Help } from "lucide-react"
import { <PERSON><PERSON><PERSON> } from "@tanstack/react-form"
import { UploadIcon } from "@radix-ui/react-icons"

import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

const MAX_FILE_SIZE_MB = 10
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

const validateFileSizes = (files: File[]): boolean => {
  const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE_BYTES)

  if (oversizedFiles.length > 0) {
    toast.error(`File size must be less than ${MAX_FILE_SIZE_MB}MB`)
    return false
  }

  return true
}

interface FileUploadProps {
  field: Field<PERSON><PERSON><any, any, any, any, File[]>
  tooltipText: string
  label: string
  required?: boolean
  error?: string
  onChange?: (e: FileList | File[] | null) => void
  disabled?: boolean
}

export function FileUpload({
  field,
  label,
  required,
  tooltipText,
  error,
  onChange,
  disabled,
}: FileUploadProps) {
  const ref = useRef<HTMLInputElement>(null)

  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: any) => {
      // Validate file sizes
      if (!validateFileSizes(acceptedFiles)) {
        return
      }

      if (acceptedFiles.length > 0 && onChange && !disabled) {
        onChange(acceptedFiles)
      }
      if (fileRejections.length > 0) {
        toast.error(`File type must be one of .png, .jpg, .docx, .pdf`)
      }
    },
    [onChange, disabled],
  )
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/png": [".png", ".jpg"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "application/pdf": [".pdf"],
    },
  })

  return (
    <div className="space-y-2">
      <div className="field-label mt-2 inline-flex items-center gap-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
        <Label className="" htmlFor={field.name}>
          {label}
        </Label>

        <HelpTooltip content={tooltipText} />
        {!required && (
          <span className="font-normal text-muted-foreground">
            &nbsp;(optional)
          </span>
        )}
      </div>

      <div
        {...getRootProps()}
        className={`relative rounded-lg border border-dashed border-gray-300 bg-slate-100/20 p-6 ${isDragActive ? "border-gray-900 opacity-50" : ""} ${disabled ? "cursor-not-allowed" : ""}`}
      >
        <div className="flex items-center justify-start gap-6 text-center">
          <div className="rounded-full bg-gray-100 p-4">
            <UploadIcon
              aria-label="Upload file"
              className="h-6 w-6 cursor-pointer"
              onClick={() => ref.current?.click()}
            />
          </div>
          <div className="flex flex-col items-start text-left">
            <div>
              <span className="text-[#242944]">Drag and drop file here</span>{" "}
              <button
                className="text-primary hover:underline"
                disabled={disabled}
                onClick={() => ref.current?.click()}
              >
                or browse
              </button>
            </div>

            <p className="text-sm text-muted-foreground">
              Supported formats DOCX, PDF, JPG, PNG
            </p>
            <p className="text-sm text-muted-foreground">
              Maximum file size 10MB
            </p>
          </div>
        </div>

        <Input
          {...getInputProps()}
          accept=".docx,.pdf,.jpg,.png"
          className="hidden"
          data-testid={`${field.name}-input`}
          disabled={disabled}
          id={field.name}
          onChange={(e) => {
            if (e.target.files) {
              const files = Array.from(e.target.files)

              if (!validateFileSizes(files)) {
                // Clear the input
                e.target.value = ""
                return
              }
            }

            if (onChange) {
              onChange(e.target.files)
              return
            }

            if (e.target.files?.[0]) {
              field?.handleChange(e.target.files[0].name as any)
              return
            }
          }}
          ref={ref}
          type="file"
        />
      </div>

      {error && <p className="mt-2 text-sm text-destructive">{error}</p>}
    </div>
  )
}

function HelpTooltip({ content }: { content: string }) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-flex items-center justify-center text-sm">
          <CircleHelp className="size-3 text-muted-foreground" />
        </span>
      </TooltipTrigger>
      <TooltipContent>{content}</TooltipContent>
    </Tooltip>
  )
}
