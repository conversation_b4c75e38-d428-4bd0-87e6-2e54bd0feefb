import { describe, expect, it, vi, beforeEach } from "vitest"
import { toast } from "sonner"
import userEvent from "@testing-library/user-event"
import { render, screen, waitFor, fireEvent } from "@testing-library/react"
import { FieldApi } from "@tanstack/react-form"

import { FileUpload } from "../FileUpload"

// Mock sonner toast
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
  },
}))

// Mock tooltip components
vi.mock("@/components/ui/tooltip", () => ({
  Tooltip: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  TooltipContent: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}))

describe("FileUpload Integration Tests", () => {
  const mockField = {
    name: "test-field",
    handleChange: vi.fn(),
    state: { value: [] },
  } as unknown as Field<PERSON>pi<any, any, any, any, File[]>

  const defaultProps = {
    field: mockField,
    label: "Test Upload",
    tooltipText: "Test tooltip",
    required: true,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("Component Rendering", () => {
    it("should render with required label and tooltip", () => {
      render(<FileUpload {...defaultProps} />)

      expect(screen.getByText("Test Upload")).toBeInTheDocument()
      expect(screen.getByText("Drag and drop file here")).toBeInTheDocument()
      expect(screen.getByText("or browse")).toBeInTheDocument()
      expect(
        screen.getByText("Supported formats DOCX, PDF, JPG, PNG"),
      ).toBeInTheDocument()
    })

    it("should render as optional when required is false", () => {
      render(<FileUpload {...defaultProps} required={false} />)

      expect(screen.getByText("(optional)")).toBeInTheDocument()
    })

    it("should display error message when error prop is provided", () => {
      const errorMessage = "This field is required"
      render(<FileUpload {...defaultProps} error={errorMessage} />)

      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByText(errorMessage)).toHaveClass("text-destructive")
    })

    it("should be disabled when disabled prop is true", () => {
      render(<FileUpload {...defaultProps} disabled={true} />)

      const fileInput = screen.getByTestId("test-field-input")
      expect(fileInput).toBeDisabled()
    })
  })

  describe("File Type Validation", () => {
    it("should accept valid file types (PDF)", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const file = new File(["test content"], "test.pdf", {
        type: "application/pdf",
      })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(mockOnChange).toHaveBeenCalledWith(expect.any(FileList))
      expect(toast.error).not.toHaveBeenCalled()
    })

    it("should accept valid file types (DOCX)", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const file = new File(["test content"], "test.docx", {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(mockOnChange).toHaveBeenCalledWith(expect.any(FileList))
      expect(toast.error).not.toHaveBeenCalled()
    })

    it("should accept valid file types (PNG)", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const file = new File(["test content"], "test.png", { type: "image/png" })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(mockOnChange).toHaveBeenCalledWith(expect.any(FileList))
      expect(toast.error).not.toHaveBeenCalled()
    })

    it("should accept valid file types (JPG)", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const file = new File(["test content"], "test.jpg", { type: "image/png" })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(mockOnChange).toHaveBeenCalledWith(expect.any(FileList))
      expect(toast.error).not.toHaveBeenCalled()
    })
  })

  describe("File Size Validation", () => {
    it("should accept files under 10MB", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      // Create a file under 10MB (5MB)
      const fileSize = 5 * 1024 * 1024 // 5MB
      const file = new File([new ArrayBuffer(fileSize)], "test.pdf", {
        type: "application/pdf",
      })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(mockOnChange).toHaveBeenCalledWith(expect.any(FileList))
      expect(toast.error).not.toHaveBeenCalled()
    })

    it("should reject files over 10MB and show toast error", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      // Create a file over 10MB (15MB)
      const fileSize = 15 * 1024 * 1024 // 15MB
      const file = new File([new ArrayBuffer(fileSize)], "large.pdf", {
        type: "application/pdf",
      })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(toast.error).toHaveBeenCalledWith(
        "File size must be less than 10MB",
      )
      expect(mockOnChange).not.toHaveBeenCalled()
    })

    it("should clear input when file size exceeds limit", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const fileSize = 15 * 1024 * 1024 // 15MB
      const file = new File([new ArrayBuffer(fileSize)], "large.pdf", {
        type: "application/pdf",
      })
      const fileInput = screen.getByTestId(
        "test-field-input",
      ) as HTMLInputElement

      await user.upload(fileInput, file)

      expect(fileInput.value).toBe("")
    })
  })

  describe("Drag and Drop Functionality", () => {
    it("should handle drag over events", () => {
      render(<FileUpload {...defaultProps} />)

      const dropzone = screen
        .getByText("Drag and drop file here")
        .closest("div")

      fireEvent.dragOver(dropzone!, {
        dataTransfer: {
          files: [],
          types: ["Files"],
        },
      })

      // Should not throw any errors and component should still be rendered
      expect(screen.getByText("Drag and drop file here")).toBeInTheDocument()
    })

    it("should handle valid file drop", async () => {
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const file = new File(["test content"], "test.pdf", {
        type: "application/pdf",
      })
      const dropzone = screen
        .getByText("Drag and drop file here")
        .closest("div")

      fireEvent.drop(dropzone!, {
        dataTransfer: {
          files: [file],
          types: ["Files"],
        },
      })

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith([file])
      })
    })

    it("should reject oversized files in drag and drop", async () => {
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const fileSize = 15 * 1024 * 1024 // 15MB
      const file = new File([new ArrayBuffer(fileSize)], "large.pdf", {
        type: "application/pdf",
      })
      const dropzone = screen
        .getByText("Drag and drop file here")
        .closest("div")

      fireEvent.drop(dropzone!, {
        dataTransfer: {
          files: [file],
          types: ["Files"],
        },
      })

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          "File size must be less than 10MB",
        )
        expect(mockOnChange).not.toHaveBeenCalled()
      })
    })

    it("should not process files when disabled", async () => {
      const mockOnChange = vi.fn()
      render(
        <FileUpload
          {...defaultProps}
          disabled={true}
          onChange={mockOnChange}
        />,
      )

      const file = new File(["test content"], "test.pdf", {
        type: "application/pdf",
      })
      const dropzone = screen
        .getByText("Drag and drop file here")
        .closest("div")

      fireEvent.drop(dropzone!, {
        dataTransfer: {
          files: [file],
          types: ["Files"],
        },
      })

      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })

  describe("Browse Button Functionality", () => {
    it("should trigger file input when browse button is clicked", async () => {
      const user = userEvent.setup()
      render(<FileUpload {...defaultProps} />)

      const browseButton = screen.getByText("or browse")
      const fileInput = screen.getByTestId("test-field-input")

      const clickSpy = vi.spyOn(fileInput, "click")

      await user.click(browseButton)

      expect(clickSpy).toHaveBeenCalled()
    })

    it("should not trigger file input when browse button is clicked and disabled", () => {
      render(<FileUpload {...defaultProps} disabled={true} />)

      const browseButton = screen.getByText("or browse")
      expect(browseButton).toBeDisabled()
    })
  })

  describe("Field Integration", () => {
    it("should call field.handleChange when no onChange prop is provided", async () => {
      const user = userEvent.setup()
      render(<FileUpload {...defaultProps} />)

      const file = new File(["test content"], "test.pdf", {
        type: "application/pdf",
      })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(mockField.handleChange).toHaveBeenCalledWith("test.pdf")
    })

    it("should prioritize onChange prop over field.handleChange", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const file = new File(["test content"], "test.pdf", {
        type: "application/pdf",
      })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, file)

      expect(mockOnChange).toHaveBeenCalledWith(expect.any(FileList))
      expect(mockField.handleChange).not.toHaveBeenCalled()
    })
  })

  describe("Multiple Files Handling", () => {
    it("should handle multiple valid files", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const file1 = new File(["content1"], "test1.pdf", {
        type: "application/pdf",
      })
      const file2 = new File(["content2"], "test2.png", { type: "image/png" })
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, [file1, file2])

      expect(mockOnChange).toHaveBeenCalledWith(expect.any(FileList))
    })

    it("should reject all files if any exceed size limit", async () => {
      const user = userEvent.setup()
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const validFile = new File(["content"], "small.pdf", {
        type: "application/pdf",
      })
      const largeFile = new File(
        [new ArrayBuffer(15 * 1024 * 1024)],
        "large.pdf",
        { type: "application/pdf" },
      )
      const fileInput = screen.getByTestId("test-field-input")

      await user.upload(fileInput, [validFile, largeFile])

      expect(toast.error).toHaveBeenCalledWith(
        "File size must be less than 10MB",
      )
      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })

  describe("Edge Cases", () => {
    it("should handle empty file selection", () => {
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const fileInput = screen.getByTestId("test-field-input")
      fireEvent.change(fileInput, { target: { files: [] } })

      expect(mockOnChange).toHaveBeenCalledWith([])
    })

    it("should handle null file selection", () => {
      const mockOnChange = vi.fn()
      render(<FileUpload {...defaultProps} onChange={mockOnChange} />)

      const fileInput = screen.getByTestId("test-field-input")
      fireEvent.change(fileInput, { target: { files: null } })

      // Component calls onChange with null when files is null
      expect(mockOnChange).toHaveBeenCalledWith(null)
    })

    it("should show drag active state", () => {
      render(<FileUpload {...defaultProps} />)

      const dropzone = screen
        .getByText("Drag and drop file here")
        .closest("div")

      fireEvent.dragEnter(dropzone!, {
        dataTransfer: {
          files: [],
          types: ["Files"],
        },
      })

      // Component should still render without errors
      expect(screen.getByText("Drag and drop file here")).toBeInTheDocument()
    })
  })
})
