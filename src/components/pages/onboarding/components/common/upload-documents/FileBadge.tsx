import { PropsWithChildren } from "react"
import { MouseEvent } from "react"
import { Download, X } from "lucide-react"

import { Badge } from "@/components/ui/badge"

interface FileBadgeProps extends PropsWithChildren {
  onDownload: (e: MouseEvent) => void
  onDelete: (e: MouseEvent) => void
  disabled?: boolean
}

export default function FileBadge({ children, ...props }: FileBadgeProps) {
  return (
    <Badge
      className="z-[2] flex max-w-40 select-none items-center justify-center rounded-full border border-secondary/90 bg-secondary/55 px-1 py-0.5 font-normal text-slate-500 hover:bg-secondary/55"
      variant="secondary"
    >
      <div className="mr-0.5 cursor-pointer rounded-full p-1 text-slate-500 hover:text-primary">
        <Download
          className="relative -mr-0 size-3"
          onClick={props.onDownload}
          strokeWidth={3}
        />
      </div>

      <span className="truncate overflow-ellipsis">{children}</span>

      {!props.disabled && (
        <div className="ml-px inline-flex items-center rounded-full">
          <div className="cursor-pointer rounded-full bg-white p-1 text-rose-400 hover:text-destructive">
            <X
              className="relative -mr-0 size-3"
              onClick={props.onDelete}
              strokeWidth={3}
            />
          </div>
        </div>
      )}
    </Badge>
  )
}
