import { PropsWithChildren } from "react"

import { ShowChildren } from "@/components/base/show-children"

import useEntityHasSubmitted from "../hooks/useEntityHasSubmitted"

interface ShowEntityChildrenProps extends PropsWithChildren {
  when: "submitted" | "onboarding"
}

export default function ShowEntityChildren({
  when,
  children,
}: ShowEntityChildrenProps) {
  const submitted = useEntityHasSubmitted()

  const condition = when === "submitted" ? submitted : !submitted

  return <ShowChildren when={condition}>{children}</ShowChildren>
}
