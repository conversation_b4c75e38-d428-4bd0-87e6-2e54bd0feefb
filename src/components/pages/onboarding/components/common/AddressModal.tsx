import { PropsWithChildren, useRef } from "react"
import { Edit } from "lucide-react"
import { ReactFormExtendedApi, useStore } from "@tanstack/react-form"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"

import { cn } from "@/lib/utils"
import { useModal } from "@/hooks/use-modal"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

interface DialogTriggerProps {
  className?: string
  "aria-label"?: string
}

interface AddressModalProps extends PropsWithChildren {
  form: ReactFormExtendedApi<any, any>
  buttonClassName?: string
  title: string
  isValid?: boolean
  disabled?: boolean
  DialogTriggerProps: DialogTriggerProps
  onCancel?: () => void
  onSave?: () => void
}

export function AddressModal({
  form,
  title,
  children,
  DialogTriggerProps: { className: btnClassName, ...triggerProps },
  isValid = true,
  onCancel,
  onSave,
  disabled,
}: AddressModalProps) {
  const { visible: open, show, close } = useModal()
  const initialAddressRef = useRef<{
    address: any
    tradingAddress: any
    sameAsRegisteredAddress: boolean
  } | null>(null) // Store previous value

  const address = useStore(form.store, (state) => state.values.address)
  const sameAsRegisteredAddress = useStore(
    form.store,
    (state) => state.values.sameAsRegisteredAddress!,
  )
  const tradingAddress = useStore(
    form.store,
    (state) => state.values.tradingAddress,
  )

  const handleOpen = (isOpen: boolean) => {
    initialAddressRef.current = {
      address,
      tradingAddress,
      sameAsRegisteredAddress,
    } // Save current value

    if (isOpen) {
      show()
      return
    }

    close()
  }

  return (
    <Dialog modal onOpenChange={handleOpen} open={open}>
      <DialogTrigger asChild>
        <Button
          className={cn(["py-0 pl-0 pr-0", btnClassName])}
          disabled={disabled}
          variant="link"
          {...triggerProps}
        >
          <Edit className="h-4 w-4" />
          {title}
        </Button>
      </DialogTrigger>

      <DialogPortal>
        <DialogContent hideClose>
          <VisuallyHidden>
            <DialogDescription className="hidden"></DialogDescription>
            <DialogTitle>Edit address</DialogTitle>
          </VisuallyHidden>

          {children}

          <DialogFooter className="mx-auto flex w-full max-w-md justify-end gap-2 pb-4">
            <form.Subscribe>
              {() => (
                <div className="flex items-center justify-end gap-4">
                  <Button
                    onClick={() => {
                      if (initialAddressRef.current !== null) {
                        form.setFieldValue(
                          "address",
                          initialAddressRef.current.address,
                        )
                        form.setFieldValue(
                          "tradingAddress",
                          initialAddressRef.current.tradingAddress,
                        )
                        form.setFieldValue(
                          "sameAsRegisteredAddress",
                          initialAddressRef.current.sameAsRegisteredAddress,
                        )
                      }

                      onCancel?.()
                      close()
                    }}
                    type="button"
                    variant="link"
                  >
                    Cancel
                  </Button>

                  <Button
                    disabled={!isValid}
                    onClick={() => {
                      onSave?.()
                      close()
                    }}
                    type="submit"
                  >
                    Save
                  </Button>
                </div>
              )}
            </form.Subscribe>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  )
}
