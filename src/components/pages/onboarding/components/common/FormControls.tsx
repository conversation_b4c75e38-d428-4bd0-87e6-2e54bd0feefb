import { ReactFormExtendedApi } from "@tanstack/react-form"

import { But<PERSON> } from "@/components/ui/button"

interface FormControlsProps {
  form: ReactFormExtendedApi<any, any>
  submitBtnText?: string
  onSubmit?: () => void
  disabled?: boolean
}

export function FormControls({
  form,
  submitBtnText,
  onSubmit,
  disabled,
}: FormControlsProps) {
  return (
    <form.Subscribe>
      {({ isFieldsValidating, isFieldsValid, isValid }) => (
        <div className="mt-4 flex justify-start gap-4">
          <Button
            disabled={
              !isFieldsValid || !isValid || isFieldsValidating || disabled
            }
            onClick={onSubmit ?? form.handleSubmit}
            type="submit"
          >
            {submitBtnText ?? "Save & next"}
          </Button>
        </div>
      )}
    </form.Subscribe>
  )
}
