import { describe, expect, it, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { act, render, screen, within } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useGlobalStore } from "@/data/global/global.store"
import { OnboardingStepper } from "@/components/pages/onboarding/components/OnboardingStepper"
import { GetUserDto } from "@/client/onboarding/types.gen"

vi.mock("@/components/ui/tooltip", () => ({
  Tooltip: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  TooltipContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-content">{children}</div>
  ),
}))

// Mo<PERSON> hasPointerCapture and scrollIntoView
window.scrollTo = vi.fn()

vi.mock("@tanstack/react-router", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
    user: {
      ownerEntityId: "test-entity",
    } as GetUserDto,
    staticDataCurrencies: [],
    staticDataCountries: [],
    staticDataPaymentActivityTypes: [],
  })),
}))

vi.mock("@/components/pages/onboarding/components/OnboardingStepper.hook.ts")

// Mock form components
vi.mock("@/components/pages/onboarding/components/entity-details-form", () => ({
  EntityDetailsForm: ({ onNext, onInvalid }: any) => (
    <div aria-label="1. Entity details">
      <div>Form</div>
      <button data-testid="bank-invalid" onClick={onInvalid}>
        Invalid
      </button>
      <button onClick={() => onNext({})}>Save & next</button>
    </div>
  ),
}))

vi.mock(
  "@/components/pages/onboarding/components/further-details-form",
  () => ({
    FurtherDetailsForm: ({ onNext, onBack }: any) => (
      <div aria-label="2. Further details">
        <button onClick={() => onBack()}>Back</button>
        <div>Form</div>
        <button onClick={() => onNext({ payload: {} })}>Save & next</button>
      </div>
    ),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/ownership-control-form",
  () => ({
    OwnershipControlForm: ({ onNext, onBack }: any) => (
      <div aria-label="3. Ownership and control">
        <button onClick={() => onBack()}>Back</button>
        <div>Form</div>
        <button onClick={() => onNext({ payload: {} })}>Save & next</button>
      </div>
    ),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/financial-information-form",
  () => ({
    FinancialInformationForm: ({ onNext, onBack }: any) => (
      <div aria-label="4. Financial information">
        <button onClick={() => onBack()}>Back</button>
        <div>Form</div>
        <button onClick={() => onNext({ payload: {} })}>Save & next</button>
      </div>
    ),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/transaction-activity-form",
  () => ({
    TransactionActivityForm: ({ onNext, onBack }: any) => (
      <div aria-label="5. Transaction activity">
        <button onClick={() => onBack()}>Back</button>
        <div>Form</div>
        <button onClick={() => onNext({ payload: {} })}>Save & next</button>
      </div>
    ),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/users-and-signatories-form",
  () => ({
    UsersAndSignatoriesForm: ({ onNext, onBack }: any) => (
      <div aria-label="6. Users & signatories">
        <button onClick={() => onBack()}>Back</button>
        <div>Form</div>
        <button onClick={() => onNext()}>Skip for now</button>
        <button onClick={() => onNext({ payload: {} })}>Save & next</button>
      </div>
    ),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/applicant-details-form",
  () => ({
    ApplicantDetailsForm: ({ onNext, onBack }: any) => (
      <div aria-label="7. Applicant details">
        <button onClick={() => onBack()}>Back</button>
        <div>Form</div>
        <button onClick={() => onNext({ payload: {} })}>Save & next</button>
      </div>
    ),
  }),
)

vi.mock("@/components/pages/onboarding/components/review-form", () => ({
  ReviewForm: ({ onNext, onBack }: any) => (
    <div aria-label="8. Review">
      <button onClick={() => onBack()}>Back</button>
      <div>Form</div>
      <button onClick={() => onNext({ payload: {} })}>
        Confirm and submit
      </button>
    </div>
  ),
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

describe("OnboardingStepper", () => {
  it("should start with entity details step", () => {
    renderWithQueryClient(<OnboardingStepper />)
    expect(screen.getByLabelText("1. Entity details")).toBeInTheDocument()
  })

  it("should progress through steps when forms are completed", async () => {
    const user = userEvent.setup()

    renderWithQueryClient(<OnboardingStepper />)

    /** 1. Entity details */
    const entityDetailsStep = screen.getByLabelText("1. Entity details")
    expect(entityDetailsStep).toBeInTheDocument()
    const entityDetailsNext = within(entityDetailsStep).getByRole("button", {
      name: "Save & next",
    })
    expect(entityDetailsNext).toBeInTheDocument()
    await user.click(entityDetailsNext)

    act(() => {
      useGlobalStore.getState().setOnboardingStepperActiveStep(1)
    })

    /** 2. Further details */
    const furtherDetailsStep = screen.getByLabelText("2. Further details")
    expect(furtherDetailsStep).toBeInTheDocument()
    const furtherDetailsNext = within(furtherDetailsStep).getByRole("button", {
      name: "Save & next",
    })
    expect(furtherDetailsNext).toBeInTheDocument()
    await user.click(furtherDetailsNext)

    act(() => {
      useGlobalStore.getState().setOnboardingStepperActiveStep(2)
    })

    /** 3. Ownership and control */
    const ownershipStep = screen.getByLabelText("3. Ownership and control")
    expect(ownershipStep).toBeInTheDocument()
    const ownershipNext = within(ownershipStep).getByRole("button", {
      name: "Save & next",
    })
    expect(ownershipNext).toBeInTheDocument()
    await user.click(ownershipNext)

    act(() => {
      useGlobalStore.getState().setOnboardingStepperActiveStep(3)
    })

    /** 4. Financial information */
    const financialStep = screen.getByLabelText("4. Financial information")
    expect(financialStep).toBeInTheDocument()
    const financialNext = within(financialStep).getByRole("button", {
      name: "Save & next",
    })
    expect(financialNext).toBeInTheDocument()
    await user.click(financialNext)

    act(() => {
      useGlobalStore.getState().setOnboardingStepperActiveStep(4)
    })

    /** 5. Transaction activity */
    const transactionStep = screen.getByLabelText("5. Transaction activity")
    expect(transactionStep).toBeInTheDocument()
    const transactionNext = within(transactionStep).getByRole("button", {
      name: "Save & next",
    })
    expect(transactionNext).toBeInTheDocument()
    await user.click(transactionNext)

    act(() => {
      useGlobalStore.getState().setOnboardingStepperActiveStep(5)
    })

    /** 6. Users & signatories */
    const usersStep = screen.getByLabelText("6. Users & signatories")
    expect(usersStep).toBeInTheDocument()
    const usersNext = within(usersStep).getByRole("button", {
      name: "Skip for now",
    })
    expect(usersNext).toBeInTheDocument()
    await user.click(usersNext)

    act(() => {
      useGlobalStore.setState({ onboardingStepperActiveStep: 6 })
    })

    /** 7. Applicant details */
    const applicantDetailsStep = screen.getByLabelText("7. Applicant details")
    expect(applicantDetailsStep).toBeInTheDocument()
    const applicantDetailsNext = within(applicantDetailsStep).getByRole(
      "button",
      {
        name: "Save & next",
      },
    )
    expect(applicantDetailsNext).toBeInTheDocument()
    await user.click(applicantDetailsNext)

    act(() => {
      useGlobalStore.setState({ onboardingStepperActiveStep: 7 })
    })

    /** 7. Review */
    expect(screen.getByLabelText("8. Review")).toBeInTheDocument()
  })
})
