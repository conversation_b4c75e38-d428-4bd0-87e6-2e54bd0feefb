import { Suspense } from "react"

import { Stepper, Step } from "@/components/base/triggers/Stepper"

import { useOnboardingStepper } from "./OnboardingStepper.hooks"
import * as Steps from "./Onboarding.steps"
import { FormStepId } from "./FormSteps"

export function OnboardingStepper() {
  const { activeStep, getStatus, handleNext, handleBack, handleStepChange } =
    useOnboardingStepper()

  return (
    <div className="flex">
      <Stepper
        onStepChange={handleStepChange}
        value={activeStep}
        variant="vertical"
      >
        <Step
          status={getStatus(FormStepId.entityDetailsForm)}
          title="Entity details"
        >
          <Suspense>
            <Steps.EntityDetailsForm onNext={handleNext} />
          </Suspense>
        </Step>

        <Step
          status={getStatus(FormStepId.furtherDetailsForm)}
          title="Further details"
        >
          <Suspense>
            <Steps.FurtherDetailsForm onBack={handleBack} onNext={handleNext} />
          </Suspense>
        </Step>

        <Step
          status={getStatus(FormStepId.ownershipAndControlForm)}
          title="Ownership and control"
        >
          <Suspense>
            <Steps.OwnershipControlForm
              onBack={handleBack}
              onNext={handleNext}
            />
          </Suspense>
        </Step>

        <Step
          status={getStatus(FormStepId.financialInformationForm)}
          title="Financial information"
        >
          <Suspense>
            <Steps.FinancialInformationForm
              onBack={handleBack}
              onNext={handleNext}
            />
          </Suspense>
        </Step>

        <Step
          status={getStatus(FormStepId.transactionActivityForm)}
          title="Transaction activity"
        >
          <Suspense>
            <Steps.TransactionActivityForm
              onBack={handleBack}
              onNext={handleNext}
            />
          </Suspense>
        </Step>

        <Step
          status={getStatus(FormStepId.usersAndSignatoriesForm)}
          title="Users and Signatories"
        >
          <Suspense>
            <Steps.UsersAndSignatoriesForm
              onBack={handleBack}
              onNext={handleNext}
            />
          </Suspense>
        </Step>

        <Step
          status={getStatus(FormStepId.applicantDetailsForm)}
          title="Applicant details"
        >
          <Suspense>
            <Steps.ApplicantDetailsForm
              onBack={handleBack}
              onNext={handleNext}
            />
          </Suspense>
        </Step>

        <Step status={getStatus(FormStepId.reviewForm)} title="Review">
          <Suspense>
            <Steps.ReviewForm onBack={handleBack} onNext={handleNext} />
          </Suspense>
        </Step>
      </Stepper>
    </div>
  )
}
