import { describe, expect, it, Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import type {
  GetEntityDocumentsDto,
  GetFinancialSourcesDto,
} from "@/client/onboarding/types.gen"

import { useRolePermissions } from "@/hooks/useRolePermissions"
import {
  useGetFinancialDocumentsSuspenseQry,
  useGetFinancialInfoSourcesSuspenseQry,
} from "@/data/onboarding/onboarding.query"
import {
  FinancialInformationForm,
  FinancialInformationFormProps,
} from "@/components/pages/onboarding/components/financial-information-form"

vi.mock(
  import("@/data/onboarding/onboarding.query"),
  async (importOriginal) => {
    const mod = await importOriginal() // type is inferred

    return {
      ...mod,
      // replace some exports
      useGetFinancialDocumentsSuspenseQry: vi.fn(),
      useGetFinancialInfoSourcesSuspenseQry: vi.fn(),
    }
  },
)

vi.mock("@tanstack/react-router", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
    staticSourceOfFunds: [
      {
        key: "Revenue",
        display: "Revenue",
      },
    ],
  })),
}))

vi.mock("@/components/ui/tooltip", () => ({
  Tooltip: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  TooltipContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-content">{children}</div>
  ),
}))
vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(),
}))
vi.mocked(useRolePermissions).mockReturnValue({
  getPermission: () => true,
})

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

const setup = (props?: Partial<FinancialInformationFormProps>) => {
  const user = userEvent.setup()
  const form = renderWithQueryClient(<FinancialInformationForm {...props} />)

  // title
  const formHeader = screen.getByText("4. Financial information")

  const [uploadInput, nextBtn] = [
    screen.getByLabelText("Upload financial statements", { exact: false }),
    screen.getByRole("button", {
      name: "Save & next",
    }),
  ]
  const fundDropdown = screen.getByRole("combobox", {
    name: "Select source of funds",
  })
  const fundOtherText = screen.getByLabelText("sourceOfFundsOther")

  return {
    formHeader,
    uploadInput,
    nextBtn,
    ...form,
    user,
    fundDropdown,
    fundOtherText,
  } as const
}

describe("FinancialInformationForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useGetFinancialDocumentsSuspenseQry as Mock).mockReturnValue({
      data: [
        {
          id: "1",
          name: "some-file.pdf",
        },
      ] as GetEntityDocumentsDto[],
      isLoading: false,
      isError: false,
    })
    ;(useGetFinancialInfoSourcesSuspenseQry as Mock).mockReturnValue({
      data: {
        sourceOfFunds: "Other",
        sourceOfWealth: "test",
      } as GetFinancialSourcesDto,
      isLoading: false,
      isError: false,
    })
  })

  it("Should show the form", () => {
    const { formHeader, uploadInput, nextBtn } = setup()

    expect(formHeader).toBeInTheDocument()
    expect(uploadInput).toBeInTheDocument()
    expect(nextBtn).toBeInTheDocument()
  })

  it("Should show the source of funds", () => {
    const { fundDropdown, fundOtherText } = setup()

    expect(fundDropdown).toBeInTheDocument()
    expect(fundOtherText).toBeInTheDocument()
    expect(fundOtherText).toHaveDisplayValue("test")
  })

  it("should handle file upload", async () => {
    const { user, uploadInput } = setup()
    const file = new File(["test"], "directors.pdf", {
      type: "application/pdf",
    })

    await user.upload(uploadInput, file)
    expect((uploadInput as HTMLInputElement).files?.[0]).toBe(file)
  })
})
