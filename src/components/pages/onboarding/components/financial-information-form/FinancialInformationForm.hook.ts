import { useEffect } from "react"
import { useForm } from "@tanstack/react-form"

import { useFormInvalid } from "@/hooks/use-form-invalid"
import {
  GET_DOCUMENT_FINANCIAL_STATEMENT_QRY_KEY,
  GET_FINANCIAL_INFO_SOURCES_QRY_KEY,
  useGetFinancialDocumentsSuspenseQry,
  useGetFinancialInfoSourcesSuspenseQry,
} from "@/data/onboarding/onboarding.query"
import {
  useDeleteDocumentMutation,
  useUploadDocumentMutation,
  useUpdateFinancialInfoSourceFundsMutation,
} from "@/data/onboarding/onboarding.mutation"
import { type IOnboardingFinancialInformation } from "@/data/onboarding/onboarding.interface"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGlobalStore } from "@/data/global/global.store"
import {
  type GetEntityDocumentsDto,
  type GetFinancialSourcesDto,
  type EnumValueDto,
} from "@/client/onboarding/types.gen"

import { FinancialInformationFormProps } from "./FinancialInformationForm"
import { getDocumentByCategory } from "../utils"
import useFormBlockWithUnsavedChanges from "../hooks/useWarnWithUnsavedChanges"

export function useFinancialInformationForm(
  props: FinancialInformationFormProps,
) {
  const documentsQry = useGetFinancialDocumentsSuspenseQry()
  const financialInfoSources = useGetFinancialInfoSourcesSuspenseQry()
  const { staticSourceOfFunds } = useLoaderData()

  const { mutateAsync: uploadDocument } = useUploadDocumentMutation(
    [GET_DOCUMENT_FINANCIAL_STATEMENT_QRY_KEY],
    undefined,
    true,
  )
  const { mutateAsync: updateFunds } =
    useUpdateFinancialInfoSourceFundsMutation([
      GET_FINANCIAL_INFO_SOURCES_QRY_KEY,
    ])
  const { mutate: deleteDocument } = useDeleteDocumentMutation([
    GET_DOCUMENT_FINANCIAL_STATEMENT_QRY_KEY,
  ])
  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)

  const form = useForm<IOnboardingFinancialInformation>({
    defaultValues: {
      sourceOfFunds: financialInfoSources?.data?.sourceOfFunds || null,
      sourceOfFundsOther: financialInfoSources?.data?.sourceOfWealth || "",
      documentsMetadata:
        documentsQry.data
          ?.filter((e) => e.category === "FinancialStatements")
          .map((e) => ({
            id: e.id,
            name: e.name!,
          })) ?? [],
      documentsToUpload: [],
    },
    onSubmit: ({ value }) =>
      handleSave({
        onSuccess: () => {
          props.onNext?.(value)
        },
      }),
    validators: {
      onMount: () => {
        setFormSeen({
          ...formSeenState,
          financialInformationForm: true,
        })

        return null
      },
    },
  })

  useEffect(() => {
    if (!documentsQry.isLoading) {
      const documents =
        documentsQry.data?.map((document) => ({
          id: document.id,
          name: document.name!,
        })) ?? []

      form.setFieldValue("documentsMetadata", documents)
      form.setFieldMeta("documentsMetadata", {
        ...form.getFieldMeta("documentsMetadata")!,
        isDirty: false,
      })
    }

    return () => {
      form.setFieldValue("documentsToUpload", [])
      form.setFieldMeta("documentsToUpload", {
        ...form.getFieldMeta("documentsToUpload")!,
        isDirty: false,
      })
    }
  }, [documentsQry.isLoading, documentsQry.data, form])

  useFormInvalid(form, props.onInvalid)

  // Block step change if data is unsaved
  useFormBlockWithUnsavedChanges(form)

  function handleDelete(documentId: string) {
    deleteDocument({ documentId })
  }

  async function handleSave(handlers?: Record<string, () => void>) {
    const [errors] = await form.validateAllFields("submit")

    if (errors) return

    await Promise.all([
      updateFunds({
        sourceOfFunds: form.state.values.sourceOfFunds,
        sourceOfWealth: form.state.values.sourceOfFundsOther,
      }),
      uploadDocument(
        {
          documentCategory: "FinancialStatements",
          payload: form.state.values.documentsToUpload,
        },
        handlers,
      ),
    ])
  }

  return {
    form,
    handleSave,
    handleDelete,
    sourceOfFunds: staticSourceOfFunds,
  }
}

export function getReviewFinancialInformation(
  staticSourceOfFunds: EnumValueDto[],
  documents: GetEntityDocumentsDto[] | undefined,
  financialInfoSourcesOfFunds: GetFinancialSourcesDto | undefined,
) {
  const financialStatements = getDocumentByCategory(
    documents,
    "FinancialStatements",
  )

  let fundName =
    staticSourceOfFunds?.filter(
      (s) => s.key == financialInfoSourcesOfFunds?.sourceOfFunds,
    )[0]?.display || ""
  if (financialInfoSourcesOfFunds?.sourceOfFunds == "Other") {
    fundName = financialInfoSourcesOfFunds?.sourceOfWealth
      ? "Other - " + financialInfoSourcesOfFunds?.sourceOfWealth
      : ""
  }

  return {
    financialStatements:
      financialStatements.map((e) => e.name).join(", ") ?? "",
    sourceOfFunds: fundName,
  }
}
