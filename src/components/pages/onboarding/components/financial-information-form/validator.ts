import { IOnboardingFinancialInformation } from "@/data/onboarding/onboarding.interface"

import { getReviewFinancialInformation } from "./FinancialInformationForm.hook"

export function validateFinancialInformationForm(
  data?: IOnboardingFinancialInformation,
) {
  const isValid =
    Array.isArray(data?.documentsMetadata) && data?.documentsMetadata.length > 0

  return isValid
}

export default function reviewFinancialInformation(
  data?: ReturnType<typeof getReviewFinancialInformation>,
) {
  const isValid = !!data?.financialStatements && data?.sourceOfFunds.length > 0

  return isValid
}
