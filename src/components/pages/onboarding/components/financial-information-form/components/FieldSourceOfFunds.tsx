import { useState } from "react"
import { useStore } from "@tanstack/react-store"

import { required } from "@/lib/form.utils"
import Subline from "@/components/ui/subline"
import { Input } from "@/components/ui/input"
import { SingleSelector } from "@/components/base/form/searchable-selector"
import { FormField } from "@/components/base/form/form"
import { FormFieldError } from "@/components/base/form/form"
import {
  StaticDataSourceOfFundsGetResponse,
  SourceOfFunds,
} from "@/client/onboarding/types.gen"

import { FinancialInformationFieldProps } from "../interface"

interface FieldUSourceOfFundsProps extends FinancialInformationFieldProps {
  sourceOfFunds: StaticDataSourceOfFundsGetResponse
}
export function FieldUSourceOfFunds({
  form,
  permission,
  sourceOfFunds,
}: FieldUSourceOfFundsProps) {
  const [fund, setFund] = useState(form.getFieldValue("sourceOfFunds") || "")

  const isOther = fund == "Other"

  const errors = useStore(
    form?.store,
    (state) => (state.fieldMeta.sourceOfFundsOther?.errors ?? []) as string[],
  )

  return (
    <>
      <form.Field
        name="sourceOfFunds"
        validators={{
          onChange: ({ value }) =>
            required(value || "", "Source of funds is required"),
        }}
      >
        {(field) => {
          return (
            <div className="flex flex-col gap-y-3">
              <FormField
                aria-label={field.name}
                field={field}
                label="Source of funds"
                required
              >
                <SingleSelector
                  disabled={!permission}
                  items={sourceOfFunds.map((s) => ({
                    value: s.key || "",
                    label: s.display || "",
                  }))}
                  onSelectedItemsChange={(selectedItem) => {
                    field.handleChange(selectedItem?.value as SourceOfFunds)
                    setFund(selectedItem.value)
                  }}
                  selectedItem={{
                    value: field.state.value || "",
                    label:
                      sourceOfFunds.filter((s) => s.key == field.state.value)[0]
                        ?.display || "",
                  }}
                  Trigger={{
                    id: field.name,
                    placeholder: "Select source of funds",
                  }}
                />
              </FormField>
            </div>
          )
        }}
      </form.Field>

      <form.Field
        mode="array"
        name="sourceOfFundsOther"
        validators={{
          onChange: ({ value }) => {
            if (isOther && (!value || value?.length === 0)) {
              return "Source of funds is required"
            }

            return undefined
          },
        }}
      >
        {(rootField) => (
          <>
            {isOther && (
              <>
                <div className="-mt-5 flex items-start gap-x-1">
                  <Subline />

                  <div className="flex flex-1 flex-col gap-y-3">
                    <Input
                      aria-label={rootField.name}
                      className="bg-background p-4 py-6 md:text-base"
                      disabled={!permission}
                      id={rootField.name}
                      onChange={(e) => rootField.handleChange(e.target.value)}
                      placeholder="Enter source funds"
                      type="text"
                      value={rootField.state.value || ""}
                    />
                  </div>
                </div>
                <div className="-mt-5 ml-12">
                  <FormFieldError
                    aria-label={`source of funds error`}
                    errors={errors}
                  />
                </div>
              </>
            )}
          </>
        )}
      </form.Field>
    </>
  )
}
