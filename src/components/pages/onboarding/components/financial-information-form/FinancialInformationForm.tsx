import { useRolePermissions } from "@/hooks/useRolePermissions"
import { type IOnboardingFinancialInformation } from "@/data/onboarding/onboarding.interface"
import { FormLayout } from "@/components/base/form/form"

import { useFinancialInformationForm } from "./FinancialInformationForm.hook"
import { Fields } from "./components"
import { GoBack } from "../common/GoBack"
import { FormControls } from "../common/FormControls"

export interface FinancialInformationFormProps {
  onNext?: (payload: IOnboardingFinancialInformation) => void
  onInvalid?: () => void
  onBack?: () => void
}

export function FinancialInformationForm(props: FinancialInformationFormProps) {
  const { form, handleDelete, sourceOfFunds } =
    useFinancialInformationForm(props)
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")
  return (
    <div
      aria-label="4. Financial information"
      className="container flex max-w-2xl flex-col gap-y-8 px-4"
    >
      {props.onBack && <GoBack onBack={props.onBack} />}

      <FormLayout
        className="mx-0 flex max-w-xl flex-col gap-y-7"
        title="4. Financial information"
      >
        <p className="text-muted-foreground">
          Please upload at least your most recent financial statements, you may
          also upload historical statements which may help our KYC processes.
        </p>

        <Fields.SourceOfFunds
          form={form}
          permission={permission}
          sourceOfFunds={sourceOfFunds}
        />

        {form && (
          <Fields.UploadDocuments
            form={form}
            onDelete={handleDelete}
            permission={permission}
          />
        )}

        <FormControls disabled={!permission} form={form} />
      </FormLayout>
    </div>
  )
}
