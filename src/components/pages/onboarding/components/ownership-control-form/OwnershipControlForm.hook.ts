import { useEffect } from "react"
import { useForm } from "@tanstack/react-form"

import { useFormInvalid } from "@/hooks/use-form-invalid"
import { useUpdateOwnershipControlFormMutation } from "@/data/onboarding/ownership-control.mutation"
import {
  GET_DOCUMENT_REGISTER_OF_DIRECTORS_QRY_KEY,
  GET_DOCUMENT_STRUCTURE_CHART_QRY_KEY,
  useGetOwnershipDocumentsSuspenseQry,
} from "@/data/onboarding/onboarding.query"
import { useDeleteDocumentMutation } from "@/data/onboarding/onboarding.mutation"
import { IOnboardingOwnershipControl } from "@/data/onboarding/onboarding.interface"
import { useGlobalStore } from "@/data/global/global.store"
import {
  DocumentCategory,
  GetEntityDocumentsDto,
} from "@/client/onboarding/types.gen"

import { OwnershipControlFormProps } from "./OwnershipControlForm"
import useFormBlockWithUnsavedChanges from "../hooks/useWarnWithUnsavedChanges"

function formIsTouched(formValues: any): boolean {
  let touched = false
  if (formValues.directors.documentsMetadata.length > 0) touched = true
  if (formValues.structureChart.documentsMetadata.length > 0) touched = true
  return touched
}

export function useOwnershipControlForm(props: OwnershipControlFormProps) {
  const documentsQry = useGetOwnershipDocumentsSuspenseQry()
  const { mutate: updateOwnershipControlMutate } =
    useUpdateOwnershipControlFormMutation()
  const { mutate: deleteDocumentMutate } = useDeleteDocumentMutation([
    GET_DOCUMENT_REGISTER_OF_DIRECTORS_QRY_KEY,
    GET_DOCUMENT_STRUCTURE_CHART_QRY_KEY,
  ])

  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)

  const defaultValues = {
    directors: {
      documentsMetadata:
        documentsQry.data
          ?.filter((e) => e.category === "RegisterOfDirectors")
          .map((e) => ({
            id: e.id,
            name: e.name!,
          })) ?? [],
      documentsToUpload: [],
    },
    structureChart: {
      documentsMetadata:
        documentsQry.data
          ?.filter((e) => e.category === "StructureChart")
          .map((e) => ({
            id: e.id,
            name: e.name!,
          })) ?? [],
      documentsToUpload: [],
    },
  }

  const isTouched = formIsTouched(defaultValues)

  const form = useForm<IOnboardingOwnershipControl>({
    defaultValues,
    onSubmit: ({ value }) =>
      handleSave({
        onSuccess: () => {
          props.onNext?.(value)
        },
      }),
    validators: {
      onMount: ({ formApi }) => {
        setFormSeen({
          ...formSeenState,
          ownershipAndControlForm: true,
        })

        if (isTouched) {
          formApi.validateAllFields("submit")
        }

        return null
      },
    },
  })

  useEffect(() => {
    if (!documentsQry.isLoading) {
      const categorizedDocuments = documentsQry.data?.reduce(
        (acc, doc) => {
          if (!doc) return acc

          if (!(doc.category! in acc))
            acc[doc.category as DocumentCategory] = []

          acc[doc.category as DocumentCategory].push({
            id: doc.id!,
            name: doc.name!,
          })

          return acc
        },
        {} as Record<DocumentCategory, { id: string; name: string }[]>,
      )

      if (categorizedDocuments?.RegisterOfDirectors) {
        form.setFieldValue(
          "directors.documentsMetadata",
          categorizedDocuments?.RegisterOfDirectors,
        )
        form.setFieldMeta("directors.documentsMetadata", {
          ...form.getFieldMeta("directors.documentsMetadata")!,
          isDirty: false,
        })
      }

      if (categorizedDocuments?.StructureChart) {
        form.setFieldValue(
          "structureChart.documentsMetadata",
          categorizedDocuments?.StructureChart,
        )
        form.setFieldMeta("structureChart.documentsMetadata", {
          ...form.getFieldMeta("structureChart.documentsMetadata")!,
          isDirty: false,
        })
      }
    }

    return () => {
      // Cleanup: We need to clear the array if the user decides to come
      // back to this step
      form.setFieldValue("directors.documentsToUpload", [])
      form.setFieldMeta("directors.documentsToUpload", {
        ...form.getFieldMeta("directors.documentsToUpload")!,
        isDirty: false,
      })

      form.setFieldValue("structureChart.documentsToUpload", [])
      form.setFieldMeta("structureChart.documentsToUpload", {
        ...form.getFieldMeta("structureChart.documentsToUpload")!,
        isDirty: false,
      })
    }
  }, [documentsQry.isLoading, documentsQry.data, form])

  useFormInvalid(form, props.onInvalid)

  // Block step change if data is unsaved
  useFormBlockWithUnsavedChanges(form)

  function handleDelete(documentId: string) {
    deleteDocumentMutate({ documentId })
  }

  async function handleSave(handlers?: Record<string, () => void>) {
    const [errors] = await form.validateAllFields("submit")

    if (errors) return

    updateOwnershipControlMutate({ form: form.state.values }, handlers)
  }

  return {
    form,
    handleSave,
    handleDelete,
  }
}

function getDocumentByCategory(
  documents: GetEntityDocumentsDto[] | undefined,
  category: DocumentCategory,
) {
  return documents?.filter((d) => d.category === category) ?? []
}

export function getReviewOwnershipControl(
  documents: GetEntityDocumentsDto[] | undefined,
) {
  const registerOfDirectorsDocuments = getDocumentByCategory(
    documents,
    "RegisterOfDirectors",
  )
  const structureChartDocuments = getDocumentByCategory(
    documents,
    "StructureChart",
  )

  return {
    directorsDocuments:
      registerOfDirectorsDocuments.map((e) => e.name).join(", ") ?? "",
    structureChartDocuments:
      structureChartDocuments.map((e) => e.name).join(", ") ?? "",
  }
}
