import { useRolePermissions } from "@/hooks/useRolePermissions"
import { IOnboardingOwnershipControl } from "@/data/onboarding/onboarding.interface"
import { FormLayout } from "@/components/base/form/form"

import { useOwnershipControlForm } from "./OwnershipControlForm.hook"
import { Fields } from "./components"
import { GoBack } from "../common/GoBack"
import { FormControls } from "../common/FormControls"

export interface OwnershipControlFormProps {
  onNext?: (payload: IOnboardingOwnershipControl) => void
  onBack?: () => void
  onInvalid?: () => void
}

export function OwnershipControlForm(props: OwnershipControlFormProps) {
  const { form, handleDelete } = useOwnershipControlForm(props)
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")
  return (
    <div
      aria-label="3. Ownership and control"
      className="container flex max-w-2xl flex-col gap-y-8 px-4"
    >
      {props.onBack && <GoBack onBack={props.onBack} />}

      <FormLayout
        className="mx-0 flex max-w-xl flex-col gap-y-7"
        title="3. Ownership and control"
      >
        <p className="text-muted-foreground">
          Please upload the documents requested below. The entity structure
          chart should be certified and dated by an authorised representative.
          You may upload multiple documents per upload field.
        </p>
        <div className="space-y-6">
          <Fields.UploadDirectors
            form={form}
            onDelete={handleDelete}
            permission={permission}
          />
          <Fields.UploadStructureChart
            form={form}
            onDelete={handleDelete}
            permission={permission}
          />
        </div>

        <FormControls disabled={!permission} form={form} />
      </FormLayout>
    </div>
  )
}
