import { <PERSON><PERSON><PERSON> } from "react"
import { <PERSON><PERSON><PERSON> } from "@tanstack/react-form"

import { createDocumentBadgeClickHandler } from "@/lib/document.utils"
import { useDocumentDownload } from "@/hooks/use-document-download"
import {
  DocumentsMetadata,
  IOnboardingOwnershipControl,
} from "@/data/onboarding/onboarding.interface"
import { FormField } from "@/components/base/form/form"

import { OwnershipControlFieldProps } from "../interface"
import { validateFiles } from "../../common/upload-documents/FileUploadValidation"
import { FileUpload } from "../../common/upload-documents/FileUpload"
import FileBadge from "../../common/upload-documents/FileBadge"

type DocumentsMetadataField = FieldApi<
  IOnboardingOwnershipControl,
  "structureChart.documentsMetadata",
  undefined,
  undefined,
  DocumentsMetadata[]
>

type DocumentsToUploadField = FieldApi<
  IOnboardingOwnershipControl,
  "structureChart.documentsToUpload",
  undefined,
  undefined,
  File[]
>

interface FieldUploadStructureChartProps extends OwnershipControlFieldProps {
  onDelete?: (documentId: string) => void
}

export function FieldUploadStructureChart({
  form,
  onDelete,
  permission,
}: FieldUploadStructureChartProps) {
  const { handleDownload } = useDocumentDownload()

  const resetFieldMetas = () => {
    form.setFieldMeta("structureChart.documentsToUpload", {
      ...form.getFieldMeta("structureChart.documentsToUpload")!,
      isDirty: false,
    })
    form.setFieldMeta("structureChart.documentsMetadata", {
      ...form.getFieldMeta("structureChart.documentsMetadata")!,
      isDirty: false,
    })
  }

  const handleFileChange = (files: File[], field: DocumentsToUploadField) => {
    const allFiles = [
      ...form.getFieldValue("structureChart.documentsMetadata"),
      ...form.getFieldValue("structureChart.documentsToUpload"),
    ]
    const hasErrors = Array.from(files)
      .map((file) => validateFiles(file, allFiles))
      .every((isValid) => !isValid)

    if (hasErrors) {
      return
    }

    Array.from(files).forEach((file) => {
      field.pushValue(file)
      form.pushFieldValue("structureChart.documentsMetadata", {
        name: file.name,
      })
    })
  }

  const handleOnDelete = (
    e: MouseEvent,
    field: DocumentsMetadataField,
    document: DocumentsMetadata,
    index: number,
  ) => {
    e.preventDefault()
    field.removeValue(index)

    if (document.id && typeof onDelete !== "undefined") {
      onDelete(document.id)
      return
    }

    // to ensure the form doesn't submit the document
    form.setFieldValue(
      "structureChart.documentsToUpload",
      form
        .getFieldValue("structureChart.documentsToUpload")!
        .filter((d) => d.name !== document.name),
    )

    resetFieldMetas()
  }

  const handleBadgeClick = createDocumentBadgeClickHandler(
    form,
    "structureChart.documentsToUpload",
    handleDownload,
  )

  return (
    <>
      <form.Field
        mode="array"
        name="structureChart.documentsToUpload"
        validators={{
          onChange: ({ value }: { value: File[] }) => {
            const values =
              form.getFieldValue("structureChart.documentsMetadata") ?? []

            if (Array.isArray(values) && values.length <= 0) {
              if (value.length > 0) return undefined

              return "Structure chart document is required"
            }

            return undefined
          },
        }}
      >
        {(rootField) => {
          return (
            <div className="flex flex-col gap-y-3">
              <FormField
                aria-label={rootField.name as string}
                field={rootField}
                required
              >
                <FileUpload
                  disabled={!permission}
                  field={rootField}
                  label="Upload entity structure chart"
                  onChange={(files) =>
                    files && handleFileChange(Array.from(files), rootField)
                  }
                  required
                  tooltipText="Upload your entity's organizational structure chart"
                />
              </FormField>
            </div>
          )
        }}
      </form.Field>

      <form.Field name="structureChart.documentsMetadata">
        {(field) => (
          <div
            aria-label="Structure chart metadata"
            className="flex flex-wrap items-center gap-2"
          >
            {field.state.value.map((document, i) => (
              <form.Field
                key={i}
                name={`structureChart.documentsMetadata[${i}].name`}
              >
                {(subField) => (
                  <div className="relative flex items-center justify-center animate-in fade-in-0 slide-in-from-bottom-10">
                    <FileBadge
                      disabled={!permission}
                      onDelete={(e) => handleOnDelete(e, field, document, i)}
                      onDownload={(e) => handleBadgeClick(e, document)}
                    >
                      {subField.state.value}
                    </FileBadge>
                  </div>
                )}
              </form.Field>
            ))}
          </div>
        )}
      </form.Field>
    </>
  )
}
