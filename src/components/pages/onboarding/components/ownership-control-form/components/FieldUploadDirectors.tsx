import { <PERSON><PERSON><PERSON> } from "react"
import { <PERSON><PERSON><PERSON> } from "@tanstack/react-form"

import { createDocumentBadgeClickHandler } from "@/lib/document.utils"
import { useDocumentDownload } from "@/hooks/use-document-download"
import {
  DocumentsMetadata,
  IOnboardingOwnershipControl,
} from "@/data/onboarding/onboarding.interface"
import { FormField } from "@/components/base/form/form"

import { OwnershipControlFieldProps } from "../interface"
import { validateFiles } from "../../common/upload-documents/FileUploadValidation"
import { FileUpload } from "../../common/upload-documents/FileUpload"
import FileBadge from "../../common/upload-documents/FileBadge"

type DocumentsMetadataField = FieldApi<
  IOnboardingOwnershipControl,
  "directors.documentsMetadata",
  undefined,
  undefined,
  DocumentsMetadata[]
>

type DocumentsToUploadField = FieldApi<
  IOnboardingOwnershipControl,
  "directors.documentsToUpload",
  undefined,
  undefined,
  File[]
>

interface FieldUploadDirectorsProps extends OwnershipControlFieldProps {
  onDelete?: (documentId: string) => void
}

export function FieldUploadDirectors({
  form,
  onDelete,
  permission,
}: FieldUploadDirectorsProps) {
  const { handleDownload } = useDocumentDownload()

  const resetFieldMetas = () => {
    form.setFieldMeta("directors.documentsToUpload", {
      ...form.getFieldMeta("directors.documentsToUpload")!,
      isDirty: false,
    })
    form.setFieldMeta("directors.documentsMetadata", {
      ...form.getFieldMeta("directors.documentsMetadata")!,
      isDirty: false,
    })
  }
  const handleFileChange = (files: File[], field: DocumentsToUploadField) => {
    const allFiles = [
      ...form.getFieldValue("directors.documentsMetadata"),
      ...form.getFieldValue("directors.documentsToUpload"),
    ]
    const hasErrors = Array.from(files)
      .map((file) => validateFiles(file, allFiles))
      .every((isValid) => !isValid)

    if (hasErrors) {
      return
    }

    Array.from(files).forEach((file) => {
      field.pushValue(file)
      form.pushFieldValue("directors.documentsMetadata", {
        name: file.name,
      })
    })
  }

  const handleOnDelete = (
    e: MouseEvent,
    field: DocumentsMetadataField,
    document: DocumentsMetadata,
    index: number,
  ) => {
    e.preventDefault()
    field.removeValue(index)

    if (document.id && typeof onDelete !== "undefined") {
      onDelete(document.id)
      return
    }

    // to ensure the form doesn't submit the document
    form.setFieldValue(
      "directors.documentsToUpload",
      form
        .getFieldValue("directors.documentsToUpload")!
        .filter((d) => d.name !== document.name),
    )

    resetFieldMetas()
  }

  const handleBadgeClick = createDocumentBadgeClickHandler(
    form,
    "directors.documentsToUpload",
    handleDownload,
  )

  return (
    <>
      <form.Field
        mode="array"
        name="directors.documentsToUpload"
        validators={{
          onChange: ({ value }: { value: File[] }) => {
            const values =
              form.getFieldValue("directors.documentsMetadata") ?? []

            if (Array.isArray(values) && values.length <= 0) {
              if (value.length > 0) return undefined

              return "Register of directors document is required"
            }

            return undefined
          },
        }}
      >
        {(rootField) => {
          return (
            <div className="flex flex-col gap-y-3">
              <FormField
                aria-label={rootField.name as string}
                field={rootField}
                required
              >
                <FileUpload
                  disabled={!permission}
                  field={rootField}
                  label="Upload register of directors or equivalent"
                  onChange={(files) =>
                    files && handleFileChange(Array.from(files), rootField)
                  }
                  required
                  tooltipText="Upload your company's register of directors document"
                />
              </FormField>
            </div>
          )
        }}
      </form.Field>

      <form.Field name="directors.documentsMetadata">
        {(field) => (
          <div
            aria-label="Company registration metadata"
            className="flex flex-wrap items-center gap-2"
          >
            {field.state.value.map((document, i) => (
              <form.Field
                key={i}
                name={`directors.documentsMetadata[${i}].name`}
              >
                {(subField) => (
                  <div className="relative flex items-center justify-center animate-in fade-in-0 slide-in-from-bottom-10">
                    <FileBadge
                      disabled={!permission}
                      onDelete={(e) => handleOnDelete(e, field, document, i)}
                      onDownload={(e) => handleBadgeClick(e, document)}
                    >
                      {subField.state.value}
                    </FileBadge>
                  </div>
                )}
              </form.Field>
            ))}
          </div>
        )}
      </form.Field>
    </>
  )
}
