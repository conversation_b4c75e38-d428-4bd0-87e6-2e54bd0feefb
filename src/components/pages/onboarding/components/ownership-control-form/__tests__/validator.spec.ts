import { describe, it, expect } from "vitest"

import { IOnboardingOwnershipControl } from "@/data/onboarding/onboarding.interface"
import { validateOwnershipControlForm } from "@/components/pages/onboarding/components/ownership-control-form/validator"

describe("Ownership Control Form Validator", () => {
  const validOwnershipControl: IOnboardingOwnershipControl = {
    directors: {
      documentsMetadata: [
        { id: "doc1", name: "directors-register.pdf" },
        { id: "doc2", name: "board-resolution.pdf" },
      ],
      documentsToUpload: [],
    },
    structureChart: {
      documentsMetadata: [{ id: "doc3", name: "ownership-structure.pdf" }],
      documentsToUpload: [],
    },
  }

  describe("validateOwnershipControlForm", () => {
    it("should return true for valid ownership control data", () => {
      const result = validateOwnershipControlForm(validOwnershipControl)
      expect(result).toBe(true)
    })

    it("should return false when data is undefined", () => {
      const result = validateOwnershipControlForm(undefined)
      expect(result).toBe(false)
    })

    it("should throw error when directors is missing", () => {
      const invalidData = {
        ...validOwnershipControl,
        directors: undefined as any,
      }
      expect(() => validateOwnershipControlForm(invalidData)).toThrow()
    })

    it("should throw error when structureChart is missing", () => {
      const invalidData = {
        ...validOwnershipControl,
        structureChart: undefined as any,
      }
      expect(() => validateOwnershipControlForm(invalidData)).toThrow()
    })

    it("should return false when both directors and structureChart are missing", () => {
      const invalidData = {
        directors: undefined as any,
        structureChart: undefined as any,
      }
      const result = validateOwnershipControlForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when directors documentsMetadata is empty", () => {
      const invalidData = {
        ...validOwnershipControl,
        directors: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when directors documentsMetadata is not an array", () => {
      const invalidData = {
        ...validOwnershipControl,
        directors: {
          documentsMetadata: null as any,
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when structureChart documentsMetadata is empty", () => {
      const invalidData = {
        ...validOwnershipControl,
        structureChart: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when structureChart documentsMetadata is not an array", () => {
      const invalidData = {
        ...validOwnershipControl,
        structureChart: {
          documentsMetadata: undefined as any,
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when both documentsMetadata arrays are empty", () => {
      const invalidData = {
        directors: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
        structureChart: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return true when directors has documents but structureChart is empty (edge case)", () => {
      // This tests the current implementation logic
      const dataWithOnlyDirectors = {
        directors: {
          documentsMetadata: [{ id: "doc1", name: "directors.pdf" }],
          documentsToUpload: [],
        },
        structureChart: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(dataWithOnlyDirectors)
      expect(result).toBe(false) // Both arrays need to have documents
    })

    it("should return true when structureChart has documents but directors is empty (edge case)", () => {
      const dataWithOnlyStructureChart = {
        directors: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
        structureChart: {
          documentsMetadata: [{ id: "doc1", name: "structure.pdf" }],
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(dataWithOnlyStructureChart)
      expect(result).toBe(false) // Both arrays need to have documents
    })

    it("should handle minimal valid data", () => {
      const minimalValidData = {
        directors: {
          documentsMetadata: [{ id: "1", name: "doc.pdf" }],
          documentsToUpload: [],
        },
        structureChart: {
          documentsMetadata: [{ id: "2", name: "chart.pdf" }],
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(minimalValidData)
      expect(result).toBe(true)
    })

    it("should ignore documentsToUpload field in validation", () => {
      const dataWithUploadFiles = {
        directors: {
          documentsMetadata: [{ id: "doc1", name: "directors.pdf" }],
          documentsToUpload: [new File(["content"], "new-doc.pdf")],
        },
        structureChart: {
          documentsMetadata: [{ id: "doc2", name: "structure.pdf" }],
          documentsToUpload: [new File(["content"], "new-chart.pdf")],
        },
      }
      const result = validateOwnershipControlForm(dataWithUploadFiles)
      expect(result).toBe(true)
    })

    it("should handle multiple documents in each category", () => {
      const dataWithMultipleDocs = {
        directors: {
          documentsMetadata: [
            { id: "doc1", name: "directors1.pdf" },
            { id: "doc2", name: "directors2.pdf" },
            { id: "doc3", name: "directors3.pdf" },
          ],
          documentsToUpload: [],
        },
        structureChart: {
          documentsMetadata: [
            { id: "doc4", name: "structure1.pdf" },
            { id: "doc5", name: "structure2.pdf" },
          ],
          documentsToUpload: [],
        },
      }
      const result = validateOwnershipControlForm(dataWithMultipleDocs)
      expect(result).toBe(true)
    })
  })
})
