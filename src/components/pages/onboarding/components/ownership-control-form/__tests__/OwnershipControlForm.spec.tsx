import { describe, expect, it, Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, waitFor } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useRolePermissions } from "@/hooks/useRolePermissions"
import { useUpdateOwnershipControlFormMutation } from "@/data/onboarding/ownership-control.mutation"
import { useGetOwnershipDocumentsSuspenseQry } from "@/data/onboarding/onboarding.query"
import { useGetEntitySuspenseQry } from "@/data/onboarding/entity-details.query"
import { useGlobalStore } from "@/data/global/global.store"
import { OwnershipControlForm } from "@/components/pages/onboarding/components/ownership-control-form"
import { GetEntityDocumentsDto } from "@/client/onboarding/types.gen"

const mockGlobalStore = {
  entity: { id: "test-entity", name: "Argentex" },
}

// Mock the useParams hook
vi.mock("@tanstack/react-router", () => ({
  useParams: () => ({ entityId: "test-entity" }),
  useNavigate: () => vi.fn(),
  Link: ({ children, to }: any) => <a href={to}>{children}</a>,
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
  })),
}))

vi.mock(
  import("@/data/onboarding/onboarding.query"),
  async (importOriginal) => {
    const mod = await importOriginal() // type is inferred

    return {
      ...mod,
      // replace some exports
      useGetOwnershipDocumentsSuspenseQry: vi.fn(),
    }
  },
)
vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(),
}))
vi.mocked(useRolePermissions).mockReturnValue({
  getPermission: () => true,
})

vi.mock("@/data/onboarding/onboarding.api", () => ({
  uploadDocuments: vi.fn(),
  fetchDocuments: vi.fn(),
}))

vi.mock("@/data/onboarding/entity-details.query", () => ({
  useGetEntitySuspenseQry: vi.fn(),
}))

vi.mock("@/data/onboarding/ownership-control.mutation", () => ({
  useUpdateOwnershipControlFormMutation: vi.fn(),
}))

// Mock the mutation hook
vi.mock("@/data/onboarding/onboarding.mutation", () => ({
  useUploadDocumentMutation: vi.fn(),
  useDeleteDocumentMutation: vi.fn(() => ({ mutate: vi.fn() })),
}))

// Mock the global store
vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(() => mockGlobalStore),
}))

// Mock tooltips
vi.mock("@/components/ui/tooltip", () => ({
  Tooltip: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  TooltipContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-content">{children}</div>
  ),
}))

// Mock hasPointerCapture and scrollIntoView
window.HTMLElement.prototype.hasPointerCapture = () => false
window.HTMLElement.prototype.scrollIntoView = vi.fn()

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const mockEntity = {
  id: "test-entity-id",
  name: "Test Entity",
}

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

function setup(props = {}) {
  const mockMutation = {
    mutateAsync: vi.fn(),
    mutate: vi.fn(),
    isLoading: false,
  }

  ;(useGetEntitySuspenseQry as any).mockReturnValue({
    data: mockEntity,
    isLoading: false,
    isError: false,
  })
  ;(useGetOwnershipDocumentsSuspenseQry as Mock).mockReturnValue({
    data: [{ id: "some-id", name: "some-file.pdf" }] as GetEntityDocumentsDto[],
    isLoading: false,
    isError: false,
  })
  ;(useUpdateOwnershipControlFormMutation as any).mockReturnValue(mockMutation)

  const user = userEvent.setup()

  const form = renderWithQueryClient(<OwnershipControlForm {...props} />)

  const formHeader = screen.getByText("3. Ownership and control")

  const [uploadRegisterOfDirectorsInput, uploadStructuredChartsInput, nextBtn] =
    [
      screen.getByLabelText("Upload register of directors or equivalent", {
        exact: false,
      }),
      screen.getByLabelText("Upload entity structure chart", { exact: false }),
      screen.getByRole("button", {
        name: "Save & next",
      }),
    ]

  return {
    mockMutation,
    formHeader,
    uploadRegisterOfDirectorsInput,
    uploadStructuredChartsInput,
    nextBtn,
    user,
    ...form,
  }
}

describe("OwnershipControlForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useGlobalStore as any).mockImplementation(
      (
        selector: (state: {
          setOnboardingHasUnsavedChanges: any
          setOnboardingSeenState: any
        }) => unknown,
      ) =>
        selector({
          setOnboardingHasUnsavedChanges: vi.fn(),
          setOnboardingSeenState: vi.fn(),
        }),
    )
  })

  it("Should render form", () => {
    const { formHeader, uploadRegisterOfDirectorsInput, nextBtn } = setup()
    expect(formHeader).toBeInTheDocument()
    expect(uploadRegisterOfDirectorsInput).toBeInTheDocument()
    expect(nextBtn).toBeInTheDocument()
  })

  it("Should render the form with all required fields", () => {
    setup()

    expect(screen.getByText("3. Ownership and control")).toBeInTheDocument()
    expect(
      screen.getByText("Upload register of directors or equivalent"),
    ).toBeInTheDocument()
    expect(
      screen.getByText("Upload entity structure chart"),
    ).toBeInTheDocument()
  })

  it("Should handle file upload for directors file", async () => {
    const { user, uploadRegisterOfDirectorsInput } = setup()
    const file = new File(["test"], "directors.pdf", {
      type: "application/pdf",
    })

    await user.upload(uploadRegisterOfDirectorsInput, file)

    expect(
      (uploadRegisterOfDirectorsInput as HTMLInputElement).files?.[0],
    ).toBe(file)
    expect(
      (uploadRegisterOfDirectorsInput as HTMLInputElement).files?.length,
    ).toBe(1)
  })

  it("Should handle file upload for structure chart", async () => {
    const { user, uploadStructuredChartsInput } = setup()
    const file = new File(["test"], "structure.pdf", {
      type: "application/pdf",
    })

    await user.upload(uploadStructuredChartsInput, file)

    expect((uploadStructuredChartsInput as HTMLInputElement).files?.[0]).toBe(
      file,
    )
    expect(
      (uploadStructuredChartsInput as HTMLInputElement).files?.length,
    ).toBe(1)
  })

  it("should handle save and next button click", async () => {
    const onNext = vi.fn()
    const {
      user,
      uploadRegisterOfDirectorsInput,
      uploadStructuredChartsInput,
      nextBtn,
      mockMutation,
    } = setup({ onNext })

    const [registerOfDirectorsFile, structureChartFile] = [
      new File(["test"], "register-of-directors.pdf", {
        type: "application/pdf",
      }),
      new File(["test"], "structure-chart.pdf", {
        type: "application/pdf",
      }),
    ]

    await user.upload(uploadRegisterOfDirectorsInput, registerOfDirectorsFile)
    await user.upload(uploadStructuredChartsInput, structureChartFile)
    await user.click(nextBtn)

    expect(mockMutation.mutate).toHaveBeenCalled()
  })

  it.skip("should handle save button click", async () => {
    const { user, mockMutation } = setup()

    const saveButton = screen.getByRole("button", { name: /^save$/i })
    await user.click(saveButton)

    expect(mockMutation.mutateAsync).toHaveBeenCalled()
  })

  it.skip("should handle cancel button click", async () => {
    const { user } = setup()
    const cancelButton = screen.getByRole("button", { name: /cancel/i })
    await user.click(cancelButton)

    // Verify form is reset
    const directorsFileInput = screen.getByTestId(
      "directorsFile-input",
    ) as HTMLInputElement
    const structureChartInput = screen.getByTestId(
      "structureChartFile-input",
    ) as HTMLInputElement

    expect(directorsFileInput.files?.length).toBe(0)
    expect(structureChartInput.files?.length).toBe(0)
  })

  it("should show validation errors when submitting empty form", async () => {
    const { user, nextBtn } = setup()

    await user.click(nextBtn)

    await waitFor(() => {
      expect(
        screen.getByText("Register of directors document is required"),
      ).toBeInTheDocument()
      expect(
        screen.getByText("Structure chart document is required"),
      ).toBeInTheDocument()
    })
  })

  it("should handle back button click", async () => {
    const onBack = vi.fn()
    const { user } = setup({ onBack })

    const backBtn = screen.getByRole("button", {
      name: "Back",
    })

    await user.click(backBtn)

    expect(onBack).toHaveBeenCalled()
  })
})
