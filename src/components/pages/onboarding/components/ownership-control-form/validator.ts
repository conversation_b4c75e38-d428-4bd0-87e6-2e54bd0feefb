import { IOnboardingOwnershipControl } from "@/data/onboarding/onboarding.interface"

import { getReviewOwnershipControl } from "./OwnershipControlForm.hook"

export function validateOwnershipControlForm(
  data?: IOnboardingOwnershipControl,
) {
  if (!data || (!data.directors && !data.structureChart)) return false

  const hasFiles =
    Array.isArray(data.directors.documentsMetadata) &&
    data.directors.documentsMetadata.length > 0 &&
    Array.isArray(data.structureChart.documentsMetadata) &&
    data.structureChart.documentsMetadata.length > 0

  const isValid = !!hasFiles

  return isValid
}

export default function reviewOwnershipControl(
  data?: ReturnType<typeof getReviewOwnershipControl>,
) {
  if (!data?.directorsDocuments || !data?.structureChartDocuments) return false

  return true
}
