import { describe, it, expect } from "vitest"

import { IOnboardingEntityDetails } from "@/data/onboarding/onboarding.interface"
import { validateEntityDetailsForm } from "@/components/pages/onboarding/components/entity-details-form/validator"

describe("Entity Details Form Validator", () => {
  const validEntityDetails: IOnboardingEntityDetails = {
    entityName: "Test Company Ltd",
    tradingNames: ["Test Trading Ltd"],
    entityType: { key: "LIMITED_COMPANY", display: "Limited Company" },
    address: {
      buildingNumber: "123",
      street: "Main Street",
      city: "London",
      country: "GB",
      postalCode: "SW1A 1AA",
      state: "England",
    },
    tradingAddress: {
      buildingNumber: "456",
      street: "Trading Street",
      city: "London",
      country: "GB",
      postalCode: "SW1A 2BB",
      state: "England",
    },
    purposeOfEntity: { key: "TRADING", display: "Trading" },
    jurisdictionOfIncorporation: { code: "GB", name: "United Kingdom" },
    registrationNumber: "12345678",
  }

  describe("validateEntityDetailsForm", () => {
    it("should return true for valid entity details", () => {
      const result = validateEntityDetailsForm(validEntityDetails)
      expect(result).toBe(true)
    })

    it("should return false when entityName is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        entityName: "",
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when entityName is undefined", () => {
      const invalidData = {
        ...validEntityDetails,
        entityName: undefined as any,
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when entityType is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        entityType: undefined as any,
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when address buildingNumber is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        address: {
          ...validEntityDetails.address!,
          buildingNumber: "",
        },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when address street is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        address: {
          ...validEntityDetails.address!,
          street: "",
        },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when address city is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        address: {
          ...validEntityDetails.address!,
          city: "",
        },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when address country is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        address: {
          ...validEntityDetails.address!,
          country: undefined as any,
        },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when address postalCode is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        address: {
          ...validEntityDetails.address!,
          postalCode: "",
        },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when purposeOfEntity display is empty", () => {
      const invalidData = {
        ...validEntityDetails,
        purposeOfEntity: { key: "TRADING", display: "" },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when purposeOfEntity display length is 0", () => {
      const invalidData = {
        ...validEntityDetails,
        purposeOfEntity: { key: "TRADING", display: "" },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when jurisdictionOfIncorporation code is missing", () => {
      const invalidData = {
        ...validEntityDetails,
        jurisdictionOfIncorporation: { code: "", name: "United Kingdom" },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when registrationNumber is empty", () => {
      const invalidData = {
        ...validEntityDetails,
        registrationNumber: "",
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when registrationNumber length is 0", () => {
      const invalidData = {
        ...validEntityDetails,
        registrationNumber: "",
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when data is undefined", () => {
      const result = validateEntityDetailsForm(undefined)
      expect(result).toBe(false)
    })

    it("should return false when address is undefined", () => {
      const invalidData = {
        ...validEntityDetails,
        address: undefined,
      } as unknown as IOnboardingEntityDetails
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should handle multiple missing fields", () => {
      const invalidData = {
        ...validEntityDetails,
        entityName: "",
        entityType: undefined as any,
        registrationNumber: "",
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should handle missing address fields", () => {
      const invalidData = {
        ...validEntityDetails,
        address: {
          buildingNumber: "",
          street: "",
          city: "",
          country: undefined as any,
          postalCode: "",
          state: "",
        },
      }
      const result = validateEntityDetailsForm(invalidData)
      expect(result).toBe(false)
    })
  })
})
