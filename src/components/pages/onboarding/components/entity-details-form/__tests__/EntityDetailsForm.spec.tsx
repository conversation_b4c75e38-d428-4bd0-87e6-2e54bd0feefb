import { describe, expect, it, Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useRolePermissions } from "@/hooks/useRolePermissions"
import { useGetEntitySuspenseQry } from "@/data/onboarding/entity-details.query"
import { useGlobalStore } from "@/data/global/global.store"
import {
  EntityDetailsForm,
  EntityDetailsFormProps,
} from "@/components/pages/onboarding/components/entity-details-form"
import {
  GetAddressListDto,
  GetEntityResponseDto,
} from "@/client/onboarding/types.gen"

const mockGlobalStore = {
  entity: {
    id: "test-entity",
    name: "Argentex",
    entityType: "LimitedCompany",
    jurisdictionOfIncorporationCountryCode: "GBP",
  },
}

vi.mock("@/data/onboarding/entity-details.query", () => ({
  useGetEntitySuspenseQry: vi.fn(),
  useGetAddressesSuspenseQry: vi.fn(() => ({
    data: [
      {
        addressType: "RegisteredAddress",
        id: "some-id",
        countryCode: "GB",
        cityOrTown: "London",
        postalCode: "NW9 75N",
        line1: "John str",
        line2: "Woodlane",
      },
    ] as GetAddressListDto[],
    isLoading: false,
    isError: false,
  })),
}))

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(() => mockGlobalStore),
}))

vi.mock("@tanstack/react-router", () => {
  const navigate = vi.fn()

  return {
    useNavigate: () => navigate,
    Link: ({ children, to }: any) => <a href={to}>{children}</a>,
    useLoaderData: vi.fn(() => ({
      entity: {
        id: "test-entity",
        name: "Acme Trading LTD",
      },
      staticDataCurrencies: [],
      staticDataCountries: [
        {
          code: "GB",
          name: "United Kingdom",
          codeIso3: "GBR",
          codeIsoNumeric: 826,
          dialCode: "+44",
          active: true,
          displayGroup: 0,
          order: 3,
        },
      ],
      staticDataEntityTypes: [
        { key: "LimitedCompany", display: "Limited Company" },
        { key: "PublicLimitedCompany", display: "Public Limited Company" },
        { key: "SoleTrader", display: "Sole Trader" },
        { key: "Charity", display: "Charity" },
        { key: "LimitedPartnership", display: "Limited Partnership" },
        {
          key: "LimitedLiabilityPartnership",
          display: "Limited Liability Partnership",
        },
        { key: "Trust", display: "Trust" },
        { key: "Partnership", display: "Partnership" },
        { key: "UnknownLegalEntity", display: "Unknown Legal Entity" },
        { key: "OtherLegalEntity", display: "Other Legal Entity" },
        { key: "Individual", display: "Individual" },
      ],
      staticDataPaymentActivityTypes: [],
      staticDataPurposeOfEntities: [
        {
          key: "SpvHoldingCompanyCoInvest",
          display: "SPV Holding Company Co-Invest",
        },
        {
          key: "TradingCompany",
          display: "Trading Company",
        },
        {
          key: "Charity",
          display: "Charity",
        },
        {
          key: "Fund",
          display: "Fund",
        },
        {
          key: "OperatingCompany",
          display: "Operating Company",
        },
        {
          key: "PortfolioCompany",
          display: "Portfolio Company",
        },
        {
          key: "Other",
          display: "Other",
        },
      ],
    })),
  }
})

vi.mock("@/data/onboarding/entity-details.mutation", () => ({
  useSaveEntityDetailsFormMutation: () => ({
    mutate: vi.fn((_data, { onSuccess }) => onSuccess?.()),
    isPending: false,
    isError: false,
    error: null,
    reset: vi.fn(),
  }),
  useUpdateAddressMutation: () => ({
    mutate: vi.fn((_data, { onSuccess }) => onSuccess?.()),
    isPending: false,
    isError: false,
    error: null,
    reset: vi.fn(),
  }),
}))

vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(),
}))
vi.mocked(useRolePermissions).mockReturnValue({
  getPermission: () => true,
})

const mockEntity = {
  id: "test-entity-id",
  entityType: "LimitedCompany",
  legalEntity: {
    name: "Acme Trading LTD",
    purposeOfEntity: "TradingCompany",
    registrationNumber: "some register",
    dateOfIncorporation: "2020-01-15",
  },
} satisfies GetEntityResponseDto

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

const setup = (props?: Partial<EntityDetailsFormProps>) => {
  ;(useGetEntitySuspenseQry as Mock).mockReturnValue({
    data: mockEntity,
    isLoading: false,
    isError: false,
  })

  const user = userEvent.setup()
  const form = renderWithQueryClient(<EntityDetailsForm {...props} />)

  // title
  const formHeader = screen.getByText("1. Entity details")

  window.HTMLElement.prototype.scrollIntoView = vi.fn()
  window.HTMLElement.prototype.hasPointerCapture = vi.fn()

  const [
    entityNameInput,
    tradingNamesField,
    entityTypeSelect,
    registeredAddressDialogBtn,
    purposeOfEntitySelect,
    dateOfIncorporationField,
    nextBtn,
  ] = [
    screen.getByRole("textbox", { name: /entity name/i }),
    screen.getByRole("group", { name: /trading name/i }),
    screen.getByLabelText("Entity type"),
    screen.getByRole("button", { name: /Registered address/i }),
    screen.getByLabelText("Purpose of entity"),
    screen.getByRole("button", { name: /Date of incorporation/i }),
    screen.getByRole("button", { name: /Save & next/i }),
  ]
  return {
    formHeader,
    entityNameInput,
    tradingNamesField,
    entityTypeSelect,
    registeredAddressDialogBtn,
    purposeOfEntitySelect,
    dateOfIncorporationField,
    nextBtn,
    ...form,
    user,
  }
}

describe("EntityDetailsForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useGlobalStore as any).mockImplementation(
      (
        selector: (state: {
          setOnboardingHasUnsavedChanges: any
          setOnboardingSeenState: any
        }) => unknown,
      ) =>
        selector({
          setOnboardingHasUnsavedChanges: vi.fn(),
          setOnboardingSeenState: vi.fn(),
        }),
    )
  })

  it("Should show the form", async () => {
    const {
      formHeader,
      entityNameInput,
      tradingNamesField,
      entityTypeSelect,
      purposeOfEntitySelect,
      dateOfIncorporationField,
      user,
    } = setup()

    expect(formHeader).toBeInTheDocument()
    expect(entityNameInput).toBeInTheDocument()
    expect(tradingNamesField).toBeInTheDocument()
    expect(purposeOfEntitySelect).toBeInTheDocument()
    expect(dateOfIncorporationField).toBeInTheDocument()

    /** entity type selector */
    expect(entityTypeSelect).toBeInTheDocument()

    await user.click(entityTypeSelect)

    expect(entityTypeSelect).toHaveAttribute("aria-expanded", "true")

    const [
      limitedCompany,
      publicLimitedCompany,
      soleTrader,
      charity,
      limitedPartnership,
      limitedLiabilityPartnership,
      trust,
      partnership,
      unknownLegalEntity,
      otherLegalEntity,
      individual,
    ] = [
      screen.getByRole("option", { name: /^Limited Company$/i }),
      screen.getByRole("option", { name: /Public Limited Company/i }),
      screen.getByRole("option", { name: /Sole Trader/i }),
      screen.getByRole("option", { name: /Charity/i }),
      screen.getByRole("option", { name: /Limited Partnership/i }),
      screen.getByRole("option", { name: /Limited Liability Partnership/i }),
      screen.getByRole("option", { name: /Trust/i }),
      screen.getByRole("option", { name: /^Partnership$/i }),
      screen.getByRole("option", { name: /Unknown Legal Entity/i }),
      screen.getByRole("option", { name: /Other Legal Entity/i }),
      screen.getByRole("option", { name: /Individual/i }),
    ]

    // assert the options in the selector
    expect(limitedCompany).toBeInTheDocument()
    expect(publicLimitedCompany).toBeInTheDocument()
    expect(soleTrader).toBeInTheDocument()
    expect(charity).toBeInTheDocument()
    expect(limitedPartnership).toBeInTheDocument()
    expect(limitedLiabilityPartnership).toBeInTheDocument()
    expect(trust).toBeInTheDocument()
    expect(partnership).toBeInTheDocument()
    expect(unknownLegalEntity).toBeInTheDocument()
    expect(otherLegalEntity).toBeInTheDocument()
    expect(individual).toBeInTheDocument()

    await user.click(limitedCompany)

    // selector must be closed and the value should be 'Limited Company'
    expect(entityTypeSelect).toHaveAttribute("aria-expanded", "false")
    expect(
      within(entityTypeSelect).getByText("Limited Company"),
    ).toBeInTheDocument()
  })

  it("Should detect trading name input change", async () => {
    const { tradingNamesField } = setup()

    /** Detect trading input change */
    const tradingFirstInput: HTMLInputElement =
      within(tradingNamesField).getByLabelText("Trading name")

    fireEvent.change(tradingFirstInput, { target: { value: "Acme Ltd" } })

    await waitFor(() => expect(tradingFirstInput.value).toBe("Acme Ltd"))
  })

  it("Should show Date of Incorporation field", async () => {
    const { dateOfIncorporationField } = setup()

    expect(dateOfIncorporationField).toBeInTheDocument()
    expect(dateOfIncorporationField).toHaveTextContent("15 Jan 2020")
  })

  it("Should submit form", async () => {
    const onNext = vi.fn()
    const {
      entityNameInput,
      tradingNamesField,
      entityTypeSelect,
      registeredAddressDialogBtn,
      nextBtn,
      user,
    } = setup({
      onNext,
    })

    expect(entityNameInput).toBeInTheDocument()
    expect(tradingNamesField).toBeInTheDocument()
    expect(entityTypeSelect).toBeInTheDocument()
    expect(registeredAddressDialogBtn).toBeInTheDocument()
    expect(nextBtn).toBeInTheDocument()

    await user.click(entityTypeSelect)

    expect(entityTypeSelect).toHaveAttribute("aria-expanded", "true")

    const limitedCompany = screen.getByRole("option", {
      name: "Limited Company",
    })

    expect(limitedCompany).toBeInTheDocument()

    await user.click(limitedCompany)
    await user.click(nextBtn)

    await user.click(registeredAddressDialogBtn)

    expect(onNext).toHaveBeenCalled()
  })
})
