import { describe, expect, it, Mock, vi, beforeEach } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, waitFor } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useRolePermissions } from "@/hooks/useRolePermissions"
import {
  useGetEntitySuspenseQry,
  useGetAddressesSuspenseQry,
} from "@/data/onboarding/entity-details.query"
import {
  useSaveEntityDetailsFormMutation,
  useUpdateAddressMutation,
} from "@/data/onboarding/entity-details.mutation"
import { useGlobalStore } from "@/data/global/global.store"
import {
  EntityDetailsForm,
  EntityDetailsFormProps,
} from "@/components/pages/onboarding/components/entity-details-form"
import {
  GetAddressListDto,
  GetEntityResponseDto,
} from "@/client/onboarding/types.gen"

// Mock all dependencies
vi.mock("@/data/onboarding/entity-details.query", () => ({
  useGetEntitySuspenseQry: vi.fn(),
  useGetAddressesSuspenseQry: vi.fn(),
}))

vi.mock("@/data/onboarding/entity-details.mutation", () => ({
  useSaveEntityDetailsFormMutation: vi.fn(),
  useUpdateAddressMutation: vi.fn(),
}))

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(),
}))

vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(),
}))

vi.mock("@tanstack/react-router", () => ({
  useParams: () => ({ entityId: "test-entity" }),
  useNavigate: () => vi.fn(),
  Link: ({ children, to }: any) => <a href={to}>{children}</a>,
  useLoaderData: vi.fn(() => ({
    entity: { id: "test-entity" },
    staticDataCountries: [
      { code: "GB", name: "United Kingdom" },
      { code: "US", name: "United States" },
      { code: "DE", name: "Germany" },
    ],
    staticDataEntityTypes: [
      { code: "LimitedCompany", display: "Limited Company" },
      { code: "Partnership", display: "Partnership" },
    ],
    staticDataPurposeOfEntity: [
      { code: "TradingCompany", display: "Trading Company" },
      { code: "HoldingCompany", display: "Holding Company" },
    ],
    staticDataStockExchanges: [
      { code: "LSE", display: "London Stock Exchange" },
      { code: "NYSE", display: "New York Stock Exchange" },
    ],
  })),
}))

// Mock data
const mockEntity: GetEntityResponseDto = {
  id: "test-entity-id",
  entityType: "LimitedCompany",
  legalEntity: {
    name: "Acme Trading LTD",
    tradingNames: ["Acme Corp", "Acme Solutions"],
    purposeOfEntity: "TradingCompany",
    registrationNumber: "12345678",
    dateOfIncorporation: "2020-01-15",
    jurisdictionOfIncorporationCountryCode: "GB",
  },
}

const mockAddresses: GetAddressListDto[] = [
  {
    addressType: "RegisteredAddress",
    id: "registered-address-id",
    countryCode: "GB",
    cityOrTown: "London",
    postalCode: "SW1A 1AA",
    line1: "123 Business Street",
    line2: "Suite 100",
  },
  {
    addressType: "TradingAddress",
    id: "trading-address-id",
    countryCode: "GB",
    cityOrTown: "Manchester",
    postalCode: "M1 1AA",
    line1: "456 Trading Avenue",
    line2: "Floor 2",
  },
]

const mockGlobalStore = {
  setOnboardingHasUnsavedChanges: vi.fn(),
  setOnboardingSeenState: vi.fn(),
}

describe("EntityDetailsForm Integration Tests", () => {
  let queryClient: QueryClient
  let mockSaveEntityMutation: any
  let mockUpdateAddressMutation: any

  beforeEach(() => {
    vi.clearAllMocks()

    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    // Setup mocks
    mockSaveEntityMutation = {
      mutate: vi.fn(),
      isPending: false,
      isError: false,
      error: null,
      reset: vi.fn(),
    }

    mockUpdateAddressMutation = {
      mutate: vi.fn(),
      isPending: false,
      isError: false,
      error: null,
      reset: vi.fn(),
    }

    // Mock implementations
    ;(useGetEntitySuspenseQry as Mock).mockReturnValue({
      data: mockEntity,
      isLoading: false,
      isError: false,
    })
    ;(useGetAddressesSuspenseQry as Mock).mockReturnValue({
      data: mockAddresses,
      isLoading: false,
      isError: false,
    })
    ;(useSaveEntityDetailsFormMutation as Mock).mockReturnValue(
      mockSaveEntityMutation,
    )
    ;(useUpdateAddressMutation as Mock).mockReturnValue(
      mockUpdateAddressMutation,
    )
    ;(useGlobalStore as unknown as Mock).mockImplementation((selector: any) =>
      selector ? selector(mockGlobalStore) : mockGlobalStore,
    )
    ;(useRolePermissions as Mock).mockReturnValue({
      getPermission: () => true,
    })

    // Mock DOM methods
    window.HTMLElement.prototype.scrollIntoView = vi.fn()
    window.HTMLElement.prototype.hasPointerCapture = vi.fn()
    window.HTMLElement.prototype.releasePointerCapture = vi.fn()
  })

  const renderForm = (props?: Partial<EntityDetailsFormProps>) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <EntityDetailsForm {...props} />
      </QueryClientProvider>,
    )
  }

  describe("Form Initialization", () => {
    it("should load and display entity data correctly", async () => {
      renderForm()

      // Check if form loads with correct data
      expect(screen.getByDisplayValue("Acme Trading LTD")).toBeInTheDocument()
      expect(screen.getByDisplayValue("Acme Corp")).toBeInTheDocument()
      expect(screen.getByDisplayValue("Acme Solutions")).toBeInTheDocument()
      expect(screen.getByDisplayValue("12345678")).toBeInTheDocument()

      // Check if date of incorporation is displayed correctly
      const dateButton = screen.getByRole("button", {
        name: /Date of incorporation/i,
      })
      expect(dateButton).toHaveTextContent("15 Jan 2020")
    })

    it("should display form fields correctly", async () => {
      renderForm()

      // Check that key form fields are present
      expect(screen.getByLabelText(/entity name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/entity type/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/purpose of entity/i)).toBeInTheDocument()
      expect(
        screen.getByRole("button", { name: /Date of incorporation/i }),
      ).toBeInTheDocument()
      // Note: Some fields use different labels or are rendered as buttons/selects
      expect(screen.getByText(/Save & next/i)).toBeInTheDocument()
    })
  })

  describe("Form Interactions", () => {
    it("should handle entity name changes", async () => {
      const user = userEvent.setup()
      renderForm()

      const entityNameInput = screen.getByLabelText(/entity name/i)
      await user.clear(entityNameInput)
      await user.type(entityNameInput, "New Company Name")

      expect(entityNameInput).toHaveValue("New Company Name")
    })

    it("should display trading names fields", async () => {
      renderForm()

      // Check that trading name fields are present
      const tradingNameInputs = screen.getAllByLabelText(/trading name/i)
      expect(tradingNameInputs.length).toBeGreaterThanOrEqual(2)

      // Check that at least some trading name inputs exist and are rendered
      expect(tradingNameInputs[0]).toBeInTheDocument()
      expect(tradingNameInputs[1]).toBeInTheDocument()
    })

    it("should handle date of incorporation selection", async () => {
      const user = userEvent.setup()
      renderForm()

      const dateButton = screen.getByRole("button", {
        name: /Date of incorporation/i,
      })
      await user.click(dateButton)

      // Check if date picker opens
      const datePicker = screen.getByRole("dialog")
      expect(datePicker).toBeInTheDocument()
    })
  })

  describe("Form Submission", () => {
    it("should display submit button", async () => {
      renderForm()

      // Check if submit button is present
      const submitButton = screen.getByRole("button", { name: /save & next/i })
      expect(submitButton).toBeInTheDocument()
      expect(submitButton).not.toBeDisabled()
    })

    it("should handle form validation errors", async () => {
      const user = userEvent.setup()
      renderForm()

      // Clear required field
      const entityNameInput = screen.getByLabelText(/entity name/i)
      await user.clear(entityNameInput)

      // Try to submit
      const submitButton = screen.getByRole("button", { name: /save & next/i })
      await user.click(submitButton)

      // Check for validation error
      await waitFor(() => {
        expect(screen.getByText(/entity name is required/i)).toBeInTheDocument()
      })
    })
  })

  describe("Date of Incorporation Field", () => {
    it("should display date of incorporation field", async () => {
      renderForm()

      const dateButton = screen.getByRole("button", {
        name: /Date of incorporation/i,
      })
      expect(dateButton).toBeInTheDocument()
      expect(dateButton).toHaveTextContent("15 Jan 2020")
    })

    it("should open date picker when clicked", async () => {
      const user = userEvent.setup()
      renderForm()

      const dateButton = screen.getByRole("button", {
        name: /Date of incorporation/i,
      })
      await user.click(dateButton)

      // Check if date picker opens
      const datePicker = screen.getByRole("dialog")
      expect(datePicker).toBeInTheDocument()
    })
  })

  describe("Permission Handling", () => {
    it("should disable date field when user lacks permissions", async () => {
      ;(useRolePermissions as Mock).mockReturnValue({
        getPermission: () => false,
      })

      renderForm()

      // Check if date button is disabled
      const dateButton = screen.getByRole("button", {
        name: /Date of incorporation/i,
      })
      expect(dateButton).toBeDisabled()
    })
  })
})
