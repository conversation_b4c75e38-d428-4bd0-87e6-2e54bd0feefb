import { required } from "@/lib/form.utils"
import { SingleSelector } from "@/components/base/form/searchable-selector"
import { FormField } from "@/components/base/form/form"
import { EnumValueDto } from "@/client/onboarding/types.gen"

import { type EntityDetailsFieldProps } from "../interface"

interface FieldEntityTypeProps extends EntityDetailsFieldProps {
  options: EnumValueDto[]
  permission: boolean
}

export function FieldEntityType({
  form,
  options,
  permission,
}: FieldEntityTypeProps) {
  return (
    <form.Field
      name="entityType"
      validators={{
        onChange: ({ value }) =>
          required(value?.display, "Entity type is required"),
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Entity type"
            required
          >
            <SingleSelector
              disabled={!permission}
              items={options.map((s) => ({
                value: s.key || "",
                label: s.display || "",
              }))}
              onSelectedItemsChange={(selectedItem) =>
                field.handleChange(
                  options.find((op) => op.key === selectedItem.value),
                )
              }
              selectedItem={{
                value: field.state.value?.key || "",
                label: field.state.value?.display || "",
              }}
              Trigger={{
                id: field.name,
                placeholder: "Select Entity type",
              }}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
