import { useStore } from "@tanstack/react-store"

import { required } from "@/lib/form.utils"
import { FormField } from "@/components/base/form/form"

import { type EntityDetailsFieldProps } from "../interface"
import CountryInput, { useCountryInput } from "../../common/CountryInput"

type FieldJurisdictionOfIncorporationProps = EntityDetailsFieldProps

export function FieldJurisdictionOfIncorporation({
  form,
  permission,
}: FieldJurisdictionOfIncorporationProps) {
  const field = useStore(
    form.store,
    (state) => state.values.jurisdictionOfIncorporation,
  )
  const { countries } = useCountryInput(field?.name ?? "")

  return (
    <form.Field
      name="jurisdictionOfIncorporation"
      validators={{
        onChange: ({ value }) =>
          required(
            value?.code,
            "Jurisdiction of incorporation cannot be empty",
          ),
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Jurisdication of incorporation"
            required
          >
            <CountryInput
              disabled={!permission}
              onChange={(value) =>
                field.handleChange(
                  countries.find((country) => country.name === value),
                )
              }
              value={field.state.value?.name || ""}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
