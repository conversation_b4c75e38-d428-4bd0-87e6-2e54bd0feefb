import { required } from "@/lib/form.utils"
import { Input } from "@/components/ui/input"
import { FormField } from "@/components/base/form/form"

import { type EntityDetailsFieldProps } from "../interface"

export function FieldRegistrationNumber({
  form,
  permission,
}: EntityDetailsFieldProps) {
  return (
    <form.Field
      name="registrationNumber"
      validators={{
        onChange: ({ value }) =>
          required(value, "Registration number cannot be empty"),
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Registration number"
            required
          >
            <Input
              className="bg-background p-4 py-6 md:text-base"
              disabled={!permission}
              id={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              type="text"
              value={field.state.value}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
