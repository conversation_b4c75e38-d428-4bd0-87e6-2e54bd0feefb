import { FieldTradingNames as TradingNames } from "./FieldTradingNames"
import { FieldTradingAddress as TradingAddress } from "./FieldTradingAddress"
import { FieldStockExchange as StockExchange } from "./FieldStockExchange"
import { FieldRegistrationNumber as RegistrationNumber } from "./FieldRegistrationNumber"
import { FieldPurposeOfEntity as PurposeOfEntity } from "./FieldPurposeOfEntity"
import { FieldJurisdictionOfIncorporation as JurisdictionOfIncorporation } from "./FieldJurisdictionOfIncorporation"
import { FieldEntityType as EntityType } from "./FieldEntityType"
import { FieldEntityName as EntityName } from "./FieldEntityName"
import { FieldDateOfIncorporation as DateOfIncorporation } from "./FieldDateOfIncorporation"
import { FieldAddress as Address } from "./FieldAddress"

export const Fields = {
  EntityName,
  TradingNames,
  EntityType,
  Address,
  TradingAddress,
  StockExchange,
  PurposeOfEntity,
  DateOfIncorporation,
  JurisdictionOfIncorporation,
  RegistrationNumber,
}
