import { required } from "@/lib/form.utils"
import { SingleSelector } from "@/components/base/form/searchable-selector"
import { FormField } from "@/components/base/form/form"
import { EnumValueDto } from "@/client/onboarding/types.gen"

import { type EntityDetailsFieldProps } from "../interface"

interface FieldPurposeOfEntityProps extends EntityDetailsFieldProps {
  options: EnumValueDto[]
}

export function FieldPurposeOfEntity({
  form,
  options,
  permission,
}: FieldPurposeOfEntityProps) {
  return (
    <form.Field
      name="purposeOfEntity"
      validators={{
        onChange: ({ value }) => {
          return required(value?.display, "Purpose of entity by is required")
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Purpose of entity"
            required
          >
            <SingleSelector
              disabled={!permission}
              items={options.map((s) => ({
                value: s.key || "",
                label: s.display || "",
              }))}
              onSelectedItemsChange={(selectedItem) =>
                field.handleChange(
                  options.find((op) => op.key === selectedItem.value),
                )
              }
              selectedItem={{
                value: field.state.value?.key || "",
                label: field.state.value?.display || "",
              }}
              Trigger={{
                id: field.name,
                placeholder: "Select Purpose of entity",
              }}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
