import { required } from "@/lib/form.utils"
import { Input } from "@/components/ui/input"
import { FormField } from "@/components/base/form/form"

import { type EntityDetailsFieldProps } from "../interface"

export function FieldEntityName({ form, permission }: EntityDetailsFieldProps) {
  return (
    <form.Field
      name="entityName"
      validators={{
        onChange: ({ value }) => required(value, "Entity name is required"),
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Entity name"
            required
          >
            <Input
              className="bg-background p-4 py-6 md:text-base"
              disabled={!permission}
              id={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="e.g. John Limited Ltd"
              type="text"
              value={field.state.value}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
