import { useEffect } from "react"
import { useStore } from "@tanstack/react-store"

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { FormFieldError } from "@/components/base/form/form"
import {
  EntityType,
  EnumValueDto,
  StockExchange,
} from "@/client/onboarding/types.gen"

import { type EntityDetailsFieldProps } from "../interface"

interface FieldSubscribeProps extends EntityDetailsFieldProps {
  subscribe: EntityType
  options: EnumValueDto[]
}

function FieldSubscribe({
  form,
  subscribe,
  options,
  permission,
}: FieldSubscribeProps) {
  const errors = useStore(
    form?.store,
    (state) => (state.fieldMeta.stockExchange?.errors ?? []) as string[],
  )

  const filteredOptions = options.filter((o) => o.key !== "NotPubliclyListed")
  const isPublicLimitedCompany = subscribe === "PublicLimitedCompany"

  useEffect(() => {
    if (!isPublicLimitedCompany) {
      form.setFieldValue("stockExchange", { key: undefined, display: "" })
      form.setFieldMeta("stockExchange", {
        ...form.getFieldMeta("stockExchange")!,
        isDirty: false,
      })
    }
  }, [form, isPublicLimitedCompany, subscribe])

  return (
    <form.Field
      name="stockExchange"
      validators={{
        onChangeListenTo: ["entityType"],
        onSubmit: ({ value, fieldApi }) => {
          const isPub =
            (fieldApi.form.getFieldValue("entityType")?.key as EntityType) ===
            "PublicLimitedCompany"

          if (isPub && value?.display === "") {
            return "Stock exchange is required"
          }

          return undefined
        },
      }}
    >
      {(field) => (
        <>
          {isPublicLimitedCompany && (
            <div className="relative -mt-4 flex items-center gap-x-2">
              <svg
                className="absolute top-3 ml-2.5"
                fill="none"
                height="16"
                viewBox="0 0 16 16"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 0V11C1 13.2091 2.79086 15 5 15H16"
                  stroke="#000D4D"
                  strokeOpacity="0.5"
                />
              </svg>

              <div className="ml-11 flex flex-1 flex-col gap-y-3">
                <Select
                  disabled={!permission}
                  onValueChange={(stockExchange) =>
                    field.handleChange(
                      options.find((op) => op.display === stockExchange),
                    )
                  }
                  value={field.state.value?.key as StockExchange}
                >
                  <SelectTrigger
                    aria-label={field.name}
                    className="flex bg-background py-6 pr-4 [&>span]:pr-3"
                    id={field.name}
                  >
                    <SelectValue placeholder="Select stock exchange">
                      <div className="flex items-center gap-2">
                        {field.state.value?.display}
                      </div>
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {filteredOptions.map((stockExchange) => (
                        <SelectItem
                          key={stockExchange.key}
                          value={stockExchange.display!}
                        >
                          <div className="flex items-center gap-2">
                            {stockExchange.display}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>

                <FormFieldError
                  aria-label={`${field.name} error`}
                  errors={errors}
                />
              </div>
            </div>
          )}
        </>
      )}
    </form.Field>
  )
}

interface FieldStockExchangeProps extends EntityDetailsFieldProps {
  options: EnumValueDto[]
}

export function FieldStockExchange({
  form,
  options,
  permission,
}: FieldStockExchangeProps) {
  return (
    <form.Subscribe selector={(state) => state.values.entityType?.key}>
      {(entityTypeField) => {
        return (
          <FieldSubscribe
            form={form}
            options={options}
            permission={permission}
            subscribe={entityTypeField as EntityType}
          />
        )
      }}
    </form.Subscribe>
  )
}
