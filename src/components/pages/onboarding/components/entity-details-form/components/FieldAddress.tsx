import { useStore } from "@tanstack/react-store"

import { cn } from "@/lib/utils"
import { required } from "@/lib/form.utils"
import { IOnboardingAddress } from "@/data/onboarding/onboarding.interface"
import { Input } from "@/components/ui/input"
import { FormField, FormLayout } from "@/components/base/form/form"

import { type EntityDetailsFieldProps } from "../interface"
import CountryInput from "../../common/CountryInput"
import { AddressModal } from "../../common/AddressModal"

export function formatAddress(address: IOnboardingAddress): string {
  return [
    `${address.buildingNumber} ${address.street}`,
    address.city,
    address.state,
    address.postalCode,
    address.country,
  ]
    .filter(Boolean)
    .join(", ")
    .trim()
}

interface FieldAddressProps extends EntityDetailsFieldProps {
  onUpdateAddress: (handlers?: Record<string, () => void>) => Promise<void>
  permission: boolean
}

export function FieldAddress({
  form,
  onUpdateAddress,
  permission,
}: FieldAddressProps) {
  const address = useStore(form.store, (state) => state.values.address)

  const addressIsEmpty =
    !address.country ||
    !address.buildingNumber ||
    !address.city ||
    !address.buildingNumber

  const resetMeta = () => {
    form.setFieldMeta("address.country", {
      ...form.getFieldMeta("address.country")!,
      isDirty: false,
    })

    form.setFieldMeta("address.buildingNumber", {
      ...form.getFieldMeta("address.buildingNumber")!,
      isDirty: false,
    })

    form.setFieldMeta("address.street", {
      ...form.getFieldMeta("address.street")!,
      isDirty: false,
    })

    form.setFieldMeta("address.city", {
      ...form.getFieldMeta("address.city")!,
      isDirty: false,
    })

    form.setFieldMeta("address.postalCode", {
      ...form.getFieldMeta("address.postalCode")!,
      isDirty: false,
    })
  }

  const handleSave = () => {
    onUpdateAddress({
      onSuccess: resetMeta,
    })
  }

  return (
    <form.Field
      name="address"
      validators={{
        onChangeListenTo: [
          "address.country",
          "address.buildingNumber",
          "address.street",
          "address.city",
          "address.postalCode",
        ],
        onChange: ({ value }) => {
          if (
            !value.buildingNumber ||
            !value.street ||
            !value.city ||
            !value.postalCode ||
            !value.country
          )
            return "Field address not valid"

          return undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col">
          <FormField
            aria-label={field.name}
            field={field}
            label="Registered address"
            required
          >
            <div
              className={cn("mt-3 flex", {
                "items-center justify-center rounded-xl bg-slate-300/15 p-3 py-2":
                  !addressIsEmpty,
              })}
            >
              {!addressIsEmpty && (
                <p className="flex flex-1 items-center text-base font-light">
                  {formatAddress(field.state.value)}
                </p>
              )}

              <AddressModal
                DialogTriggerProps={{
                  className: cn({
                    "pl-0 pt-0": !addressIsEmpty,
                    "pr-1": addressIsEmpty,
                  }),
                  "aria-label": "Registered address",
                }}
                disabled={!permission}
                form={form}
                isValid={
                  address.country !== "" &&
                  address.buildingNumber !== "" &&
                  address.city !== "" &&
                  address.postalCode !== ""
                }
                onCancel={resetMeta}
                onSave={handleSave}
                title={addressIsEmpty ? "Registered address" : ""}
              >
                <FormLayout
                  className="w-full [&>:first-child]:mb-4"
                  title="Registered address"
                >
                  <form.Field
                    name="address.country"
                    validators={{
                      onMount: ({ value }) =>
                        required(value, "Country is required"),
                      onSubmit: ({ value }) =>
                        required(value, "Country is required"),
                    }}
                  >
                    {(field) => {
                      return (
                        <div className="flex flex-col gap-y-3">
                          <FormField
                            aria-label={field.name}
                            field={field}
                            label="Country"
                            required
                          >
                            <CountryInput
                              onChange={(value) =>
                                value && field.handleChange(value)
                              }
                              value={field.state.value}
                            />
                          </FormField>
                        </div>
                      )
                    }}
                  </form.Field>

                  <form.Field
                    name="address.buildingNumber"
                    validators={{
                      onMount: ({ value }) =>
                        required(
                          value.trim(),
                          "House / Building number is required",
                        ),
                      onChange: ({ value }) =>
                        required(
                          value.trim(),
                          "House / Building number is required",
                        ),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="House / Building number"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="address.street"
                    validators={{
                      onMount: ({ value }) =>
                        required(value.trim(), "Street is required"),
                      onChange: ({ value }) =>
                        required(value.trim(), "Street is required"),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="Street address"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="address.city"
                    validators={{
                      onMount: ({ value }) =>
                        required(value.trim(), "Town / City is required"),
                      onChange: ({ value }) =>
                        required(value.trim(), "Town / City is required"),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="Town / City"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="address.state"
                    validators={{
                      onMount: ({ value }) => required(value.trim(), ""),
                      onChange: ({ value }) => required(value.trim(), ""),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="County"
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="address.postalCode"
                    validators={{
                      onMount: ({ value }) =>
                        required(value.trim(), "Postcode is required"),
                      onChange: ({ value }) =>
                        required(value.trim(), "Postcode is required"),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="Postcode"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>
                </FormLayout>
              </AddressModal>
            </div>
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
