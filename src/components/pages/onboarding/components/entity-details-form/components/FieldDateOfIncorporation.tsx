import { useMemo } from "react"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { useStore } from "@tanstack/react-store"

import { format2Date, parseDate } from "@/lib/date.utils"
import { useToggle } from "@/hooks/use-toggle"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { FormField } from "@/components/base/form/form"
import { DateOfBirthPicker } from "@/components/base/date-of-birth-picker"

import { type EntityDetailsFieldProps } from "../interface"

type FieldDateOfIncorporationProps = EntityDetailsFieldProps

export function FieldDateOfIncorporation({
  form,
  permission,
}: FieldDateOfIncorporationProps) {
  const [isOpen, toggle] = useToggle()
  const date = useStore(form.store, (state) => state.values.dateOfIncorporation)

  const currentDate = useMemo(() => {
    if (!date || date === "") return "Select date of incorporation"

    return format(parseDate(date) || new Date(), "dd MMM yyyy")
  }, [date])

  return (
    <form.Field
      name="dateOfIncorporation"
      validators={{
        onChange: ({ value }) => {
          if (!value?.trim()) {
            return "Date of incorporation is required"
          }

          const parsedDate = parseDate(value)
          if (!parsedDate) {
            return "Please enter a valid date"
          }

          // Check if date is not in the future
          const today = new Date()
          today.setHours(23, 59, 59, 999) // Set to end of today
          if (parsedDate > today) {
            return "Date of incorporation cannot be in the future"
          }

          return undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Date of incorporation"
            required
          >
            <Popover
              onOpenChange={isOpen ? toggle.off : toggle.on}
              open={isOpen}
            >
              <PopoverTrigger asChild>
                <Button
                  className="h-10 w-full rounded-xl p-4 py-6 text-left text-base font-normal"
                  disabled={!permission}
                  id={field.name}
                  variant="outline"
                >
                  <span>{currentDate}</span>
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                align="start"
                className="w-auto rounded-xl p-0 text-base"
              >
                <DateOfBirthPicker
                  defaultMonth={
                    field.state.value ? parseDate(field.state.value) : undefined
                  }
                  disabled={{ after: new Date() }}
                  endMonth={new Date()}
                  mode="single"
                  onSelect={(selectedDate) => {
                    if (selectedDate) {
                      field.handleChange(format2Date(selectedDate))
                      toggle.off()
                    }
                  }}
                  selected={
                    field.state.value ? parseDate(field.state.value) : undefined
                  }
                />
              </PopoverContent>
            </Popover>
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
