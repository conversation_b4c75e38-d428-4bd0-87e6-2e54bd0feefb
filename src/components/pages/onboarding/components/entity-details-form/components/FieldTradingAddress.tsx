import { useStore } from "@tanstack/react-store"

import { cn } from "@/lib/utils"
import { required } from "@/lib/form.utils"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { FormField, FormLayout } from "@/components/base/form/form"

import { formatAddress } from "./FieldAddress"
import { type EntityDetailsFieldProps } from "../interface"
import CountryInput from "../../common/CountryInput"
import { AddressModal } from "../../common/AddressModal"

interface FieldTradingAddressProps extends EntityDetailsFieldProps {
  onUpdateAddress: (handlers?: Record<string, () => void>) => Promise<void>
}

export function FieldTradingAddress({
  form,
  onUpdateAddress,
  permission,
}: FieldTradingAddressProps) {
  const address = useStore(form.store, (state) => state.values.address)
  const sameAsRegisteredAddress = useStore(
    form.store,
    (state) => !!state.values.sameAsRegisteredAddress,
  )
  const tradingAddress = useStore(
    form.store,
    (state) => state.values.tradingAddress,
  )

  const addressIsEmpty =
    !tradingAddress.country ||
    !tradingAddress.buildingNumber ||
    !tradingAddress.city ||
    !tradingAddress.buildingNumber

  const isValid = () => {
    if (sameAsRegisteredAddress) return true

    return (
      tradingAddress.country !== "" &&
      tradingAddress.buildingNumber !== "" &&
      tradingAddress.street !== "" &&
      tradingAddress.city !== "" &&
      tradingAddress.postalCode !== ""
    )
  }

  const resetMeta = () => {
    form.setFieldMeta("tradingAddress.country", {
      ...form.getFieldMeta("tradingAddress.country")!,
      isDirty: false,
    })

    form.setFieldMeta("tradingAddress.buildingNumber", {
      ...form.getFieldMeta("tradingAddress.buildingNumber")!,
      isDirty: false,
    })

    form.setFieldMeta("tradingAddress.street", {
      ...form.getFieldMeta("tradingAddress.street")!,
      isDirty: false,
    })

    form.setFieldMeta("tradingAddress.city", {
      ...form.getFieldMeta("tradingAddress.city")!,
      isDirty: false,
    })

    form.setFieldMeta("tradingAddress.postalCode", {
      ...form.getFieldMeta("tradingAddress.postalCode")!,
      isDirty: false,
    })
  }

  const handleSave = () => {
    if (form.state.values.sameAsRegisteredAddress) {
      form.setFieldValue("tradingAddress", address)
    }

    onUpdateAddress({
      onSuccess: resetMeta,
    })
  }

  return (
    <form.Field
      name="tradingAddress"
      validators={{
        onChange: ({ value, fieldApi }) => {
          const isSameAsRegisteredAddress = !!fieldApi.form.getFieldValue(
            "sameAsRegisteredAddress",
          )

          if (isSameAsRegisteredAddress) return undefined

          if (formatAddress(value) === "")
            return "Trading address not valid here"

          return undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col">
          <FormField
            aria-label={field.name}
            field={field}
            label="Trading address"
            required={!form.state.values.sameAsRegisteredAddress}
          >
            <div
              className={cn("mt-3 flex", {
                "items-center justify-center rounded-xl bg-slate-300/15 p-3 py-2":
                  !addressIsEmpty,
              })}
            >
              {!addressIsEmpty && (
                <p className="flex flex-1 items-center text-base font-light">
                  {sameAsRegisteredAddress
                    ? "Same as registered address"
                    : formatAddress(field.state.value)}
                </p>
              )}

              <AddressModal
                DialogTriggerProps={{
                  className: cn({
                    "pl-0 pt-0": !addressIsEmpty,
                    "pr-1": addressIsEmpty,
                  }),
                  "aria-label": "Trading address",
                }}
                disabled={!permission}
                form={form}
                isValid={isValid()}
                onCancel={resetMeta}
                onSave={handleSave}
                title={addressIsEmpty ? "Trading address" : ""}
              >
                <FormLayout
                  className="w-full space-y-4 [&>:first-child]:mb-4"
                  title="Trading address"
                >
                  <form.Field name="sameAsRegisteredAddress">
                    {(field) => (
                      <div className="mb-6 flex items-center">
                        <Switch
                          checked={field.state.value}
                          className="mr-4"
                          id="tradingAddressSwitch"
                          onCheckedChange={(e) => field.handleChange(e)}
                          required
                        />
                        <Label
                          className="font-normal"
                          htmlFor="tradingAddressSwitch"
                        >
                          Same as registered address
                        </Label>
                      </div>
                    )}
                  </form.Field>

                  <form.Subscribe
                    selector={(state) => state.values.sameAsRegisteredAddress}
                  >
                    {(isSameAsRegisteredAddress) => (
                      <>
                        <form.Field
                          name="tradingAddress.country"
                          validators={{
                            onSubmit: ({ value }) =>
                              required(value, "Country is required"),
                          }}
                        >
                          {(field) => (
                            <>
                              {!isSameAsRegisteredAddress && (
                                <div className="flex flex-col gap-y-3">
                                  <FormField
                                    aria-label={field.name}
                                    field={field}
                                    label="Country"
                                    required
                                  >
                                    <CountryInput
                                      onChange={(value) =>
                                        value && field.handleChange(value)
                                      }
                                      value={field.state.value}
                                    />
                                  </FormField>
                                </div>
                              )}
                            </>
                          )}
                        </form.Field>

                        <form.Field
                          name="tradingAddress.buildingNumber"
                          validators={{
                            onChange: ({ value }) =>
                              required(
                                value.trim(),
                                "House / Building number is required",
                              ),
                          }}
                        >
                          {(field) => (
                            <>
                              {!isSameAsRegisteredAddress && (
                                <div className="flex flex-col gap-y-3">
                                  <FormField
                                    aria-label={field.name}
                                    field={field}
                                    label="House / Building number"
                                    required
                                  >
                                    <Input
                                      className="bg-background p-4 py-6 md:text-base"
                                      id={field.name}
                                      onChange={(e) =>
                                        field.handleChange(e.target.value)
                                      }
                                      type="text"
                                      value={field.state.value}
                                    />
                                  </FormField>
                                </div>
                              )}
                            </>
                          )}
                        </form.Field>

                        <form.Field
                          name="tradingAddress.street"
                          validators={{
                            onChange: ({ value }) =>
                              required(value.trim(), "Street is required"),
                          }}
                        >
                          {(field) => (
                            <>
                              {!isSameAsRegisteredAddress && (
                                <div className="flex flex-col gap-y-3">
                                  <FormField
                                    aria-label={field.name}
                                    field={field}
                                    label="Street address"
                                    required
                                  >
                                    <Input
                                      className="bg-background p-4 py-6 md:text-base"
                                      id={field.name}
                                      onChange={(e) =>
                                        field.handleChange(e.target.value)
                                      }
                                      type="text"
                                      value={field.state.value}
                                    />
                                  </FormField>
                                </div>
                              )}
                            </>
                          )}
                        </form.Field>

                        <form.Field
                          name="tradingAddress.city"
                          validators={{
                            onChange: ({ value }) =>
                              required(value.trim(), "Town / City is required"),
                          }}
                        >
                          {(field) => (
                            <>
                              {!isSameAsRegisteredAddress && (
                                <div className="flex flex-col gap-y-3">
                                  <FormField
                                    aria-label={field.name}
                                    field={field}
                                    label="Town / City"
                                    required
                                  >
                                    <Input
                                      className="bg-background p-4 py-6 md:text-base"
                                      id={field.name}
                                      onChange={(e) =>
                                        field.handleChange(e.target.value)
                                      }
                                      type="text"
                                      value={field.state.value}
                                    />
                                  </FormField>
                                </div>
                              )}
                            </>
                          )}
                        </form.Field>

                        <form.Field name="tradingAddress.state">
                          {(field) => (
                            <>
                              {!isSameAsRegisteredAddress && (
                                <div className="flex flex-col gap-y-3">
                                  <FormField
                                    aria-label={field.name}
                                    field={field}
                                    label="County"
                                  >
                                    <Input
                                      className="bg-background p-4 py-6 md:text-base"
                                      id={field.name}
                                      onChange={(e) =>
                                        field.handleChange(e.target.value)
                                      }
                                      type="text"
                                      value={field.state.value}
                                    />
                                  </FormField>
                                </div>
                              )}
                            </>
                          )}
                        </form.Field>

                        <form.Field
                          name="tradingAddress.postalCode"
                          validators={{
                            onChange: ({ value }) =>
                              required(value.trim(), "Postcode is required"),
                          }}
                        >
                          {(field) => (
                            <>
                              {!isSameAsRegisteredAddress && (
                                <div className="flex flex-col gap-y-3">
                                  <FormField
                                    aria-label={field.name}
                                    field={field}
                                    label="Postcode"
                                    required
                                  >
                                    <Input
                                      className="bg-background p-4 py-6 md:text-base"
                                      id={field.name}
                                      onChange={(e) =>
                                        field.handleChange(e.target.value)
                                      }
                                      type="text"
                                      value={field.state.value}
                                    />
                                  </FormField>
                                </div>
                              )}
                            </>
                          )}
                        </form.Field>
                      </>
                    )}
                  </form.Subscribe>
                </FormLayout>
              </AddressModal>
            </div>
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
