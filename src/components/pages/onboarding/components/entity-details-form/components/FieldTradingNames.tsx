import { X } from "lucide-react"
import { last, isEmpty } from "lodash-es"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

import { type EntityDetailsFieldProps } from "../interface"

export function FieldTradingNames({
  form,
  permission,
}: EntityDetailsFieldProps) {
  return (
    <form.Field mode="array" name="tradingNames">
      {(rootField) => (
        <div className="flex flex-col gap-y-3">
          <fieldset aria-label="Trading name(s)" role="group">
            <legend className="mb-3 mt-2 inline-flex items-center text-sm font-medium leading-none after:ml-0.5 after:text-destructive peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Trading name(s)
              <span className="font-normal text-muted-foreground">
                {" "}
                (optional)
              </span>
            </legend>

            <div className="flex flex-col gap-y-3">
              {rootField.state.value.map((_, i) => (
                <form.Field key={i} name={`tradingNames[${i}]`}>
                  {(subField) => (
                    <div className="relative flex items-center justify-center animate-in fade-in-0 slide-in-from-bottom-10">
                      <Input
                        aria-label="Trading name"
                        className="bg-background p-4 py-6 pr-[4.2rem] md:text-base"
                        disabled={!permission}
                        id={`tradingNames[${i}]`}
                        onChange={(e) => subField.handleChange(e.target.value)}
                        placeholder="e.g Acme Limited LTD"
                        type="text"
                        value={subField.state.value}
                      />

                      <Button
                        className="absolute right-3 top-1/2 z-10 -translate-y-1/2 pr-1 hover:bg-transparent hover:text-primary"
                        disabled={!permission}
                        onClick={() => {
                          if (i > 0) {
                            return rootField.removeValue(i)
                          }

                          subField.setValue("")
                        }}
                        variant="ghost"
                      >
                        <X />
                      </Button>
                    </div>
                  )}
                </form.Field>
              ))}
            </div>
          </fieldset>

          <Button
            className="self-end pr-0"
            disabled={!permission}
            onClick={() => {
              if (isEmpty(last(rootField.state.value))) {
                return
              }
              rootField.pushValue("")
            }}
            variant="link"
          >
            Add trading name
          </Button>
        </div>
      )}
    </form.Field>
  )
}
