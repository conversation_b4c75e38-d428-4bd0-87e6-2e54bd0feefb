import { IOnboardingEntityDetails } from "@/data/onboarding/onboarding.interface"

import { getReviewEntityDetails } from "./EntityDetailsForm.hook"

export function validateEntityDetailsForm(data?: IOnboardingEntityDetails) {
  const address = data?.address

  let isValid = true

  if (!data?.entityName || !data?.entityType) {
    isValid = false
  }

  if (
    !address?.buildingNumber ||
    !address?.city ||
    !address?.country ||
    !address?.street ||
    !address.postalCode
  ) {
    isValid = false
  }

  if (data?.purposeOfEntity?.display?.length == 0) isValid = false
  if (!data?.jurisdictionOfIncorporation?.code) isValid = false
  if (data?.registrationNumber?.length == 0) isValid = false

  return isValid
}

export default function reviewEntityDetails(
  data?: ReturnType<typeof getReviewEntityDetails>,
) {
  const isValid =
    !!data?.entityName &&
    !!data?.entityType &&
    !!data?.registeredAddress?.buildingNumber &&
    !!data?.registeredAddress?.city &&
    !!data?.registeredAddress?.country &&
    !!data?.registeredAddress?.postalCode &&
    !!data?.registeredAddress?.street &&
    data?.purposeOfEntity?.display !== "" &&
    !!data?.jurisdiction?.code &&
    !!data?.registrationNumber

  return isValid
}
