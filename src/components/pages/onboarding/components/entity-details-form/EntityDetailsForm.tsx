import { useRolePermissions } from "@/hooks/useRolePermissions"
import { type IOnboardingEntityDetails } from "@/data/onboarding/onboarding.interface"
import { FormLayout } from "@/components/base/form/form"

import { useEntityDetailsForm } from "./EntityDetailsForm.hook"
import { Fields } from "./components"
import { FormControls } from "../common/FormControls"

export interface EntityDetailsFormProps {
  onNext?: (payload: IOnboardingEntityDetails) => void
  onInvalid?: () => void
}

export function EntityDetailsForm(props: EntityDetailsFormProps) {
  const {
    form,
    entityTypesOptions,
    purposeOfEntityOptions,
    stockExchangeOptions,
    handleUpdateAddress,
  } = useEntityDetailsForm(props)
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")
  return (
    <div
      aria-label="1. Entity details"
      className="container flex max-w-2xl flex-col gap-y-8 px-4"
    >
      <FormLayout
        className="mx-0 flex max-w-xl flex-col gap-y-7"
        title="1. Entity details"
      >
        <p className="text-muted-foreground">
          Please provide the high level details of the entity. If you have a
          country-specific entity type that is not in the list, please choose
          the nearest equivalent.
        </p>
        <Fields.EntityName form={form} permission={permission} />
        <Fields.TradingNames form={form} permission={permission} />
        <Fields.EntityType
          form={form}
          options={entityTypesOptions}
          permission={permission}
        />
        <Fields.StockExchange
          form={form}
          options={stockExchangeOptions}
          permission={permission}
        />
        <Fields.Address
          form={form}
          onUpdateAddress={handleUpdateAddress}
          permission={permission}
        />
        <Fields.TradingAddress
          form={form}
          onUpdateAddress={handleUpdateAddress}
          permission={permission}
        />
        <Fields.PurposeOfEntity
          form={form}
          options={purposeOfEntityOptions}
          permission={permission}
        />
        <Fields.DateOfIncorporation form={form} permission={permission} />
        <Fields.JurisdictionOfIncorporation
          form={form}
          permission={permission}
        />
        <Fields.RegistrationNumber form={form} permission={permission} />

        <FormControls disabled={!permission} form={form} />
      </FormLayout>
    </div>
  )
}
