import { isEmpty, reject } from "lodash-es"

import { IOnboardingEntityDetails } from "@/data/onboarding/onboarding.interface"
import {
  EntityType,
  EnumValueDto,
  GetAddressListDto,
  GetCountryListDto,
  GetEntityResponseDto,
  PurposeOfEntity,
  StockExchange,
  UpdateEntityRequestDto,
} from "@/client/onboarding/types.gen"

import {
  findJurisdiction,
  findStaticData,
  getAddress,
  isNotEmptyObject,
  mapAddress,
} from "../utils"

interface GetFormDefaultValues {
  entity: GetEntityResponseDto
  addresses: GetAddressListDto[]
  staticData: {
    countries: GetCountryListDto[]
    entityTypes: EnumValueDto[]
    purposeOfEntities: EnumValueDto[]
    stockExchange: EnumValueDto[]
  }
}

export function getFormDefaultValues({
  entity,
  addresses,
  staticData: { countries, entityTypes, purposeOfEntities, stockExchange },
}: GetFormDefaultValues): IOnboardingEntityDetails {
  const registeredAddress = getAddress(addresses, "RegisteredAddress")
  const tradingAddress = getAddress(addresses, "TradingAddress")

  const isSameAsRegisteredAddress =
    (isNotEmptyObject(tradingAddress) &&
      registeredAddress.countryCode === tradingAddress.countryCode &&
      registeredAddress.line1 === tradingAddress.line1 &&
      registeredAddress.line2 === tradingAddress.line2 &&
      registeredAddress.stateOrProvince === tradingAddress.stateOrProvince &&
      registeredAddress.cityOrTown === tradingAddress.cityOrTown &&
      registeredAddress.postalCode === tradingAddress.postalCode) ||
    !isNotEmptyObject(tradingAddress)

  return {
    entityName: entity?.legalEntity?.name ?? "",
    tradingNames: entity?.legalEntity?.tradingNames?.length
      ? entity.legalEntity.tradingNames
      : [""],
    entityType: findStaticData(entityTypes, entity?.entityType),
    address: mapAddress(countries, registeredAddress),
    tradingAddress: mapAddress(countries, tradingAddress),
    stockExchange: findStaticData(
      stockExchange,
      entity?.legalEntity?.stockExchange,
    ),
    purposeOfEntity: findStaticData(
      purposeOfEntities,
      entity?.legalEntity?.purposeOfEntity,
    ),
    dateOfIncorporation: entity?.legalEntity?.dateOfIncorporation ?? "",
    jurisdictionOfIncorporation: findJurisdiction(
      countries,
      entity?.legalEntity?.jurisdictionOfIncorporationCountryCode,
    ),
    registrationNumber: entity?.legalEntity?.registrationNumber ?? "",
    sameAsRegisteredAddress: isSameAsRegisteredAddress,
  }
}

interface CreateFormPayload {
  form: IOnboardingEntityDetails
  entity: GetEntityResponseDto
}

export function createFormPayload({
  form,
  entity: entityData,
}: CreateFormPayload) {
  return {
    entity: {
      ...entityData,
      entityType: (form.entityType?.key as EntityType)!,
      legalEntity: {
        ...entityData?.legalEntity,
        name: form.entityName,
        tradingNames: reject(
          reject(form.tradingNames, isEmpty),
          (s) => s.replace(/\s/g, "").length == 0,
        ),
        purposeOfEntity: form.purposeOfEntity?.key as PurposeOfEntity,
        dateOfIncorporation: form.dateOfIncorporation || null,
        jurisdictionOfIncorporationCountryCode:
          form.jurisdictionOfIncorporation?.code,
        registrationNumber: form.registrationNumber,
        stockExchange: (form.stockExchange?.key as StockExchange) || undefined,
        gicsCode: entityData.legalEntity?.industryCode,
      },
      naturalEntity: null,
    } as UpdateEntityRequestDto,
  }
}

export function formIsTouched(
  formValues: IOnboardingEntityDetails,
  entity: GetEntityResponseDto,
): boolean {
  let touched = false

  if (formValues.address?.buildingNumber?.length > 0) touched = true
  if (formValues.address?.city?.length > 0) touched = true
  if (formValues.address?.country?.length > 0) touched = true
  if (formValues.address?.postalCode?.length > 0) touched = true
  if (formValues.address?.state?.length > 0) touched = true
  if (formValues.address?.street?.length > 0) touched = true
  if (formValues.tradingAddress?.buildingNumber?.length > 0) touched = true
  if (formValues.tradingAddress?.city?.length > 0) touched = true
  if (formValues.tradingAddress?.country?.length > 0) touched = true
  if (formValues.tradingAddress?.postalCode?.length > 0) touched = true
  if (formValues.tradingAddress?.state?.length > 0) touched = true
  if (formValues.tradingAddress?.street?.length > 0) touched = true
  if (entity?.legalEntity?.purposeOfEntity) touched = true
  if (entity?.legalEntity?.stockExchange) touched = true
  if (entity?.legalEntity?.registrationNumber) touched = true
  if (entity?.legalEntity?.dateOfIncorporation) touched = true

  return touched
}
interface GetReviewDefaultValues {
  entity: GetEntityResponseDto
  addresses: GetAddressListDto[]
  staticData: {
    staticDataCountries: GetCountryListDto[]
    staticDataEntityTypes: EnumValueDto[]
    staticDataPurposeOfEntities: EnumValueDto[]
    staticDataStockExchange: EnumValueDto[]
  }
}
export function getReviewDefaultValues({
  entity,
  staticData,
  addresses,
}: GetReviewDefaultValues) {
  const {
    staticDataCountries: countries,
    staticDataEntityTypes: entityTypes,
    staticDataPurposeOfEntities: purposeOfEntities,
    staticDataStockExchange: stockExchange,
  } = staticData

  const registeredAddress = getAddress(addresses, "RegisteredAddress")
  const tradingAddress = getAddress(addresses, "TradingAddress")
  return {
    entityType: findStaticData(entityTypes, entity?.entityType),
    address: mapAddress(countries, registeredAddress),
    tradingAddress: mapAddress(countries, tradingAddress),
    stockExchange: findStaticData(
      stockExchange,
      entity?.legalEntity?.stockExchange,
    ),
    purposeOfEntity: findStaticData(
      purposeOfEntities,
      entity?.legalEntity?.purposeOfEntity,
    ),
    dateOfIncorporation: entity?.legalEntity?.dateOfIncorporation ?? "",
    jurisdictionOfIncorporation: findJurisdiction(
      countries,
      entity?.legalEntity?.jurisdictionOfIncorporationCountryCode,
    ),
  }
}
