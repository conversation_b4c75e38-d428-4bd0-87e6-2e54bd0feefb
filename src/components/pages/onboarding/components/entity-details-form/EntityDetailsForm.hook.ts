import { useMemo } from "react"
import { isEmpty } from "lodash-es"
import { useForm } from "@tanstack/react-form"

import { type IOnboardingEntityDetails } from "@/data/onboarding/onboarding.interface"
import {
  useGetAddressesSuspenseQry,
  useGetEntitySuspenseQry,
} from "@/data/onboarding/entity-details.query"
import {
  useSaveEntityDetailsFormMutation,
  useUpdateAddressMutation,
} from "@/data/onboarding/entity-details.mutation"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGlobalStore } from "@/data/global/global.store"
import {
  type GetEntityResponseDto,
  type GetAddressListDto,
  type EnumValueDto,
  type GetCountryListDto,
} from "@/client/onboarding/types.gen"

import {
  getFormDefaultValues,
  getReviewDefaultValues,
  createFormPayload,
  formIsTouched,
} from "./EntityDetailsForm.form"
import { EntityDetailsFormProps } from "./EntityDetailsForm"
import { formatAddress, getAddress, mapCreateAddressPayload } from "../utils"
import useFormBlockWithUnsavedChanges from "../hooks/useWarnWithUnsavedChanges"

export function useEntityDetailsForm(props: EntityDetailsFormProps) {
  const prefetchData = useLoaderData()
  const { data: addresses } = useGetAddressesSuspenseQry()
  const { data: entity } = useGetEntitySuspenseQry()
  const { mutate: saveEntityDetailsMutate } = useSaveEntityDetailsFormMutation()
  const { mutate: updateAddressMutate } = useUpdateAddressMutation()
  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)

  const defaultValues = useMemo(
    () =>
      getFormDefaultValues({
        entity,
        addresses,
        staticData: {
          countries: prefetchData.staticDataCountries,
          entityTypes: prefetchData.staticDataEntityTypes,
          purposeOfEntities: prefetchData.staticDataPurposeOfEntities,
          stockExchange: prefetchData.staticDataStockExchange,
        },
      }),
    [
      entity,
      addresses,
      prefetchData.staticDataCountries,
      prefetchData.staticDataEntityTypes,
      prefetchData.staticDataPurposeOfEntities,
      prefetchData.staticDataStockExchange,
    ],
  )
  const isTouched = formIsTouched(defaultValues, entity)

  const form = useForm<IOnboardingEntityDetails>({
    defaultValues,
    onSubmit: ({ value }) =>
      handleUpdateEntity({
        onSuccess: () => {
          props.onNext?.(value)
          form.reset(defaultValues, { keepDefaultValues: true })
        },
      }),
    validators: {
      onMount: ({ formApi }) => {
        setFormSeen({
          ...formSeenState,
          entityDetailsForm: true,
        })

        if (isTouched) {
          // TODO: Come back after https://github.com/TanStack/form/issues/1487
          // formApi.reset()
          formApi.validateAllFields("submit")
        }

        return null
      },
    },
  })

  useFormBlockWithUnsavedChanges(form)

  async function handleUpdateEntity(handlers?: Record<string, () => void>) {
    const [errors] = await form.validateAllFields("submit")

    if (errors) return

    const payload = createFormPayload({
      form: form.state.values,
      entity,
    })

    saveEntityDetailsMutate(payload, handlers)
  }

  async function handleUpdateAddress(handlers?: Record<string, () => void>) {
    const [errors] = await Promise.all([
      form.validateField("address.country", "submit"),
      form.validateField("address.buildingNumber", "submit"),
      form.validateField("address.street", "submit"),
      form.validateField("address.city", "submit"),
      form.validateField("address.postalCode", "submit"),
    ])

    if (!isEmpty(errors))
      throw new Error("[Entity Details] Unable to update address")

    const filterAddress = getAddress(addresses, "RegisteredAddress")
    const filterTradingAddress = getAddress(addresses, "TradingAddress")

    const address = mapCreateAddressPayload({
      id: filterAddress?.id,
      addr: form.state.values.address,
      type: "RegisteredAddress",
      countries: prefetchData.staticDataCountries,
    })

    updateAddressMutate(
      {
        address,
        tradingAddress: mapCreateAddressPayload({
          id: filterTradingAddress?.id,
          addr: form.state.values.tradingAddress!,
          type: "TradingAddress",
          countries: prefetchData.staticDataCountries,
          override: form.state.values.sameAsRegisteredAddress
            ? {
                cityOrTown: address.cityOrTown,
                countryCode: address.countryCode,
                line1: address.line1,
                line2: address.line2,
                postalCode: address.postalCode,
                stateOrProvince: address.stateOrProvince,
              }
            : {},
        }),
      },
      handlers,
    )
  }

  return {
    form,
    entityName: entity?.legalEntity?.name,
    entityTypesOptions: prefetchData.staticDataEntityTypes ?? [],
    purposeOfEntityOptions: prefetchData.staticDataPurposeOfEntities ?? [],
    stockExchangeOptions: prefetchData.staticDataStockExchange ?? [],
    handleUpdateEntity,
    handleUpdateAddress,
  }
}

interface GetReviewEntityDetails {
  staticData: {
    staticDataCountries: GetCountryListDto[]
    staticDataEntityTypes: EnumValueDto[]
    staticDataPurposeOfEntities: EnumValueDto[]
    staticDataStockExchange: EnumValueDto[]
  }
  entity: GetEntityResponseDto | undefined
  addresses: GetAddressListDto[] | undefined
}
export function getReviewEntityDetails({
  staticData,
  entity,
  addresses,
}: GetReviewEntityDetails) {
  if (entity == undefined || addresses == undefined)
    return {
      registeredAddress: {
        buildingNumber: "",
        city: "",
        country: "",
        postalCode: "",
        street: "",
      },
      entityName: "",
      tradingNames: "",
      entityType: { display: "" },
      stockExchange: { display: "" },
      address: "",
      tradingAddress: "",
      purposeOfEntity: { display: "" },
      dateOfIncorporation: "",
      jurisdiction: { code: "", name: "" },
      registrationNumber: "",
    }
  const reviewDefaultValues = getReviewDefaultValues({
    staticData,
    entity,
    addresses,
  })

  return {
    registeredAddress: reviewDefaultValues.address, //to check details of address
    entityName: entity.legalEntity?.name ?? "",
    tradingNames: entity.legalEntity?.tradingNames?.join(", ") ?? "",
    entityType: reviewDefaultValues.entityType,
    stockExchange: reviewDefaultValues?.stockExchange,
    address: formatAddress(reviewDefaultValues.address),
    tradingAddress: formatAddress(reviewDefaultValues.tradingAddress),
    purposeOfEntity: reviewDefaultValues.purposeOfEntity,
    dateOfIncorporation: entity.legalEntity?.dateOfIncorporation ?? "",
    jurisdiction: reviewDefaultValues.jurisdictionOfIncorporation,
    registrationNumber: entity.legalEntity?.registrationNumber ?? "",
  }
}
