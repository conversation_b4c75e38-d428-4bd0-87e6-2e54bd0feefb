import { useState } from "react"

import { useGlobalStore, SeenState } from "@/data/global/global.store"

import { useValidateSteps } from "./hooks/useValidateSteps"
import { useUnmount } from "./hooks/useUnmount"
import { FormStepId } from "./FormSteps"

const MIN_STEP = 0
const MAX_STEP = 7

// Helper function to determine step status
const useStepStatus = (
  activeStep: number,
  isStepValid: (step: FormStepId) => boolean,
) => {
  const formSeen = useGlobalStore(
    (state) => (state.onboardingSeenState as SeenState) ?? {},
  )
  const seenIndex = Math.max(
    ...Object.keys(formSeen).map((st: string) =>
      formSeen[st as keyof SeenState]
        ? FormStepId[st as keyof typeof FormStepId]
        : 0,
    ),
  )

  return function getStatus(
    stepIndex: FormStepId,
  ): "current" | "completed" | "incomplete" | "upcoming" | undefined {
    const isValid = isStepValid(stepIndex)

    if (stepIndex === activeStep) {
      return "current"
    }

    if (stepIndex <= seenIndex) {
      return isValid ? "completed" : "incomplete"
    }

    return "upcoming"
  }
}

export function useOnboardingStepper() {
  const { isStepValid } = useValidateSteps()
  const [activeStep, setActiveStep] = useState(0)
  const hasUnsavedChanges = useGlobalStore(
    (state) => state.onboardingHasUnsavedChanges,
  )
  const setHasUnsavedChanges = useGlobalStore(
    (state) => state.setOnboardingHasUnsavedChanges,
  )
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)

  const handleNext = () => {
    const nextStepIndex = Math.min(activeStep + 1, MAX_STEP)
    setActiveStep(nextStepIndex)
  }

  const handleBack = (step?: number) => {
    if (activeStep === 0) return

    const prevStepIndex = Math.max(step ?? activeStep - 1, MIN_STEP)
    setActiveStep(prevStepIndex)
  }

  const getStatus = useStepStatus(activeStep, isStepValid)

  useUnmount(() => {
    setFormSeen({
      entityDetailsForm: false,
      furtherDetailsForm: false,
      ownershipAndControlForm: false,
      financialInformationForm: false,
      transactionActivityForm: false,
      usersAndSignatoriesForm: false,
      applicantDetailsForm: false,
      reviewForm: false,
    })
  })

  const handleStepChange = (step: number) => {
    if (hasUnsavedChanges) {
      const skip = confirm("You have unsaved changes. Discard changes!")

      if (!skip) return
      setHasUnsavedChanges(false) // if click okey, set false
    }

    setActiveStep(step)
  }

  return {
    activeStep,
    handleNext,
    handleBack,
    getStatus,
    handleStepChange,
  }
}
