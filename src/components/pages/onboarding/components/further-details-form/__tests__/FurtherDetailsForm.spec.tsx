import { describe, expect, it, Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen, within } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useRolePermissions } from "@/hooks/useRolePermissions"
import { useGetProofOfExistenceDocumentsSuspenseQry } from "@/data/onboarding/onboarding.query"
import { useGetEntitySuspenseQry } from "@/data/onboarding/entity-details.query"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGlobalStore } from "@/data/global/global.store"
import { TooltipProvider } from "@/components/ui/tooltip"
import {
  FurtherDetailsForm,
  FurtherDetailsFormProps,
} from "@/components/pages/onboarding/components/further-details-form"
import {
  EnumValueDto,
  GetEntityDocumentsDto,
} from "@/client/onboarding/types.gen"

const mockGlobalStore = {
  entity: { id: "test-entity", name: "Argentex" },
}

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(() => mockGlobalStore),
}))

vi.mock("@/data/onboarding/entity-details.query", () => ({
  useGetEntitySuspenseQry: vi.fn(),
}))

vi.mock("@/data/onboarding/static-data.query", () => ({
  useGetEntityRegulationQry: vi.fn(),
}))

vi.mock("@/data/onboarding/further-details.query", () => ({
  useGetInvestmentsSuspenseQry: vi.fn(),
}))

vi.mock(
  import("@/data/onboarding/onboarding.query"),
  async (importOriginal) => {
    const mod = await importOriginal() // type is inferred

    return {
      ...mod,
      // replace some exports
      useGetProofOfExistenceDocumentsSuspenseQry: vi.fn(),
    }
  },
)

vi.mock("@/data/onboarding/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "1",
    },
  })),
}))

vi.mock("@tanstack/react-router", () => {
  const navigate = vi.fn()

  return {
    useNavigate: () => navigate,
    Link: ({ children, to }: any) => <a href={to}>{children}</a>,
  }
})

vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(),
}))
vi.mocked(useRolePermissions).mockReturnValue({
  getPermission: () => true,
})

// Mock hasPointerCapture and scrollIntoView
window.HTMLElement.prototype.hasPointerCapture = () => false
window.HTMLElement.prototype.scrollIntoView = vi.fn()

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const mockEntity = {
  id: "test-entity-id",
  name: "Test Entity",
}

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

function setup(props?: Partial<FurtherDetailsFormProps>) {
  ;(useGetEntitySuspenseQry as Mock).mockReturnValue({
    data: mockEntity,
    isLoading: false,
    isError: false,
  })

  renderWithQueryClient(
    <TooltipProvider>
      <FurtherDetailsForm {...props} />
    </TooltipProvider>,
  )

  return {
    entityRegulatedRadioYes: screen.getByRole("radio", {
      name: /Yes/i,
    }),
    entityRegulatedRadioNo: screen.getByRole("radio", {
      name: /No/i,
    }),
    regulationsSelectedItems: screen.getByLabelText("Selected items"),
    regulatedBy: screen.getByLabelText("Regulations"),
    websiteInput: screen.getByRole("textbox", { name: /website/i }),
  }
}

describe("FurtherDetailsForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useGlobalStore as any).mockImplementation(
      (
        selector: (state: {
          setOnboardingHasUnsavedChanges: any
          setOnboardingSeenState: any
        }) => unknown,
      ) =>
        selector({
          setOnboardingHasUnsavedChanges: vi.fn(),
          setOnboardingSeenState: vi.fn(),
        }),
    )
  })

  it("Should handle regulation selection", async () => {
    ;(useLoaderData as Mock).mockReturnValue({
      entity: {
        id: "1",
      },
      staticDataEntityRegulations: [
        { key: "FCA", display: "FCA" },
        { key: "PRA", display: "PRA" },
      ] as EnumValueDto[],
      staticDataGicsClassificationIndustries: [
        { key: 201010, display: "Aerospace & Defense" },
        { key: 203010, display: "Air Freight & Logistics" },
      ] as EnumValueDto,
    })
    ;(useGetProofOfExistenceDocumentsSuspenseQry as Mock).mockReturnValue({
      data: [
        {
          id: "some-id",
          category: "ProofOfExistence",
          originalFileName: "some-file.pdf",
          uploadDateTime: "02-06-2004",
        },
      ] satisfies GetEntityDocumentsDto[],
      isLoading: false,
      isError: false,
    })

    const { regulationsSelectedItems, regulatedBy } = setup()
    const user = userEvent.setup()

    expect(regulatedBy).toBeInTheDocument()

    await user.click(regulatedBy)

    const [fca, pra] = [
      screen.getByRole("option", { name: /^FCA$/i }),
      screen.getByRole("option", { name: /^PRA$/i }),
    ]

    expect(fca).toBeInTheDocument()
    expect(pra).toBeInTheDocument()

    await user.click(fca)
    expect(
      within(regulationsSelectedItems).getByText("FCA"),
    ).toBeInTheDocument()

    expect(regulatedBy).toHaveAttribute("aria-expanded", "true")

    await user.click(pra)
    expect(
      within(regulationsSelectedItems).getByText("PRA"),
    ).toBeInTheDocument()

    expect(regulatedBy).toHaveAttribute("aria-expanded", "true")

    await user.click(document.body)

    expect(regulatedBy).toHaveAttribute("aria-expanded", "false")
  })

  it("Should handle website input", async () => {
    const { websiteInput } = setup()
    await userEvent.type(websiteInput, "www.test.com")
    expect(websiteInput).toHaveValue("www.test.com")
  })
})
