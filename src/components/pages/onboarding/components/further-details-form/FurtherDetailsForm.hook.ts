import { useEffect } from "react"
import { useForm } from "@tanstack/react-form"

import { useFormInvalid } from "@/hooks/use-form-invalid"
import {
  GET_DOCUMENT_PROOF_OF_EXISTENCE_QRY_KEY,
  useGetProofOfExistenceDocumentsSuspenseQry,
} from "@/data/onboarding/onboarding.query"
import { useDeleteDocumentMutation } from "@/data/onboarding/onboarding.mutation"
import { IOnboardingFurtherDetails } from "@/data/onboarding/onboarding.interface"
import { useSaveFurtherDetailsFormMutation } from "@/data/onboarding/further-details.mutation"
import { useGetEntitySuspenseQry } from "@/data/onboarding/entity-details.query"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGlobalStore } from "@/data/global/global.store"
import {
  EntityRegulation,
  EnumValueDto,
  GetEntityResponseDto,
  UpdateEntityRequestDto,
  GetEntityDocumentsDto,
} from "@/client/onboarding/types.gen"

import { FurtherDetailsFormProps } from "./FurtherDetailsForm"
import useFormBlockWithUnsavedChanges from "../hooks/useWarnWithUnsavedChanges"

const mapRegulationEnum = (
  entity: GetEntityResponseDto,
): EntityRegulation[] => {
  if (!entity?.legalEntity?.regulation) return []

  if (entity?.legalEntity?.regulation === "NotRegulated")
    return ["NotRegulated"]

  return entity?.legalEntity?.regulation.split(", ") as EntityRegulation[]
}

function findStaticData<D extends EnumValueDto[], K>(
  data: D | undefined,
  key: K,
) {
  return data?.find((e) => e.key === key) ?? { display: "", key: "" }
}
function formIsTouched(formValues: any): boolean {
  let touched = false
  if (formValues.website.length > 0) touched = true
  if (formValues.natureOfBusiness.key.length > 0) touched = true
  if (formValues.phoneNumber.length > 0) touched = true
  if (formValues.documentsMetadata.length > 0) touched = true
  return touched
}

export function useFurtherDetailsForm(props: FurtherDetailsFormProps) {
  const {
    staticDataEntityRegulations,
    staticDataGicsClassificationIndustries,
  } = useLoaderData()
  const { data: entity } = useGetEntitySuspenseQry()
  const documentsQry = useGetProofOfExistenceDocumentsSuspenseQry()
  const { mutate: deleteDocumentMutate } = useDeleteDocumentMutation([
    GET_DOCUMENT_PROOF_OF_EXISTENCE_QRY_KEY,
  ])
  const { mutate: updateFurtherDetailsMutate } =
    useSaveFurtherDetailsFormMutation()

  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)

  const regulation = mapRegulationEnum(entity)

  const defaultValues = {
    regulation,
    website: entity?.website ?? "",
    natureOfBusiness: findStaticData(
      staticDataGicsClassificationIndustries,
      entity.legalEntity?.industryCode?.toString(),
    ),
    phoneNumber: entity?.phoneNumber ?? "",
    documentsMetadata:
      documentsQry.data?.map((e) => ({
        id: e.id,
        name: e.name!,
      })) ?? [],
    documentsToUpload: [],
  }
  const isTouched = formIsTouched(defaultValues)
  const form = useForm<IOnboardingFurtherDetails>({
    defaultValues,
    onSubmit: ({ value }) =>
      handleSave({
        onSuccess: () => {
          props.onNext?.(value)
          form.reset(defaultValues, { keepDefaultValues: true })
        },
      }),
    validators: {
      onMount: ({ formApi }) => {
        setFormSeen({
          ...formSeenState,
          furtherDetailsForm: true,
        })

        if (isTouched) {
          formApi.validateAllFields("submit")
        }

        return null
      },
    },
  })

  useEffect(() => {
    if (!documentsQry.isLoading) {
      const documents =
        documentsQry.data?.map((document) => ({
          id: document.id,
          name: document.name!,
        })) ?? []

      form.setFieldValue("documentsMetadata", documents)
      form.setFieldMeta("documentsMetadata", {
        ...form.getFieldMeta("documentsMetadata")!,
        isDirty: false,
      })
    }

    return () => {
      form.setFieldValue("documentsToUpload", [])
      form.setFieldMeta("documentsToUpload", {
        ...form.getFieldMeta("documentsToUpload")!,
        isDirty: false,
      })
    }
  }, [documentsQry.isLoading, documentsQry.data, form])

  useFormInvalid(form, props.onInvalid)

  // Block step change if data is unsaved
  useFormBlockWithUnsavedChanges(form)

  function handleDelete(documentId: string) {
    deleteDocumentMutate({ documentId })
  }

  function isRegulated() {
    if (regulation) {
      return regulation[0] !== ("NotRegulated" as EntityRegulation)
    }

    if ((entity?.legalEntity?.regulation as string) === "") return false

    if (entity?.legalEntity?.regulation === "NotRegulated") return false

    return true
  }

  async function handleSave(handlers?: Record<string, () => void>) {
    const [errors] = await form.validateAllFields("submit")

    if (errors) return

    updateFurtherDetailsMutate(
      {
        entity: entity as UpdateEntityRequestDto,
        form: form.state.values,
      },
      handlers,
    )
  }

  return {
    form,
    isRegulated: isRegulated(),
    entityRegulationOptions: staticDataEntityRegulations ?? [],
    gicsClassificationIndustries: staticDataGicsClassificationIndustries ?? [],
    handleSave,
    handleDelete,
  }
}

interface GetReviewFurtherDetails {
  staticData: {
    staticDataGicsClassificationIndustries: EnumValueDto[]
  }
  entity: GetEntityResponseDto | undefined
  proofOfExistenceDocs: GetEntityDocumentsDto[] | undefined
}
export function getReviewFurtherDetails({
  staticData,
  entity,
  proofOfExistenceDocs,
}: GetReviewFurtherDetails) {
  const { staticDataGicsClassificationIndustries } = staticData
  if (entity == undefined || proofOfExistenceDocs == undefined)
    return {
      regulation: "",
      website: "",
      phoneNumber: "",
      natureOfBusiness: "",
      documentsToUpload: "",
    }

  const mapRegulationEnum = () => {
    if (!entity?.legalEntity?.regulation) return ""

    if (entity?.legalEntity?.regulation === "NotRegulated")
      return "Not Regulated"

    return entity?.legalEntity?.regulation
  }

  return {
    regulation: mapRegulationEnum(),
    website: entity.website ?? "",
    phoneNumber: entity.phoneNumber ?? "",
    natureOfBusiness:
      staticDataGicsClassificationIndustries
        .filter((i) => i.key === entity.legalEntity?.industryCode?.toString())
        .map((e) => e.display)
        .join(", ") ?? "",
    documentsToUpload:
      proofOfExistenceDocs?.map((e) => e.name).join(", ") ?? "",
  }
}
