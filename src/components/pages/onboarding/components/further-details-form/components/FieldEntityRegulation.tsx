import { useMemo } from "react"
import { useStore } from "@tanstack/react-store"

import { useToggle } from "@/hooks/use-toggle"
import Subline from "@/components/ui/subline"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { MultiSelector } from "@/components/base/form/searchable-selector"
import { FormFieldError } from "@/components/base/form/form"
import { EnumValueDto } from "@/client/onboarding/types.gen"

import { type FurtherDetailsFieldProps } from "../interface"

interface FieldEntityRegulationProps extends FurtherDetailsFieldProps {
  options: EnumValueDto[]
  isRegulated?: boolean
}

export function FieldEntityRegulation({
  form,
  options,
  isRegulated = true,
  permission,
}: FieldEntityRegulationProps) {
  const [isOn, toggle] = useToggle(isRegulated)

  const errors = useStore(
    form?.store,
    (state) => (state.fieldMeta.regulation?.errors ?? []) as string[],
  )

  const value = useMemo(() => {
    return isOn ? "Yes" : "No"
  }, [isOn])

  const entityOptions = useMemo(
    () => options.filter((e) => !(isOn && e.key === "NotRegulated")),
    [isOn, options],
  )

  const handleToggle = (value: "Yes" | "No") => {
    if (value === "No") {
      toggle.off()
      form.setFieldValue("regulation", ["NotRegulated"])
      return
    }

    form.setFieldValue("regulation", [])

    toggle.on()
  }

  return (
    <div>
      <div className="flex flex-col items-start">
        <p className="field-label inline-flex items-center text-sm font-medium leading-none after:ml-0.5 after:text-destructive peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          Is the entity regulated?
        </p>

        <RadioGroup
          aria-label="Entity regulation"
          className="mt-3 grid grid-cols-2 gap-2"
          disabled={!permission}
          onValueChange={handleToggle}
          value={value}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              className="size-5 data-[state=unchecked]:border-slate-200 data-[state=checked]:bg-primary [&_svg]:size-2 [&_svg]:data-[state=checked]:text-white"
              id="yes"
              value="Yes"
            />
            <Label htmlFor="yes">Yes</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              className="size-5 data-[state=unchecked]:border-slate-200 data-[state=checked]:bg-primary [&_svg]:size-2 [&_svg]:data-[state=checked]:text-white"
              id="no"
              value="No"
            />
            <Label htmlFor="no">No</Label>
          </div>
        </RadioGroup>
      </div>

      <form.Field
        mode="array"
        name="regulation"
        validators={{
          onChange: ({ value }) => {
            if (isOn && Array.isArray(value) && value.length === 0) {
              return "Regulation by is required"
            }

            return undefined
          },
        }}
      >
        {(rootField) => (
          <>
            {isOn && (
              <div className="mt-3 flex items-start gap-x-2">
                <Subline />

                <div className="flex flex-1 flex-col gap-y-3">
                  <MultiSelector
                    disabled={!permission}
                    items={entityOptions.map((e) => ({
                      value: e.key!,
                      label: e.display!,
                    }))}
                    onSelectedItemsChange={(selectedItems) => {
                      const hasOtherValues = selectedItems.some(
                        (e) => e !== "NotRegulated",
                      )

                      const notRegulatedIndex = selectedItems.findIndex(
                        (e) => e !== "NotRegulated",
                      )

                      if (hasOtherValues && notRegulatedIndex > -1) {
                        toggle.on()
                        rootField.setValue(
                          selectedItems.slice(notRegulatedIndex),
                        )

                        return
                      }

                      rootField.setValue(selectedItems)
                    }}
                    selectedItems={rootField.state.value}
                    Trigger={{
                      "aria-label": "Regulations",
                      placeholder: "Select regulations...",
                      itemName: "regulation",
                      id: rootField.name,
                    }}
                  />

                  <FormFieldError
                    aria-label={`${rootField.name} error`}
                    errors={errors}
                  />
                </div>
              </div>
            )}
          </>
        )}
      </form.Field>
    </div>
  )
}
