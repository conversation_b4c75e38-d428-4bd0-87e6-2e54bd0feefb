import { required } from "@/lib/form.utils"
import { SingleSelector } from "@/components/base/form/searchable-selector"
import { FormField } from "@/components/base/form/form"
import { EnumValueDto } from "@/client/onboarding/types.gen"

import { type FurtherDetailsFieldProps } from "../interface"

interface FieldNatureOfBusinessProps extends FurtherDetailsFieldProps {
  options: EnumValueDto[]
  permission: boolean
}

export function FieldNatureOfBusiness({
  form,
  options,
  permission,
}: FieldNatureOfBusinessProps) {
  return (
    <form.Field
      name="natureOfBusiness"
      validators={{
        onChange: ({ value }) =>
          required(value?.display, "Nature of business is required"),
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Nature of business"
            required
          >
            <SingleSelector
              disabled={!permission}
              items={options.map((s) => ({
                value: s.key || "",
                label: s.display || "",
              }))}
              onSelectedItemsChange={(selectedItem) =>
                field.handleChange(
                  options.find((op) => op.key === selectedItem.value),
                )
              }
              selectedItem={{
                value: field.state.value?.key || "",
                label: field.state.value?.display || "",
              }}
              Trigger={{
                id: field.name,
                placeholder: "Select Nature of business",
              }}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
