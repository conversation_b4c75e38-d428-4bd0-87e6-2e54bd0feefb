import { Input } from "@/components/ui/input"
import { FormField } from "@/components/base/form/form"

import { type FurtherDetailsFieldProps } from "../interface"

export function FieldPhoneNumber({
  form,
  permission,
}: FurtherDetailsFieldProps) {
  return (
    <form.Field name="phoneNumber">
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField aria-label={field.name} field={field} label="Phone number">
            <Input
              className="bg-background p-4 py-6 md:text-base"
              disabled={!permission}
              id={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              type="tel"
              value={field.state.value}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
