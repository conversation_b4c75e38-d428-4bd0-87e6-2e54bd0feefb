import { Input } from "@/components/ui/input"
import { FormField } from "@/components/base/form/form"

import { type FurtherDetailsFieldProps } from "../interface"

const urlRegex =
  /^(https?:\/\/)?([\w\\-]+\.)+[\w\\-]+(\/[\w\-._~:/?#[\]@!$&'()*+,;=]*)?$/i

export function FieldWebsite({ form, permission }: FurtherDetailsFieldProps) {
  return (
    <form.Field
      name="website"
      validators={{
        onChange: ({ value }) => {
          if (value.length != 0 && !urlRegex.test(value.trim())) {
            return "Enter a valid website URL"
          }
          return undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField aria-label={field.name} field={field} label="Website">
            <Input
              className="bg-background p-4 py-6 md:text-base"
              disabled={!permission}
              id={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="www.argentex.com"
              type="text"
              value={field.state.value}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
