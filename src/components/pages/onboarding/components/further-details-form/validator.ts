import { IOnboardingFurtherDetails } from "@/data/onboarding/onboarding.interface"

import { getReviewFurtherDetails } from "./FurtherDetailsForm.hook"

export function validateFurtherDetailsForm(data?: IOnboardingFurtherDetails) {
  const isValid =
    !!data?.regulation &&
    data.documentsMetadata.length > 0 &&
    data.natureOfBusiness?.key !== ""
  return isValid
}

export default function reviewFurtherDetails(
  data?: ReturnType<typeof getReviewFurtherDetails>,
) {
  const isValid =
    !!data?.regulation &&
    data.documentsToUpload.length > 0 &&
    data.natureOfBusiness !== ""
  return isValid
}
