import { useRolePermissions } from "@/hooks/useRolePermissions"
import { IOnboardingFurtherDetails } from "@/data/onboarding/onboarding.interface"
import { FormLayout } from "@/components/base/form/form"

import { useFurtherDetailsForm } from "./FurtherDetailsForm.hook"
import { Fields } from "./components"
import { GoBack } from "../common/GoBack"
import { FormControls } from "../common/FormControls"

export interface FurtherDetailsFormProps {
  onNext?: (payload: IOnboardingFurtherDetails) => void
  onBack?: () => void
  onInvalid?: () => void
}

export function FurtherDetailsForm(props: FurtherDetailsFormProps) {
  const {
    form,
    entityRegulationOptions,
    gicsClassificationIndustries,
    isRegulated,
    handleDelete,
  } = useFurtherDetailsForm(props)
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")
  return (
    <div
      aria-label="2. Further details"
      className="container flex max-w-2xl flex-col gap-y-8 px-4"
    >
      {props.onBack && <GoBack onBack={props.onBack} />}

      <FormLayout
        className="mx-0 flex max-w-xl flex-col gap-y-7"
        title="2. Further details"
      >
        <p className="text-muted-foreground">
          Please provide the additional entity information and upload the
          certificate of incorporation or equivalent registration document(s).
        </p>
        <Fields.EntityRegulation
          form={form}
          isRegulated={isRegulated}
          options={entityRegulationOptions ?? []}
          permission={permission}
        />
        <Fields.NatureOfBusiness
          form={form}
          options={gicsClassificationIndustries}
          permission={permission}
        />
        <Fields.Website form={form} permission={permission} />
        <Fields.PhoneNumber form={form} permission={permission} />
        <Fields.UploadDocuments
          form={form}
          onDelete={handleDelete}
          permission={permission}
        />

        <FormControls disabled={!permission} form={form} />
      </FormLayout>
    </div>
  )
}
