import { useMemo, useState } from "react"
import { Pen } from "lucide-react"
import { useQueryClient } from "@tanstack/react-query"

import { useRolePermissions } from "@/hooks/useRolePermissions"
import { useModal } from "@/hooks/use-modal"
import { useGetAllUsersWithEntitiesQry } from "@/data/user"
import {
  GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY,
  useGetAllUsersQry,
} from "@/data/entity/entity.query"
import { useEntityAccessMutations } from "@/data/entity-access/entity-access.mutation"
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import EditUserCard from "@/components/pages/_components/edit-user-card"
import {
  EnumValueDto,
  LoginEntityApproverLevel,
} from "@/client/onboarding/types.gen"

interface EditUserModalProps {
  entityId: string
  email: string
  name: string
  onBack: () => void
}

function useEditUserModal(props: EditUserModalProps) {
  const queryClient = useQueryClient()
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")
  const { data: allUsers } = useGetAllUsersWithEntitiesQry({ enabled: true })
  const { data: users } = useGetAllUsersQry(props.entityId)
  const { updateEntityAccess } = useEntityAccessMutations(props.entityId)
  const [role, setRole] = useState<EnumValueDto | undefined>()
  const [signatory, setSignatory] = useState<
    LoginEntityApproverLevel | undefined
  >()
  const {
    visible: isModalOpen,
    toggle: toggleModal,
    close: closeModal,
  } = useModal()

  const entityAccessId = useMemo(() => {
    const currentUser = users?.find((user) => user.email === props.email)

    return currentUser?.entityAccess?.entityAccessId
  }, [props.email, users])

  const currentUser = allUsers?.find((user) => user.email === props.email)

  function handleRole(role: EnumValueDto) {
    setRole(role)
  }

  function handleSignatoryChange(signatory: LoginEntityApproverLevel) {
    setSignatory(signatory)
  }

  function handleSubmit() {
    if (!entityAccessId) return

    updateEntityAccess(
      {
        entityAccessId,
        payload: {
          roles: [role?.display ?? ""],
          approverLevel: signatory,
        },
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [
              "entity",
              GET_ALL_USERS_BY_ENTITY_ID_QRY_KEY,
              props.entityId,
            ],
          })
        },
        onSettled: closeModal,
      },
    )
  }

  return {
    isModalOpen,
    currentUser,
    permission,
    toggleModal,
    handleRole,
    handleSignatoryChange,
    handleSubmit,
  }
}

export function EditUserModal(props: EditUserModalProps) {
  const {
    isModalOpen,
    currentUser,
    permission,
    toggleModal,
    handleRole,
    handleSignatoryChange,
    handleSubmit,
  } = useEditUserModal(props)

  return (
    <Dialog modal onOpenChange={toggleModal} open={isModalOpen}>
      <DialogTrigger asChild>
        <Button
          className="self-end pr-0 underline underline-offset-4 hover:bg-transparent"
          variant="link"
        >
          <Pen />
          <span>Edit</span>
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
        </DialogHeader>

        {permission && (
          <EditUserCard
            email={props.email}
            entities={currentUser?.entities?.map(
              ({ displayName }) => displayName ?? "",
            )}
            entityId={props.entityId}
            entityName={props.name}
            name={props.name}
            onRoleChange={handleRole}
            onSignatoryChange={handleSignatoryChange}
          />
        )}

        <DialogFooter>
          <div className="flex justify-end">
            <Button
              className="w-full text-muted-foreground sm:w-auto"
              type="button"
              variant="link"
            >
              Cancel
            </Button>

            <Button className="w-full sm:w-auto" onClick={handleSubmit}>
              Save changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
