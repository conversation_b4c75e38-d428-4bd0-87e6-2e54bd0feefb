import { PropsWithChildren } from "react"

import { cn } from "@/lib/utils"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { Badge as CoreBadge } from "@/components/ui/badge"
import { RemoveUser } from "@/components/pages/_components/remove-user"
import { ShowChildren } from "@/components/base/show-children"

import { EditUserModal } from "./EditUserModal"
import { UsersAndSignatoriesFieldProps } from "../interface"

interface BadgeProps extends PropsWithChildren {
  className?: string
}

function Badge({ children, className }: BadgeProps) {
  return (
    <CoreBadge
      aria-label="User roles"
      className={cn(
        "border-slate-600/40 px-1 py-px font-normal text-slate-600",
        className,
      )}
      variant="outline"
    >
      {children}
    </CoreBadge>
  )
}

function useUserList() {
  const {
    entity: { legalEntity },
    entityId,
    user: { email },
  } = useLoaderData()

  return { entityId, legalEntity, email }
}

export function UserList({ form, permission }: UsersAndSignatoriesFieldProps) {
  const { email, entityId, legalEntity } = useUserList()

  if (form.state.values.users.length <= 0) return

  return (
    <form.Field name="users">
      {(field) => (
        <div aria-label="Users" className="space-y-1">
          {field.state.value?.length &&
            field.state.value.map((user, index) => (
              <div
                className="flex items-start justify-between py-4"
                key={index}
              >
                <div className="mt-1 flex flex-col gap-1">
                  <div className="flex items-center gap-2">
                    <h3
                      aria-label="User name"
                      className="font-medium text-foreground"
                    >
                      {user.name}
                    </h3>

                    {user.email === email && (
                      <Badge className="border-primary/40 text-primary">
                        You
                      </Badge>
                    )}

                    <Badge>{user.roles}</Badge>
                  </div>
                  <div className="flex">
                    <p
                      aria-label="User email"
                      className="text-sm text-muted-foreground"
                    >
                      {user.email}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2">
                  <EditUserModal
                    email={user.email}
                    entityId={entityId}
                    name={user.name}
                    onBack={() => {}}
                  />

                  <div className="min-w-24">
                    <ShowChildren when={user.email !== email && permission}>
                      <RemoveUser
                        ButtonProps={{
                          disabled: user.email === email,
                          text: form.state.isSubmitting
                            ? "Removing..."
                            : "Remove",
                        }}
                        disabled={form.state.isSubmitting}
                        email={user.email}
                        entityId={entityId}
                        entityName={legalEntity?.name}
                      />
                    </ShowChildren>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
    </form.Field>
  )
}
