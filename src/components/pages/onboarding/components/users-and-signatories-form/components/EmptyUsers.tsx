import { Plus } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"

interface EmptyUsersProps {
  onSkip?: () => void
  onClick?: () => void
}

export function EmptyUsers({ onClick, onSkip }: EmptyUsersProps) {
  return (
    <>
      <div className="mt-2">
        <p className="text-medium mb-4 font-semibold text-foreground">
          No users found
        </p>
        <div className="flex max-w-32 flex-col gap-4">
          <div className="mb-4 flex items-center gap-1">
            <Plus className="h-4 w-4 text-primary" />
            <Button
              className="h-auto p-0 font-normal underline hover:bg-transparent"
              onClick={onClick}
              variant="link"
            >
              Add a user
            </Button>
          </div>
          <Button onClick={onSkip}>Skip for now</Button>
        </div>
      </div>
    </>
  )
}
