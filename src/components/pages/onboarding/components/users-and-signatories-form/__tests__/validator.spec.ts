import { describe, it, expect } from "vitest"

import { IOnboardingUsersAndSignatories } from "@/data/onboarding/onboarding.interface"
import {
  validateUsersAndSignatoriesForm,
  default as reviewUsersAndSignatories,
} from "@/components/pages/onboarding/components/users-and-signatories-form/validator"

describe("Users and Signatories Form Validator", () => {
  const validUsersAndSignatories: IOnboardingUsersAndSignatories = {
    users: [
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        roles: ["Admin", "Signatory"],
        isSignatory: true,
      },
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        roles: ["User"],
        isSignatory: false,
      },
    ],
  }

  describe("validateUsersAndSignatoriesForm", () => {
    it("should return true for any data (validation is skipped)", () => {
      const result = validateUsersAndSignatoriesForm(validUsersAndSignatories)
      expect(result).toBe(true)
    })

    it("should return true when data is undefined", () => {
      const result = validateUsersAndSignatoriesForm(undefined)
      expect(result).toBe(true)
    })

    it("should return true for empty users array", () => {
      const emptyData = { users: [] }
      const result = validateUsersAndSignatoriesForm(emptyData)
      expect(result).toBe(true)
    })

    it("should return true for any user configuration", () => {
      const anyData = {
        users: [
          {
            name: "",
            email: "",
            roles: [],
            isSignatory: false,
          },
        ],
      }
      const result = validateUsersAndSignatoriesForm(anyData)
      expect(result).toBe(true)
    })

    it("should return true for null data", () => {
      const result = validateUsersAndSignatoriesForm(null as any)
      expect(result).toBe(true)
    })
  })

  describe("reviewUsersAndSignatories (default export)", () => {
    it("should return true for any review data (validation is skipped)", () => {
      const mockReviewData = {
        users: [
          {
            name: "Test User",
            email: "<EMAIL>",
            roles: ["Admin"],
            isSignatory: true,
          },
        ],
      }
      const result = reviewUsersAndSignatories(mockReviewData)
      expect(result).toBe(true)
    })

    it("should return true when review data is undefined", () => {
      const result = reviewUsersAndSignatories(undefined)
      expect(result).toBe(true)
    })

    it("should return true for empty review data", () => {
      const emptyReviewData = { users: [] }
      const result = reviewUsersAndSignatories(emptyReviewData)
      expect(result).toBe(true)
    })

    it("should return true for null review data", () => {
      const result = reviewUsersAndSignatories(null as any)
      expect(result).toBe(true)
    })

    it("should return true for complex review data structure", () => {
      const complexReviewData = {
        users: [
          {
            name: "Admin User",
            email: "<EMAIL>",
            roles: ["Admin", "Signatory", "Approver"],
            isSignatory: true,
          },
          {
            name: "Regular User",
            email: "<EMAIL>",
            roles: ["User"],
            isSignatory: false,
          },
          {
            name: "Manager",
            email: "<EMAIL>",
            roles: ["Manager", "Signatory"],
            isSignatory: true,
          },
        ],
      }
      const result = reviewUsersAndSignatories(complexReviewData)
      expect(result).toBe(true)
    })
  })

  describe("Integration scenarios", () => {
    it("should handle the same data structure for both validators", () => {
      const testData = validUsersAndSignatories

      const validateResult = validateUsersAndSignatoriesForm(testData)
      const reviewResult = reviewUsersAndSignatories(testData)

      expect(validateResult).toBe(true)
      expect(reviewResult).toBe(true)
    })

    it("should handle edge cases consistently", () => {
      const edgeCases = [
        undefined,
        null,
        { users: [] },
        { users: [{ name: "", email: "", roles: [], isSignatory: false }] },
      ]

      edgeCases.forEach((testCase) => {
        const validateResult = validateUsersAndSignatoriesForm(testCase as any)
        const reviewResult = reviewUsersAndSignatories(testCase as any)

        expect(validateResult).toBe(true)
        expect(reviewResult).toBe(true)
      })
    })
  })

  describe("Documentation and behavior verification", () => {
    it("should skip validation as documented in the implementation", () => {
      // This test verifies that the current implementation intentionally
      // skips validation for this optional form step

      const invalidButAcceptedData = {
        users: [
          {
            name: "", // Empty name
            email: "invalid-email", // Invalid email format
            roles: [], // No roles
            isSignatory: true, // Signatory but no valid roles
          },
        ],
      }

      const result = validateUsersAndSignatoriesForm(invalidButAcceptedData)
      expect(result).toBe(true) // Should still return true because validation is skipped
    })

    it("should maintain consistent behavior for review function", () => {
      // Verify that the review function also skips validation consistently
      const anyReviewData = {
        users: [
          {
            name: "Any Name",
            email: "<EMAIL>",
            roles: ["Any Role"],
            isSignatory: Math.random() > 0.5, // Random boolean
          },
        ],
      }

      const result = reviewUsersAndSignatories(anyReviewData)
      expect(result).toBe(true)
    })
  })
})
