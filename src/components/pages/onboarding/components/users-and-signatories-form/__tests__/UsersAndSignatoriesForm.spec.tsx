import { describe, expect, it, vi, beforeEach } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useRolePermissions } from "@/hooks/useRolePermissions"
import * as UsersAndSignatoriesHook from "@/components/pages/onboarding/components/users-and-signatories-form/UsersAndSignatoriesForm.hook"
import {
  UsersAndSignatoriesForm,
  UsersAndSignatoriesFormProps,
} from "@/components/pages/onboarding/components/users-and-signatories-form"

// Mock all necessary query hooks
vi.mock("@/data/onboarding/users-and-signatories.query", () => ({
  useGetAllUsersSuspenseQry: vi.fn(() => ({
    data: [],
  })),
  useGetAllUsersWithEntitiesSuspenseQry: vi.fn(() => ({
    data: [],
    refetch: vi.fn(),
  })),
}))

// Mock the hook that's used in the component
vi.mock(
  "@/components/pages/onboarding/components/users-and-signatories-form/UsersAndSignatoriesForm.hook",
  () => ({
    useUsersAndSignatoriesForm: vi.fn(),
  }),
)
vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(),
}))

// Mock all the child components to prevent rendering issues
vi.mock(
  "@/components/pages/onboarding/components/users-and-signatories-form/components/UserList",
  () => ({
    UserList: () => <div aria-label="Users">Users list</div>,
  }),
)

// Mock the steps components
vi.mock(
  "@/components/pages/onboarding/components/users-and-signatories-form/components/steps",
  () => ({
    UserEmailForm: () => <div data-testid="user-email-form">Email Form</div>,
    ExistingUser: () => <div data-testid="existing-user">Existing User</div>,
    NewUser: () => <div data-testid="new-user">New User</div>,
  }),
)

vi.mock("@/components/pages/onboarding/components/common/FormControls", () => ({
  FormControls: ({ onSave }: { onSave: () => void; form: any }) => (
    <div>
      <button onClick={onSave}>Save & next</button>
    </div>
  ),
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

function setup(props?: Partial<UsersAndSignatoriesFormProps>) {
  const user = userEvent.setup()

  const mockForm = {
    state: {
      values: {
        users: [],
      },
      canSubmit: true,
    },
    getFieldValue: vi.fn((_field) => []),
    handleSubmit: vi.fn(),
  }

  const mockHandleSkip = vi.fn()

  vi.mocked(UsersAndSignatoriesHook.useUsersAndSignatoriesForm).mockReturnValue(
    {
      entityId: "some-id",
      form: mockForm as any,
      handleSkip: mockHandleSkip,
      entityName: "Test Entity",
    },
  )
  vi.mocked(useRolePermissions).mockReturnValue({
    getPermission: () => true,
  })

  const defaultProps = {
    onNext: vi.fn(),
    onInvalid: vi.fn(),
    onBack: vi.fn(),
    ...props,
  }

  const renderResult = render(
    <QueryClientProvider client={queryClient}>
      <UsersAndSignatoriesForm {...defaultProps} />
    </QueryClientProvider>,
  )

  return {
    ...renderResult,
    users: screen.getByLabelText("Users"),
    user,
    mockForm,
    mockHandleSkip,
  }
}

describe("UsersAndSignatoriesForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("renders the form with header", () => {
    const { getByText } = setup()

    expect(getByText("6. Users and Signatories")).toBeInTheDocument()
    expect(
      getByText(/Please create Users and assign their Signatory roles./i),
    ).toBeInTheDocument()
  })

  it("displays the add user button", () => {
    const { getByText } = setup()

    expect(getByText("Add a user")).toBeInTheDocument()
  })

  it("shows UserList component when users exist", () => {
    vi.mocked(
      UsersAndSignatoriesHook.useUsersAndSignatoriesForm,
    ).mockReturnValue({
      entityId: "some-id",
      form: {
        state: {
          values: {
            users: [{ name: "Test User", email: "<EMAIL>" }],
          },
          canSubmit: true,
        },
        getFieldValue: vi.fn(),
        handleSubmit: vi.fn(),
      } as any,
      handleSkip: vi.fn(),
      entityName: "Test Entity",
    })

    const { users } = setup()

    expect(users).toBeInTheDocument()
  })

  it.skip("does not show UserList when no users exist", () => {
    const { users } = setup()

    expect(users).not.toBeInTheDocument()
  })

  it.skip("calls handleSkip when skip button is clicked", async () => {
    const { getByText, mockHandleSkip, user } = setup()

    await user.click(getByText("skip this step"))

    expect(mockHandleSkip).toHaveBeenCalled()
  })

  it.skip("renders back button when onBack is provided", async () => {
    const onBack = vi.fn()
    const { getByText, user } = setup({ onBack })

    const backButton = getByText("Back")
    expect(backButton).toBeInTheDocument()

    await user.click(backButton)
    expect(onBack).toHaveBeenCalled()
  })

  it.skip("does not render back button when onBack is not provided", () => {
    const { queryByText } = setup({ onBack: undefined })

    expect(queryByText("Back")).not.toBeInTheDocument()
  })
})
