import { describe, expect, it, vi, beforeEach } from "vitest"
import { renderHook, act } from "@testing-library/react"

import { useFormInvalid } from "@/hooks/use-form-invalid"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGetAllUsersQry } from "@/data/entity/entity.query"
import { useUsersAndSignatoriesForm } from "@/components/pages/onboarding/components/users-and-signatories-form/UsersAndSignatoriesForm.hook"

// Mock dependencies
vi.mock("@/data/onboarding/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entityId: "entity-id",
    entity: {
      legalEntity: {
        name: "Test Entity Name",
      },
    },
  })),
}))

vi.mock("@/hooks/use-form-invalid", () => ({
  useFormInvalid: vi.fn(),
}))

vi.mock("@/data/entity/entity.query", () => ({
  useGetAllUsersQry: vi.fn(() => ({
    data: [],
  })),
}))

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn((selector) =>
    selector({
      setOnboardingSeenState: vi.fn(),
    }),
  ),
}))

// Mock @tanstack/react-form
vi.mock("@tanstack/react-form", () => ({
  useForm: vi.fn(({ onSubmit, defaultValues }) => {
    const submit = () => {
      if (onSubmit) {
        onSubmit({ value: defaultValues })
      }
    }

    return {
      handleSubmit: submit,
      getFieldValue: (field: string) => defaultValues[field] || [],
      state: {
        values: defaultValues || {},
        canSubmit: true,
      },
    }
  }),
}))

describe("UsersAndSignatoriesForm.hook", () => {
  const mockProps = {
    onNext: vi.fn(),
    handleSkip: vi.fn(),
    onInvalid: vi.fn(),
    onBack: vi.fn(),
  }

  beforeEach(() => {
    vi.resetAllMocks()
  })

  describe("Form initialization", () => {
    it("initializes with users from API", () => {
      const mockUsers = [
        {
          displayName: "John Doe",
          email: "<EMAIL>",
          entityAccess: { roles: [{ name: "Admin" }] },
        },
      ]

      vi.mocked(useGetAllUsersQry).mockReturnValue({
        data: mockUsers,
      } as any)

      const { result } = renderHook(() => useUsersAndSignatoriesForm(mockProps))

      expect(result.current.form).toBeDefined()
      expect(useFormInvalid).toHaveBeenCalledWith(
        result.current.form,
        mockProps.onInvalid,
      )

      // Check that the default values were set correctly
      expect(result.current.form.getFieldValue("users")).toEqual([
        {
          name: "John Doe",
          email: "<EMAIL>",
          roles: ["Admin"],
          isSignatory: false,
        },
      ])
    })

    it("initializes with empty users when API returns no data", () => {
      vi.mocked(useGetAllUsersQry).mockReturnValue({
        data: [],
      } as any)

      const { result } = renderHook(() => useUsersAndSignatoriesForm(mockProps))

      expect(result.current.form.getFieldValue("users")).toEqual([])
    })
  })

  describe("handleSkip functionality", () => {
    it("calls onNext with empty users when skipping", () => {
      const { result } = renderHook(() => useUsersAndSignatoriesForm(mockProps))

      act(() => {
        result.current.handleSkip()
      })

      expect(mockProps.onNext).toHaveBeenCalledWith({ users: [] })
    })
  })

  describe("Entity name", () => {
    it("returns the correct entity name from loader data", () => {
      const { result } = renderHook(() => useUsersAndSignatoriesForm(mockProps))
      expect(result.current.entityName).toBe("Test Entity Name")
    })

    it("returns fallback entity name when not available", () => {
      vi.mocked(useLoaderData).mockReturnValue({
        entityId: "entity-id",
        entity: {},
      } as any)

      const { result } = renderHook(() => useUsersAndSignatoriesForm(mockProps))
      expect(result.current.entityName).toBe("the entity")
    })
  })

  describe("Form submission", () => {
    it("calls onNext with form values when submitted", () => {
      const { result } = renderHook(() => useUsersAndSignatoriesForm(mockProps))

      // Mock the form submission
      act(() => {
        result.current.form.handleSubmit()
      })

      expect(mockProps.onNext).toHaveBeenCalled()
    })
  })
})
