import { useRolePermissions } from "@/hooks/useRolePermissions"
import { IOnboardingUsersAndSignatories } from "@/data/onboarding/onboarding.interface"
import { But<PERSON> } from "@/components/ui/button"
import { AddUserWizard } from "@/components/pages/_components/add-user-wizard"

import { useUsersAndSignatoriesForm } from "./UsersAndSignatoriesForm.hook"
import { UserList } from "./components/UserList"
import { GoBack } from "../common/GoBack"
import { FormControls } from "../common/FormControls"

export interface UsersAndSignatoriesFormProps {
  onNext?: (payload: IOnboardingUsersAndSignatories) => void
  handleSkip?: () => void
  onInvalid?: () => void
  onBack?: () => void
}

function Header({
  handleSkip,
}: Pick<UsersAndSignatoriesFormProps, "handleSkip">) {
  return (
    <>
      <h1 className="text-2xl font-semibold">6. Users and Signatories</h1>

      <p className="text-muted-foreground">
        Please create Users and assign their Signatory roles. You may
        <Button className="p-1 underline" onClick={handleSkip} variant="link">
          skip this step
        </Button>
        and configure your Users later.
      </p>
    </>
  )
}

export function UsersAndSignatoriesForm(props: UsersAndSignatoriesFormProps) {
  const { entityId, form, entityName, handleSkip } =
    useUsersAndSignatoriesForm(props)
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")

  return (
    <div className="container flex max-w-2xl flex-col gap-y-8 px-4">
      {props.onBack && <GoBack onBack={props.onBack} />}

      <div className="mx-0 flex max-w-xl flex-col gap-y-7">
        <Header handleSkip={handleSkip} />

        <UserList form={form} permission={permission} />

        {permission && (
          <AddUserWizard
            Button={{ variant: "link" }}
            entityId={entityId}
            entityName={entityName}
          />
        )}

        <FormControls disabled={!permission} form={form} />
      </div>
    </div>
  )
}
