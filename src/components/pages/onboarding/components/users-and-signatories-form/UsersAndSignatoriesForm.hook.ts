import { useForm } from "@tanstack/react-form"

import { useFormInvalid } from "@/hooks/use-form-invalid"
import { IOnboardingUsersAndSignatories } from "@/data/onboarding/onboarding.interface"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGlobalStore } from "@/data/global/global.store"
import { useGetAllUsersQry } from "@/data/entity/entity.query"
import { type GetAllUsersWithAccessToEntityResponseDto } from "@/client/onboarding/types.gen"

import { UsersAndSignatoriesFormProps } from "./UsersAndSignatoriesForm"

export function useUsersAndSignatoriesForm(
  props: UsersAndSignatoriesFormProps,
) {
  const loaderData = useLoaderData()
  const { data: allUsers = [] } = useGetAllUsersQry(loaderData.entityId)
  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)
  const entityName = loaderData.entity?.legalEntity?.name || "the entity"

  const form = useForm<IOnboardingUsersAndSignatories>({
    defaultValues: {
      users: allUsers?.map((user) => ({
        name: user.displayName ?? "",
        email: user.email ?? "",
        roles: (user.entityAccess?.roles ?? []).map(({ name }) => name!),
        isSignatory: false,
      })),
    },
    onSubmit: ({ value }) => {
      props.onNext?.(value)
    },
    validators: {
      onMount: () => {
        setFormSeen({
          ...formSeenState,
          usersAndSignatoriesForm: true,
        })

        return null
      },
    },
  })

  useFormInvalid(form, props.onInvalid)

  const handleSkip = () => {
    // Call onNext directly to move to the next step
    props.onNext?.({ users: [] })
  }

  return {
    form,
    handleSkip,
    entityName,
    entityId: loaderData.entityId,
  }
}

export function getReviewUsersAndSignatories(
  allUsers: GetAllUsersWithAccessToEntityResponseDto[] | undefined,
) {
  if (allUsers == undefined)
    return {
      users: [],
    }

  return {
    users: allUsers.map((user) => ({
      name: user.displayName ?? "",
      email: user.email ?? "",
      roles: (user.entityAccess?.roles ?? []).map(({ name }) => name!),
      isSignatory: false,
    })),
  }
}
