import { IOnboardingUsersAndSignatories } from "@/data/onboarding/onboarding.interface"

import { getReviewUsersAndSignatories } from "./UsersAndSignatoriesForm.hook"

export function validateUsersAndSignatoriesForm(
  _data?: IOnboardingUsersAndSignatories,
) {
  return true // Skip validation for now since it's optional
}

export default function reviewUsersAndSignatories(
  _data?: ReturnType<typeof getReviewUsersAndSignatories>,
) {
  return true // Skip validation for now since it's optional
}
