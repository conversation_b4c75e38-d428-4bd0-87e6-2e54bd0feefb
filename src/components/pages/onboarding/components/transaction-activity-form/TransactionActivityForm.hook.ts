import { useForm } from "@tanstack/react-form"

import { Currency } from "@/lib/constants/currency.constants"
import { useFormInvalid } from "@/hooks/use-form-invalid"
import { useExpectedTransactionActivitySuspenseQry } from "@/data/onboarding/transaction-activity.query"
import { useUpdateTransactionActivityFormMutation } from "@/data/onboarding/transaction-activity.mutation"
import { IOnboardingExpectedTransactionActivity } from "@/data/onboarding/onboarding.interface"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGlobalStore } from "@/data/global/global.store"
import { type GetExpectedTransactionActivityDto } from "@/client/onboarding/types.gen"

import { TransactionActivityFormProps } from "./TransactionActivityForm"
import useFormBlockWithUnsavedChanges from "../hooks/useWarnWithUnsavedChanges"

function formIsTouched(formValues: any): boolean {
  let touched = false
  if (formValues.expectedTransactionCurrencies.length > 0) touched = true
  if (formValues.expectedTransactionJurisdictions.length > 0) touched = true
  if (formValues.yearlyNumberOfPaymentsInbound.length > 0) touched = true
  if (formValues.yearlyNumberOfPaymentsOutbound.length > 0) touched = true
  if (formValues.yearlyValueOfPaymentsInbound.length > 0) touched = true
  if (formValues.yearlyValueOfPaymentsOutbound.length > 0) touched = true
  if (formValues.maximumTransactionSize.length > 0) touched = true
  if (formValues.activityTypes.length > 0) touched = true
  return touched
}

export function useTransactionActivityForm(
  props: TransactionActivityFormProps,
) {
  const {
    staticDataCurrencies,
    staticDataCountries,
    staticDataTransactionActivityTypes,
  } = useLoaderData()
  const expectedTransactionActivity =
    useExpectedTransactionActivitySuspenseQry()
  const { mutate: updateTransactionActivity } =
    useUpdateTransactionActivityFormMutation()
  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)

  const defaultValues = {
    activityBaseCurrency:
      expectedTransactionActivity?.data.yearlyValueOfPaymentsCurrencyCode ?? "",
    expectedTransactionCurrencies:
      expectedTransactionActivity?.data.expectedTransactionCurrencies ?? [],
    expectedTransactionJurisdictions:
      expectedTransactionActivity?.data.expectedTransactionJurisdictions ?? [],
    yearlyNumberOfPaymentsInbound: Math.round(
      (expectedTransactionActivity?.data.yearlyNumberOfPaymentsInbound ?? 0) /
        12,
    ),
    yearlyNumberOfPaymentsOutbound: Math.round(
      (expectedTransactionActivity?.data.yearlyNumberOfPaymentsOutbound ?? 0) /
        12,
    ),
    yearlyValueOfPaymentsInbound:
      (expectedTransactionActivity?.data.yearlyValueOfPaymentsInbound ?? 0) /
      12,
    yearlyValueOfPaymentsOutbound:
      (expectedTransactionActivity?.data.yearlyValueOfPaymentsOutbound ?? 0) /
      12,
    maximumTransactionSize:
      expectedTransactionActivity?.data.maximumTransactionSize ?? 0,
    activityTypes:
      expectedTransactionActivity?.data.activityTypes?.map((types) =>
        String(types.transactionActivityTypeCode),
      ) ?? [],
  }
  const isTouched = formIsTouched(defaultValues)
  const form = useForm<IOnboardingExpectedTransactionActivity>({
    defaultValues,
    onSubmit: ({ value }) => {
      handleSave({
        onSuccess: () => {
          props.onNext?.(value)
          form.reset(defaultValues, { keepDefaultValues: true })
        },
      })
    },
    validators: {
      onMount: ({ formApi }) => {
        setFormSeen({
          ...formSeenState,
          transactionActivityForm: true,
        })

        if (isTouched) {
          formApi.validateAllFields("submit")
        }

        return null
      },
    },
  })

  useFormInvalid(form, props.onInvalid)

  // Block step change if data is unsaved
  useFormBlockWithUnsavedChanges(form)

  async function handleSave(handlers?: Record<string, () => void>) {
    const [errors] = await form.validateAllFields("submit")

    if (errors) return

    updateTransactionActivity(
      {
        form: form.state.values,
        transactionActivityData: expectedTransactionActivity?.data,
      },
      handlers,
    )
  }

  return {
    form,
    currencies: staticDataCurrencies.map((currency) => ({
      value: currency.code! as Currency,
      label: currency.code! as Currency,
    })),
    countries: staticDataCountries.map((country) => ({
      value: country.code!,
      label: country.name!,
    })),
    transactionActivityTypes: staticDataTransactionActivityTypes.map(
      (transactionActivityType) => ({
        value: String(transactionActivityType.code)!,
        label: transactionActivityType.name!,
      }),
    ),
    handleSave,
  }
}

export function getReviewTransactionActivity(
  transaction: GetExpectedTransactionActivityDto | undefined,
) {
  if (transaction == undefined)
    return {
      activityBaseCurrency: "",
      expectedTransactionCurrencies: "",
      transactionJurisdictions: "",
      monthlyTransactionsInbound: "",
      monthlyTransactionsOutbound: "",
      monthlyIncomingTransactionsValue: "",
      monthlyOutgoingTransactionsValue: "",
      maximumTransactionSize: "",
      transactionActivityType: "",
    }

  return {
    activityBaseCurrency: transaction.yearlyValueOfPaymentsCurrencyCode ?? "",
    expectedTransactionCurrencies: (
      transaction.expectedTransactionCurrencies ?? []
    )?.join(", "),
    transactionJurisdictions: (
      transaction.expectedTransactionJurisdictions ?? []
    ).join(", "),
    monthlyTransactionsInbound: Math.round(
      (transaction.yearlyNumberOfPaymentsInbound ?? 0) / 12,
    ).toString(),
    monthlyTransactionsOutbound: Math.round(
      (transaction.yearlyNumberOfPaymentsOutbound ?? 0) / 12,
    ).toString(),
    monthlyIncomingTransactionsValue: (
      (transaction.yearlyValueOfPaymentsInbound ?? 0) / 12
    ).toString(),
    monthlyOutgoingTransactionsValue: (
      (transaction.yearlyValueOfPaymentsOutbound ?? 0) / 12
    ).toString(),
    maximumTransactionSize: (
      transaction.maximumTransactionSize ?? 0
    ).toString(),
    transactionActivityType: (
      transaction.activityTypes?.map((e) => e.transactionActivityTypeName!) ??
      []
    ).join(", "),
  }
}
