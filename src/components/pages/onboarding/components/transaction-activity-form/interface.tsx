import { ReactFormExtendedApi } from "@tanstack/react-form"

import type { IOnboardingExpectedTransactionActivity } from "@/data/onboarding/onboarding.interface"
export type OnboardingTransactionActivityForm = ReactFormExtendedApi<
  IOnboardingExpectedTransactionActivity,
  undefined
>

export interface TransactionActivityFieldProps {
  form: OnboardingTransactionActivityForm
  permission: boolean
}
