import { IOnboardingExpectedTransactionActivity } from "@/data/onboarding/onboarding.interface"

import { getReviewTransactionActivity } from "./TransactionActivityForm.hook"

export function validateTransactionActivityForm(
  data?: IOnboardingExpectedTransactionActivity,
) {
  const isValid =
    !!data?.activityBaseCurrency &&
    data?.activityTypes?.length > 0 &&
    data?.expectedTransactionCurrencies?.length > 0 &&
    data?.expectedTransactionJurisdictions?.length > 0 &&
    data?.maximumTransactionSize > 0 &&
    data?.yearlyNumberOfPaymentsInbound > 0 &&
    data?.yearlyNumberOfPaymentsOutbound > 0 &&
    data?.yearlyValueOfPaymentsInbound > 0 &&
    data?.yearlyValueOfPaymentsOutbound > 0
  return isValid
}

export default function reviewTransactionActivity(
  data?: ReturnType<typeof getReviewTransactionActivity>,
) {
  const isValid =
    !!data?.activityBaseCurrency &&
    data?.expectedTransactionCurrencies !== "" &&
    data?.maximumTransactionSize !== "0" &&
    data?.monthlyIncomingTransactionsValue !== "0" &&
    data?.monthlyOutgoingTransactionsValue !== "0" &&
    data?.monthlyTransactionsInbound !== "0" &&
    data?.monthlyTransactionsOutbound !== "0" &&
    !!data?.transactionActivityType &&
    !!data?.transactionJurisdictions

  return isValid
}
