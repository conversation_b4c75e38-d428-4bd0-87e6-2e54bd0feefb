import { <PERSON><PERSON><PERSON> } from "@tanstack/react-form"

import { Currency } from "@/lib/constants/currency.constants"
import { IOnboardingExpectedTransactionActivity } from "@/data/onboarding/onboarding.interface"
import { FormField } from "@/components/base/form/form"
import { CoreCurrencyInput } from "@/components/base/currency/CurrencyInput"

import { type TransactionActivityFieldProps } from "../interface"

type CoreCurrencyInputProps = {
  field: FieldApi<
    IOnboardingExpectedTransactionActivity,
    "yearlyValueOfPaymentsInbound",
    undefined,
    undefined,
    number
  >
  permission: boolean
}

interface CurrencyInputProps extends CoreCurrencyInputProps {
  baseCurrency: Currency
}

function CurrencyInput({
  field,
  baseCurrency,
  permission,
}: CurrencyInputProps) {
  return (
    <div className="flex flex-col gap-y-3">
      <FormField
        aria-label={field.name}
        field={field}
        label="Average value of inbound transactions per month"
        required
      >
        <div className="relative">
          <CoreCurrencyInput
            className="peer h-9 border border-input py-6 pl-16 shadow-sm"
            currency={baseCurrency}
            disabled={!permission}
            id={field.name}
            onChange={(e) => field.handleChange(e)}
            value={field.state.value}
          />
          <span
            aria-hidden
            className="inset-center absolute bottom-0 left-[2rem] peer-disabled:opacity-50"
          >
            {baseCurrency}
          </span>
        </div>
      </FormField>
    </div>
  )
}

export function FieldMonthlyIncomingTransactions({
  form,
  permission,
}: TransactionActivityFieldProps) {
  return (
    <form.Subscribe
      selector={(state) => state.values.activityBaseCurrency as Currency}
    >
      {(baseCurrency) => (
        <form.Field
          name="yearlyValueOfPaymentsInbound"
          validators={{
            onChange: ({ value }) => {
              if (isNaN(value) || value < 0 || value == 0) {
                return "Average value of inbound transactions per month is required"
              }

              return undefined
            },
          }}
        >
          {(field) => (
            <CurrencyInput
              baseCurrency={baseCurrency}
              field={field}
              permission={permission}
            />
          )}
        </form.Field>
      )}
    </form.Subscribe>
  )
}
