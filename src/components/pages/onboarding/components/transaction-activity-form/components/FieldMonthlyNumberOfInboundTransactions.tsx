import { Input } from "@/components/ui/input"
import { FormField } from "@/components/base/form/form"

import { type TransactionActivityFieldProps } from "../interface"

export function FieldMonthlyNumberOfInboundTransactions({
  form,
  permission,
}: TransactionActivityFieldProps) {
  return (
    <form.Field
      name="yearlyNumberOfPaymentsInbound"
      validators={{
        onChange: ({ value }) => {
          if (isNaN(value) || value < 0 || value == 0) {
            return "Number of inbound transactions per month is required"
          }

          return undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Number of inbound transactions per month"
            required
          >
            <Input
              className="bg-background p-4 py-6 md:text-base"
              disabled={!permission}
              id={field.name}
              onBlur={(e) => {
                if (isNaN(e.target.valueAsNumber)) {
                  field.setValue(0)
                } else {
                  // Round value to whole number
                  const roundedValue = Math.round(e.target.valueAsNumber)
                  field.setValue(roundedValue)
                  // Update the input value to show the rounded number without decimal
                  e.target.value = roundedValue.toString()
                }
              }}
              onChange={(e) => {
                if (e.target.valueAsNumber < 0) return

                // Round value to whole number on change
                const roundedValue = Math.round(e.target.valueAsNumber)
                // Handle case when input has decimal point but nothing after it
                if (e.target.value.endsWith(".")) {
                  e.target.setAttribute("value", e.target.value.slice(0, -1))
                } else {
                  e.target.setAttribute("value", e.target.value)
                }
                field.handleChange(roundedValue)
              }}
              type="number"
              value={field?.state?.value?.toString()}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
