import { cn } from "@/lib/utils"
import { MultiSelector } from "@/components/base/form/searchable-selector"
import { FormField } from "@/components/base/form/form"

import { type TransactionActivityFieldProps } from "../interface"

interface FieldTransactionJurisdictionsProps
  extends TransactionActivityFieldProps {
  countries: { value: string; label: string }[]
}

export function FieldTransactionJurisdictions({
  form,
  countries,
  permission,
}: FieldTransactionJurisdictionsProps) {
  return (
    <form.Field
      mode="array"
      name="expectedTransactionJurisdictions"
      validators={{
        onChange: ({ value }) => {
          if (Array.isArray(value) && value.length === 0) {
            return "Transactions jurisdictions is required"
          }

          return undefined
        },
      }}
    >
      {(rootField) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={rootField.name}
            field={rootField}
            label={
              <span>
                <span>Transaction jurisdictions</span>
                <span className="ml-1 font-light">(Choose all that apply)</span>
              </span>
            }
            required
          >
            <MultiSelector
              disabled={!permission}
              items={countries}
              onSelectedItemsChange={(selectedItems) =>
                rootField.setValue(selectedItems)
              }
              selectedItems={rootField.state.value}
              Trigger={{
                id: rootField.name,
                "aria-label": "Transaction jurisdictions",
                placeholder: "Select all transaction jurisdictions...",
                itemName: "jurisdiction",
                Tooltip: {
                  triggerClassName: cn("max-w-28"),
                  contentClassName: cn(
                    "mb-1 bg-white text-slate-900 ring-1 ring-slate-400/50",
                  ),
                },
              }}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
