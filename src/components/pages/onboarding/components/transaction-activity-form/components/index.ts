import { FieldValueOfLargestTransaction as ValueOfLargestTransaction } from "./FieldValueOfLargestTransaction"
import { FieldTransactionJurisdictions as TransactionJurisdictions } from "./FieldTransactionJurisdictions"
import { FieldTransactionCurrencies as TransactionCurrencies } from "./FieldTransactionCurrencies"
import { FieldTransactionActivityType as TransactionActivityType } from "./FieldTransactionActivityType"
import { FieldMonthlyOutgoingTransactions as MonthlyOutgoingTransactions } from "./FieldMonthlyOutgoingTransactions"
import { FieldMonthlyNumberOfOutboundTransactions as MonthlyNumberOfOutboundTransactions } from "./FieldMonthlyNumberOfOutboundTransactions"
import { FieldMonthlyNumberOfInboundTransactions as MonthlyNumberOfInboundTransactions } from "./FieldMonthlyNumberOfInboundTransactions"
import { FieldMonthlyIncomingTransactions as MonthlyIncomingTransactions } from "./FieldMonthlyIncomingTransactions"

export const Fields = {
  TransactionCurrencies,
  TransactionJurisdictions,
  MonthlyNumberOfInboundTransactions,
  MonthlyNumberOfOutboundTransactions,
  MonthlyIncomingTransactions,
  MonthlyOutgoingTransactions,
  TransactionActivityType,
  ValueOfLargestTransaction,
}
