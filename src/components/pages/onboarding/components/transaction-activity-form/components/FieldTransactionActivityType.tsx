import { cn } from "@/lib/utils"
import { MultiSelector } from "@/components/base/form/searchable-selector"
import { FormField } from "@/components/base/form/form"

import { TransactionActivityFieldProps } from "../interface"

interface FieldTransactionActivityTypeProps
  extends TransactionActivityFieldProps {
  transactionActivityTypes: { value: string; label: string }[]
}

export function FieldTransactionActivityType({
  form,
  transactionActivityTypes,
  permission,
}: FieldTransactionActivityTypeProps) {
  return (
    <form.Field
      mode="array"
      name="activityTypes"
      validators={{
        onChange: ({ value }) => {
          if (Array.isArray(value) && value.length === 0) {
            return "Transaction activity type(s) is required"
          }

          return undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label={
              <span>
                <span>Transaction activity type(s)</span>
                <span className="ml-1 font-light">(Choose all that apply)</span>
              </span>
            }
            required
          >
            <MultiSelector
              disabled={!permission}
              items={transactionActivityTypes}
              onSelectedItemsChange={(selectedItems) =>
                field.setValue(selectedItems)
              }
              selectedItems={field.state.value}
              Trigger={{
                id: field.name,
                itemName: "type",
                placeholder: "Transaction activity type(s)",
                Tooltip: {
                  triggerClassName: cn("max-w-28"),
                  contentClassName: cn(
                    "mb-1 bg-white text-slate-900 ring-1 ring-slate-400/50",
                  ),
                },
              }}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
