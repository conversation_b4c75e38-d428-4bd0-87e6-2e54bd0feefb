import { Currency } from "@/lib/constants/currency.constants"
import { MultiSelector } from "@/components/base/form/searchable-selector"
import { FormField } from "@/components/base/form/form"

import { type TransactionActivityFieldProps } from "../interface"

interface FieldTransactionCurrenciesProps
  extends TransactionActivityFieldProps {
  currencies: { value: Currency; label: Currency }[]
}

export function FieldTransactionCurrencies({
  form,
  currencies,
  permission,
}: FieldTransactionCurrenciesProps) {
  return (
    <form.Field
      mode="array"
      name="expectedTransactionCurrencies"
      validators={{
        onChange: ({ value }) => {
          if (Array.isArray(value) && value.length === 0) {
            return "Transactions currencies is required"
          }

          return undefined
        },
      }}
    >
      {(rootField) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={rootField.name}
            field={rootField}
            label={
              <span>
                <span>Transaction currencies</span>
                <span className="ml-1 font-light">(Choose all that apply)</span>
              </span>
            }
            required
          >
            <MultiSelector
              disabled={!permission}
              items={currencies}
              onSelectedItemsChange={(selectedItems) =>
                rootField.setValue(selectedItems)
              }
              selectedItems={rootField.state.value}
              Trigger={{
                "aria-label": "Transaction currencies",
                placeholder: "Select all transaction currencies...",
                itemName: "currency",
                id: rootField.name,
              }}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
