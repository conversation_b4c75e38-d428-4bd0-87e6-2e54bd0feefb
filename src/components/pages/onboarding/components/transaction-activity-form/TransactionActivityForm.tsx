import { useRolePermissions } from "@/hooks/useRolePermissions"
import { IOnboardingExpectedTransactionActivity } from "@/data/onboarding/onboarding.interface"
import { FormLayout } from "@/components/base/form/form"

import { useTransactionActivityForm } from "./TransactionActivityForm.hook"
import { Fields } from "./components"
import { GoBack } from "../common/GoBack"
import { FormControls } from "../common/FormControls"

export interface TransactionActivityFormProps {
  onNext?: (payload: IOnboardingExpectedTransactionActivity) => void
  onInvalid?: () => void
  onBack?: () => void
}

export function TransactionActivityForm(props: TransactionActivityFormProps) {
  const { form, currencies, countries, transactionActivityTypes } =
    useTransactionActivityForm(props)
  const { getPermission } = useRolePermissions()
  const permission = getPermission("UserManagement.Add")
  return (
    <div
      aria-label="5. Transaction activity"
      className="container flex max-w-2xl flex-col gap-y-8 px-4"
    >
      {props.onBack && <GoBack onBack={props.onBack} />}

      <FormLayout
        className="mx-0 flex max-w-xl flex-col gap-y-7"
        title="5. Transaction activity"
      >
        <p className="text-muted-foreground">
          Please provide us with the transactional activity you intend on the
          multi-currency virtual bank account we will provide you.
        </p>
        <Fields.TransactionCurrencies
          currencies={currencies}
          form={form}
          permission={permission}
        />
        <Fields.TransactionJurisdictions
          countries={countries}
          form={form}
          permission={permission}
        />
        <Fields.MonthlyNumberOfInboundTransactions
          form={form}
          permission={permission}
        />
        <Fields.MonthlyNumberOfOutboundTransactions
          form={form}
          permission={permission}
        />
        <Fields.MonthlyIncomingTransactions
          form={form}
          permission={permission}
        />
        <Fields.MonthlyOutgoingTransactions
          form={form}
          permission={permission}
        />
        <Fields.ValueOfLargestTransaction form={form} permission={permission} />
        <Fields.TransactionActivityType
          form={form}
          permission={permission}
          transactionActivityTypes={transactionActivityTypes}
        />
        <FormControls disabled={!permission} form={form} />
      </FormLayout>
    </div>
  )
}
