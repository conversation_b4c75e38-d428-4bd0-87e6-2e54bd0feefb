import { describe, expect, it, Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import {
  QueryClient,
  QueryClientProvider,
  useSuspenseQuery,
} from "@tanstack/react-query"

import { useGlobalStore } from "@/data/global/global.store"
import {
  TransactionActivityForm,
  TransactionActivityFormProps,
} from "@/components/pages/onboarding/components/transaction-activity-form"

vi.mock("@tanstack/react-query", () => ({
  useSuspenseQuery: vi.fn(() => ({
    data: {},
  })),
  QueryClient: vi.fn(),
  useQueryClient: vi.fn(),
  useMutation: vi.fn(() => ({
    mutate: vi.fn(),
  })),
  useQuery: vi.fn(() => ({
    isLoading: false,
  })),
  queryOptions: vi.fn(),
  QueryClientProvider: ({ children }: any) => <>{children}</>,
}))

vi.mock("@/data/onboarding/transaction-activity.query", () => ({
  useExpectedTransactionActivitySuspenseQry: vi.fn(() => ({
    data: {
      id: "1",
      name: "Test Entity",
    },
    isLoading: false,
    isError: false,
  })),
}))

vi.mock("@tanstack/react-router", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
    staticDataCurrencies: [],
    staticDataCountries: [],
    staticDataTransactionActivityTypes: [],
  })),
}))

const mockGlobalStore = {
  entity: { id: "test-entity", name: "Argentex" },
}

vi.mock("@/components/base/currency/CurrencyInput", () => ({
  CurrencyInput: () => vi.fn(),
  CoreCurrencyInput: (props: any) => <input {...props} />,
}))

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(() => mockGlobalStore),
  useCurrenciesQuery: vi.fn(),
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

const setup = (props?: Partial<TransactionActivityFormProps>) => {
  const user = userEvent.setup()

  ;(useSuspenseQuery as Mock).mockReturnValue({
    isLoading: false,
    data: {
      id: "test-entity",
    },
  })

  const form = renderWithQueryClient(<TransactionActivityForm {...props} />)

  // title
  const formHeader = screen.getByText("5. Transaction activity")

  window.HTMLElement.prototype.scrollIntoView = vi.fn()
  window.HTMLElement.prototype.hasPointerCapture = vi.fn()

  const [
    transactionCurrencies,
    transactionJurisdictions,
    numberOfInTransactionsPerMonth,
    numberOfOutTransactionsPerMonth,
    averageMonthlyValueOfInboundTransactions,
    averageMonthlyValueOfOutboundTransactions,
    largestExpectedTransaction,
    transactionActivityType,
    nextBtn,
  ] = [
    screen.getByLabelText("Transaction currencies"),
    screen.getByLabelText("Transaction jurisdictions"),
    screen.getByLabelText("Number of inbound transactions per month", {
      exact: false,
    }),
    screen.getByLabelText("Number of outbound transactions per month", {
      exact: false,
    }),
    screen.getByLabelText("Average value of inbound transactions per month"),
    screen.getByLabelText("Average value of outbound transactions per month"),
    screen.getByLabelText("Value of your largest expected transaction"),
    screen.getByLabelText("Transaction activity type(s)"),
    screen.getByRole("button", {
      name: "Save & next",
    }),
  ]

  return {
    formHeader,
    transactionCurrencies,
    transactionJurisdictions,
    numberOfInTransactionsPerMonth,
    numberOfOutTransactionsPerMonth,
    averageMonthlyValueOfInboundTransactions,
    averageMonthlyValueOfOutboundTransactions,
    largestExpectedTransaction,
    transactionActivityType,
    nextBtn,
    ...form,
    user,
  } as const
}

describe("TransactionActivityForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useGlobalStore as any).mockImplementation(
      (
        selector: (state: {
          setOnboardingHasUnsavedChanges: any
          setOnboardingSeenState: any
        }) => unknown,
      ) =>
        selector({
          setOnboardingHasUnsavedChanges: vi.fn(),
          setOnboardingSeenState: vi.fn(),
        }),
    )
  })

  it("Should show the form", () => {
    const {
      formHeader,
      transactionCurrencies,
      transactionJurisdictions,
      numberOfInTransactionsPerMonth,
      numberOfOutTransactionsPerMonth,
      averageMonthlyValueOfInboundTransactions,
      averageMonthlyValueOfOutboundTransactions,
      largestExpectedTransaction,
      transactionActivityType,
      nextBtn,
    } = setup()

    expect(formHeader).toBeInTheDocument()
    expect(transactionCurrencies).toBeInTheDocument()
    expect(transactionCurrencies).toBeInTheDocument()
    expect(transactionJurisdictions).toBeInTheDocument()
    expect(numberOfInTransactionsPerMonth).toBeInTheDocument()
    expect(numberOfOutTransactionsPerMonth).toBeInTheDocument()
    expect(averageMonthlyValueOfInboundTransactions).toBeInTheDocument()
    expect(averageMonthlyValueOfOutboundTransactions).toBeInTheDocument()
    expect(largestExpectedTransaction).toBeInTheDocument()
    expect(transactionActivityType).toBeInTheDocument()
    expect(nextBtn).toBeInTheDocument()
  })
})
