import { describe, it, expect } from "vitest"

import { IOnboardingExpectedTransactionActivity } from "@/data/onboarding/onboarding.interface"
import { validateTransactionActivityForm } from "@/components/pages/onboarding/components/transaction-activity-form/validator"

describe("Transaction Activity Form Validator", () => {
  const validTransactionActivity: IOnboardingExpectedTransactionActivity = {
    activityBaseCurrency: "GBP",
    activityTypes: ["PAYMENTS", "FX_TRADING"],
    expectedTransactionCurrencies: ["GBP", "EUR"],
    expectedTransactionJurisdictions: ["GB", "EU"],
    maximumTransactionSize: 100000,
    yearlyNumberOfPaymentsInbound: 50,
    yearlyNumberOfPaymentsOutbound: 75,
    yearlyValueOfPaymentsInbound: 500000,
    yearlyValueOfPaymentsOutbound: 750000,
  }

  describe("validateTransactionActivityForm", () => {
    it("should return true for valid transaction activity data", () => {
      const result = validateTransactionActivityForm(validTransactionActivity)
      expect(result).toBe(true)
    })

    it("should return false when activityBaseCurrency is missing", () => {
      const invalidData = {
        ...validTransactionActivity,
        activityBaseCurrency: "",
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when activityBaseCurrency is undefined", () => {
      const invalidData = {
        ...validTransactionActivity,
        activityBaseCurrency: undefined as any,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when activityTypes is empty", () => {
      const invalidData = {
        ...validTransactionActivity,
        activityTypes: [],
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when activityTypes is undefined", () => {
      const invalidData = {
        ...validTransactionActivity,
        activityTypes: undefined as any,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when expectedTransactionCurrencies is empty", () => {
      const invalidData = {
        ...validTransactionActivity,
        expectedTransactionCurrencies: [],
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when expectedTransactionJurisdictions is empty", () => {
      const invalidData = {
        ...validTransactionActivity,
        expectedTransactionJurisdictions: [],
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when maximumTransactionSize is 0", () => {
      const invalidData = {
        ...validTransactionActivity,
        maximumTransactionSize: 0,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when maximumTransactionSize is negative", () => {
      const invalidData = {
        ...validTransactionActivity,
        maximumTransactionSize: -1000,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when yearlyNumberOfPaymentsInbound is 0", () => {
      const invalidData = {
        ...validTransactionActivity,
        yearlyNumberOfPaymentsInbound: 0,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when yearlyNumberOfPaymentsOutbound is 0", () => {
      const invalidData = {
        ...validTransactionActivity,
        yearlyNumberOfPaymentsOutbound: 0,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when yearlyValueOfPaymentsInbound is 0", () => {
      const invalidData = {
        ...validTransactionActivity,
        yearlyValueOfPaymentsInbound: 0,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when yearlyValueOfPaymentsOutbound is 0", () => {
      const invalidData = {
        ...validTransactionActivity,
        yearlyValueOfPaymentsOutbound: 0,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when data is undefined", () => {
      const result = validateTransactionActivityForm(undefined)
      expect(result).toBe(false)
    })

    it("should handle multiple invalid fields", () => {
      const invalidData = {
        ...validTransactionActivity,
        activityBaseCurrency: "",
        activityTypes: [],
        maximumTransactionSize: 0,
        yearlyNumberOfPaymentsInbound: 0,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return true with minimum valid values", () => {
      const minimalValidData = {
        ...validTransactionActivity,
        maximumTransactionSize: 1,
        yearlyNumberOfPaymentsInbound: 1,
        yearlyNumberOfPaymentsOutbound: 1,
        yearlyValueOfPaymentsInbound: 1,
        yearlyValueOfPaymentsOutbound: 1,
      }
      const result = validateTransactionActivityForm(minimalValidData)
      expect(result).toBe(true)
    })

    it("should handle negative payment values", () => {
      const invalidData = {
        ...validTransactionActivity,
        yearlyNumberOfPaymentsInbound: -5,
        yearlyValueOfPaymentsInbound: -1000,
      }
      const result = validateTransactionActivityForm(invalidData)
      expect(result).toBe(false)
    })

    it("should handle single item arrays", () => {
      const validDataWithSingleItems = {
        ...validTransactionActivity,
        activityTypes: ["PAYMENTS"],
        expectedTransactionCurrencies: ["GBP"],
        expectedTransactionJurisdictions: ["GB"],
      }
      const result = validateTransactionActivityForm(validDataWithSingleItems)
      expect(result).toBe(true)
    })
  })
})
