import { HttpStatusCode } from "axios"

import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { assertResponse } from "@/data/onboarding/_exception-handler"
import { ServiceUnavailableError } from "@/data/global/global.exceptions"

export default function useEntityHasSubmitted() {
  const prefetchData = useLoaderData()
  const { data } = useEntityAccessQuery(prefetchData.entityId)

  assertResponse(data, {
    [HttpStatusCode.ServiceUnavailable]: new ServiceUnavailableError(
      "[useEntityHasSubmitted]: Unable to retrieve entityAccess",
    ),
  })

  return !!data?.find(
    (entity) =>
      entity.entityId === prefetchData.entityId &&
      ["Client", "Onboarding", "ReadyForOnboarding"].includes(
        entity.entityStatus!,
      ),
  )
}
