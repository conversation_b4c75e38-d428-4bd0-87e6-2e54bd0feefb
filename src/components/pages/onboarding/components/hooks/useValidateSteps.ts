import { useGetAllQueries } from "@/data/onboarding/common.query"
import { ValidationState } from "@/data/global/global.store"

import reviewUsersAndSignatories from "../users-and-signatories-form/validator"
import { getReviewUsersAndSignatories } from "../users-and-signatories-form/UsersAndSignatoriesForm.hook"
import reviewTransactionActivity from "../transaction-activity-form/validator"
import { getReviewTransactionActivity } from "../transaction-activity-form/TransactionActivityForm.hook"
import reviewOwnershipControl from "../ownership-control-form/validator"
import { getReviewOwnershipControl } from "../ownership-control-form/OwnershipControlForm.hook"
import reviewFurtherDetails from "../further-details-form/validator"
import { getReviewFurtherDetails } from "../further-details-form/FurtherDetailsForm.hook"
import { FormStepId } from "../FormSteps"
import reviewFinancialInformation from "../financial-information-form/validator"
import { getReviewFinancialInformation } from "../financial-information-form/FinancialInformationForm.hook"
import reviewEntityDetails from "../entity-details-form/validator"
import { getReviewEntityDetails } from "../entity-details-form/EntityDetailsForm.hook"
import reviewApplicantDetails from "../applicant-details-form/validator"
import { getReviewApplicantDetails } from "../applicant-details-form/ApplicantDetailsForm.hook"

export function useValidateSteps() {
  const {
    staticData,
    entity,
    addresses,
    proofOfExistenceDocs,
    ownershipDocs,
    financialDocs,
    expectedTransactions,
    users,
    applicantDetailsUser,
    applicantDetailsUserAddresses,
    applicantDetailsUserDocs,
    financialInfoSourcesOfFunds,
    isError,
    isFetching,
    isLoading,
    isSuccess,
    isFetched,
    refetch,
  } = useGetAllQueries()

  const entityDetails = getReviewEntityDetails({
    staticData,
    entity,
    addresses,
  })

  const furtherDetails = getReviewFurtherDetails({
    staticData,
    entity,
    proofOfExistenceDocs,
  })

  const ownershipControl = getReviewOwnershipControl(ownershipDocs)

  const financialInformation = getReviewFinancialInformation(
    staticData.staticSourceOfFunds,
    financialDocs,
    financialInfoSourcesOfFunds,
  )

  const transactionActivity = getReviewTransactionActivity(expectedTransactions)

  const usersAndSignatories = getReviewUsersAndSignatories(users)

  const applicantDetails = getReviewApplicantDetails({
    staticData,
    applicantDetailsUser,
    applicantDetailsUserAddresses,
    applicantDetailsUserDocs,
  })

  const steps = {
    entityDetailsForm: reviewEntityDetails(entityDetails),
    furtherDetailsForm: reviewFurtherDetails(furtherDetails),
    ownershipAndControlForm: reviewOwnershipControl(ownershipControl),
    financialInformationForm: reviewFinancialInformation(financialInformation),
    transactionActivityForm: reviewTransactionActivity(transactionActivity),
    usersAndSignatoriesForm: reviewUsersAndSignatories(usersAndSignatories),
    applicantDetailsForm: reviewApplicantDetails(applicantDetails),
  } as ValidationState

  const isStepValid = (step: FormStepId) => {
    const stepName = FormStepId[step] as keyof ValidationState

    return Boolean(steps[stepName])
  }

  return {
    isError,
    isFetching,
    isLoading,
    isSuccess,
    isFetched,
    form: {
      entityDetails,
      furtherDetails,
      ownershipControl,
      financialInformation,
      transactionActivity,
      usersAndSignatories,
      applicantDetails,
    },
    steps,
    isStepValid,
    refetch,
  }
}
