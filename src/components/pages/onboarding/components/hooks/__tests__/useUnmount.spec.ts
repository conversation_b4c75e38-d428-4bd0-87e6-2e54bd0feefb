import { describe, it, expect, vi } from "vitest"
import { renderHook } from "@testing-library/react"

import { useUnmount } from "@/components/pages/onboarding/components/hooks/useUnmount"

describe("useUnmount Hook", () => {
  it("should call cleanup function when component unmounts", () => {
    const cleanupFn = vi.fn()

    const { unmount } = renderHook(() => useUnmount(cleanupFn))

    // Cleanup function should not be called during mount
    expect(cleanupFn).not.toHaveBeenCalled()

    // Unmount the component
    unmount()

    // Cleanup function should be called once on unmount
    expect(cleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should not call cleanup function during re-renders", () => {
    const cleanupFn = vi.fn()

    const { rerender } = renderHook(() => useUnmount(cleanupFn))

    // Re-render multiple times
    rerender()
    rerender()
    rerender()

    // Cleanup function should not be called during re-renders
    expect(cleanupFn).not.toHaveBeenCalled()
  })

  it("should call the latest cleanup function when unmounting", () => {
    const firstCleanupFn = vi.fn()
    const secondCleanupFn = vi.fn()

    const { rerender, unmount } = renderHook(
      ({ cleanupFn }) => useUnmount(cleanupFn),
      {
        initialProps: { cleanupFn: firstCleanupFn },
      },
    )

    // Update the cleanup function
    rerender({ cleanupFn: secondCleanupFn })

    // Unmount the component
    unmount()

    // Only the latest cleanup function should be called
    expect(firstCleanupFn).not.toHaveBeenCalled()
    expect(secondCleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should handle multiple cleanup functions correctly", () => {
    const cleanupFn1 = vi.fn()
    const cleanupFn2 = vi.fn()
    const cleanupFn3 = vi.fn()

    const { rerender, unmount } = renderHook(
      ({ cleanupFn }) => useUnmount(cleanupFn),
      {
        initialProps: { cleanupFn: cleanupFn1 },
      },
    )

    // Update cleanup function multiple times
    rerender({ cleanupFn: cleanupFn2 })
    rerender({ cleanupFn: cleanupFn3 })

    // Unmount the component
    unmount()

    // Only the final cleanup function should be called
    expect(cleanupFn1).not.toHaveBeenCalled()
    expect(cleanupFn2).not.toHaveBeenCalled()
    expect(cleanupFn3).toHaveBeenCalledTimes(1)
  })

  it("should handle cleanup function that throws an error", () => {
    const errorCleanupFn = vi.fn(() => {
      throw new Error("Cleanup error")
    })

    const { unmount } = renderHook(() => useUnmount(errorCleanupFn))

    // The actual implementation does throw when cleanup function throws
    expect(() => unmount()).toThrow("Cleanup error")

    // Cleanup function should still be called
    expect(errorCleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should handle async cleanup function", () => {
    const asyncCleanupFn = vi.fn(async () => {
      await new Promise((resolve) => setTimeout(resolve, 10))
      return "cleanup complete"
    })

    const { unmount } = renderHook(() => useUnmount(asyncCleanupFn))

    unmount()

    // Async cleanup function should be called
    expect(asyncCleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should handle cleanup function with side effects", () => {
    let sideEffectValue = 0
    const sideEffectCleanupFn = vi.fn(() => {
      sideEffectValue = 42
    })

    const { unmount } = renderHook(() => useUnmount(sideEffectCleanupFn))

    expect(sideEffectValue).toBe(0)

    unmount()

    expect(sideEffectCleanupFn).toHaveBeenCalledTimes(1)
    expect(sideEffectValue).toBe(42)
  })

  it("should work with arrow function cleanup", () => {
    const cleanupFn = vi.fn(() => console.log("cleanup"))

    const { unmount } = renderHook(() => useUnmount(cleanupFn))

    unmount()

    expect(cleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should work with regular function cleanup", () => {
    const cleanupFn = vi.fn(function cleanup() {
      console.log("cleanup")
    })

    const { unmount } = renderHook(() => useUnmount(cleanupFn))

    unmount()

    expect(cleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should handle empty cleanup function", () => {
    const emptyCleanupFn = vi.fn(() => {})

    const { unmount } = renderHook(() => useUnmount(emptyCleanupFn))

    expect(() => unmount()).not.toThrow()
    expect(emptyCleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should work correctly across re-renders", () => {
    const cleanupFn = vi.fn()

    const { rerender, unmount } = renderHook(() => {
      useUnmount(cleanupFn)
    })

    // Re-render with same cleanup function
    rerender()

    // Cleanup function should not be called during re-renders
    expect(cleanupFn).not.toHaveBeenCalled()

    unmount()
    expect(cleanupFn).toHaveBeenCalledTimes(1)
  })

  it("should handle rapid mount/unmount cycles", () => {
    const cleanupFn = vi.fn()

    // Mount and unmount multiple times
    for (let i = 0; i < 5; i++) {
      const { unmount } = renderHook(() => useUnmount(cleanupFn))
      unmount()
    }

    // Cleanup function should be called once for each mount/unmount cycle
    expect(cleanupFn).toHaveBeenCalledTimes(5)
  })

  it("should work in strict mode (double effect execution)", () => {
    const cleanupFn = vi.fn()

    // Simulate React strict mode by manually calling the effect twice
    const { unmount } = renderHook(() => useUnmount(cleanupFn))

    // In strict mode, effects run twice in development
    // but cleanup should still only run once on unmount
    unmount()

    expect(cleanupFn).toHaveBeenCalledTimes(1)
  })
})
