import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"

import { useEntityAccessQuery } from "@/data/onboarding/entity-access.query"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import useEntityHasSubmitted from "@/components/pages/onboarding/components/hooks/useEntityHasSubmitted"

// Mock dependencies
vi.mock("@/data/onboarding/entity-access.query")
vi.mock("@/data/onboarding/$entityId.loader")
vi.mock("@/data/onboarding/_exception-handler", () => ({
  assertResponse: vi.fn(),
}))

describe("useEntityHasSubmitted Hook", () => {
  const mockEntityId = "entity-123"
  const mockLoaderData = {
    entityId: mockEntityId,
  } as any

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useLoaderData).mockReturnValue(mockLoaderData)
  })

  it("should return true when entity has submitted status", () => {
    const mockEntityAccess = [
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: "Client",
        roles: ["Admin"],
      },
      {
        entityId: "other-entity",
        entityName: "Other Entity",
        entityStatus: "PreSubmission",
        roles: ["User"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(true)
  })

  it("should return true for Onboarding status", () => {
    const mockEntityAccess = [
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: "Onboarding",
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(true)
  })

  it("should return true for ReadyForOnboarding status", () => {
    const mockEntityAccess = [
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: "ReadyForOnboarding",
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(true)
  })

  it("should return false when entity has not submitted", () => {
    const mockEntityAccess = [
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: "PreSubmission",
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(false)
  })

  it("should return false when entity is not found", () => {
    const mockEntityAccess = [
      {
        entityId: "different-entity",
        entityName: "Different Entity",
        entityStatus: "Client",
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(false)
  })

  it("should return false when entityAccess is empty", () => {
    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: [],
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(false)
  })

  it("should handle multiple entities and find the correct one", () => {
    const mockEntityAccess = [
      {
        entityId: "entity-1",
        entityName: "Entity 1",
        entityStatus: "PreSubmission",
        roles: ["User"],
      },
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: "Client",
        roles: ["Admin"],
      },
      {
        entityId: "entity-3",
        entityName: "Entity 3",
        entityStatus: "Rejected",
        roles: ["User"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(true)
  })

  it("should handle null data gracefully", () => {
    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: null,
    } as any)

    // The assertResponse mock will handle the error throwing
    expect(() => {
      renderHook(() => useEntityHasSubmitted())
    }).not.toThrow()
  })

  it("should handle undefined data gracefully", () => {
    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: undefined,
    } as any)

    // The assertResponse mock will handle the error throwing
    expect(() => {
      renderHook(() => useEntityHasSubmitted())
    }).not.toThrow()
  })

  it("should handle entity with null entityStatus", () => {
    const mockEntityAccess = [
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: null,
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(false)
  })

  it("should handle entity with undefined entityStatus", () => {
    const mockEntityAccess = [
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: undefined,
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    const { result } = renderHook(() => useEntityHasSubmitted())

    expect(result.current).toBe(false)
  })

  it("should call useEntityAccessQuery with correct entityId", () => {
    const mockEntityAccess = [
      {
        entityId: mockEntityId,
        entityName: "Test Entity",
        entityStatus: "Client",
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQuery).mockReturnValue({
      data: mockEntityAccess,
    } as any)

    renderHook(() => useEntityHasSubmitted())

    expect(useEntityAccessQuery).toHaveBeenCalledWith(mockEntityId)
  })

  it("should handle different entity statuses correctly", () => {
    const testCases = [
      { status: "Client", expected: true },
      { status: "Onboarding", expected: true },
      { status: "ReadyForOnboarding", expected: true },
      { status: "PreSubmission", expected: false },
      { status: "Rejected", expected: false },
      { status: "Suspended", expected: false },
      { status: "Active", expected: false },
    ]

    testCases.forEach(({ status, expected }) => {
      const mockEntityAccess = [
        {
          entityId: mockEntityId,
          entityName: "Test Entity",
          entityStatus: status,
          roles: ["Admin"],
        },
      ]

      vi.mocked(useEntityAccessQuery).mockReturnValue({
        data: mockEntityAccess,
      } as any)

      const { result } = renderHook(() => useEntityHasSubmitted())

      expect(result.current).toBe(expected)
    })
  })
})
