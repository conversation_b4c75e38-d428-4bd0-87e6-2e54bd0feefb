import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"
import { FormApi } from "@tanstack/react-form"

import { useGlobalStore } from "@/data/global/global.store"
import useFormBlockWithUnsavedChanges from "@/components/pages/onboarding/components/hooks/useWarnWithUnsavedChanges"

// Mock the global store
vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(),
}))

describe("useFormBlockWithUnsavedChanges Hook", () => {
  let mockForm: Partial<FormApi<any, any>>
  let mockSetHasUnsavedChanges: ReturnType<typeof vi.fn>
  let mockSubscribe: ReturnType<typeof vi.fn>
  let mockUnsubscribe: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()

    mockSetHasUnsavedChanges = vi.fn()
    mockUnsubscribe = vi.fn()
    mockSubscribe = vi.fn(() => mockUnsubscribe)

    mockForm = {
      store: {
        state: {
          isDirty: false,
        },
        subscribe: mockSubscribe,
      },
    } as any

    vi.mocked(useGlobalStore).mockReturnValue(mockSetHasUnsavedChanges)
  })

  it("should initialize and subscribe to form store", () => {
    mockForm.store!.state.isDirty = true

    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    expect(mockSubscribe).toHaveBeenCalledTimes(1)
    // The hook doesn't call setHasUnsavedChanges on mount, only in subscription
    expect(mockSetHasUnsavedChanges).not.toHaveBeenCalled()
  })

  it("should subscribe to form store changes", () => {
    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    expect(mockSubscribe).toHaveBeenCalledTimes(1)
    expect(typeof mockSubscribe.mock.calls[0][0]).toBe("function")
  })

  it("should call setHasUnsavedChanges when form becomes dirty", () => {
    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    // Get the subscription callback
    const subscriptionCallback = mockSubscribe.mock.calls[0][0]

    // Simulate form becoming dirty
    mockForm.store!.state.isDirty = true
    subscriptionCallback()

    expect(mockSetHasUnsavedChanges).toHaveBeenCalledWith(true)
  })

  it("should call setHasUnsavedChanges when form becomes clean", () => {
    // Start with dirty form
    mockForm.store!.state.isDirty = true

    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    const subscriptionCallback = mockSubscribe.mock.calls[0][0]

    // Simulate form becoming clean
    mockForm.store!.state.isDirty = false
    subscriptionCallback()

    expect(mockSetHasUnsavedChanges).toHaveBeenCalledWith(false)
  })

  it("should handle multiple state changes", () => {
    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    const subscriptionCallback = mockSubscribe.mock.calls[0][0]

    // Multiple state changes
    mockForm.store!.state.isDirty = true
    subscriptionCallback()

    mockForm.store!.state.isDirty = false
    subscriptionCallback()

    mockForm.store!.state.isDirty = true
    subscriptionCallback()

    expect(mockSetHasUnsavedChanges).toHaveBeenCalledTimes(3) // Only subscription calls, no initial call
    expect(mockSetHasUnsavedChanges).toHaveBeenNthCalledWith(1, true)
    expect(mockSetHasUnsavedChanges).toHaveBeenNthCalledWith(2, false)
    expect(mockSetHasUnsavedChanges).toHaveBeenNthCalledWith(3, true)
  })

  it("should unsubscribe when component unmounts", () => {
    const { unmount } = renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    expect(mockUnsubscribe).not.toHaveBeenCalled()

    unmount()

    expect(mockUnsubscribe).toHaveBeenCalledTimes(1)
  })

  it("should handle form store changes when dependencies change", () => {
    const { rerender } = renderHook(
      ({ form }) => useFormBlockWithUnsavedChanges(form),
      {
        initialProps: { form: mockForm as FormApi<any, any> },
      },
    )

    expect(mockSubscribe).toHaveBeenCalledTimes(1)

    // Create new form with different store
    const newMockForm = {
      store: {
        state: { isDirty: true },
        subscribe: vi.fn(() => vi.fn()),
      },
    } as any

    rerender({ form: newMockForm })

    // Should subscribe to new form store
    expect(newMockForm.store.subscribe).toHaveBeenCalledTimes(1)
    // Should unsubscribe from old form store
    expect(mockUnsubscribe).toHaveBeenCalledTimes(1)
  })

  it("should handle setHasUnsavedChanges function changes", () => {
    const newMockSetHasUnsavedChanges = vi.fn()

    const { rerender } = renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    // Change the setHasUnsavedChanges function
    vi.mocked(useGlobalStore).mockReturnValue(newMockSetHasUnsavedChanges)

    rerender()

    const subscriptionCallback = mockSubscribe.mock.calls[1][0]
    mockForm.store!.state.isDirty = true
    subscriptionCallback()

    expect(newMockSetHasUnsavedChanges).toHaveBeenCalledWith(true)
  })

  it("should convert isDirty to boolean correctly", () => {
    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    const subscriptionCallback = mockSubscribe.mock.calls[0][0]

    // Test truthy values
    mockForm.store!.state.isDirty = "truthy" as any
    subscriptionCallback()
    expect(mockSetHasUnsavedChanges).toHaveBeenCalledWith(true)

    // Test falsy values
    mockForm.store!.state.isDirty = 0 as any
    subscriptionCallback()
    expect(mockSetHasUnsavedChanges).toHaveBeenCalledWith(false)

    mockForm.store!.state.isDirty = null as any
    subscriptionCallback()
    expect(mockSetHasUnsavedChanges).toHaveBeenCalledWith(false)

    mockForm.store!.state.isDirty = undefined as any
    subscriptionCallback()
    expect(mockSetHasUnsavedChanges).toHaveBeenCalledWith(false)
  })

  it("should handle form with initially undefined isDirty", () => {
    mockForm.store!.state.isDirty = undefined as any

    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    // Hook doesn't call setHasUnsavedChanges on mount, only in subscription
    expect(mockSetHasUnsavedChanges).not.toHaveBeenCalled()
  })

  it("should handle subscription callback errors gracefully", () => {
    mockSetHasUnsavedChanges.mockImplementation(() => {
      throw new Error("Global store error")
    })

    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    const subscriptionCallback = mockSubscribe.mock.calls[0][0]

    // Should not throw when subscription callback encounters error
    expect(() => {
      mockForm.store!.state.isDirty = true
      subscriptionCallback()
    }).toThrow("Global store error")
  })

  it("should work with different form types", () => {
    interface TestFormData {
      name: string
      email: string
    }

    const typedMockForm = {
      store: {
        state: { isDirty: false },
        subscribe: vi.fn(() => vi.fn()),
      },
    } as unknown as FormApi<TestFormData, any>

    renderHook(() => useFormBlockWithUnsavedChanges(typedMockForm))

    expect(typedMockForm.store.subscribe).toHaveBeenCalledTimes(1)
  })

  it("should maintain ref stability across re-renders", () => {
    const { rerender } = renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    const firstSubscribeCall = mockSubscribe.mock.calls[0][0]

    rerender()

    // Check if there's a second subscribe call (depends on implementation)
    if (mockSubscribe.mock.calls.length > 1) {
      const secondSubscribeCall = mockSubscribe.mock.calls[1][0]
      // The subscription callback should be different due to useEffect re-running
      expect(firstSubscribeCall).not.toBe(secondSubscribeCall)
    } else {
      // If no second call, the effect didn't re-run (which is also valid)
      expect(mockSubscribe.mock.calls).toHaveLength(1)
    }
  })

  it("should handle rapid form state changes", () => {
    renderHook(() =>
      useFormBlockWithUnsavedChanges(mockForm as FormApi<any, any>),
    )

    const subscriptionCallback = mockSubscribe.mock.calls[0][0]

    // Rapid state changes
    for (let i = 0; i < 10; i++) {
      mockForm.store!.state.isDirty = i % 2 === 0
      subscriptionCallback()
    }

    expect(mockSetHasUnsavedChanges).toHaveBeenCalledTimes(10) // Only subscription calls, no initial call
  })
})
