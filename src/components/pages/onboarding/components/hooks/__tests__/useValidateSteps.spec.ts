import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"

import { useGetAllQueries } from "@/data/onboarding/common.query"
import { useValidateSteps } from "@/components/pages/onboarding/components/hooks/useValidateSteps"

// Mock all dependencies
vi.mock("@/data/onboarding/common.query", () => ({
  useGetAllQueries: vi.fn(),
}))

// Mock all validator functions
vi.mock(
  "@/components/pages/onboarding/components/users-and-signatories-form/validator",
  () => ({
    default: vi.fn(),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/transaction-activity-form/validator",
  () => ({
    default: vi.fn(),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/ownership-control-form/validator",
  () => ({
    default: vi.fn(),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/further-details-form/validator",
  () => ({
    default: vi.fn(),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/financial-information-form/validator",
  () => ({
    default: vi.fn(),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/entity-details-form/validator",
  () => ({
    default: vi.fn(),
  }),
)

vi.mock(
  "@/components/pages/onboarding/components/applicant-details-form/validator",
  () => ({
    default: vi.fn(),
  }),
)

// Mock all hook functions - simplified approach
vi.mock(
  "@/components/pages/onboarding/components/users-and-signatories-form/UsersAndSignatoriesForm.hook",
)
vi.mock(
  "@/components/pages/onboarding/components/transaction-activity-form/TransactionActivityForm.hook",
)
vi.mock(
  "@/components/pages/onboarding/components/ownership-control-form/OwnershipControlForm.hook",
)
vi.mock(
  "@/components/pages/onboarding/components/further-details-form/FurtherDetailsForm.hook",
)
vi.mock(
  "@/components/pages/onboarding/components/financial-information-form/FinancialInformationForm.hook",
)
vi.mock(
  "@/components/pages/onboarding/components/entity-details-form/EntityDetailsForm.hook",
)
vi.mock(
  "@/components/pages/onboarding/components/applicant-details-form/ApplicantDetailsForm.hook",
)

describe("useValidateSteps Hook", () => {
  const mockQueryData = {
    staticData: {},
    entity: {},
    addresses: [],
    proofOfExistenceDocs: [],
    ownershipDocs: [],
    financialDocs: [],
    expectedTransactions: {},
    users: [],
    applicantDetailsUser: {},
    applicantDetailsUserAddresses: [],
    applicantDetailsUserDocs: [],
    isError: false,
    isFetching: false,
    isLoading: false,
    isSuccess: true,
    isFetched: true,
    refetch: vi.fn(),
  } as any

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useGetAllQueries).mockReturnValue(mockQueryData)
  })

  it("should return query states correctly", () => {
    const { result } = renderHook(() => useValidateSteps())

    expect(result.current.isError).toBe(false)
    expect(result.current.isFetching).toBe(false)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isSuccess).toBe(true)
    expect(result.current.isFetched).toBe(true)
    expect(typeof result.current.refetch).toBe("function")
  })

  it("should return form data structure", () => {
    const { result } = renderHook(() => useValidateSteps())

    expect(result.current.form).toBeDefined()
    expect(typeof result.current.form).toBe("object")
  })

  it("should return validation steps structure", () => {
    const { result } = renderHook(() => useValidateSteps())

    expect(result.current.steps).toBeDefined()
    expect(typeof result.current.steps).toBe("object")
  })

  it("should have isStepValid function", () => {
    const { result } = renderHook(() => useValidateSteps())

    expect(typeof result.current.isStepValid).toBe("function")
  })

  it("should handle loading state", () => {
    const loadingData = {
      ...mockQueryData,
      isLoading: true,
      isSuccess: false,
      isFetched: false,
    }
    vi.mocked(useGetAllQueries).mockReturnValue(loadingData)

    const { result } = renderHook(() => useValidateSteps())

    expect(result.current.isLoading).toBe(true)
    expect(result.current.isSuccess).toBe(false)
    expect(result.current.isFetched).toBe(false)
  })

  it("should handle error state", () => {
    const errorData = { ...mockQueryData, isError: true, isSuccess: false }
    vi.mocked(useGetAllQueries).mockReturnValue(errorData)

    const { result } = renderHook(() => useValidateSteps())

    expect(result.current.isError).toBe(true)
    expect(result.current.isSuccess).toBe(false)
  })

  it("should handle fetching state", () => {
    const fetchingData = { ...mockQueryData, isFetching: true }
    vi.mocked(useGetAllQueries).mockReturnValue(fetchingData)

    const { result } = renderHook(() => useValidateSteps())

    expect(result.current.isFetching).toBe(true)
  })

  it("should call refetch function", () => {
    const mockRefetch = vi.fn()
    const dataWithRefetch = { ...mockQueryData, refetch: mockRefetch }
    vi.mocked(useGetAllQueries).mockReturnValue(dataWithRefetch)

    const { result } = renderHook(() => useValidateSteps())

    result.current.refetch()

    expect(mockRefetch).toHaveBeenCalledTimes(1)
  })
})
