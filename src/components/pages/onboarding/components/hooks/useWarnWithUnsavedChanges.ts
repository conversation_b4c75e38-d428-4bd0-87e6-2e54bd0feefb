import { useEffect, useRef } from "react"
import { FormApi } from "@tanstack/react-form"

import { useGlobalStore } from "@/data/global/global.store"

export default function useFormBlockWithUnsavedChanges(
  form: FormApi<any, any>,
) {
  const hasUnsavedChanges = useRef(form.store.state.isDirty)
  const setHasUnsavedChanges = useGlobalStore(
    (state) => state.setOnboardingHasUnsavedChanges,
  )

  useEffect(() => {
    return form.store.subscribe(() => {
      const { isDirty } = form.store.state
      hasUnsavedChanges.current = isDirty

      setHasUnsavedChanges(!!isDirty)
    })
  }, [form.store, setHasUnsavedChanges])
}
