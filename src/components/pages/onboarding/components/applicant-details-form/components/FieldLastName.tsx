import { required } from "@/lib/form.utils"
import { Input } from "@/components/ui/input"
import { FormField } from "@/components/base/form/form"

import { type ApplicantDetailsFieldProps } from "../interface"

export function FieldLastName({ form }: ApplicantDetailsFieldProps) {
  return (
    <form.Field
      name="lastName"
      validators={{
        onChange: ({ value }) => required(value, "Last name is required"),
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Last name"
            required
          >
            <Input
              className="bg-background p-4 py-6 md:!text-base"
              id={field.name}
              onChange={(e) => field.handleChange(e.target.value)}
              type="text"
              value={field.state.value}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
