import { useStore } from "@tanstack/react-store"

import { required } from "@/lib/form.utils"
import { Country } from "@/lib/constants/country.constants"
import { FormField } from "@/components/base/form/form"
import { GetCountryListDto } from "@/client/onboarding/types.gen"

import { type ApplicantDetailsFieldProps } from "../interface"
import CountryInput, { useCountryInput } from "../../common/CountryInput"

type FieldNationalityProps = ApplicantDetailsFieldProps

export function FieldNationality({ form }: FieldNationalityProps) {
  const field = useStore(
    form.store,
    (state) => state.values.nationality,
  ) as Country

  const { countries } = useCountryInput("", (c) => c.code == field)
  const country = countries.find(
    (country) => country.code === field,
  ) as GetCountryListDto

  return (
    <form.Field
      name="nationality"
      validators={{
        onChange: ({ value }) => required(value, "Nationality is required"),
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Nationality"
            required
          >
            <CountryInput
              id={field.name}
              onChange={(value) => {
                const country = countries.find(
                  (country) => country.name === value,
                ) as GetCountryListDto
                field.handleChange(country?.code as Country)
              }}
              value={country?.name || ""}
            />
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
