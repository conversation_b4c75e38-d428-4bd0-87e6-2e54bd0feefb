import { MouseEvent } from "react"
import { Field<PERSON><PERSON> } from "@tanstack/react-form"

import { createDocumentBadgeClickHandler } from "@/lib/document.utils"
import { useDocumentDownload } from "@/hooks/use-document-download"
import { DocumentsMetadata } from "@/data/onboarding/onboarding.interface"
import { FormField } from "@/components/base/form/form"

import type {
  ApplicantDetailsFieldProps,
  IOnboardingApplicantDetailsForm,
} from "../interface"

import { validateFiles } from "../../common/upload-documents/FileUploadValidation"
import { FileUpload } from "../../common/upload-documents/FileUpload"
import FileBadge from "../../common/upload-documents/FileBadge"

type DocumentsMetadataField = FieldApi<
  IOnboardingApplicantDetailsForm,
  "proofOfAddress.documentsMetadata",
  undefined,
  undefined,
  DocumentsMetadata[]
>

type DocumentsToUploadField = FieldApi<
  IOnboardingApplicantDetailsForm,
  "proofOfAddress.documentsToUpload",
  undefined,
  undefined,
  File[]
>

interface FieldUploadProofOfAddressProps extends ApplicantDetailsFieldProps {
  onDelete?: (documentId: string) => void
}

export function FieldUploadProofOfAddress({
  form,
  onDelete,
}: FieldUploadProofOfAddressProps) {
  const { handleDownload } = useDocumentDownload()

  const resetFieldMetas = () => {
    form.setFieldMeta("proofOfAddress.documentsToUpload", {
      ...form.getFieldMeta("proofOfAddress.documentsToUpload")!,
      isDirty: false,
    })

    form.setFieldMeta("proofOfAddress.documentsMetadata", {
      ...form.getFieldMeta("proofOfAddress.documentsMetadata")!,
      isDirty: false,
    })
  }

  const handleFileChange = (files: File[], field: DocumentsToUploadField) => {
    const allFiles = [
      ...form.getFieldValue("proofOfAddress.documentsToUpload"),
      ...form.getFieldValue("proofOfAddress.documentsMetadata"),
    ]
    const hasErrors = Array.from(files)
      .map((file) => validateFiles(file, allFiles))
      .every((isValid) => !isValid)

    if (hasErrors) {
      return
    }

    Array.from(files).forEach((file) => {
      field.pushValue(file)
      form.pushFieldValue("proofOfAddress.documentsMetadata", {
        name: file.name,
      })
    })
  }

  const handleOnDelete = (
    e: MouseEvent,
    field: DocumentsMetadataField,
    document: DocumentsMetadata,
    index: number,
  ) => {
    e.preventDefault()
    field.removeValue(index)

    if (document.id && typeof onDelete !== "undefined") {
      onDelete(document.id)
      return
    }

    // to ensure the form doesn't submit the document
    form.setFieldValue(
      "proofOfAddress.documentsToUpload",
      form
        .getFieldValue("proofOfAddress.documentsToUpload")!
        .filter((d) => d.name !== document.name),
    )

    resetFieldMetas()
  }

  const handleBadgeClick = createDocumentBadgeClickHandler(
    form,
    "documentsToUpload",
    handleDownload,
  )

  return (
    <>
      <form.Field
        mode="array"
        name="proofOfAddress.documentsToUpload"
        validators={{
          onChangeListenTo: ["residentialAddress.country"],
          onChange: ({ value, fieldApi }) => {
            if (
              fieldApi.form.getFieldValue("residentialAddress.country") ===
                "United Kingdom" ||
              fieldApi.form.getFieldValue("residentialAddress.country") == ""
            )
              return undefined

            const values =
              form.getFieldValue("proofOfAddress.documentsMetadata") ?? []

            if (Array.isArray(values) && values.length <= 0) {
              if (value.length > 0) return undefined

              return "Proof of Address is required for Individuals residing outside of the UK"
            }

            return undefined
          },
        }}
      >
        {(rootField) => {
          return (
            <div className="flex flex-col gap-y-3">
              <FormField
                aria-label={rootField.name as string}
                field={rootField}
              >
                <FileUpload
                  field={rootField}
                  label="Proof of address"
                  onChange={(files) =>
                    files && handleFileChange(Array.from(files), rootField)
                  }
                  required={
                    rootField.form.getFieldValue(
                      "residentialAddress.country",
                    ) !== "United Kingdom"
                  }
                  tooltipText="Upload your document"
                />
              </FormField>
            </div>
          )
        }}
      </form.Field>

      <form.Field name="proofOfAddress.documentsMetadata">
        {(field) => (
          <div
            aria-label="Documents metadata"
            className="flex flex-wrap items-center gap-2"
          >
            {field.state.value.map((document, i) => (
              <form.Field
                key={i}
                name={`proofOfAddress.documentsMetadata[${i}].name`}
              >
                {(subField) => (
                  <div className="relative flex items-center justify-center animate-in fade-in-0 slide-in-from-bottom-10">
                    <FileBadge
                      onDelete={(e) => handleOnDelete(e, field, document, i)}
                      onDownload={(e) => handleBadgeClick(e, document)}
                    >
                      {subField.state.value}
                    </FileBadge>
                  </div>
                )}
              </form.Field>
            ))}
          </div>
        )}
      </form.Field>
    </>
  )
}
