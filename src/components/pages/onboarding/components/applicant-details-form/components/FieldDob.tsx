import { useMemo } from "react"
import { CalendarIcon } from "lucide-react"
import { subYears } from "date-fns/subYears"
import { isBefore } from "date-fns/isBefore"
import { format } from "date-fns"
import { useStore } from "@tanstack/react-store"

import { useToggle } from "@/hooks/use-toggle"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { FormField } from "@/components/base/form/form"
import { DateOfBirthPicker } from "@/components/base/date-of-birth-picker"

import { type ApplicantDetailsFieldProps } from "../interface"

const MIN_AGE = 16

const today = new Date()

function isBeforeDate(date: Date) {
  return isBefore(date, subYears(today, MIN_AGE))
}

export function FieldDob({ form }: ApplicantDetailsFieldProps) {
  const [isOpen, toggle] = useToggle()
  const date = useStore(form.store, (state) => state.values.dob)

  const currentDate = useMemo(() => {
    if (!date || date === "") return "DD / MM / YYYY"

    return format(date, "dd MMM yyyy")
  }, [date])

  return (
    <form.Field
      name="dob"
      validators={{
        onChange: ({ value }) => {
          const isMinAgeOlder = isBeforeDate(new Date(value))

          return !isMinAgeOlder ? "You must be 16 years or older" : undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col gap-y-3">
          <FormField
            aria-label={field.name}
            field={field}
            label="Date of birth"
            required
          >
            <Popover
              onOpenChange={isOpen ? toggle.off : toggle.on}
              open={isOpen}
            >
              <PopoverTrigger asChild>
                <Button
                  className="h-10 w-full rounded-xl p-4 py-6 text-left text-base font-normal"
                  id={field.name}
                  variant="outline"
                >
                  <span>{currentDate}</span>
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                align="start"
                className="w-auto rounded-xl p-0 text-base"
              >
                <DateOfBirthPicker
                  defaultMonth={
                    field.state.value ? new Date(field.state.value) : undefined
                  }
                  disabled={{ after: today }}
                  endMonth={today}
                  mode="single"
                  onSelect={(e) => {
                    if (e) {
                      field.handleChange(e.toDateString())
                      toggle.off()
                    }
                  }}
                  selected={
                    field.state.value ? new Date(field.state.value) : undefined
                  }
                />
              </PopoverContent>
            </Popover>
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
