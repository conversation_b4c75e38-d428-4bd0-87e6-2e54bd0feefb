import { useStore } from "@tanstack/react-store"

import { cn } from "@/lib/utils"
import { required } from "@/lib/form.utils"
import { IOnboardingAddress } from "@/data/onboarding/onboarding.interface"
import { Input } from "@/components/ui/input"
import { FormField, FormLayout } from "@/components/base/form/form"

import { type ApplicantDetailsFieldProps } from "../interface"
import CountryInput from "../../common/CountryInput"
import { AddressModal } from "../../common/AddressModal"

function formatAddress(address: IOnboardingAddress): string {
  return [
    `${address.buildingNumber} ${address.street}`,
    address.city,
    address.state,
    address.postalCode,
    address.country,
  ]
    .filter(Boolean)
    .join(", ")
    .trim()
}

interface FieldResidentialAddressProps extends ApplicantDetailsFieldProps {
  onUpdateAddress: (handlers?: Record<string, () => void>) => Promise<void>
}

export function FieldResidentialAddress({
  form,
  onUpdateAddress,
}: FieldResidentialAddressProps) {
  const address = useStore(
    form.store,
    (state) => state.values.residentialAddress,
  )

  const addressIsEmpty =
    !address.country ||
    !address.buildingNumber ||
    !address.city ||
    !address.buildingNumber

  const resetMeta = () => {
    form.setFieldMeta("residentialAddress.country", {
      ...form.getFieldMeta("residentialAddress.country")!,
      isDirty: false,
    })

    form.setFieldMeta("residentialAddress.buildingNumber", {
      ...form.getFieldMeta("residentialAddress.buildingNumber")!,
      isDirty: false,
    })

    form.setFieldMeta("residentialAddress.street", {
      ...form.getFieldMeta("residentialAddress.street")!,
      isDirty: false,
    })

    form.setFieldMeta("residentialAddress.city", {
      ...form.getFieldMeta("residentialAddress.city")!,
      isDirty: false,
    })

    form.setFieldMeta("residentialAddress.postalCode", {
      ...form.getFieldMeta("residentialAddress.postalCode")!,
      isDirty: false,
    })
  }

  const handleSave = () => {
    onUpdateAddress({
      onSuccess: resetMeta,
    })
  }

  return (
    <form.Field
      name="residentialAddress"
      validators={{
        onChangeListenTo: [
          "residentialAddress.country",
          "residentialAddress.buildingNumber",
          "residentialAddress.street",
          "residentialAddress.city",
          "residentialAddress.postalCode",
          "nationality",
        ],
        onChange: ({ value }) => {
          if (
            !value.buildingNumber ||
            !value.street ||
            !value.city ||
            !value.postalCode ||
            !value.country
          )
            return "Field address not valid"

          return undefined
        },
      }}
    >
      {(field) => (
        <div className="flex flex-col">
          <FormField
            aria-label={field.name}
            field={field}
            label="Residential address"
            required
          >
            <div
              className={cn("mt-3 flex", {
                "items-center justify-center rounded-xl bg-slate-300/15 p-3 py-2":
                  !addressIsEmpty,
              })}
            >
              {!addressIsEmpty && (
                <p className="flex flex-1 items-center text-base font-light">
                  {formatAddress(field.state.value)}
                </p>
              )}

              <AddressModal
                DialogTriggerProps={{
                  className: cn({
                    "pl-0 pt-0": !addressIsEmpty,
                    "pr-1": addressIsEmpty,
                  }),
                  "aria-label": "Residential address",
                }}
                form={form}
                isValid={
                  address.country !== "" &&
                  address.buildingNumber !== "" &&
                  address.city !== "" &&
                  address.postalCode !== ""
                }
                onCancel={resetMeta}
                onSave={handleSave}
                title={addressIsEmpty ? "Residential address" : ""}
              >
                <FormLayout
                  className="w-full space-y-4 [&>:first-child]:mb-4"
                  title="Residential address"
                >
                  <form.Field
                    name="residentialAddress.country"
                    validators={{
                      onMount: ({ value }) =>
                        required(value, "Country is required"),
                      onSubmit: ({ value }) =>
                        required(value, "Country is required"),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="Country"
                          required
                        >
                          <CountryInput
                            onChange={(value) =>
                              value && field.handleChange(value)
                            }
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="residentialAddress.buildingNumber"
                    validators={{
                      onMount: ({ value }) =>
                        required(
                          value.trim(),
                          "House / Building number is required",
                        ),
                      onChange: ({ value }) =>
                        required(
                          value.trim(),
                          "House / Building number is required",
                        ),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="House / Building number"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="residentialAddress.street"
                    validators={{
                      onMount: ({ value }) =>
                        required(value.trim(), "Street is required"),
                      onChange: ({ value }) =>
                        required(value.trim(), "Street is required"),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="Street address"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="residentialAddress.city"
                    validators={{
                      onMount: ({ value }) =>
                        required(value.trim(), "Town / City is required"),
                      onChange: ({ value }) =>
                        required(value.trim(), "Town / City is required"),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="Town / City"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="residentialAddress.state"
                    validators={{
                      onMount: ({ value }) => required(value.trim(), ""),
                      onChange: ({ value }) => required(value.trim(), ""),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="County"
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>

                  <form.Field
                    name="residentialAddress.postalCode"
                    validators={{
                      onMount: ({ value }) =>
                        required(value.trim(), "Postcode is required"),
                      onChange: ({ value }) =>
                        required(value.trim(), "Postcode is required"),
                    }}
                  >
                    {(field) => (
                      <div className="flex flex-col gap-y-3">
                        <FormField
                          aria-label={field.name}
                          field={field}
                          label="Postcode"
                          required
                        >
                          <Input
                            className="bg-background p-4 py-6 md:text-base"
                            id={field.name}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="text"
                            value={field.state.value}
                          />
                        </FormField>
                      </div>
                    )}
                  </form.Field>
                </FormLayout>
              </AddressModal>
            </div>
          </FormField>
        </div>
      )}
    </form.Field>
  )
}
