import { useEffect } from "react"
import { isEmpty } from "lodash-es"
import { format } from "date-fns"
import { useForm } from "@tanstack/react-form"

import { useFormInvalid } from "@/hooks/use-form-invalid"
import {
  GET_DOCUMENT_PASSPORT_OR_DRIVING_LICENSE_QRY_KEY,
  GET_DOCUMENT_PROOF_OF_ADDRESS_QRY_KEY,
  useGetApplicantDetailsDocumentsSuspenseQry,
} from "@/data/onboarding/onboarding.query"
import {
  useDeleteDocumentMutation,
  useUploadDocumentMutation,
} from "@/data/onboarding/onboarding.mutation"
import {
  useGetNaturalEntityAddressesSuspenseQry,
  useGetNaturalEntitySuspenseQry,
} from "@/data/onboarding/applicant-details.query"
import {
  useUpdateApplicantDetailsMutation,
  useUpdateNaturalEntityAddressMutation,
} from "@/data/onboarding/applicant-details.mutation"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useGlobalStore } from "@/data/global/global.store"
import {
  type CreateEntityNaturalEntityResponseDto2,
  type GetAddressListDto,
  type GetEntityDocumentsDto,
  type GetCountryListDto,
} from "@/client/onboarding/types.gen"

import { IOnboardingApplicantDetailsForm } from "./interface"
import { ApplicantDetailsFormProps } from "./ApplicantDetailsForm"
import {
  formatAddress,
  getAddress,
  getDocumentByCategory,
  mapAddress,
  mapCreateAddressPayload,
} from "../utils"
import useFormBlockWithUnsavedChanges from "../hooks/useWarnWithUnsavedChanges"

function formIsTouched(formValues: any): boolean {
  let touched = false // firstName, lastname default
  if (formValues.dob.length > 0) touched = true
  if (formValues.nationality.length > 0) touched = true
  if (formValues.residentialAddress?.buildingNumber?.length > 0) touched = true
  if (formValues.residentialAddress?.city?.length > 0) touched = true
  if (formValues.residentialAddress?.country?.length > 0) touched = true
  if (formValues.residentialAddress?.postalCode?.length > 0) touched = true
  if (formValues.residentialAddress?.state?.length > 0) touched = true
  if (formValues.residentialAddress?.street?.length > 0) touched = true
  if (formValues.passportOrDrivingLicense?.documentsMetadata?.length > 0)
    touched = true
  if (formValues.proofOfAddress?.documentsMetadata?.length > 0) touched = true
  return touched
}

export function useApplicantDetailsForm(props: ApplicantDetailsFormProps) {
  const prefetchData = useLoaderData()
  const documentsQry = useGetApplicantDetailsDocumentsSuspenseQry()
  const { data: addresses } = useGetNaturalEntityAddressesSuspenseQry()
  const {
    data: { naturalEntity: user },
  } = useGetNaturalEntitySuspenseQry()
  const { mutate: updateAddressMutate } =
    useUpdateNaturalEntityAddressMutation()
  const { mutate: updateApplicantDetailsMutate } =
    useUpdateApplicantDetailsMutation()
  const { mutateAsync: uploadDocument } = useUploadDocumentMutation(
    [
      GET_DOCUMENT_PASSPORT_OR_DRIVING_LICENSE_QRY_KEY,
      GET_DOCUMENT_PROOF_OF_ADDRESS_QRY_KEY,
    ],
    prefetchData.user.ownerEntityId,
    true,
  )
  const { mutate: deleteDocument } = useDeleteDocumentMutation(
    [
      GET_DOCUMENT_PASSPORT_OR_DRIVING_LICENSE_QRY_KEY,
      GET_DOCUMENT_PROOF_OF_ADDRESS_QRY_KEY,
    ],
    prefetchData.user.ownerEntityId,
  )
  const formSeenState = useGlobalStore((state) => state.onboardingSeenState)
  const setFormSeen = useGlobalStore((state) => state.setOnboardingSeenState)
  const residentialAddress = getAddress(addresses, "ResidentialAddress")

  const defaultValues = {
    firstName: user?.givenName ?? "",
    lastName: user?.familyName ?? "",
    dob: user?.dateOfBirth ?? "",
    nationality: user?.nationalityCountryCode ?? "",
    residentialAddress: mapAddress(
      prefetchData.staticDataCountries,
      residentialAddress,
    ),
    passportOrDrivingLicense: {
      documentsMetadata:
        documentsQry.data
          ?.filter((e) => e.category === "ProofOfIdentity")
          .map((e) => ({
            id: e.id,
            name: e.name!,
          })) ?? [],
      documentsToUpload: [],
    },
    proofOfAddress: {
      documentsMetadata:
        documentsQry.data
          ?.filter((e) => e.category === "ProofOfAddress")
          .map((e) => ({
            id: e.id,
            name: e.name!,
          })) ?? [],
      documentsToUpload: [],
    },
  }
  const isTouched = formIsTouched(defaultValues)

  const form = useForm<IOnboardingApplicantDetailsForm>({
    defaultValues,
    onSubmit: () =>
      handleSave({
        onSuccess: () => {
          props.onNext?.()
          form.reset(defaultValues, { keepDefaultValues: true })
        },
      }),
    validators: {
      onMount: ({ formApi }) => {
        setFormSeen({
          ...formSeenState,
          applicantDetailsForm: true,
        })

        if (isTouched) {
          formApi.validateAllFields("submit")
        }

        return null
      },
    },
  })

  useEffect(() => {
    if (!documentsQry.isLoading) {
      const passportOrDrivingLicenseDocuments =
        documentsQry.data
          ?.filter((e) => e.category === "ProofOfIdentity")
          .map((e) => ({
            id: e.id,
            name: e.name!,
          })) ?? []

      const proofOfAddressDocuments =
        documentsQry.data
          ?.filter((e) => e.category === "ProofOfAddress")
          .map((e) => ({
            id: e.id,
            name: e.name!,
          })) ?? []

      form.setFieldValue(
        "passportOrDrivingLicense.documentsMetadata",
        passportOrDrivingLicenseDocuments,
      )
      form.setFieldMeta("passportOrDrivingLicense.documentsMetadata", {
        ...form.getFieldMeta("passportOrDrivingLicense.documentsMetadata")!,
        isDirty: false,
      })

      form.setFieldValue(
        "proofOfAddress.documentsMetadata",
        proofOfAddressDocuments,
      )
      form.setFieldMeta("proofOfAddress.documentsMetadata", {
        ...form.getFieldMeta("proofOfAddress.documentsMetadata")!,
        isDirty: false,
      })
    }

    return () => {
      form.setFieldValue("passportOrDrivingLicense.documentsToUpload", [])
      form.setFieldMeta("passportOrDrivingLicense.documentsToUpload", {
        ...form.getFieldMeta("passportOrDrivingLicense.documentsToUpload")!,
        isDirty: false,
      })

      form.setFieldValue("proofOfAddress.documentsToUpload", [])
      form.setFieldMeta("proofOfAddress.documentsToUpload", {
        ...form.getFieldMeta("proofOfAddress.documentsToUpload")!,
        isDirty: false,
      })
    }
  }, [documentsQry.isLoading, documentsQry.data, form])

  useFormInvalid(form, props.onInvalid)

  // Block step change if data is unsaved
  useFormBlockWithUnsavedChanges(form)

  function handleDeleteDocument(documentId: string) {
    deleteDocument({ documentId })
  }

  async function handleSave(handlers?: Record<string, () => void>) {
    const [errors] = await form.validateAllFields("submit")

    if (errors) return

    await Promise.all([
      uploadDocument({
        documentCategory: "ProofOfIdentity",
        payload: form.state.values.passportOrDrivingLicense.documentsToUpload,
      }),
      uploadDocument({
        documentCategory: "ProofOfAddress",
        payload: form.state.values.proofOfAddress.documentsToUpload,
      }),
    ])

    updateApplicantDetailsMutate(
      {
        entityType: "Individual",
        naturalEntity: {
          givenName: form.state.values.firstName,
          familyName: form.state.values.lastName,
          dateOfBirth: format(form.state.values.dob, "yyyy-MM-dd"),
          nationalityCountryCode: form.state.values.nationality,
        },
      },
      handlers,
    )
  }

  async function handleUpdateAddress(handlers?: Record<string, () => void>) {
    const [errors] = await Promise.all([
      form.validateField("residentialAddress.country", "submit"),
      form.validateField("residentialAddress.buildingNumber", "submit"),
      form.validateField("residentialAddress.street", "submit"),
      form.validateField("residentialAddress.city", "submit"),
      form.validateField("residentialAddress.postalCode", "submit"),
    ])

    if (!isEmpty(errors))
      throw new Error("[Applicant Details] Unable to update address")

    const filterAddress = getAddress(addresses, "ResidentialAddress")

    updateAddressMutate(
      {
        residentialAddress: mapCreateAddressPayload({
          id: filterAddress?.id,
          addr: form.state.values.residentialAddress,
          type: "ResidentialAddress",
          countries: prefetchData.staticDataCountries,
        }),
      },
      handlers,
    )
  }

  return {
    form,
    handleSave,
    handleUpdateAddress,
    handleDeleteDocument,
  }
}

interface GetReviewApplicantDetails {
  staticData: {
    staticDataCountries: GetCountryListDto[]
  }
  applicantDetailsUser: CreateEntityNaturalEntityResponseDto2 | null | undefined
  applicantDetailsUserAddresses: GetAddressListDto[] | undefined
  applicantDetailsUserDocs: GetEntityDocumentsDto[] | undefined
}
export function getReviewApplicantDetails({
  staticData,
  applicantDetailsUser,
  applicantDetailsUserAddresses,
  applicantDetailsUserDocs,
}: GetReviewApplicantDetails) {
  if (
    applicantDetailsUser == undefined ||
    applicantDetailsUserAddresses == undefined ||
    applicantDetailsUserDocs == undefined
  )
    return {
      ResidentialAddress: {
        cityOrTown: "",
        countryCode: "",
        postalCode: "",
        line1: "",
        line2: "",
      },
      firstName: "",
      lastName: "",
      dob: "",
      nationality: "",
      residentialAddress: "",
      ProofOfIdentityDocs: "",
      ProofOfAddressDocs: "",
    }

  const address = getAddress(
    applicantDetailsUserAddresses,
    "ResidentialAddress",
  )
  const residentialAddressMapped = mapAddress(
    staticData.staticDataCountries,
    address,
  )

  const ProofOfIdentity = getDocumentByCategory(
    applicantDetailsUserDocs,
    "ProofOfIdentity",
  )
  const ProofOfAddress = getDocumentByCategory(
    applicantDetailsUserDocs,
    "ProofOfAddress",
  )

  return {
    ResidentialAddress: address, // to check details like city
    firstName: applicantDetailsUser?.givenName ?? "",
    lastName: applicantDetailsUser?.familyName ?? "",
    dob: applicantDetailsUser?.dateOfBirth ?? "",
    nationality: applicantDetailsUser?.nationalityCountryCode ?? "",
    residentialAddress: formatAddress(residentialAddressMapped),
    ProofOfIdentityDocs: ProofOfIdentity?.map((e) => e.name).join(", ") ?? "",
    ProofOfAddressDocs: ProofOfAddress?.map((e) => e.name).join(", ") ?? "",
  }
}
