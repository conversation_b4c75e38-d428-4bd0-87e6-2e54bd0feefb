import { describe, it, expect } from "vitest"

import { validateApplicantDetailsForm } from "@/components/pages/onboarding/components/applicant-details-form/validator"
import { IOnboardingApplicantDetailsForm } from "@/components/pages/onboarding/components/applicant-details-form/interface"

describe("Applicant Details Form Validator", () => {
  const validApplicantDetails: IOnboardingApplicantDetailsForm = {
    firstName: "John",
    lastName: "Doe",
    nationality: "GB",
    dob: "1990-01-15",
    passportOrDrivingLicense: {
      documentsMetadata: [{ id: "doc1", name: "passport.pdf" }],
      documentsToUpload: [],
    },
    residentialAddress: {
      buildingNumber: "123",
      street: "Main Street",
      state: "England",
      city: "London",
      country: "United Kingdom",
      postalCode: "SW1A 1AA",
    },
    proofOfAddress: {
      documentsMetadata: [],
      documentsToUpload: [],
    },
  }

  describe("validateApplicantDetailsForm", () => {
    it("should return true for valid applicant details (UK resident)", () => {
      const result = validateApplicantDetailsForm(validApplicantDetails)
      expect(result).toBe(true)
    })

    it("should return true for valid non-UK resident with proof of address", () => {
      const nonUkResident = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          country: "France",
        },
        proofOfAddress: {
          documentsMetadata: [{ id: "doc2", name: "utility-bill.pdf" }],
          documentsToUpload: [],
        },
      }
      const result = validateApplicantDetailsForm(nonUkResident)
      expect(result).toBe(true)
    })

    it("should return false when firstName is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        firstName: "",
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when lastName is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        lastName: "",
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when nationality is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        nationality: undefined as any,
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when dob is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        dob: "",
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when passport/driving license documents are missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        passportOrDrivingLicense: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when residential address buildingNumber is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          buildingNumber: "",
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when residential address city is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          city: "",
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when residential address country is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          country: "",
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when residential address postalCode is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          postalCode: "",
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when residential address street is missing", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          street: "",
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false for non-UK resident without proof of address", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          country: "France",
        },
        proofOfAddress: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false for non-UK resident with null proof of address documents", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: {
          ...validApplicantDetails.residentialAddress,
          country: "Germany",
        },
        proofOfAddress: {
          documentsMetadata: null as any,
          documentsToUpload: [],
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })

    it("should return false when data is undefined", () => {
      const result = validateApplicantDetailsForm(undefined)
      expect(result).toBe(false)
    })

    it("should throw error when residentialAddress is undefined", () => {
      const invalidData = {
        ...validApplicantDetails,
        residentialAddress: undefined as any,
      }
      expect(() => validateApplicantDetailsForm(invalidData)).toThrow()
    })

    it("should handle case-sensitive country check for UK", () => {
      const ukVariations = [
        "United Kingdom",
        "united kingdom",
        "UNITED KINGDOM",
        "UK",
        "uk",
      ]

      ukVariations.forEach((country) => {
        const testData = {
          ...validApplicantDetails,
          residentialAddress: {
            ...validApplicantDetails.residentialAddress,
            country,
          },
        }
        const result = validateApplicantDetailsForm(testData)
        // Only exact match "United Kingdom" should not require proof of address
        const shouldBeValid = country === "United Kingdom"
        expect(result).toBe(shouldBeValid)
      })
    })

    it("should handle multiple missing fields", () => {
      const invalidData = {
        ...validApplicantDetails,
        firstName: "",
        lastName: "",
        dob: "",
        passportOrDrivingLicense: {
          documentsMetadata: [],
          documentsToUpload: [],
        },
      }
      const result = validateApplicantDetailsForm(invalidData)
      expect(result).toBe(false)
    })
  })
})
