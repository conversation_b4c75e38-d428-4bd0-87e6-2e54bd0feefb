import { describe, expect, it, Mock, vi } from "vitest"
import userEvent from "@testing-library/user-event"
import { render, screen } from "@testing-library/react"
import {
  QueryClient,
  QueryClientProvider,
  useSuspenseQuery,
} from "@tanstack/react-query"

import { useGetApplicantDetailsDocumentsSuspenseQry } from "@/data/onboarding/onboarding.query"
import { useGlobalStore } from "@/data/global/global.store"
import { TooltipProvider } from "@/components/ui/tooltip"
import {
  ApplicantDetailsForm,
  ApplicantDetailsFormProps,
} from "@/components/pages/onboarding/components/applicant-details-form"
import {
  GetAddressListDto,
  GetEntityDocumentsDto,
  GetEntityResponseDto,
  GetUserDto,
} from "@/client/onboarding/types.gen"

vi.mock("@tanstack/react-query", () => ({
  useSuspenseQuery: vi.fn(() => ({
    data: {},
  })),
  QueryClient: vi.fn(),
  useQueryClient: vi.fn(),
  useMutation: vi.fn(() => ({
    mutate: vi.fn(),
  })),
  useQuery: vi.fn(() => ({
    isLoading: false,
  })),
  queryOptions: vi.fn(),
  QueryClientProvider: ({ children }: any) => <>{children}</>,
}))

vi.mock("@/data/onboarding/applicant-details.query", () => ({
  useGetNaturalEntitySuspenseQry: vi.fn(() => ({
    data: {
      id: "1",
      naturalEntity: {
        givenName: "John",
        familyName: "Doe",
        dateOfBirth: "1997-06-02",
        nationalityCountryCode: "GB",
      },
    } as GetEntityResponseDto,
    isLoading: false,
    isError: false,
  })),
  useGetNaturalEntityAddressesSuspenseQry: vi.fn(() => ({
    data: [
      {
        addressType: "ResidentialAddress",
        id: "some-id",
        countryCode: "GB",
        cityOrTown: "London",
        postalCode: "NW9 75N",
        line1: "John str",
        line2: "Woodlane",
      },
    ] as GetAddressListDto[],
    isLoading: false,
    isError: false,
  })),
}))

vi.mock(
  import("@/data/onboarding/onboarding.query"),
  async (importOriginal) => {
    const mod = await importOriginal() // type is inferred

    return {
      ...mod,
      // replace some exports
      useGetApplicantDetailsDocumentsSuspenseQry: vi.fn(),
    }
  },
)

vi.mock("@tanstack/react-router", () => ({
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
    user: {
      ownerEntityId: "test-entity",
    } as GetUserDto,
    staticDataCurrencies: [],
    staticDataCountries: [],
    staticDataPaymentActivityTypes: [],
  })),
}))

const mockGlobalStore = {
  entity: { id: "test-entity", name: "Argentex" },
}

vi.mock("@/components/base/currency/CurrencyInput", () => ({
  CurrencyInput: () => vi.fn(),
  CoreCurrencyInput: (props: any) => <input {...props} />,
}))

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(() => mockGlobalStore),
  useCurrenciesQuery: vi.fn(),
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

const setup = (props?: Partial<ApplicantDetailsFormProps>) => {
  const user = userEvent.setup()

  ;(useSuspenseQuery as Mock).mockReturnValue({
    isLoading: false,
    data: {
      id: "test-entity",
    },
  })

  const form = renderWithQueryClient(
    <TooltipProvider>
      <ApplicantDetailsForm {...props} />
    </TooltipProvider>,
  )

  // title
  const formHeader = screen.getByText("7. Applicant details")

  window.HTMLElement.prototype.scrollIntoView = vi.fn()
  window.HTMLElement.prototype.hasPointerCapture = vi.fn()

  const [
    firstName,
    lastName,
    dateOfBirth,
    Nationality,
    validPassportOrDrivingLicense,
    proofOfAddress,
    nextBtn,
  ] = [
    screen.getByLabelText("First name"),
    screen.getByLabelText("Last name"),
    screen.getByLabelText("Date of birth"),
    screen.getByLabelText("Nationality"),
    screen.getByLabelText("Residential address"),
    screen.getByLabelText("Valid passport or driving license"),
    screen.getByLabelText("Proof of address"),
    screen.getByRole("button", {
      name: "Save & next",
    }),
  ]

  return {
    formHeader,
    firstName,
    lastName,
    dateOfBirth,
    Nationality,
    validPassportOrDrivingLicense,
    proofOfAddress,
    nextBtn,
    ...form,
    user,
  } as const
}

describe("ApplicantDetailsForm", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useGlobalStore as any).mockImplementation(
      (
        selector: (state: {
          setOnboardingHasUnsavedChanges: any
          setOnboardingSeenState: any
        }) => unknown,
      ) =>
        selector({
          setOnboardingHasUnsavedChanges: vi.fn(),
          setOnboardingSeenState: vi.fn(),
        }),
    )
    ;(useGetApplicantDetailsDocumentsSuspenseQry as Mock).mockReturnValue({
      data: [
        {
          id: "1",
          name: "some-file.pdf",
        },
      ] as GetEntityDocumentsDto[],
      isLoading: false,
      isError: false,
    })
  })

  it("Should show the form", () => {
    const {
      formHeader,
      firstName,
      lastName,
      dateOfBirth,
      Nationality,
      validPassportOrDrivingLicense,
      proofOfAddress,
      nextBtn,
    } = setup()

    expect(formHeader).toBeInTheDocument()
    expect(firstName).toBeInTheDocument()
    expect(lastName).toBeInTheDocument()
    expect(dateOfBirth).toBeInTheDocument()
    expect(Nationality).toBeInTheDocument()
    expect(validPassportOrDrivingLicense).toBeInTheDocument()
    expect(proofOfAddress).toBeInTheDocument()
    expect(nextBtn).toBeInTheDocument()
  })
})
