import { ReactFormExtendedApi } from "@tanstack/react-form"

import { type UploadDocuments } from "@/data/onboarding/onboarding.interface"

interface ResidentialAddress {
  buildingNumber: string
  street: string
  state: string
  city: string
  postalCode: string
  country: string
}

export interface IOnboardingApplicantDetailsForm {
  firstName: string
  lastName: string
  dob: string
  nationality: string
  residentialAddress: ResidentialAddress
  passportOrDrivingLicense: UploadDocuments
  proofOfAddress: UploadDocuments
}

type OnboardingApplicantDetailsForm = ReactFormExtendedApi<
  IOnboardingApplicantDetailsForm,
  undefined
>

export interface ApplicantDetailsFieldProps {
  form: OnboardingApplicantDetailsForm
}
