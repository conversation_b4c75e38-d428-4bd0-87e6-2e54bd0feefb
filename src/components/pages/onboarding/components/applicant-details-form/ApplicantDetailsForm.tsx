import { FormLayout } from "@/components/base/form/form"

import { Fields } from "./components"
import { useApplicantDetailsForm } from "./ApplicantDetailsForm.hook"
import { GoBack } from "../common/GoBack"
import { FormControls } from "../common/FormControls"

export interface ApplicantDetailsFormProps {
  onNext?: () => void
  onInvalid?: () => void
  onBack?: () => void
}

export function ApplicantDetailsForm(props: ApplicantDetailsFormProps) {
  const { form, handleUpdateAddress, handleDeleteDocument } =
    useApplicantDetailsForm(props)

  return (
    <div
      aria-label="7. Applicant details"
      className="container flex max-w-2xl flex-col gap-y-8 px-4"
    >
      {props.onBack && <GoBack onBack={props.onBack} />}

      <FormLayout
        className="mx-0 flex max-w-xl flex-col gap-y-7"
        title="7. Applicant details"
      >
        <p className="text-muted-foreground">
          Please provide your details below. By doing so, you confirm that you
          are a director or other authorised person with the authority to submit
          this application.
        </p>
        <Fields.FirstName form={form} />
        <Fields.LastName form={form} />
        <Fields.Dob form={form} />
        <Fields.Nationality form={form} />
        <Fields.ResidentialAddress
          form={form}
          onUpdateAddress={handleUpdateAddress}
        />
        <Fields.UploadPassportOrDrivingLicense
          form={form}
          onDelete={handleDeleteDocument}
        />
        <Fields.UploadProofOfAddress
          form={form}
          onDelete={handleDeleteDocument}
        />

        <FormControls form={form} />
      </FormLayout>
    </div>
  )
}
