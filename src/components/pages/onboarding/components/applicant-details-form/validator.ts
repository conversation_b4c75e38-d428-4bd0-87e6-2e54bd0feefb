import { isEmpty } from "lodash-es"

import { IOnboardingApplicantDetailsForm } from "./interface"
import { getReviewApplicantDetails } from "./ApplicantDetailsForm.hook"

export function validateApplicantDetailsForm(
  data?: IOnboardingApplicantDetailsForm,
) {
  let isValid =
    !!data?.firstName &&
    !!data?.lastName &&
    !!data?.nationality &&
    !!data?.dob &&
    data?.passportOrDrivingLicense.documentsMetadata.length > 0 &&
    !!data?.residentialAddress?.buildingNumber &&
    !!data?.residentialAddress?.city &&
    !!data?.residentialAddress?.country &&
    !!data?.residentialAddress?.postalCode &&
    !!data?.residentialAddress?.street

  if (data?.residentialAddress.country !== "United Kingdom") {
    isValid =
      Array.isArray(data?.proofOfAddress?.documentsMetadata) &&
      data?.proofOfAddress?.documentsMetadata.length > 0
  }

  return isValid
}

export default function reviewApplicantDetails(
  data?: ReturnType<typeof getReviewApplicantDetails>,
) {
  let isValid = false

  // user
  const hasValidUserFields =
    !!data?.firstName && !!data?.lastName && !!data?.dob && !!data?.nationality

  // address
  const hasValidAddressFields =
    !!data?.ResidentialAddress?.countryCode &&
    !!data?.ResidentialAddress?.line1 &&
    !!data?.ResidentialAddress?.line2 &&
    !!data?.ResidentialAddress?.cityOrTown &&
    !!data?.ResidentialAddress?.postalCode

  let hasValidDocumentsField = !!data?.ProofOfIdentityDocs

  if (hasValidAddressFields && data?.ResidentialAddress?.countryCode !== "GB") {
    hasValidDocumentsField = !isEmpty(data?.ProofOfAddressDocs.split(","))
  }

  isValid =
    hasValidUserFields && hasValidAddressFields && hasValidDocumentsField

  return isValid
}
