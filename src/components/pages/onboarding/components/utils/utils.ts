import { IOnboardingAddress } from "@/data/onboarding/onboarding.interface"
import {
  AddressType,
  CreateAddressRequestDto,
  DocumentCategory,
  EnumValueDto,
  GetAddressListDto,
  GetCountryListDto,
  GetEntityDocumentsDto,
  UpdateAddressDto,
} from "@/client/onboarding/types.gen"

export function findStaticData<D extends EnumValueDto[], K>(
  data: D | undefined,
  key: K,
) {
  return data?.find((e) => e.key === key) ?? { display: "", key: "" }
}

export function findCountryByCode(
  countries: GetCountryListDto[],
  code?: string,
) {
  return countries?.find((e) => e.code === code)?.name ?? ""
}

export function findCountryByName(
  countries: GetCountryListDto[],
  name: string,
) {
  return countries?.find((e) => e.name === name)?.code ?? ""
}

export function getAddress(addresses: GetAddressListDto[], type: AddressType) {
  return addresses?.find(({ addressType }) => addressType === type) ?? {}
}

export function isNotEmptyObject(obj: object) {
  return Object.keys(obj).length > 0
}

export function findJurisdiction(
  data: GetCountryListDto[],
  key: string | undefined | null,
) {
  return data?.find((e) => e.code === key) ?? {}
}

export const mapAddress = (
  countries: GetCountryListDto[],
  address: GetAddressListDto,
) => ({
  buildingNumber: address?.line1 ?? "",
  street: address?.line2 ?? "",
  state: address?.stateOrProvince ?? "",
  city: address?.cityOrTown ?? "",
  postalCode: address?.postalCode ?? "",
  country: findCountryByCode(countries, address?.countryCode),
})

interface ICreateAddressPayload {
  countries: GetCountryListDto[]
  addr: IOnboardingAddress
  type: AddressType
  id?: string
  override?: CreateAddressRequestDto | UpdateAddressDto
}

export const mapCreateAddressPayload = ({
  countries,
  addr,
  type,
  id,
  override,
}: ICreateAddressPayload) => ({
  id,
  line1: addr.buildingNumber,
  line2: addr.street,
  stateOrProvince: addr.state,
  cityOrTown: addr.city,
  postalCode: addr.postalCode,
  addressType: type,
  countryCode: findCountryByName(countries, addr.country),
  ...override,
})

export interface FormatAddress {
  buildingNumber: string
  street: string
  state: string
  city: string
  postalCode: string
  country: string
}

export const formatAddress = (address?: FormatAddress) => {
  if (!address) return ""

  if (
    !address.buildingNumber ||
    !address.street ||
    !address.city ||
    !address.postalCode ||
    !address.country
  )
    return ""

  const parts = [
    address.buildingNumber,
    address.street,
    address.city,
    address.state,
    address.postalCode,
    address.country,
  ].filter(Boolean)

  return parts.join(", ")
}

export function getDocumentByCategory(
  documents: GetEntityDocumentsDto[] | undefined,
  category: DocumentCategory,
) {
  return documents?.filter((d) => d.category === category) ?? []
}
