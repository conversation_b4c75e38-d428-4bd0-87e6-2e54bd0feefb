import { describe, it, expect } from "vitest"

import {
  findStaticData,
  findCountryByCode,
  findCountryByName,
  formatAddress,
  getDocumentByCategory,
} from "@/components/pages/onboarding/components/utils/utils"
import {
  EnumValueDto,
  GetCountryListDto,
  GetEntityDocumentsDto,
  DocumentCategory,
} from "@/client/onboarding/types.gen"

describe("Onboarding Utils", () => {
  describe("findStaticData", () => {
    const mockStaticData: EnumValueDto[] = [
      { key: "KEY1", display: "Display 1" },
      { key: "KEY2", display: "Display 2" },
      { key: "KEY3", display: "Display 3" },
    ]

    it("should find data by key", () => {
      const result = findStaticData(mockStaticData, "KEY2")
      expect(result).toEqual({ key: "KEY2", display: "Display 2" })
    })

    it("should return default object when key not found", () => {
      const result = findStaticData(mockStaticData, "NONEXISTENT")
      expect(result).toEqual({ display: "", key: "" })
    })

    it("should return default object when data is undefined", () => {
      const result = findStaticData(undefined, "KEY1")
      expect(result).toEqual({ display: "", key: "" })
    })

    it("should return default object when data is empty", () => {
      const result = findStaticData([], "KEY1")
      expect(result).toEqual({ display: "", key: "" })
    })
  })

  describe("findCountryByCode", () => {
    const mockCountries: GetCountryListDto[] = [
      { code: "US", name: "United States" },
      { code: "GB", name: "United Kingdom" },
      { code: "CA", name: "Canada" },
    ]

    it("should find country name by code", () => {
      const result = findCountryByCode(mockCountries, "GB")
      expect(result).toBe("United Kingdom")
    })

    it("should return empty string when code not found", () => {
      const result = findCountryByCode(mockCountries, "XX")
      expect(result).toBe("")
    })

    it("should return empty string when code is undefined", () => {
      const result = findCountryByCode(mockCountries, undefined)
      expect(result).toBe("")
    })

    it("should return empty string when countries array is empty", () => {
      const result = findCountryByCode([], "US")
      expect(result).toBe("")
    })
  })

  describe("findCountryByName", () => {
    const mockCountries: GetCountryListDto[] = [
      { code: "US", name: "United States" },
      { code: "GB", name: "United Kingdom" },
      { code: "CA", name: "Canada" },
    ]

    it("should find country code by name", () => {
      const result = findCountryByName(mockCountries, "United Kingdom")
      expect(result).toBe("GB")
    })

    it("should return empty string when name not found", () => {
      const result = findCountryByName(mockCountries, "Nonexistent Country")
      expect(result).toBe("")
    })

    it("should return empty string when countries array is empty", () => {
      const result = findCountryByName([], "United States")
      expect(result).toBe("")
    })

    it("should be case sensitive", () => {
      const result = findCountryByName(mockCountries, "united states")
      expect(result).toBe("")
    })
  })

  describe("formatAddress", () => {
    it("should format complete address correctly", () => {
      const address = {
        buildingNumber: "123",
        street: "Main Street",
        city: "New York",
        state: "NY",
        postalCode: "10001",
        country: "United States",
      }

      const result = formatAddress(address)
      expect(result).toBe(
        "123, Main Street, New York, NY, 10001, United States",
      )
    })

    it("should format address without state", () => {
      const address = {
        buildingNumber: "456",
        street: "Oak Avenue",
        state: "", // empty state should be filtered out
        city: "London",
        postalCode: "SW1A 1AA",
        country: "United Kingdom",
      }

      const result = formatAddress(address)
      expect(result).toBe("456, Oak Avenue, London, SW1A 1AA, United Kingdom")
    })

    it("should return empty string when address is undefined", () => {
      const result = formatAddress(undefined)
      expect(result).toBe("")
    })

    it("should return empty string when required fields are missing", () => {
      const incompleteAddress = {
        buildingNumber: "123",
        street: "Main Street",
        // missing city, postalCode, country
      } as any

      const result = formatAddress(incompleteAddress)
      expect(result).toBe("")
    })

    it("should return empty string when buildingNumber is missing", () => {
      const address = {
        street: "Main Street",
        city: "New York",
        postalCode: "10001",
        country: "United States",
      } as any

      const result = formatAddress(address)
      expect(result).toBe("")
    })

    it("should return empty string when street is missing", () => {
      const address = {
        buildingNumber: "123",
        city: "New York",
        postalCode: "10001",
        country: "United States",
      } as any

      const result = formatAddress(address)
      expect(result).toBe("")
    })

    it("should filter out falsy values", () => {
      const address = {
        buildingNumber: "123",
        street: "Main Street",
        city: "New York",
        state: "", // empty string should be filtered out
        postalCode: "10001",
        country: "United States",
      }

      const result = formatAddress(address)
      expect(result).toBe("123, Main Street, New York, 10001, United States")
    })
  })

  describe("getDocumentByCategory", () => {
    const mockDocuments: GetEntityDocumentsDto[] = [
      {
        id: "1",
        category: "PROOF_OF_EXISTENCE" as DocumentCategory,
        name: "doc1.pdf",
      },
      {
        id: "2",
        category: "FINANCIAL_DOCUMENTS" as DocumentCategory,
        name: "doc2.pdf",
      },
      {
        id: "3",
        category: "PROOF_OF_EXISTENCE" as DocumentCategory,
        name: "doc3.pdf",
      },
      {
        id: "4",
        category: "OWNERSHIP_DOCUMENTS" as DocumentCategory,
        name: "doc4.pdf",
      },
    ]

    it("should filter documents by category", () => {
      const result = getDocumentByCategory(
        mockDocuments,
        "PROOF_OF_EXISTENCE" as DocumentCategory,
      )
      expect(result).toHaveLength(2)
      expect(result[0].id).toBe("1")
      expect(result[1].id).toBe("3")
    })

    it("should return empty array when no documents match category", () => {
      const result = getDocumentByCategory(
        mockDocuments,
        "APPLICANT_DETAILS_DOCUMENTS" as DocumentCategory,
      )
      expect(result).toEqual([])
    })

    it("should return empty array when documents is undefined", () => {
      const result = getDocumentByCategory(
        undefined,
        "PROOF_OF_EXISTENCE" as DocumentCategory,
      )
      expect(result).toEqual([])
    })

    it("should return empty array when documents is empty", () => {
      const result = getDocumentByCategory(
        [],
        "PROOF_OF_EXISTENCE" as DocumentCategory,
      )
      expect(result).toEqual([])
    })

    it("should handle different document categories", () => {
      const financialDocs = getDocumentByCategory(
        mockDocuments,
        "FINANCIAL_DOCUMENTS" as DocumentCategory,
      )
      expect(financialDocs).toHaveLength(1)
      expect(financialDocs[0].id).toBe("2")

      const ownershipDocs = getDocumentByCategory(
        mockDocuments,
        "OWNERSHIP_DOCUMENTS" as DocumentCategory,
      )
      expect(ownershipDocs).toHaveLength(1)
      expect(ownershipDocs[0].id).toBe("4")
    })
  })
})
