import {
  IDebitDetailsResponse,
  IFXDetailsResponse,
  IFeeDetailsResponse,
} from "@/data/transactions/transactions.interface"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

export function formattedFXDetails({
  details,
}: {
  details: IDebitDetailsResponse | IFXDetailsResponse | IFeeDetailsResponse
}) {
  if (
    details.fxRate &&
    details.fromAccount.currency !== details.toAccount.currency
  ) {
    return (
      <div className="flex items-center gap-2">
        <span>
          {`${details.fromAccount.currency}:${details.toAccount.currency} ${details.fxRate}`}
        </span>
        <CurrencyText
          amount={details.amount * details.fxRate}
          currency={details.toAccount.currency}
          displayModes={["amount", "code"]}
        />
      </div>
    )
  }
  return "N/A"
}
