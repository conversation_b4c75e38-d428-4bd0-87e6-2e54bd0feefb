import { IFeeDetailsResponse } from "@/data/transactions/transactions.interface"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

import { TransactionDetailRow } from "./TransactionDetailRow"
import { formattedFXDetails } from "./FormattedFXDetails"

export function FeeDetails({ details }: { details: IFeeDetailsResponse }) {
  return (
    <>
      {details.fromAccount && (
        <TransactionDetailRow
          label="From"
          value={`${details.fromAccount.name} (${details.fromAccount.accountNumber})`}
        />
      )}
      {details.toAccount && (
        <TransactionDetailRow
          label="To"
          value={`${details.toAccount.name} (${details.toAccount.accountNumber})`}
        />
      )}
      {details.fxRate && (
        <TransactionDetailRow
          label="FX rate"
          value={formattedFXDetails({ details })}
        />
      )}
      {details.fee && (
        <TransactionDetailRow
          label="Payment Type/Fee"
          value={
            <div className="flex items-center gap-2">
              <span>{`${details.fee.type} /`}</span>
              <CurrencyText
                amount={details.fee.amount}
                currency={details.fee.currency}
                displayModes={["amount", "code"]}
              />
            </div>
          }
        />
      )}
      {details.invoiceId && (
        <TransactionDetailRow label="Invoice ID" value={details.invoiceId} />
      )}
    </>
  )
}
