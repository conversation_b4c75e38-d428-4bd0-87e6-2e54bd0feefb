import { IDebitDetailsResponse } from "@/data/transactions/transactions.interface"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

import { TransactionDetailRow } from "./TransactionDetailRow"
import { formattedFXDetails } from "./FormattedFXDetails"
export function DebitDetails({ details }: { details: IDebitDetailsResponse }) {
  return (
    <>
      {details.fromAccount && (
        <TransactionDetailRow
          label="From"
          value={`${details.fromAccount.name} (${details.fromAccount.accountNumber})`}
        />
      )}
      {details.toAccount && (
        <TransactionDetailRow
          label="To"
          value={`${details.toAccount.name} (${details.toAccount.accountNumber})`}
        />
      )}
      {details.fxRate && (
        <TransactionDetailRow
          label="FX rate"
          value={formattedFXDetails({ details })}
        />
      )}

      {details.fee && (
        <TransactionDetailRow
          label="Payment Type/Fee"
          // value={`${details.fee.type} / ${details.fee.amount} ${details.fee.currency}`}
          value={
            <div className="flex items-center gap-2">
              <span>{`${details.fee.type} / `}</span>
              <CurrencyText
                amount={details.fee.amount}
                currency={details.fee.currency}
                displayModes={["amount", "code"]}
              />
            </div>
          }
        />
      )}

      {details.reference && (
        <TransactionDetailRow label="Reference" value={details.reference} />
      )}
      {details.purpose && (
        <TransactionDetailRow label="Purpose" value={details.purpose} />
      )}
      {details.createdBy && (
        <TransactionDetailRow
          label="Created by"
          value={`${details.createdBy.name} (${details.createdBy.email})`}
        />
      )}
      {details.approvedBy && details.approvedBy.length > 0 && (
        <TransactionDetailRow
          label="Approved by"
          value={details.approvedBy
            .map((approver) => `${approver.email} (${approver.grade})`)
            .join("\n")}
        />
      )}
    </>
  )
}
