import { Info } from "lucide-react"

import { formatDate } from "@/lib/date.utils"
import { Currency } from "@/lib/constants/currency.constants"
import {
  ITransaction,
  TRANSACTION_MAP,
} from "@/data/transactions/transactions.interface"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { BasicPagination } from "@/components/base/pagination/BasicPagination"

import { TransactionTypeChip } from "./TransactionTypeChip"
import { cn } from "@/lib/utils"

interface TransactionsTableProps {
  transactions: ITransaction[]
  onTransactionClick: (transaction: ITransaction) => void
  totalItems: number
  currentFilters: {
    pageNumber: number
    pageSize: number
  }
  onFilterChange: (filters: { pageNumber: number; pageSize: number }) => void
}

export function TransactionsTable({
  transactions,
  onTransactionClick,
  totalItems,
  currentFilters,
  onFilterChange,
}: TransactionsTableProps) {
  if (transactions.length === 0) {
    return (
      <div className="flex h-32 items-center justify-center rounded-lg border border-dashed">
        <p className="text-muted-foreground">No transactions found</p>
      </div>
    )
  }

  return (
    <>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="whitespace-nowrap text-sm font-medium">
                Type
              </TableHead>
              <TableHead className="whitespace-nowrap text-sm font-medium">
                Date
              </TableHead>
              <TableHead className="whitespace-nowrap text-sm font-medium">
                Description
              </TableHead>
              <TableHead className="whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end">
                  Credit
                  <Tooltip>
                    <TooltipTrigger asChild></TooltipTrigger>
                    <TooltipContent className="border-1 border-border bg-background text-muted-foreground shadow-md">
                      Money received into your account
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableHead>
              <TableHead className="whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end">
                  Debit
                  <Tooltip>
                    <TooltipTrigger asChild></TooltipTrigger>
                    <TooltipContent className="border-1 border-border bg-background text-muted-foreground shadow-md">
                      Money that left your account
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableHead>
              <TableHead className="whitespace-nowrap text-right text-sm font-medium">
                Balance
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.map((transaction) => (
              <TransactionRow
                key={transaction.id}
                onClick={() => onTransactionClick(transaction)}
                transaction={transaction}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      {totalItems > 0 && (
        <BasicPagination
          data={{
            totalCount: totalItems,
            pageNumber: currentFilters.pageNumber,
            pageSize: currentFilters.pageSize,
            totalPages: Math.ceil(totalItems / currentFilters.pageSize),
            hasNextPage:
              currentFilters.pageNumber <
              Math.ceil(totalItems / currentFilters.pageSize),
            hasPreviousPage: currentFilters.pageNumber > 1,
            isFirstPage: currentFilters.pageNumber === 1,
            isLastPage:
              currentFilters.pageNumber >=
              Math.ceil(totalItems / currentFilters.pageSize),
            pageStartIndex:
              (currentFilters.pageNumber - 1) * currentFilters.pageSize + 1,
            pageEndIndex: Math.min(
              currentFilters.pageNumber * currentFilters.pageSize,
              totalItems,
            ),
          }}
          onNext={() =>
            onFilterChange({
              ...currentFilters,
              pageNumber: currentFilters.pageNumber + 1,
            })
          }
          onPrevious={() =>
            onFilterChange({
              ...currentFilters,
              pageNumber: Math.max(1, currentFilters.pageNumber - 1),
            })
          }
          totalItems={totalItems}
          pageSize={currentFilters.pageSize}
        />
      )}
    </>
  )
}

function TransactionRow({
  transaction,
  onClick,
}: {
  transaction: ITransaction
  onClick: () => void
}) {
  const isCredit = TRANSACTION_MAP[transaction.transactionType] === "IN"
  const isDebit = TRANSACTION_MAP[transaction.transactionType] === "OUT"
  const isFX = TRANSACTION_MAP[transaction.transactionType] === "FX"
  const currency = transaction.currency || ""

  return (
    <TableRow
      className={cn("hover:bg-muted/5", {
        "cursor-pointer": !!transaction.sourceType,
      })}
      onClick={() => {
        if (!transaction.sourceType) return
        onClick()
      }}
    >
      <TableCell className="py-3">
        <TransactionTypeChip type={transaction.transactionType} />
      </TableCell>
      <TableCell className="py-3">{formatDate(transaction.date)}</TableCell>
      <TableCell className="py-3">
        {transaction.description}
        {transaction.reference && (
          <>
            <br />
            <span className="text-xs text-muted-foreground">
              {transaction.reference}
            </span>
          </>
        )}
      </TableCell>
      <TableCell className="py-3 text-right">
        {isCredit && transaction.amount && (
          <CurrencyText amount={transaction.amount} currency={currency} />
        )}
        {isFX && transaction.direction === "Credit" && transaction.amount && (
          <CurrencyText amount={transaction.amount} currency={currency} />
        )}
      </TableCell>
      <TableCell className="py-3 text-right">
        {isDebit && transaction.amount && (
          <CurrencyText amount={transaction.amount} currency={currency} />
        )}
        {isFX && transaction.direction === "Debit" && transaction.amount && (
          <CurrencyText amount={transaction.amount} currency={currency} />
        )}
      </TableCell>
      <TableCell className="py-3 text-right font-medium">
        <CurrencyText amount={transaction.runningBalance} currency={currency} />
      </TableCell>
    </TableRow>
  )
}
