import { formatAmount, formatDate } from "@/lib/date.utils"
import { Currency } from "@/lib/constants/currency.constants"
import { Card, CardContent } from "@/components/ui/card"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

interface TransactionSummaryCardsProps {
  dateStart: string
  dateEnd: string
  currency: Currency
  summary: {
    openingBalance: number
    closingBalance: number
    totalCredits: number
    totalDebits: number
  }
}

export function TransactionSummaryCards({
  dateStart,
  dateEnd,
  currency,
  summary,
}: TransactionSummaryCardsProps) {
  return (
    <div className="flex gap-4">
      <Card className="rounded-2xl border-0 bg-sidebar">
        <CardContent className="space-y-2 p-4">
          <div
            className="flex items-center justify-between"
            data-testid="opening-balance"
          >
            <div className="text-xs text-muted-foreground">
              Opening balance {currency} ({formatDate(dateStart)})
            </div>
            <div
              className="ml-8 text-sm font-medium"
              data-testid="opening-balance-amount"
            >
              <CurrencyText
                amount={summary.openingBalance}
                currency={currency}
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              Closing balance {currency} ({formatDate(dateEnd)})
            </div>
            <div className="ml-8 text-sm font-medium">
              <CurrencyText
                amount={summary.closingBalance}
                currency={currency}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="w-fit rounded-2xl border-0 bg-sidebar">
        <CardContent className="space-y-2 p-4">
          <div className="flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              Credits {currency}
            </div>
            <div className="ml-32 text-sm font-medium">
              <CurrencyText amount={summary.totalCredits} currency={currency} />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              Debits {currency}
            </div>
            <div className="ml-32 text-sm font-medium">
              <CurrencyText amount={summary.totalDebits} currency={currency} />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
