import { ICreditDetailsResponse } from "@/data/transactions/transactions.interface"

import { TransactionDetailRow } from "./TransactionDetailRow"

export function CreditDetails({
  details,
}: {
  details: ICreditDetailsResponse
}) {
  return (
    <>
      {details.fromAccount && (
        <TransactionDetailRow
          label="From"
          value={`${details.fromAccount.name} (${details.fromAccount.accountNumber})`}
        />
      )}
      {details.toAccount && (
        <TransactionDetailRow
          label="To"
          value={`${details.toAccount.name} (${details.toAccount.accountNumber})`}
        />
      )}
      {details.reference && (
        <TransactionDetailRow label="Reference" value={details.reference} />
      )}
    </>
  )
}
