import { ElementType, useMemo } from "react"
import { Tag } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import {
  ArrowTopRightIcon,
  ArrowBottomRightIcon,
  LoopIcon,
} from "@radix-ui/react-icons"

import { formatDate, formatDateTime } from "@/lib/date.utils"
import { Currency } from "@/lib/constants/currency.constants"
import { useTransactionQuery } from "@/data/transactions/transactions.query"
import {
  IInboundPaymentResponse,
  IOutboundPaymentResponse,
  ITransaction,
  TRANSACTION_MAP,
  TTransactionMap,
} from "@/data/transactions/transactions.interface"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

import { TransactionDetailRow } from "./TransactionDetailRow"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { PaymentStatusBadge } from "../../payments/components/payment-table/PaymentStatusCell"
import { format } from "date-fns"
import { formatAccountNumber } from "@/lib/bank.utils"
import { PayeeDetailsExpandable } from "../../payees/components/PayeeDetailsExpandable"
import { TransactionIcon } from "./TransactionTypeChip"

interface TransactionDetailSheetProps {
  transaction: ITransaction
  currency: Currency
  open: boolean
  onOpenChange: (open: boolean) => void
  accountBalanceId: string
}

const icons: Record<TTransactionMap, ElementType> = {
  OUT: ArrowTopRightIcon,
  IN: ArrowBottomRightIcon,
  FX: LoopIcon,
  FEES: Tag,
}

const labels: Record<TTransactionMap, string> = {
  OUT: "Payment out",
  IN: "Payment in",
  FX: "FX Trade",
  FEES: "Fees",
}

export function TransactionDetailSheet({
  transaction,
  open,
  onOpenChange,
}: TransactionDetailSheetProps) {
  const _type = useMemo(() => {
    return TRANSACTION_MAP[transaction.transactionType]
  }, [transaction])

  const typeLabel = useMemo(() => {
    return _type ? labels[_type] : null
  }, [_type])

  const { data: details, isLoading } = useTransactionQuery(transaction)

  const Icon = useMemo(() => {
    return _type ? icons[_type] : null
  }, [_type])

  return (
    <>
      <Sheet onOpenChange={onOpenChange} open={open}>
        <VisuallyHidden>
          <SheetTitle>{typeLabel ?? "Transaction details"}</SheetTitle>
          <SheetDescription>
            {transaction.description ?? "Transaction description"}
          </SheetDescription>
        </VisuallyHidden>
        <SheetContent className="overflow-y-auto sm:max-w-md md:max-w-lg lg:max-w-xl">
          {isLoading && (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="8" />
            </div>
          )}

          {details && typeLabel === "Payment out" && (
            <>
              <OutboundPaymentDetailSheet
                details={details as IOutboundPaymentResponse}
              />
            </>
          )}

          {details && typeLabel === "Payment in" && (
            <InboundPaymentDetailSheet
              details={details as IInboundPaymentResponse}
            />
          )}
        </SheetContent>
      </Sheet>
    </>
  )
}

function OutboundPaymentDetailSheet({
  details,
}: {
  details: IOutboundPaymentResponse
}) {
  const amount = details.amount
  const currency = details.currency
  const isFx = false
  const paymentDetails = details

  const payee = useMemo(() => {
    return {
      id: details.toAccount.id,
      accountName: details.toAccount.accountName,
      accountNumber: details.toAccount.accountNumber,
      iban: details.toAccount.iban,
      type: details.toAccount.type,
      bank: {
        name: details.toAccount.bank.name,
        nationalId: details.toAccount.bank.nationalId,
        nationalIdType: null,
        swiftBic: "",
        country: {
          name: details.toAccount.bank.country,
          formalName: details.toAccount.bank.country,
          codeIso2: details.toAccount.country,
          codeIso3: "",
          codeIso3Numeric: "",
          phoneCode: "",
          ibanLength: 0,
          ibanRegex: null,
          ibanSupported: false,
          accountNumberType: "",
          nationalIdType: "",
          isSepaCountry: false,
          paymentPurposeCodeRequired: false,
        },
      },
    }
  }, [details.toAccount])
  return (
    <>
      <div className="mt-8 flex items-center gap-3">
        <TransactionIcon type="OUT" />
        <span className="text-lg font-medium">Payment out</span>
        <div className="ml-auto flex items-center">
          <PaymentStatusBadge
            status={details.currentStatus}
            paymentType="OutboundPayment"
          />
        </div>
      </div>

      <div className="mt-4 space-y-1 rounded-2xl bg-muted/50 p-4">
        <div className="mb-6 mt-3">
          <div className="text-2xl font-semibold">
            {amount && <CurrencyText amount={amount} currency={currency} />}
          </div>
        </div>

        <TransactionDetailRow
          label="Payment date"
          value={formatDate(details.valueDate)}
        />
        <TransactionDetailRow
          label="From"
          value={
            details.fromAccount?.clientAccount?.accountName +
              formatAccountNumber(
                details.fromAccount?.clientAccount?.virtualIban,
              ) || "N/A"
          }
        />
        <TransactionDetailRow
          label="To"
          value={details.toAccount.accountName || "N/A"}
        />
        <TransactionDetailRow
          label=""
          value={
            details.toAccount && (
              <PayeeDetailsExpandable noBorder noCopy payee={payee} />
            )
          }
        />

        {isFx && (
          <>
            <TransactionDetailRow
              label="Fx Rate"
              value={"Set on final approval"}
            />
            <TransactionDetailRow
              label="Payee receives"
              value={"Set on final approval"}
            />
          </>
        )}
      </div>

      <div className="mt-4 space-y-1 rounded-2xl bg-muted/50 p-4">
        <TransactionDetailRow label="Code" value={details?.code} />
        <TransactionDetailRow
          label="Created by"
          value={
            details?.createdBy?.displayName ||
            details?.createdBy?.email ||
            "N/A"
          }
        />

        <TransactionDetailRow
          label="Created on"
          value={
            details?.createdAt
              ? format(new Date(details?.createdAt), "d MMM yyyy HH:mm")
              : "N/A"
          }
        />

        <TransactionDetailRow
          label="Payment type"
          value={details?.type.toLocaleUpperCase()}
        />

        <TransactionDetailRow
          label="Payment reference"
          value={details?.reference}
        />

        <TransactionDetailRow label="Purpose" value={details?.purpose} />
        {paymentDetails?.feeOption && (
          <TransactionDetailRow label="Fee option" value={details.feeOption} />
        )}
      </div>
    </>
  )
}

function InboundPaymentDetailSheet({
  details,
}: {
  details: IInboundPaymentResponse
}) {
  const amount = details.amount
  const currency = details.currency
  return (
    <>
      <div className="mt-8 flex items-center gap-3">
        <TransactionIcon type="IN" />
        <span className="text-lg font-medium">Payment in</span>
      </div>
      <div className="mt-4 space-y-1 rounded-2xl bg-muted/50 p-4">
        <div className="mb-6 mt-3">
          <div className="text-2xl font-semibold">
            {amount && (
              <div className="flex items-center gap-2">
                <CurrencyText amount={amount} currency={currency} />
              </div>
            )}
          </div>
        </div>
        <TransactionDetailRow
          label="From"
          value={details.client.name || "N/A"}
        />
        <TransactionDetailRow label="Code" value={details?.code} />
        <TransactionDetailRow
          label="Payment date"
          value={details?.valueDate ? formatDate(details?.valueDate) : "N/A"}
        />
        <TransactionDetailRow
          label="To"
          value={
            details.currencyAccount?.clientAccount?.accountName +
              formatAccountNumber(
                details.currencyAccount?.clientAccount?.virtualIban ||
                  details.currencyAccount?.clientAccount?.accountNumber,
              ) || "N/A"
          }
        />
        <TransactionDetailRow label="Reference" value={details.reference} />
        <TransactionDetailRow
          label="Created by"
          value={
            details?.createdBy?.displayName ||
            details?.createdBy?.email ||
            "N/A"
          }
        />
        <TransactionDetailRow
          label="Created on"
          value={
            details?.createdAt ? formatDateTime(details?.createdAt) : "N/A"
          }
        />
      </div>
    </>
  )
}
