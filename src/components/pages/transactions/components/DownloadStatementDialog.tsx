import { DateRange } from "react-day-picker"
import { useState } from "react"
import { Download } from "lucide-react"

import { Currency } from "@/lib/constants/currency.constants"
import { formatAccountNumber } from "@/lib/bank.utils"
import { useDownloadTransactionsMutation } from "@/data/transactions/transactions.query"
import { IDownloadTransactionsParams } from "@/data/transactions/transactions.interface"
import { Account } from "@/data/account/account.interface"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"

interface DownloadStatementDialogProps {
  dateRange: DateRange
  currencies: Currency[]
  accountBalanceId: string
  selectedAccount: Account | undefined
  hideText?: boolean
  hasTransactions?: boolean
}

export function DownloadStatementDialog({
  dateRange,
  currencies,
  accountBalanceId,
  selectedAccount,
  hideText,
  hasTransactions = true,
}: DownloadStatementDialogProps) {
  const [format, setFormat] = useState<"PDF" | "CSV">("PDF")
  const [selectedCurrencies, setSelectedCurrencies] = useState<Currency[]>([])
  const [open, setOpen] = useState(false)

  const downloadMutation = useDownloadTransactionsMutation()

  const handleDownload = async () => {
    try {
      if (!currencies.length) {
        console.error("No currencies available for download")
        return
      }

      const fileType = format.toLowerCase()
      if (fileType !== "pdf" && fileType !== "csv") {
        console.error("Invalid file type selected")
        return
      }

      const params: IDownloadTransactionsParams = {
        accountId: accountBalanceId,
        fileType: fileType as "csv" | "pdf",
        currency: currencies[0],
        fromDate: dateRange.from?.toISOString().split("T")[0] || "",
        toDate: dateRange.to?.toISOString().split("T")[0] || "",
      }

      const blob = await downloadMutation.mutateAsync(params)

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = `transactions-${fileType}-${new Date().toISOString()}.${fileType}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error downloading transactions:", error)
    }
  }

  return (
    <Popover onOpenChange={setOpen} open={open}>
      <PopoverTrigger asChild>
        <Button className="gap-2" variant="default" disabled={!hasTransactions}>
          <Download className="h-4 w-4" />
          {!hideText && "Download statement"}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-96 p-4">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium">
              {selectedAccount?.accountName}
              <span className="text-muted-foreground">
                {" "}
                {formatAccountNumber(selectedAccount?.virtualIban ?? "")}
              </span>
            </h3>
            <p className="text-sm text-muted-foreground">
              {dateRange.from?.toLocaleDateString()} -{" "}
              {dateRange.to?.toLocaleDateString()}
            </p>
          </div>

          <div className="space-y-2">
            <p className="text-sm">Format</p>
            <RadioGroup
              className="gap-4"
              defaultValue={format}
              onValueChange={(value) => setFormat(value as "PDF" | "CSV")}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem id="pdf" value="PDF" />
                <Label
                  className="text-xs font-normal text-foreground"
                  htmlFor="pdf"
                >
                  PDF
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem id="csv" value="CSV" />
                <Label className="text-xs font-normal" htmlFor="csv">
                  CSV
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* <p className="text-sm">Currency</p>

          <div className="border rounded-lg p-2">
            <div className="max-h-40 space-y-2 overflow-auto">
              {currencies.map((currency) => (
                <div className="flex items-center space-x-2" key={currency}>
                  <Checkbox
                    checked={selectedCurrencies.includes(currency)}
                    id={currency}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedCurrencies([...selectedCurrencies, currency])
                      } else {
                        setSelectedCurrencies(
                          selectedCurrencies.filter((c) => c !== currency),
                        )
                      }
                    }}
                  />
                  <Label className="text-xs font-normal" htmlFor={currency}>
                    {currency}
                  </Label>
                </div>
              ))}
            </div>
          </div> */}

          <div className="flex justify-end gap-2">
            <Button onClick={() => setOpen(false)} variant="link-muted">
              Cancel
            </Button>
            <Button
              disabled={!hasTransactions || !currencies.length}
              onClick={() => {
                handleDownload()
                setOpen(false)
              }}
              variant="default"
            >
              Download
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
