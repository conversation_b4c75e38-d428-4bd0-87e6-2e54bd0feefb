import { DateRange } from "react-day-picker"

import { Currency } from "@/lib/constants/currency.constants"
import { Account } from "@/data/account/account.interface"
import { AccountSelector } from "@/components/pages/account/components/AccountSelector"
import { CurrencySelector } from "@/components/base/currency/CurrencySelector"

import { TransactionDateRangePicker } from "./TransactionDateRangePicker"

interface TransactionFiltersProps {
  accounts: Account[]
  vBan: string
  currency: Currency
  date?: DateRange
  onAccountChange: (accountId: string) => void
  onCurrencyChange: (currency: Currency) => void
  onDateChange: (date: DateRange | undefined) => void
  availableCurrencies: Currency[]
}

export function TransactionFilters({
  accounts,
  vBan,
  currency,
  date,
  onAccountChange,
  onCurrencyChange,
  onDateChange,
  availableCurrencies,
}: TransactionFiltersProps) {
  return (
    <div className="flex gap-4">
      <div className="min-w-[200px] flex-1">
        <AccountSelector
          accounts={accounts}
          className="h-10 w-full rounded-xl border border-input bg-background shadow-sm"
          onChange={onAccountChange}
          selector="virtualIban"
          value={vBan}
        />
      </div>

      <CurrencySelector
        availableCurrencies={availableCurrencies}
        buttonClassName="border rounded-xl px-3 py-1 h-full bg-background shadow-sm"
        currency={currency}
        onChange={onCurrencyChange}
      />

      <div className="flex-1">
        <TransactionDateRangePicker
          className="h-10 w-fit rounded-xl border border-input bg-background shadow-sm hover:bg-background/50"
          date={date}
          onDateChange={onDateChange}
        />
      </div>
    </div>
  )
}
