import { ElementType } from "react"
import { Tag } from "lucide-react"
import { TooltipArrow } from "@radix-ui/react-tooltip"
import {
  ArrowTopRightIcon,
  ArrowBottomRightIcon,
  LoopIcon,
  LayersIcon,
} from "@radix-ui/react-icons"

import {
  TRANSACTION_MAP,
  TTransactionMap,
  TTransactionType,
} from "@/data/transactions/transactions.interface"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

const TransactionTypeIcons: Record<TTransactionMap, ElementType> = {
  OUT: ArrowTopRightIcon,
  IN: ArrowBottomRightIcon,
  FX: LoopIcon,
  FEES: Tag,
}

const TransactionTypeLabelsExtended: Record<TTransactionMap, string> = {
  OUT: "Payment out",
  IN: "Payment in",
  FX: "FX Trade",
  FEES: "Fees",
}

export function TransactionIcon({
  type,
  className,
}: {
  type: TTransactionMap
  className?: string
}) {
  const Icon = TransactionTypeIcons[type]
  return <Icon className={cn("h-5 w-5 text-gray-500", className)} />
}

export function TransactionTypeChip({ type }: { type: TTransactionType }) {
  const displayType = TRANSACTION_MAP[type]
  const Icon = TransactionTypeIcons[displayType]

  if (!Icon) return null

  if (!Icon) {
    return null
  }

  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-2 rounded-full px-3 py-1">
              <Icon className="h-5 w-5 text-gray-500" />
            </div>
          </TooltipTrigger>
          <div className="relative">
            <TooltipContent
              className="flex items-center gap-2 rounded-[10px] border-none bg-sidebar-primary px-3 py-1.5 text-xs text-sidebar-primary-foreground"
              side="right"
              sideOffset={2}
            >
              <div className="absolute -left-1 top-1/2 h-3 w-3 -translate-y-1/2 rotate-45 bg-sidebar-primary" />
              <p className="relative z-10 text-sm">
                {TransactionTypeLabelsExtended[displayType]}
              </p>
            </TooltipContent>
          </div>
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}
