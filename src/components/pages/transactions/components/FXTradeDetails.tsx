import { IFXDetailsResponse } from "@/data/transactions/transactions.interface"
import { CurrencyText } from "@/components/base/currency/CurrencyText"

import { TransactionDetailRow } from "./TransactionDetailRow"
import { formattedFXDetails } from "./FormattedFXDetails"

export function FXTradeDetails({ details }: { details: IFXDetailsResponse }) {
  return (
    <>
      {details.fromAccount && (
        <TransactionDetailRow
          label="From"
          value={`${details.fromAccount.currency})`}
        />
      )}
      {details.toAccount && (
        <TransactionDetailRow
          label="To"
          value={`${details.toAccount.currency})`}
        />
      )}
      {details.fxRate && (
        <TransactionDetailRow
          label="FX rate"
          value={formattedFXDetails({ details })}
        />
      )}
      {details.fee ? (
        <TransactionDetailRow
          label="Payment type/fee"
          value={
            <div className="flex items-center gap-2">
              <span>{`${details.fee?.type} / `}</span>
              <CurrencyText
                amount={details.fee?.amount || 0}
                currency={details.fee?.currency || ""}
                displayModes={["amount", "code"]}
              />
            </div>
          }
        />
      ) : (
        <TransactionDetailRow
          label="Payment type/fee"
          value={<span>N/A</span>}
        />
      )}
      {details.createdBy && (
        <TransactionDetailRow
          label="Created by"
          value={`${details.createdBy.name} (${details.createdBy.email})`}
        />
      )}
      {details.approvedBy && details.approvedBy.length > 0 && (
        <TransactionDetailRow
          label="Approved by"
          value={details.approvedBy
            .map((approver) => `${approver.email} (${approver.grade})`)
            .join("\n")}
        />
      )}
    </>
  )
}
