import { DateRange } from "react-day-picker"
import { useState } from "react"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"

import { cn } from "@/lib/utils"
import { Separator } from "@/components/ui/separator"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"

interface TransactionDateRangePickerProps {
  date?: DateRange
  onDateChange: (date: DateRange | undefined) => void
  className?: string
}

const handleTodayClick = () => {
  const today = new Date()
  return {
    from: today,
    to: today,
  }
}

const handlePreviousDayClick = () => {
  const today = new Date()
  const previousDay = new Date(today)
  previousDay.setDate(today.getDate() - 1)

  // If previous day is Saturday, go back to Friday
  if (previousDay.getDay() === 6) {
    previousDay.setDate(previousDay.getDate() - 1)
  }
  // If previous day is Sunday, go back to Friday
  if (previousDay.getDay() === 0) {
    previousDay.setDate(previousDay.getDate() - 2)
  }

  return {
    from: previousDay,
    to: previousDay,
  }
}

function getPreviousMonthRange(): { from: Date; to: Date } {
  const now = new Date()

  // Get the first day of the current month
  const firstDayOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1)

  // The last day of the previous month is one day before the first day of current month
  const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth.getTime() - 1)

  // The first day of the previous month is the 1st day of that month
  const firstDayOfPreviousMonth = new Date(
    lastDayOfPreviousMonth.getFullYear(),
    lastDayOfPreviousMonth.getMonth(),
    1,
  )

  return {
    from: firstDayOfPreviousMonth,
    to: lastDayOfPreviousMonth,
  }
}

function getCurrentMonthToDateRange(): { from: Date; to: Date } {
  const now = new Date()

  // First day of current month at 00:00:00.000
  const firstDayOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  firstDayOfCurrentMonth.setHours(0, 0, 0, 0)

  return {
    from: firstDayOfCurrentMonth,
    to: now,
  }
}

export function TransactionDateRangePicker({
  date,
  onDateChange,
  className,
}: TransactionDateRangePickerProps) {
  const [showCalendar, setShowCalendar] = useState(false)
  const [open, setOpen] = useState(false)
  const [tempDate, setTempDate] = useState<DateRange | undefined>(date)

  const handlePresetClick = (preset: DateRange) => {
    onDateChange(preset)
    setOpen(false)
  }

  const handleOpenChange = (open: boolean) => {
    setOpen(open)
    if (!open) {
      setShowCalendar(false)
      setTempDate(date)
    }
  }

  return (
    <Popover onOpenChange={handleOpenChange} open={open}>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            "items-center justify-between text-left font-normal",
            !date && "text-muted-foreground",
            className,
          )}
          variant="outline"
        >
          <div className="flex items-center">
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "dd MMM yyyy")} -{" "}
                  {format(date.to, "dd MMM yyyy")}
                </>
              ) : (
                format(date.from, "dd MMM yyyy")
              )
            ) : (
              <span>Pick a date</span>
            )}
            <CalendarIcon className="ml-2 mr-2 h-4 w-4 opacity-45" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="!w-auto rounded-2xl p-0">
        {!showCalendar ? (
          <div className="flex flex-col p-2">
            <Button
              className="justify-start text-muted-foreground"
              onClick={() => handlePresetClick(handleTodayClick())}
              variant="ghost"
            >
              Today
            </Button>
            <Button
              className="justify-start text-muted-foreground"
              onClick={() => handlePresetClick(handlePreviousDayClick())}
              variant="ghost"
            >
              Previous working day
            </Button>
            <Button
              className="justify-start text-muted-foreground"
              onClick={() => {
                const { from, to } = getCurrentMonthToDateRange()
                handlePresetClick({
                  from: from,
                  to: to,
                })
              }}
              variant="ghost"
            >
              This month
            </Button>
            <Button
              className="justify-start text-muted-foreground"
              onClick={() => {
                const { from, to } = getPreviousMonthRange()
                handlePresetClick({
                  from: from,
                  to: to,
                })
              }}
              variant="ghost"
            >
              Last month
            </Button>
            <Button
              className="justify-start text-muted-foreground"
              onClick={() => {
                const today = new Date()
                const oneYearAgo = new Date(
                  today.getFullYear() - 1,
                  today.getMonth(),
                  today.getDate(),
                )
                handlePresetClick({
                  from: oneYearAgo,
                  to: today,
                })
              }}
              variant="ghost"
            >
              Last 12 months
            </Button>
            <Separator className="my-1" />
            <Button
              className="justify-start text-muted-foreground"
              onClick={() => setShowCalendar(true)}
              variant="ghost"
            >
              Custom range
            </Button>
          </div>
        ) : (
          <div className="flex flex-col">
            <Calendar
              defaultMonth={date?.from}
              autoFocus
              mode="range"
              numberOfMonths={2}
              onSelect={setTempDate}
              selected={tempDate}
            />
            <div className="flex items-center justify-end border-t p-3">
              <Button
                className="text-gray-500 underline underline-offset-4"
                onClick={() => {
                  setShowCalendar(false)
                  setOpen(false)
                }}
                variant="ghost"
              >
                Cancel
              </Button>
              <Button
                disabled={!tempDate?.from || !tempDate?.to}
                onClick={() => {
                  onDateChange(tempDate)
                  setOpen(false)
                }}
                variant="default"
              >
                Confirm range
              </Button>
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  )
}
