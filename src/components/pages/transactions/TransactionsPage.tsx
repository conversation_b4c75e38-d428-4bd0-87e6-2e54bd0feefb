import { <PERSON><PERSON><PERSON><PERSON> } from "react-day-picker"
import { useState, useEffect, useMemo } from "react"
import { useNavigate, useSearch } from "@tanstack/react-router"

import { cn } from "@/lib/utils"
import { Currency } from "@/lib/constants/currency.constants"
import { useMediaQuery } from "@/hooks/useMediaQuery"
import {
  useDownloadTransactionsMutation,
  useAccountTransactionsQuery,
} from "@/data/transactions/transactions.query"
import { ITransaction } from "@/data/transactions/transactions.interface"
import { useAccountStore } from "@/data/account/account.store"
import { useAccountsListQuery } from "@/data/account/account.query"
import { Account } from "@/data/account/account.interface"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

import { TransactionsTable } from "./components/TransactionsTable"
import { TransactionFilters } from "./components/TransactionFilters"
import { TransactionDetailSheet } from "./components/TransactionDetailSheet"
import { DownloadStatementDialog } from "./components/DownloadStatementDialog"
import { TradeDetailsSheet } from "../trade/components/TradeDetailsSheet"

interface ITransactionsPageProps {
  entityId: string
}

export function TransactionsPage({ entityId }: ITransactionsPageProps) {
  const search: any = useSearch({
    strict: false,
  })

  const [selectedTransaction, setSelectedTransaction] =
    useState<ITransaction | null>(null)
  const [sheetOpen, setSheetOpen] = useState(false)
  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1, 23),
    to: new Date(),
  })
  const [accountBalanceId, setAccountBalanceId] = useState<string>("")
  const [availableCurrencies, setAvailableCurrencies] = useState<Currency[]>([])
  const [vBan, setVBan] = useState<string>(search.id || "")
  const [pageNumber, setPageNumber] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const { data: accounts } = useAccountsListQuery()

  const { displayCurrency } = useAccountStore()
  const [currency, setCurrency] = useState<Currency>(
    search.currency || displayCurrency,
  )

  // Find the currently selected account at the component level
  const selectedAccount: Account | undefined = useMemo(
    () => accounts?.find((account) => account.virtualIban === vBan),
    [accounts, vBan],
  )

  const { mutate: downloadTransactions, isPending: isDownloading } =
    useDownloadTransactionsMutation()

  const { data, isLoading, isError } = useAccountTransactionsQuery(entityId, {
    pageNumber,
    pageSize,
    accountId: accountBalanceId,
    currency: currency,
    fromDate:
      date?.from?.toISOString().split("T")[0] ||
      new Date(new Date().getFullYear(), new Date().getMonth(), 1, 23)
        .toISOString()
        .split("T")[0],
    toDate:
      date?.to?.toISOString().split("T")[0] ||
      new Date().toISOString().split("T")[0],
    orderByField: "createdAt",
    orderByDirection: "desc",
  })
  const navigate = useNavigate()

  useEffect(() => {
    const newSearch = {
      ...search,
    }
    if (newSearch.currency !== currency && currency) {
      newSearch.currency = currency
    }
    if (newSearch.id !== vBan && vBan) {
      newSearch.id = vBan
    }
    navigate({
      to: "/$entityId/transactions",
      search: newSearch,
      params: { entityId },
      replace: true,
    })
  }, [currency, vBan])

  const isMobile = useMediaQuery("(max-width: 1245px)")

  useEffect(() => {
    if (accounts && accounts.length > 0) {
      // If no account is selected yet, select the first account
      if (!vBan) {
        const firstAccount = accounts[0]
        setVBan(firstAccount.virtualIban)
      }

      // Find the currently selected account
      if (selectedAccount) {
        // Get available currencies for the selected account
        const newAvailableCurrencies = selectedAccount.balances.map(
          (balance) => balance.currency,
        )
        setAvailableCurrencies(newAvailableCurrencies)

        // Find the balance for the current display currency
        const balance = selectedAccount.balances.find(
          (balance) => balance.currency === currency,
        )

        // If the current display currency is not available in this account,
        // switch to the first available currency
        if (!balance && newAvailableCurrencies.length > 0) {
          setCurrency(newAvailableCurrencies[0])
        } else if (balance) {
          setAccountBalanceId(balance.id)
        }
      }
    }
  }, [accounts, vBan, currency, selectedAccount])

  const handleAccountChange = (newVirtualIban: string) => {
    setVBan(newVirtualIban)
    const selectedAccount = accounts?.find(
      (account) => account.virtualIban === newVirtualIban,
    )

    // Get available currencies for the selected account
    const newAvailableCurrencies =
      selectedAccount?.balances.map((balance) => balance.currency) || []
    setAvailableCurrencies(newAvailableCurrencies)

    // If the current displayCurrency is not available in the new account,
    // switch to the first available currency
    if (
      newAvailableCurrencies.length > 0 &&
      !newAvailableCurrencies.includes(currency)
    ) {
      setCurrency(newAvailableCurrencies[0])
    }
  }

  const handleCurrencyChange = (newCurrency: Currency) => {
    setCurrency(newCurrency)
  }

  const handleDateChange = (newDate: DateRange | undefined) => {
    setDate(newDate)
  }

  if (isError) {
    return <div>Error fetching transactions</div>
  }

  return (
    <div className="flex flex-col gap-4 px-4">
      <div className="flex items-center justify-between">
        <div
          className={cn(
            "flex w-full items-center",
            isMobile ? "flex-col space-y-2" : "flex-row",
          )}
        >
          <div className={cn("w-full", !isMobile && "w-3/4")}>
            <TransactionFilters
              accounts={accounts || []}
              availableCurrencies={availableCurrencies}
              currency={currency}
              date={date}
              onAccountChange={handleAccountChange}
              onCurrencyChange={handleCurrencyChange}
              onDateChange={handleDateChange}
              vBan={vBan}
            />
          </div>
          <div className={cn("flex w-full justify-end", !isMobile && "w-1/4")}>
            <DownloadStatementDialog
              accountBalanceId={accountBalanceId}
              currencies={availableCurrencies}
              dateRange={date || { from: new Date(), to: new Date() }}
              selectedAccount={selectedAccount}
              hasTransactions={Boolean(
                data?.data && Array.isArray(data.data) && data.data.length > 0,
              )}
            />
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="mt-1 flex h-96 w-full items-center justify-center">
          <LoadingSpinner size="8" />
        </div>
      )}

      {data && (
        <TransactionsTable
          onTransactionClick={(transaction) => {
            setSelectedTransaction(transaction)
            setSheetOpen(true)
          }}
          transactions={data.data}
          totalItems={data.totalCount}
          currentFilters={{
            pageNumber,
            pageSize,
          }}
          onFilterChange={(filters) => {
            setPageNumber(filters.pageNumber)
            setPageSize(filters.pageSize)
          }}
        />
      )}

      {selectedTransaction &&
        selectedTransaction.sourceType &&
        selectedTransaction.sourceType !== "FxTrade" && (
          <TransactionDetailSheet
            accountBalanceId={accountBalanceId}
            currency={currency}
            onOpenChange={setSheetOpen}
            open={sheetOpen}
            transaction={selectedTransaction}
          />
        )}

      {selectedTransaction && selectedTransaction.sourceType === "FxTrade" && (
        <TradeDetailsSheet
          id={selectedTransaction.sourceId}
          onClose={() => {
            setSelectedTransaction(null)
            setSheetOpen(false)
          }}
        />
      )}
    </div>
  )
}
