import { cn } from "@/lib/utils"
import { ITradeStatus } from "@/data/trade/trade.interface"
import { CheckCircleIcon, ClockIcon, XCircleIcon } from "lucide-react"

function TradeStatusBadge({ status }: { status: ITradeStatus }) {
  const variants = {
    Brokered: "bg-warning/5 text-warning border-warning/50 stroke-warning/50",
    Settled: "bg-success/10 text-success border-success/50 stroke-success/50",
    Failed:
      "bg-destructive/10 text-destructive border-destructive/50 stroke-destructive/50",
  }

  return (
    <span
      className={cn(
        "flex w-fit flex-none items-center gap-1 text-nowrap rounded-lg border px-2 py-1 text-xs",
        variants[status],
      )}
    >
      {status === "Brokered" && <ClockIcon className="h-3 w-fit" />}
      {status === "Settled" && <CheckCircleIcon className="h-3 w-fit" />}
      {status === "Failed" && <XCircleIcon className="h-3 w-fit" />}
      {status}
    </span>
  )
}

export default TradeStatusBadge
