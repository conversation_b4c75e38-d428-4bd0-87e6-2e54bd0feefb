import { useMemo } from "react"
import { TriangleAlert } from "lucide-react"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import { formatDate } from "@/lib/date.utils"
import {
  useDownloadTradeConfirmationMutation,
  useTradeByIdQuery,
} from "@/data/trade/trade.query"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DisplayCard, DisplayCardRow } from "@/components/base/card/DisplayCard"
import TradeStatusBadge from "./TradeStatusBadge"
import { TransactionIcon } from "../../transactions/components/TransactionTypeChip"
import { Link, useNavigate } from "@tanstack/react-router"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
interface TradeDetailsSheetProps {
  id?: string
  onClose?: () => void
}

export function TradeDetailsSheet({ id, onClose }: TradeDetailsSheetProps) {
  const { data: trade, isLoading, isError } = useTradeByIdQuery(id)
  const { entity } = useLoaderData()
  const navigate = useNavigate()
  const isSettled = useMemo(() => {
    return trade?.status === "Settled"
  }, [trade])

  function onOpenChange(open: boolean) {
    if (!open) {
      onClose?.()
    }
  }

  const downloadMutation = useDownloadTradeConfirmationMutation()

  const handleTradeConfirmationDownload = async () => {
    try {
      if (!trade?.id) {
        console.error("No trade ID provided for download")
        return
      }

      const blob = await downloadMutation.mutateAsync(trade.id)

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = `trade-${trade.code}-${new Date().toISOString()}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error downloading trade confirmation:", error)
    }
  }

  return (
    <Sheet onOpenChange={onOpenChange} open={!!id}>
      <VisuallyHidden>
        <SheetTitle>Trade details</SheetTitle>
        <SheetDescription>
          View trade details and settlement information
        </SheetDescription>
      </VisuallyHidden>
      <SheetContent className="flex flex-col gap-4 overflow-y-auto sm:max-w-md md:max-w-lg lg:max-w-xl">
        {isError && (
          <>
            <div className="mb-6 mt-10 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TransactionIcon type="FX" />
                <h2 className="text-xl font-semibold">Trade</h2>
              </div>
              <TradeStatusBadge status={"Failed"} />
            </div>
            <div className="flex h-96 items-center justify-center">
              <p className="text-red-500">Error loading trade details</p>
            </div>
          </>
        )}
        {isLoading ? (
          <>
            <div className="mb-6 mt-10 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TransactionIcon type="FX" />
                <h2 className="text-xl font-semibold">Trade</h2>
              </div>
            </div>
            <div className="flex h-96 items-center justify-center">
              <LoadingSpinner size="8" />
            </div>
          </>
        ) : trade ? (
          <>
            <div className="mb-6 mt-10 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TransactionIcon type="FX" />

                <h2 className="text-xl font-semibold">
                  Trade {trade.sell.currency}:{trade.buy.currency}
                </h2>
              </div>
              <TradeStatusBadge status={trade.status} />
            </div>

            {trade.settlementDetails && (
              <div className="flex flex-col gap-4">
                <Card className="p-4">
                  <div className="flex gap-2">
                    <div className="flex-none">
                      <TriangleAlert className="h-6 w-6 text-warning" />
                    </div>
                    <div className="flex flex-col gap-1">
                      <h3 className="text-base font-medium text-warning">
                        Settlement required
                      </h3>
                      <p className="text-sm font-semibold">
                        Please ensure that that you settle your account using
                        the details below by 17:00 {formatDate(trade.valueDate)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        For quicker settlement, please reference the Trade code{" "}
                        {trade.code}
                      </p>
                    </div>
                  </div>
                </Card>
                <DisplayCard title="Settlement details">
                  <DisplayCardRow
                    label="Account name"
                    value={trade.settlementDetails.accountName}
                    copyable
                  />
                  <DisplayCardRow
                    label="IBAN"
                    value={trade.settlementDetails.iban}
                    copyable
                  />
                  <DisplayCardRow
                    label="Bank name"
                    value={trade.settlementDetails.bankName}
                    copyable
                  />
                  <DisplayCardRow
                    label="Amount"
                    value={
                      <CurrencyText
                        amount={trade.settlementDetails.money.amount}
                        currency={trade.settlementDetails.money.currency}
                      />
                    }
                  />
                  <DisplayCardRow
                    label="Reference"
                    value={trade.settlementDetails.reference}
                    copyable
                  />
                </DisplayCard>
              </div>
            )}

            <DisplayCard
              title={
                <div className="text-xl">
                  <div className="flex items-center justify-start gap-1">
                    <span>
                      Trade {trade.sell.currency}:{trade.buy.currency}
                    </span>
                  </div>
                </div>
              }
            >
              <DisplayCardRow label="Trade code" value={trade.code} />
              {trade.outboundPaymentId &&
                trade.outboundPaymentCode &&
                entity?.id && (
                  <DisplayCardRow
                    label="View payment code"
                    value={
                      <Link
                        to="/$entityId/payments"
                        params={{ entityId: entity.id }}
                        search={{ paymentId: trade.outboundPaymentCode }}
                        className="text-primary underline hover:text-primary/80"
                      >
                        {trade.outboundPaymentCode}
                      </Link>
                    }
                  />
                )}
              <DisplayCardRow
                label="Contract date"
                value={formatDate(trade.createdAt)}
              />
              <DisplayCardRow
                label="Value date"
                value={formatDate(trade.valueDate)}
              />
              <DisplayCardRow
                label="Sell"
                value={
                  <CurrencyText
                    amount={trade.sell.amount}
                    currency={trade.sell.currency}
                  />
                }
              />
              <DisplayCardRow
                label="Buy"
                value={
                  <div className="flex items-center gap-1">
                    <CurrencyText
                      amount={trade.buy.amount}
                      currency={trade.buy.currency}
                    />
                  </div>
                }
              />
              <DisplayCardRow
                label="FX rate"
                value={
                  <div className="flex flex-col items-end text-right">
                    <span>{trade.sellRate}</span>
                    <span className="text-xs text-muted-foreground">
                      ({trade.buyRate})
                    </span>
                  </div>
                }
              />
              <DisplayCardRow
                label="Instructed by"
                value={`${trade.createdBy.email}`}
              />
              <DisplayCardRow
                label="Created on"
                value={`${formatDate(trade?.createdAt)}`}
              />
            </DisplayCard>
          </>
        ) : null}
        {
          <div className="flex justify-end">
            <Button
              className="inline-flex items-center gap-2 rounded-full bg-green-100 px-3 py-2 transition-colors hover:bg-green-200"
              aria-label="Download trade confirmation PDF"
              onClick={handleTradeConfirmationDownload}
            >
              <img
                src="/download.svg"
                alt="Download"
                className="h-6 w-6"
                style={{ borderRadius: "9999px", padding: "2px" }}
              />
              <span className="sr-only">Download PDF</span>
            </Button>
          </div>
        }
      </SheetContent>
    </Sheet>
  )
}
