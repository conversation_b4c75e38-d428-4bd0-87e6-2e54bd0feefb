import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Des<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { useState, useEffect, useMemo, useRef } from "react"
import { Refresh<PERSON><PERSON>, ArrowR<PERSON> } from "lucide-react"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyFlag } from "@/components/base/currency/CurrencyFlag"
import { ReviewRow } from "@/components/base/review/ReviewRow"
import { AlertDescription, AlertTitle } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { AlertBox } from "@/components/base/alert-box"
import {
  useTradeExecuteMutation,
  useTradeRateQuery,
} from "@/data/trade/trade.query"
import {
  ITradeRateRequestDTO,
  ITradeExecuteResponseDTO,
  ITradeRateResponseDTO,
} from "@/data/trade/trade.interface"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import { formatDate } from "@/lib/date.utils"
import { useNavigate } from "@tanstack/react-router"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import { CopyButton } from "@/components/base/icons/CopyButton"

interface QuoteFlowProps extends ITradeRateRequestDTO {
  isOpen: boolean
  onClose: () => void
  onQuoteAccepted?: () => void
  clientAccountId: string
}

type QuoteState = "quote" | "expired" | "settlement"

const validSeconds = 10

export function QuoteFlow({
  isOpen,
  onClose,
  onQuoteAccepted,
  clientAccountId,
  ...props
}: QuoteFlowProps) {
  const [state, setState] = useState<QuoteState>("quote")
  const [timeLeft, setTimeLeft] = useState(validSeconds)
  const [tradeSettlement, setTradeSettlement] =
    useState<ITradeExecuteResponseDTO | null>(null)
  const timer = useRef<NodeJS.Timeout | null>(null)
  const {
    data: tradeResponse,
    isLoading: isTradeRateLoading,
    isRefetching,
    refetch,
  } = useTradeRateQuery({
    ...props,
  })

  const { mutate: executeTrade, isPending: isTradeExecuting } =
    useTradeExecuteMutation()

  useEffect(() => {
    if (tradeResponse) {
      setTimeLeft(validSeconds)
    }
  }, [tradeResponse])

  useEffect(() => {
    if (state === "quote") {
      timer.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            setState("expired")
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => {
      if (timer.current) {
        clearInterval(timer.current)
      }
    }
  }, [state])

  const handleExecuteTrade = () => {
    // Call your trade execution API here
    if (!tradeResponse) return
    if (timer.current) {
      clearInterval(timer.current)
    }
    executeTrade(
      {
        fxQuoteRequestId: tradeResponse.fxQuoteRequestId,
        fxBrokeredQuoteId: tradeResponse.fxBrokeredQuoteId,
        clientAccountId,
      },
      {
        onSuccess: (data) => {
          setTradeSettlement(data)
          setState("settlement")
          onQuoteAccepted?.()
        },
      },
    )
  }
  const navigate = useNavigate()
  const { entity } = useLoaderData()

  const handleRefreshRate = async () => {
    await refetch()
    setState("quote")
    setTimeLeft(validSeconds)
  }

  const onOpenChange = (open: boolean) => {
    if (!open) {
      if (entity?.id && state === "settlement") {
        navigate({ to: "/$entityId/trades", params: { entityId: entity.id } })
      }
      onClose()
    }
  }

  const progress = useMemo(() => {
    return (timeLeft / validSeconds) * 100
  }, [timeLeft])

  const currentRateText = useMemo(() => {
    if (!tradeResponse) return "-"
    if (tradeResponse.fixedSide === "Sell") {
      return tradeResponse.sellRate.toFixed(7)
    }
    return tradeResponse.buyRate.toFixed(7)
  }, [tradeResponse])

  const currentCurrencies = useMemo(() => {
    if (!tradeResponse) return []
    if (tradeResponse.fixedSide === "Sell") {
      return [tradeResponse.sell.currency, tradeResponse.buy.currency]
    }
    return [tradeResponse.buy.currency, tradeResponse.sell.currency]
  }, [tradeResponse])

  const reverseRateText = useMemo(() => {
    if (!tradeResponse) return "-"
    if (tradeResponse.fixedSide === "Sell") {
      return tradeResponse.buyRate.toFixed(7)
    }
    return tradeResponse.sellRate.toFixed(7)
  }, [tradeResponse])

  const renderContent = () => {
    if (isTradeRateLoading) {
      return (
        <DialogContent className="flex h-32 max-w-md items-center justify-center">
          <VisuallyHidden>
            <DialogHeader>
              <DialogTitle>Loading...</DialogTitle>
              <DialogDescription>
                Please wait while we load the trade rate.
              </DialogDescription>
            </DialogHeader>
          </VisuallyHidden>
          <LoadingSpinner size="4" />
        </DialogContent>
      )
    }

    if (!tradeResponse) {
      return (
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>Please try again later.</DialogDescription>
          </DialogHeader>
          <TradeSummary tradeRequest={props} />

          <AlertBox severity="error">
            <AlertTitle>Error loading trade rate</AlertTitle>
            <AlertDescription>
              It is not possible to to proceed.
            </AlertDescription>
          </AlertBox>
        </DialogContent>
      )
    }

    switch (state) {
      case "quote":
        return (
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Confirm Rate and Trade</DialogTitle>
            </DialogHeader>
            <TradeSummary tradeResponse={tradeResponse} />
            <div className="h-36 space-y-1 rounded-lg bg-muted p-4">
              {timeLeft > 0 && (
                <>
                  <div className="flex flex-col items-center font-semibold">
                    <p className={cn("text-lg")}>
                      {`1 ${currentCurrencies[0]} = ${currentRateText.slice(0, -2)}`}
                      <span className="text-sm">
                        {currentRateText.slice(-2)}
                      </span>
                      <span className="ms-1">{currentCurrencies[1]}</span>
                    </p>
                    <p className={cn("mt-1 text-sm opacity-80")}>
                      {`1 ${currentCurrencies[1]} = ${reverseRateText.slice(0, -2)}`}
                      <span className="text-xs">
                        {reverseRateText.slice(-2)}
                      </span>
                      <span className="ms-1">{currentCurrencies[0]}</span>
                    </p>
                  </div>

                  <div>
                    <div className="h-4 w-full overflow-hidden border border-border bg-white">
                      <div
                        className={cn(
                          "duration-50 h-full transition-all",
                          timeLeft <= 3 ? "bg-destructive" : "bg-[#FFD233]",
                        )}
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                    <div className="mt-2 flex justify-center text-sm">
                      <span>Quote valid for {timeLeft}s</span>
                    </div>
                  </div>
                </>
              )}
            </div>
            <DialogFooter className="flex flex-col gap-2 sm:flex-row">
              <Button onClick={onClose} variant="link">
                Cancel
              </Button>
              <Button
                disabled={timeLeft === 0 || isTradeExecuting}
                onClick={handleExecuteTrade}
              >
                {isTradeExecuting ? "Executing..." : "Execute Trade"}
              </Button>
            </DialogFooter>
          </DialogContent>
        )

      case "expired":
        return (
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Rate Expired</DialogTitle>
            </DialogHeader>
            <TradeSummary tradeResponse={tradeResponse} />
            <div className="flex h-36 flex-col items-center justify-evenly space-y-1 rounded-lg bg-muted p-4">
              <p className="text-center font-semibold text-foreground">
                Rate expired
              </p>
              <Button
                className="h-auto w-full p-0 text-primary hover:text-primary/90"
                disabled={isRefetching}
                onClick={handleRefreshRate}
                variant="link"
              >
                <RefreshCw
                  className={cn("h-5 w-5", isRefetching ? "animate-spin" : "")}
                />
                Refresh rate
              </Button>
            </div>

            <DialogFooter>
              <Button onClick={onClose} variant="link">
                Cancel
              </Button>
              <Button disabled>Execute Trade</Button>
            </DialogFooter>
          </DialogContent>
        )

      case "settlement":
        return (
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Settlement Required</DialogTitle>
              <DialogDescription>
                Please ensure that you settle your account using the details
                below by{" "}
                <strong>
                  17:00{" "}
                  {tradeSettlement?.settlementDetails.valueDate &&
                    formatDate(tradeSettlement?.settlementDetails.valueDate)}
                </strong>
              </DialogDescription>
            </DialogHeader>
            {tradeSettlement?.settlementDetails && (
              <div>
                <ReviewRow
                  className="pb-0"
                  label="Amount"
                  noBorder
                  value={
                    <div className="flex items-center gap-2">
                      <CurrencyText
                        amount={tradeSettlement?.settlementDetails.money.amount}
                        currency={
                          tradeSettlement?.settlementDetails.money.currency
                        }
                      />
                      <CurrencyFlag
                        currency={
                          tradeSettlement?.settlementDetails.money.currency
                        }
                        size="md"
                      />
                    </div>
                  }
                />
                {tradeSettlement?.settlementDetails.bankName && (
                  <ReviewRow
                    className="pb-0"
                    label="Broker Name"
                    noBorder
                    value={
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {tradeSettlement?.settlementDetails.bankName}
                        </span>
                        <CopyButton
                          text={tradeSettlement?.settlementDetails.bankName}
                          label="Broker name"
                        />
                      </div>
                    }
                  />
                )}

                {tradeSettlement?.settlementDetails.accountName && (
                  <ReviewRow
                    className="pb-0"
                    label="Account name"
                    noBorder
                    value={
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {tradeSettlement?.settlementDetails.accountName}
                        </span>
                        <CopyButton
                          text={tradeSettlement?.settlementDetails.accountName}
                          label="Account name"
                        />
                      </div>
                    }
                  />
                )}
                {tradeSettlement?.settlementDetails.iban && (
                  <ReviewRow
                    className="pb-0"
                    label="IBAN"
                    noBorder
                    value={
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {tradeSettlement?.settlementDetails.iban}
                        </span>
                        <CopyButton
                          text={tradeSettlement?.settlementDetails.iban}
                          label="IBAN"
                        />
                      </div>
                    }
                  />
                )}
                {tradeSettlement?.settlementDetails.reference && (
                  <ReviewRow
                    className="pb-0"
                    label="Reference"
                    noBorder
                    value={
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {tradeSettlement?.settlementDetails.reference}
                        </span>
                        <CopyButton
                          text={tradeSettlement?.settlementDetails.reference}
                          label="Reference"
                        />
                      </div>
                    }
                  />
                )}
              </div>
            )}

            <DialogFooter>
              <Button className="w-full" onClick={() => onOpenChange(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        )
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {renderContent()}
    </Dialog>
  )
}

function TradeSummary({
  tradeResponse,
  tradeRequest,
}: {
  tradeResponse?: ITradeRateResponseDTO
  tradeRequest?: ITradeRateRequestDTO
}) {
  if (tradeResponse) {
    return (
      <div className="flex justify-between gap-4">
        <div className="flex flex-1 flex-col items-center gap-2">
          <div className="flex items-center text-muted-foreground">
            <span className="pe-2 font-medium">Sell</span>
            <CurrencyFlag currency={tradeResponse.sell.currency} size="md" />
            <span className="ps-1">{tradeResponse.sell.currency}</span>
          </div>
          <div className="flex items-center justify-between font-bold">
            <CurrencyText
              amount={tradeResponse.sell.amount}
              currency={tradeResponse.sell.currency}
            />
          </div>
        </div>
        <div className="flex w-8 flex-none items-center justify-center">
          <ArrowRight />
        </div>
        <div className="flex flex-1 flex-col items-center gap-2">
          <div className="flex items-center text-muted-foreground">
            <span className="pe-2 font-medium">Buy</span>
            <CurrencyFlag currency={tradeResponse.buy.currency} size="md" />
            <span className="ps-1">{tradeResponse.buy.currency}</span>
          </div>
          <div className="flex items-center justify-between font-bold">
            <CurrencyText
              amount={tradeResponse.buy.amount}
              currency={tradeResponse.buy.currency}
            />
          </div>
        </div>
      </div>
    )
  }

  if (tradeRequest) {
    return (
      <div className="flex justify-between gap-4">
        <div className="flex flex-1 flex-col items-center gap-2">
          <div className="flex items-center text-muted-foreground">
            <span className="pe-2 font-medium">Sell</span>
            <CurrencyFlag currency={tradeRequest.sellCurrency} size="md" />
            <span className="ps-1">{tradeRequest.sellCurrency}</span>
          </div>
          {tradeRequest.fixedSide === "Sell" && (
            <div className="flex items-center justify-between font-bold">
              <CurrencyText
                amount={tradeRequest.amount ?? 0}
                currency={tradeRequest.sellCurrency}
              />
            </div>
          )}
        </div>
        <div className="flex w-8 flex-none items-center justify-center">
          <ArrowRight />
        </div>
        <div className="flex flex-1 flex-col items-center gap-2">
          <div className="flex items-center text-muted-foreground">
            <span className="pe-2 font-medium">Buy</span>
            <CurrencyFlag currency={tradeRequest.buyCurrency} size="md" />
            <span className="ps-1">{tradeRequest.buyCurrency}</span>
          </div>
          {tradeRequest.fixedSide === "Buy" && (
            <div className="flex items-center justify-between font-bold">
              <CurrencyText
                amount={tradeRequest.amount ?? 0}
                currency={tradeRequest.buyCurrency}
              />
            </div>
          )}
        </div>
      </div>
    )
  }
  return null
}
