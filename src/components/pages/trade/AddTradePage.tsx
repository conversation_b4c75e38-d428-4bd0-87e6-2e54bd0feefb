import { useEffect, useMemo, useState } from "react"
import { Link } from "@tanstack/react-router"
import { useForm, useStore } from "@tanstack/react-form"

import { format2Date } from "@/lib/date.utils"
import { Currency } from "@/lib/constants/currency.constants"
import { useValueDatesQuery } from "@/data/trade/trade.query"
import { useAccountsListQuery } from "@/data/account/account.query"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { QuoteFlow } from "@/components/pages/trade/components/QuoteFlow"
import { AccountSelector } from "@/components/pages/account/components/AccountSelector"
import { useLoaderData } from "@/components/layout/entity/$entityId.loader"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"
import { FormField } from "@/components/base/form/form"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencySelector } from "@/components/base/currency/CurrencySelector"
import { DatePicker } from "@/components/base/form/datepicker"
import { CurrencyEnter } from "@/components/base/currency/CurrencyEnter"
import { useCurrencySelector } from "@/hooks/useCurrencySelector"
import { useTradableCurrenciesQuery } from "@/data/global/global.query"

interface TradeForm {
  account?: string
  sellAmount?: number
  sellCurrency: Currency
  buyAmount?: number
  buyCurrency: Currency
  valueDate?: string
}

export function AddTradePage() {
  const { entity } = useLoaderData()
  const { data: accounts, isLoading } = useAccountsListQuery()
  const [showQuoteFlow, setShowQuoteFlow] = useState(false)

  const form = useForm<TradeForm>({
    defaultValues: {
      account: undefined,
      sellAmount: undefined,
      sellCurrency: "GBP",
      buyAmount: undefined,
      buyCurrency: "EUR",
    },
  })

  const buyCurrency = useStore(form.store, (state) => state.values.buyCurrency)
  const sellCurrency = useStore(
    form.store,
    (state) => state.values.sellCurrency,
  )
  const buyAmount = useStore(form.store, (state) => state.values.buyAmount)
  const sellAmount = useStore(form.store, (state) => state.values.sellAmount)

  useEffect(() => {
    if (buyAmount) {
      form.setFieldValue("sellAmount", undefined)
    }
  }, [buyAmount])

  useEffect(() => {
    if (sellAmount) {
      form.setFieldValue("buyAmount", undefined)
    }
  }, [sellAmount])

  const direction = useMemo(() => {
    if (buyAmount) {
      return "Buy"
    }
    return "Sell"
  }, [buyAmount, sellAmount])

  const { data: valueDates, isLoading: isValueDatesLoading } =
    useValueDatesQuery({
      lhsCurrency: sellCurrency,
      rhsCurrency: buyCurrency,
    })

  useEffect(() => {
    if (valueDates?.valueDateOptions.length) {
      form.setFieldValue("valueDate", valueDates.valueDateOptions.at(-1))
    }
  }, [valueDates])

  const { availableCurrencies } = useCurrencySelector(
    accounts ? [accounts[0]] : [],
  )

  const { data: tradableCurrencies, isLoading: tradableCurrenciesLoading } =
    useTradableCurrenciesQuery(accounts?.[0]?.clientAccountId ?? "", {
      enabled: !!accounts?.[0]?.clientAccountId,
    })

  const account = useStore(form.store, (state) => state.values.account)
  const accountDetails = useMemo(() => {
    if (!account) return null
    return accounts?.find((acc) => acc.virtualIban === account)
  }, [account, accounts])

  const sellAccountBalance = useMemo(() => {
    if (!accountDetails) return 0
    return (
      accountDetails.balances.find(
        (balance) => balance.currency === sellCurrency,
      )?.balance ?? 0
    )
  }, [accountDetails, sellCurrency])

  const buyAccountBalance = useMemo(() => {
    if (!accountDetails) return 0
    return (
      accountDetails.balances.find(
        (balance) => balance.currency === buyCurrency,
      )?.balance ?? 0
    )
  }, [accountDetails, buyCurrency])

  const amount = useMemo(() => {
    if (sellAmount) {
      return sellAmount ?? 0
    }
    return buyAmount ?? 0
  }, [sellAmount, buyAmount, buyCurrency, sellCurrency])

  function handleSubmit() {
    if (form.state.values.account && form.state.values.valueDate && amount) {
      setShowQuoteFlow(true)
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-96 w-full items-center justify-center">
        <LoadingSpinner size="8" />
      </div>
    )
  }

  return (
    <div className="content-container flex flex-1 flex-col overflow-x-hidden p-4">
      <div className="mx-auto w-full max-w-md">
        <h1 className="mb-6 text-center text-2xl font-bold">Place a trade</h1>

        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
          }}
        >
          <div className="space-y-3">
            <form.Field
              children={(field) => (
                <FormField field={field} label="Account" required>
                  <AccountSelector
                    accounts={accounts || []}
                    defaultFirst={false}
                    selectOnly={true}
                    value={field.state.value}
                    onChange={(value) => {
                      if (value) {
                        field.handleChange(value)
                      }
                    }}
                  />
                </FormField>
              )}
              name="account"
            />

            <form.Subscribe
              selector={(state) => state.values.account}
              children={(account) => {
                if (!account) return null

                const selectedAccount = accounts?.find(
                  (acc) => acc.virtualIban === account,
                )

                return (
                  <div className="">
                    <Card className="space-y-2 rounded-xl border-none bg-sidebar p-4">
                      {/* Sell Section */}
                      <form.Field
                        name="sellAmount"
                        children={(field) => (
                          <FormField
                            field={field}
                            label="Sell"
                            trailing={
                              <span className="mt-2 text-xs text-muted-foreground">
                                Available balance{" "}
                                <CurrencyText
                                  amount={sellAccountBalance}
                                  currency={sellCurrency}
                                />
                              </span>
                            }
                            required
                          >
                            <form.Field
                              name="sellCurrency"
                              listeners={{
                                onChange: ({ value }) => {
                                  // if sell currency is the same with buy currency swap currencies
                                  if (value === buyCurrency) {
                                    form.setFieldValue(
                                      "buyCurrency",
                                      sellCurrency,
                                    )
                                    form.setFieldValue(
                                      "sellCurrency",
                                      buyCurrency,
                                    )
                                  }
                                },
                              }}
                              children={(_c) => (
                                <div className="flex overflow-hidden rounded-lg border shadow-sm">
                                  <CurrencyEnter
                                    className="flex-1"
                                    inputClassName="bg-background rounded-r-none shadow-none outline-none border-none !ring-0"
                                    currency={_c.state.value}
                                    placeholder="Sell amount"
                                    value={field.state.value}
                                    onChange={(value) => {
                                      field.handleChange(value)
                                    }}
                                  />
                                  <CurrencySelector
                                    currency={_c.state.value as Currency}
                                    availableCurrencies={availableCurrencies}
                                    onChange={(currency) =>
                                      _c.handleChange(currency)
                                    }
                                  />
                                </div>
                              )}
                            />
                          </FormField>
                        )}
                        validators={{
                          onChange: ({ value }) => {
                            if (!value) return "Please enter an amount"
                            if (isNaN(Number(value)))
                              return "Please enter a valid amount"
                            return undefined
                          },
                        }}
                      />

                      {/* Buy Section */}
                      <form.Field
                        name="buyAmount"
                        children={(field) => (
                          <FormField
                            field={field}
                            label="Buy"
                            required
                            trailing={
                              <span className="mt-2 text-xs text-muted-foreground">
                                Available balance{" "}
                                <CurrencyText
                                  amount={buyAccountBalance}
                                  currency={buyCurrency}
                                />
                              </span>
                            }
                          >
                            <form.Field
                              name="buyCurrency"
                              listeners={{
                                onChange: ({ value }) => {
                                  if (value === sellCurrency) {
                                    form.setFieldValue(
                                      "sellCurrency",
                                      buyCurrency,
                                    )
                                    form.setFieldValue(
                                      "buyCurrency",
                                      sellCurrency,
                                    )
                                  }
                                },
                              }}
                              children={(_c) => (
                                <div className="flex overflow-hidden rounded-lg border shadow-sm">
                                  <CurrencyEnter
                                    className="flex-1"
                                    inputClassName="bg-background rounded-r-none shadow-none outline-none border-none !ring-0"
                                    currency={_c.state.value}
                                    placeholder="Buy amount"
                                    value={field.state.value}
                                    onChange={(value) => {
                                      field.handleChange(value)
                                    }}
                                  />
                                  <CurrencySelector
                                    currency={_c.state.value as Currency}
                                    availableCurrencies={
                                      tradableCurrencies?.map(
                                        (c) => c.code as Currency,
                                      ) ?? []
                                    }
                                    onChange={(currency) =>
                                      _c.handleChange(currency)
                                    }
                                  />
                                </div>
                              )}
                            />
                          </FormField>
                        )}
                        validators={{
                          onChange: ({ value }) => {
                            if (!value) return "Please enter an amount"
                            if (isNaN(Number(value)))
                              return "Please enter a valid amount"
                            return undefined
                          },
                        }}
                      />
                    </Card>
                    {isValueDatesLoading ? (
                      <div className="flex h-24 w-full items-center justify-center p-4">
                        <LoadingSpinner size="4" />
                      </div>
                    ) : (
                      <div className="mt-4 ps-2">
                        <form.Field
                          children={(field) => (
                            <FormField
                              field={field}
                              label="Value date"
                              required
                              info="The date the trade will be executed"
                            >
                              <DatePicker
                                value={
                                  field.state.value
                                    ? new Date(field.state.value)
                                    : undefined
                                }
                                onChange={(value) => {
                                  field.handleChange(
                                    value ? format2Date(value) : undefined,
                                  )
                                }}
                                className="mt-2 h-12 rounded-lg shadow-sm"
                                calendarProps={{
                                  disabled: (date) => {
                                    if (
                                      valueDates?.valueDateOptions.includes(
                                        format2Date(date),
                                      )
                                    ) {
                                      return false
                                    }
                                    return true
                                  },
                                }}
                              />
                            </FormField>
                          )}
                          name="valueDate"
                        />
                      </div>
                    )}
                    <div className="mx-2 mt-4 flex gap-3">
                      <Button
                        type="submit"
                        disabled={!amount}
                        onClick={() => handleSubmit()}
                      >
                        Get quote
                      </Button>
                      <Link
                        to="/$entityId/trades"
                        params={{ entityId: entity.id! }}
                      >
                        <Button type="button" variant="link-muted">
                          Cancel
                        </Button>
                      </Link>
                    </div>
                  </div>
                )
              }}
            />
          </div>
        </form>
      </div>
      {showQuoteFlow && form.state.values.valueDate && accountDetails && (
        <QuoteFlow
          isOpen={showQuoteFlow}
          onClose={() => setShowQuoteFlow(false)}
          amount={amount}
          fixedSide={direction}
          valueDate={form.state.values.valueDate}
          sellCurrency={form.state.values.sellCurrency}
          buyCurrency={form.state.values.buyCurrency}
          clientAccountId={accountDetails.clientAccountId}
        />
      )}
    </div>
  )
}
