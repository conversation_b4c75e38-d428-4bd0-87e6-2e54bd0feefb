import { useState, useEffect } from "react"
import { Search } from "lucide-react"
import { LoopIcon } from "@radix-ui/react-icons"
import { Link, useNavigate, useSearch } from "@tanstack/react-router"

import { formatDate } from "@/lib/date.utils"
import { useTradesQuery } from "@/data/trade/trade.query"
import {
  ITradeListingRequestQueryDTO,
  ITradeListingItemDTO,
} from "@/data/trade/trade.interface"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { BadgeProps } from "@/components/ui/badge"
import { ButtonGroup } from "@/components/pages/payments/components/ButtonGroup"
import { BasicPagination } from "@/components/base/pagination/BasicPagination"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { TradeDetailsSheet } from "./components/TradeDetailsSheet"
import TradeStatusBadge from "./components/TradeStatusBadge"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

export function TradeListingPage({
  entityId,
  tradeId,
}: {
  entityId: string
  tradeId?: string
}) {
  const [searchTerm, setSearchTerm] = useState("")
  const [params, setParams] = useState<ITradeListingRequestQueryDTO>({
    status: "All",
    pageNumber: 1,
    pageSize: 10,
  })

  const searchParams: any = useSearch({
    strict: false,
  })

  const [selectedTradeId, setSelectedTradeId] = useState<string | undefined>(
    searchParams.tradeId,
  )

  useEffect(() => {
    if (selectedTradeId) {
      navigate({
        search: {
          ...searchParams,
          tradeId: selectedTradeId,
        },
        replace: true,
      })
    } else {
      const s = { ...searchParams }
      delete s.tradeId
      navigate({
        search: {
          ...s,
        },
        replace: true,
      })
    }
  }, [selectedTradeId])

  const { data: trades, isLoading, isError } = useTradesQuery(params)

  const handleSortChange = (field: string) => {
    const newDirection =
      params.orderByField === field && params.orderByDirection === "asc"
        ? "desc"
        : "asc"

    setParams((prev) => ({
      ...prev,
      orderByField: field,
      orderByDirection: newDirection,
    }))
  }

  const getSortIndicator = (field: string) => {
    if (params.orderByField !== field) return null
    return params.orderByDirection === "asc" ? " ↑" : " ↓"
  }

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setParams((prev) => ({
      ...prev,
      freeText: value,
      pageNumber: 1,
    }))
  }

  const statusFilters = ["All", "Brokered", "Settled"]

  const handleStatusChange = (status: string) => {
    setParams((prev) => ({
      ...prev,
      status: status as "All" | "Brokered" | "Settled" | "",
      pageNumber: 1,
    }))
  }

  const navigate = useNavigate()

  const handleTradeClick = (trade: ITradeListingItemDTO) => {
    setSelectedTradeId(trade.id)

    // Update URL with payment ID
    navigate({
      search: { ...searchParams, tradeId: trade.id },
      replace: true,
    })
  }

  return (
    <div className="flex flex-col gap-4 px-4">
      {/* Header with Search and Status Filters */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative w-60">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              className="w-full rounded-xl pl-9 shadow-none"
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search trades"
              type="text"
              value={searchTerm}
            />
          </div>

          <div className="flex space-x-2">
            <ButtonGroup
              active={params.status === "" ? "All" : params.status}
              items={statusFilters}
              setActive={handleStatusChange}
            />
          </div>
        </div>

        {/* Add Trade Button */}
        <Link params={{ entityId }} to="/$entityId/trades/add">
          <Button className="ml-auto py-2">
            <LoopIcon className="h-4 w-4 opacity-70" /> Trade
          </Button>
        </Link>
      </div>

      {isLoading ? (
        <div className="mt-1 flex h-96 w-full items-center justify-center">
          <LoadingSpinner size="8" />
        </div>
      ) : isError ? (
        <div className="py-4 text-center text-muted-foreground">
          Error loading trades
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-none">
                  <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                    <Button
                      className="h-8 p-0 text-xs font-bold hover:bg-transparent"
                      onClick={() => handleSortChange("valueDate")}
                      variant="ghost"
                    >
                      Value Date{getSortIndicator("valueDate")}
                    </Button>
                  </TableHead>
                  <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                    <Button
                      className="h-8 p-0 text-xs font-bold hover:bg-transparent"
                      onClick={() => handleSortChange("code")}
                      variant="ghost"
                    >
                      Code{getSortIndicator("code")}
                    </Button>
                  </TableHead>
                  <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                    Sell
                  </TableHead>
                  <TableHead className="w-2/12 whitespace-nowrap text-xs font-bold">
                    Buy
                  </TableHead>

                  <TableHead className="w-1/12 whitespace-nowrap text-xs font-bold">
                    <Button
                      className="h-8 p-0 text-xs font-bold hover:bg-transparent"
                      onClick={() => handleSortChange("buyRate")}
                      variant="ghost"
                    >
                      Rate{getSortIndicator("buyRate")}
                    </Button>
                  </TableHead>
                  <TableHead className="w-1/12 whitespace-nowrap text-xs font-bold">
                    <Button
                      className="h-8 p-0 text-xs font-bold hover:bg-transparent"
                      onClick={() => handleSortChange("status")}
                      variant="ghost"
                    >
                      Status{getSortIndicator("status")}
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {trades?.data.length === 0 ? (
                  <TableRow>
                    <TableCell
                      className="py-8 text-center text-muted-foreground"
                      colSpan={6}
                    >
                      No trades found
                    </TableCell>
                  </TableRow>
                ) : (
                  trades?.data.map((trade: ITradeListingItemDTO) => (
                    <TableRow
                      className="cursor-pointer hover:bg-muted/5"
                      key={trade.id}
                      onClick={() => handleTradeClick(trade)}
                    >
                      <TableCell className="py-4">
                        {formatDate(trade.valueDate)}
                      </TableCell>
                      <TableCell className="py-4">{trade.code}</TableCell>

                      <TableCell className="py-4">
                        <div className="font-normal">
                          <CurrencyText
                            amount={trade.sell.amount}
                            currency={trade.sell.currency}
                          />
                          <div className="text-xs text-muted-foreground">
                            {trade.sell.currency}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="font-normal">
                          <CurrencyText
                            amount={trade.buy.amount}
                            currency={trade.buy.currency}
                          />
                          <div className="text-xs text-muted-foreground">
                            {trade.buy.currency}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex flex-col gap-1">
                          <span className="whitespace-nowrap">{`${trade.fixedSide === "Buy" ? trade.buyRate : trade.sellRate}`}</span>
                          <span className="whitespace-nowrap text-xs text-muted-foreground">
                            {`${trade.fixedSide === "Buy" ? trade.sellRate : trade.buyRate}`}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <TradeStatusBadge status={trade.status} />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {trades && trades.totalCount > 0 && (
            <BasicPagination
              data={trades}
              onNext={() =>
                setParams({
                  ...params,
                  pageNumber: (params.pageNumber || 1) + 1,
                })
              }
              onPrevious={() =>
                setParams({
                  ...params,
                  pageNumber: (params.pageNumber || 1) - 1,
                })
              }
              totalItems={trades.totalCount}
              pageSize={trades.pageSize}
            />
          )}
          {selectedTradeId && (
            <TradeDetailsSheet
              id={selectedTradeId}
              onClose={() => setSelectedTradeId(undefined)}
            />
          )}
        </>
      )}
    </div>
  )
}
