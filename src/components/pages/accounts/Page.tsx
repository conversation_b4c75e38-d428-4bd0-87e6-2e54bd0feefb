import { Account } from "@/data/account/account.interface"
import { AccountListHeader } from "@/components/pages/accounts/components/AccountListHeader"

import { AccountListTile } from "./components/AccountListTile"

interface AccountsPageProps {
  entityId: string
  accounts?: Account[]
}

export function AccountsPage({ accounts, entityId }: AccountsPageProps) {
  if (accounts && accounts.length === 0) {
    return <div>No accounts found</div>
  }

  return (
    <div className="container mx-4 mt-3 space-y-6">
      {accounts && accounts.length > 0 && (
        <div className="space-y-4">
          <AccountListHeader accounts={accounts} entityId={entityId} />
          <div className="space-y-4">
            {accounts.map((account, index) => (
              <AccountListTile account={account} key={index} />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
