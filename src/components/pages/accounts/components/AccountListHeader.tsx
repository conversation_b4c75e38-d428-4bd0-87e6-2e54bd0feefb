import { useMemo } from "react"
import { HelpCircle, Plus } from "lucide-react"
import { Currency } from "@/lib/constants/currency.constants"
import { useAccountStore } from "@/data/account/account.store"
import { Account } from "@/data/account/account.interface"
import { CurrencySelector } from "@/components/base/currency/CurrencySelector"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip"

interface AccountListHeaderProps {
  entityId: string
  accounts: Account[]
}

export function AccountListHeader({
  entityId,
  accounts,
}: AccountListHeaderProps) {
  const { displayCurrency, setDisplayCurrency } = useAccountStore()

  // Extract unique currencies from all accounts
  const availableCurrencies = useMemo(() => {
    if (!accounts?.length) return []

    const currencies = accounts.flatMap((account) =>
      account.balances.map((balance) => balance.currency),
    )

    return [...new Set(currencies)] as Currency[]
  }, [accounts])

  return (
    <div className="flex items-center justify-end pb-2">
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">
          Display estimated account balances in
        </span>
        <CurrencySelector
          availableCurrencies={availableCurrencies}
          buttonClassName="border rounded-md px-3 py-1"
          currency={displayCurrency}
          onChange={setDisplayCurrency}
        />
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Estimated total balance based on current market FX rates.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  )
}
