import { useMemo, useState } from "react"
import {
  ChevronDown,
  ChevronRight,
  Edit,
  MoreVertical,
  ArrowRight,
  List,
  ChevronUp,
  Loader,
} from "lucide-react"
import { Link, useLocation, useParams, useSearch } from "@tanstack/react-router"

import { useCurrencyName } from "@/hooks/useCurrencyName"
import { useRateForCurrencyPairQuery } from "@/data/trade/trade.query"
import { useAccountStore } from "@/data/account/account.store"
import { Account } from "@/data/account/account.interface"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { CurrencyText } from "@/components/base/currency/CurrencyText"
import { CurrencyFlag } from "@/components/base/currency/CurrencyFlag"
import { CurrencyBadge } from "@/components/base/currency/CurrencyBadge"
import { ArrowTopRightIcon } from "@radix-ui/react-icons"

interface AccountListTileProps {
  account: Account
}

export function AccountListTile({ account }: AccountListTileProps) {
  const { displayCurrency } = useAccountStore()
  const search: any = useSearch({
    strict: false,
  })
  const { href } = useLocation()
  const [isExpanded, setIsExpanded] = useState(
    search.open &&
      (!search.accountId || search.accountId === account.virtualIban),
  )
  const { getCurrencyName } = useCurrencyName()

  const balance = useMemo(() => {
    return account.balances.find(
      (balance) => balance.currency === displayCurrency,
    )
  }, [account.balances, displayCurrency])

  const otherBalances = useMemo(() => {
    return account.balances.filter(
      (balance) => balance.currency !== displayCurrency,
    )
  }, [account.balances, displayCurrency])

  const balanceLimit = useMemo(() => {
    return balance ? 1 : 2
  }, [balance])

  const elapasedBalances = useMemo(() => {
    return otherBalances?.slice(balanceLimit)
  }, [otherBalances, balanceLimit])

  const { entityId } = useParams({ from: "/_auth/$entityId/accounts" })

  const { data: rateForCurrencyPair, isLoading: isRateForCurrencyPairLoading } =
    useRateForCurrencyPairQuery({
      displayCurrency,
      originalCurrency: "GBP",
    })

  const rate = useMemo(() => {
    return rateForCurrencyPair?.rate ?? 1
  }, [rateForCurrencyPair])

  const isRateLoading = useMemo(() => {
    if (displayCurrency === "GBP") {
      return false
    }
    return isRateForCurrencyPairLoading
  }, [isRateForCurrencyPairLoading, displayCurrency])

  return (
    <div className="overflow-hidden rounded-xl bg-sidebar shadow-sm">
      <div
        className="group flex cursor-pointer justify-between p-6"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold">{account.accountName}</h3>
          </div>
          <p className="text-sm text-muted-foreground">{account.virtualIban}</p>
        </div>

        <div className="flex flex-1 items-center justify-center">
          <div className="flex items-center gap-2">
            {balance && <CurrencyBadge currency={balance.currency} />}
            {otherBalances
              ?.slice(0, balanceLimit)
              .map((balance, idx) => (
                <CurrencyBadge currency={balance.currency} key={idx} />
              ))}
            {elapasedBalances.length > 0 && (
              <Popover>
                <PopoverTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button
                    className="px-1 text-muted-foreground"
                    size="sm"
                    variant="link"
                  >
                    + {elapasedBalances.length} more
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-fit">
                  <div className="flex flex-col gap-2">
                    {elapasedBalances.map((balance, idx) => (
                      <CurrencyBadge currency={balance.currency} key={idx} />
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>

        <div className="flex flex-row items-center gap-4">
          <div className="text-right">
            <div className="text-sm text-muted-foreground">
              Estimated balance
            </div>
            {isRateLoading ? (
              <Loader className="animate-spin" />
            ) : (
              <CurrencyText
                amount={account.totalBalance * rate}
                className="text-xl font-semibold text-foreground"
                currency={displayCurrency}
              />
            )}
          </div>
          <div className="flex flex-col items-center">
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 text-primary" />
            ) : (
              <ChevronDown className="h-5 w-5 text-primary" />
            )}
          </div>
        </div>
      </div>

      <Collapsible onOpenChange={setIsExpanded} open={isExpanded}>
        <CollapsibleContent className="border-border bg-sidebar pt-2">
          <div className="mx-6">
            {account.balances.map((balance, idx) => (
              <div
                className="flex items-center rounded-md border-b border-border py-4 last:border-b-0"
                key={idx}
              >
                <CurrencyFlag currency={balance.currency} />

                <div className="ml-3 flex items-center gap-2">
                  <span className="text-base font-medium">
                    {balance.currency}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {getCurrencyName(balance.currency)}
                  </span>
                </div>
                <div className="ml-auto flex items-center gap-4">
                  <CurrencyText
                    amount={balance.balance}
                    className="text-base font-medium text-foreground"
                    currency={balance.currency}
                  />
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button className="h-8 w-8" size="icon" variant="ghost">
                        <MoreVertical className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="w-48 rounded-md"
                    >
                      <DropdownMenuItem asChild className="cursor-pointer">
                        <Link
                          params={{ entityId }}
                          className="px-4 py-2"
                          search={{
                            from: account.virtualIban,
                            fromCurrency: balance.currency,
                            fromRoute: href,
                          }}
                          to="/$entityId/payments/send"
                        >
                          <ArrowTopRightIcon className="size-5 text-slate-500" />
                          <span>Pay</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild className="cursor-pointer">
                        <Link
                          params={{ entityId }}
                          className="px-4 py-2"
                          search={{
                            id: account.virtualIban,
                            currency: balance.currency,
                          }}
                          to="/$entityId/transactions"
                        >
                          <div className="flex items-center gap-2">
                            <List className="size-5 text-slate-500" />
                            <span>Transactions</span>
                          </div>
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
