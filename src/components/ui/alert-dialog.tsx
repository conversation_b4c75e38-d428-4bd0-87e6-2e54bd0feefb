import React from "react"
import { X } from "lucide-react"
import * as AlertDialogPrimitive from "@radix-ui/react-alert-dialog"

import { cn } from "@/lib/utils"

import { Button, buttonVariants } from "./button"

const AlertDialog = AlertDialogPrimitive.Root
const AlertDialogTrigger = AlertDialogPrimitive.Trigger

// Add a context to hold the onOpenChange function
const AlertDialogContext = React.createContext<
  ((open: boolean) => void) | undefined
>(undefined)

const AlertDialogContent = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content> & {
    onOpenChange?: (open: boolean) => void
  }
>(({ className, onOpenChange, ...props }, ref) => (
  <AlertDialogContext.Provider value={onOpenChange}>
    <AlertDialogPrimitive.Portal>
      <AlertDialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black/50" />
      <AlertDialogPrimitive.Content
        className={cn(
          "fixed left-[50%] top-[50%] z-50 translate-x-[-50%] translate-y-[-50%] rounded-3xl bg-white p-6 shadow-lg",
          className,
        )}
        ref={ref}
        {...props}
      />
    </AlertDialogPrimitive.Portal>
  </AlertDialogContext.Provider>
))
AlertDialogContent.displayName = "AlertDialogContent"

const AlertDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("mb-4", className)} {...props} />
)
AlertDialogHeader.displayName = "AlertDialogHeader"

const AlertDialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("mt-4 flex justify-end gap-2", className)} {...props} />
)
AlertDialogFooter.displayName = "AlertDialogFooter"

const AlertDialogTitle = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title> & {
    hideCloseButton?: boolean
  }
>(({ className, hideCloseButton = false, ...props }, ref) => {
  const onOpenChange = React.useContext(AlertDialogContext)

  return (
    <div className="relative flex w-full items-center justify-between">
      <AlertDialogPrimitive.Title
        className={cn("pr-8 text-lg font-semibold", className)}
        ref={ref}
        {...props}
      />
      {!hideCloseButton && onOpenChange && (
        <Button
          aria-label="Close dialog"
          className="absolute right-0 top-0 h-fit rounded-full bg-accent p-2 text-muted-foreground transition-colors hover:bg-muted hover:text-foreground"
          onClick={() => onOpenChange(false)}
          variant="link-muted"
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
})
AlertDialogTitle.displayName = "AlertDialogTitle"

const AlertDialogDescription = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Description
    className={cn("mt-2 text-sm text-gray-500", className)}
    ref={ref}
    {...props}
  />
))
AlertDialogDescription.displayName = "AlertDialogDescription"

const AlertDialogAction = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.AlertDialogAction>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.AlertDialogAction>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Action
    className={cn(buttonVariants({ variant: "destructive" }), className)}
    ref={ref}
    {...props}
  />
))
AlertDialogAction.displayName = "AlertDialogAction"

const AlertDialogCancel = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.AlertDialogCancel>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.AlertDialogCancel>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Cancel
    className={cn(buttonVariants({ variant: "ghost" }), className)}
    ref={ref}
    {...props}
  />
))
AlertDialogCancel.displayName = "AlertDialogCancel"

export {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
}
