import { Toaster as Son<PERSON> } from "sonner"
import { useTheme } from "next-themes"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      className="toaster group"
      closeButton // Adds a close button
      duration={5000} // Auto-dismiss after 5 seconds
      position="top-right"
      theme={theme as ToasterProps["theme"]}
      toastOptions={{
        classNames: {
          toast:
            "group toast flex items-center gap-3 py-3 px-4 min-h-0 relative pr-10",
          description: "group-[.toast]:text-muted-foreground relative",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          success:
            "border-l-[5px] border-l-[#16a34a] bg-green-50 text-green-800 border border-green-300 relative",
          error:
            "border-l-[5px] border-l-destructive bg-red-50 text-red-800 border border-red-300 relative",
          closeButton:
            "absolute left-80 top-1/4 -translate-y-1/4 border-0 shadow-none ring-0 outline-none text-black text-3xl font-semibold !bg-transparent rounded-none",
        },
      }}
      visibleToasts={1} // Limits visible toasts
      {...props}
    />
  )
}

export { Toaster }
