import { useBreadcrumb } from "@/hooks/use-breadcrumb"

import { EntitySidebar } from "./EntitySidebar"
import { EntityHeader } from "./EntityHeader"
import { useLoaderData } from "./$entityId.loader"
import { DefaultLayout } from "../DefaultLayout"

export default function Entity(props: React.PropsWithChildren) {
  const { entity } = useLoaderData()

  useBreadcrumb("entity", {
    path: `${entity?.id!}`,
    label: entity?.legalEntity?.name ?? "Entity",
  })

  return (
    <DefaultLayout Header={<EntityHeader />} Sidebar={<EntitySidebar />}>
      <div className="flex-1 bg-background">
        <div className="content-container flex flex-1 flex-col overflow-x-hidden py-4">
          {props.children}
        </div>
      </div>
    </DefaultLayout>
  )
}
