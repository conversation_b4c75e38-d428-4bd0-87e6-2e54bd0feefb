import { useMemo } from "react"
import { ChevronDown } from "lucide-react"
import { <PERSON>, useRouter, useLocation } from "@tanstack/react-router"

import { queryClient } from "@/main"
import { getEntityNavItems } from "@/lib/constants/entity-navigation"
import { useEntityAccessQry } from "@/data/onboarding/onboarding.query"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import ArgentexIcon2 from "@/components/base/icons/ArgentexIcon2"
import ArgentexIcon from "@/components/base/icons/ArgentexIcon"
import { TermsAndConditionsModal } from "@/components/modals/TermsAndConditionsModal"

import { useLoaderData } from "./$entityId.loader"
import { queryKeys } from "@/lib/constants/query.constants"
import { EntityStatus } from "@/client/onboarding/types.gen"

// Add the link function from the Hub
const link = (status: EntityStatus) => {
  if (["PreSubmission", "Onboarding", "ReadyForOnboarding"].includes(status))
    return "/onboarding/$entityId"
  return "/$entityId"
}

export function EntitySidebar() {
  const { entity } = useLoaderData()
  const { data, isLoading } = useEntityAccessQry()
  const navItems = getEntityNavItems(entity?.id, data)
  const router = useRouter()
  const location = useLocation()

  const currentEntityName = useMemo(() => {
    if (!data || !entity.id) return null

    const currentEntity = data.find(
      (e) => e.entityId?.toLocaleLowerCase() === entity.id!.toLocaleLowerCase(),
    )
    return currentEntity?.entityName
  }, [data, entity])

  const handleNavigate = () => {
    // Invalidate accounts cache when navigating between entities
    queryClient.invalidateQueries({
      predicate: (query) => {
        // This will match both ["accounts"] and ["accounts", someEntityId]
        return query.queryKey[0] === queryKeys.global.accounts
      },
    })
  }

  // Get the current path segments
  const getCurrentPathSegments = () => {
    const pathSegments = location.pathname.split("/")
    // Get segments after the entityId (index 1)
    return pathSegments.slice(2).join("/")
  }

  // Handle entity change while preserving current route context
  const handleEntityChange = (newEntityId: string, status: EntityStatus) => {
    // Invalidate accounts cache when navigating between entities
    handleNavigate()
    const currentPathSegments = getCurrentPathSegments()
    if (currentPathSegments) {
      router.navigate({
        to: `/$entityId/${currentPathSegments}`,
        params: { entityId: newEntityId },
      })
    } else {
      router.navigate({
        to: link(status),
        params: { entityId: newEntityId },
      })
    }
  }

  return (
    <Sidebar className="border-none" collapsible="icon">
      <SidebarHeader className="flex flex-col bg-sidebar">
        <SidebarMenu>
          <SidebarMenuItem className="flex flex-row items-center justify-center">
            <SidebarMenuButton
              asChild
              className="flex-1 group-data-[collapsible=icon]:!size-12"
              size="lg"
            >
              <Link
                className="relative flex aspect-square size-12 items-center rounded-xl bg-sidebar text-sidebar-primary"
                onClick={handleNavigate}
                to="/"
              >
                <div className="flex w-full items-start gap-2 p-2 pl-3 delay-100 group-data-[state=expanded]:visible group-data-[state=collapsed]:invisible">
                  <ArgentexIcon className="text-sidebar-primary" />
                </div>
                <div className="absolute flex h-full w-full items-center delay-100 group-data-[state=collapsed]:invisible group-data-[state=expanded]:invisible">
                  <ArgentexIcon2 className="[&>path:first-of-type]:fill-transparent group-hover/menu-item:[&>path:nth-child(2)]:fill-sidebar-primary" />
                </div>
              </Link>
            </SidebarMenuButton>
            <SidebarTrigger className="h-12 w-12 rounded-xl" />
          </SidebarMenuItem>
          <SidebarMenuItem className="px-3 py-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton className="w-full rounded-xl border border-border bg-background px-6 py-5 text-sm font-medium text-foreground shadow-sm focus-visible:border-ring/20 focus-visible:outline-none focus-visible:ring-[0.5px] focus-visible:ring-ring/30 group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:border-none group-data-[state=collapsed]:bg-transparent group-data-[state=collapsed]:p-0 group-data-[state=collapsed]:shadow-none">
                  <div className="flex w-full items-center justify-between group-data-[state=collapsed]:hidden">
                    <span className="truncate">
                      {currentEntityName || "Select Entity"}
                    </span>
                    <ChevronDown className="ml-2 h-4 w-4 flex-shrink-0" />
                  </div>
                  <div className="hidden group-data-[state=collapsed]:flex group-data-[state=collapsed]:h-8 group-data-[state=collapsed]:w-8 group-data-[state=collapsed]:items-center group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:rounded-full group-data-[state=collapsed]:bg-primary/10 group-data-[state=collapsed]:text-primary">
                    <span className="p-4 text-xs font-semibold">
                      {currentEntityName?.charAt(0) || "E"}
                    </span>
                  </div>
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[--radix-popper-anchor-width] p-1">
                {!isLoading &&
                  data?.map((entity) => (
                    <DropdownMenuItem
                      className="py-2"
                      key={entity.entityId}
                      onClick={() => {
                        if (entity.entityId) {
                          handleEntityChange(
                            entity.entityId,
                            entity.entityStatus ?? "Client",
                          )
                        }
                      }}
                    >
                      <span className="w-full px-3">{entity.entityName}</span>
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="bg-sidebar">
        <SidebarGroup>
          <SidebarGroupContent className="px-3">
            <SidebarMenu>
              {navItems.map((item) => (
                <SidebarMenuItem className="py-1" key={item.label}>
                  <SidebarMenuButton asChild>
                    <Link
                      activeOptions={{
                        exact: item.exact ?? true,
                      }}
                      activeProps={{
                        className:
                          "bg-sidebar-accent text-primary font-semibold rounded-xl",
                      }}
                      className="flex items-center gap-3 px-4 py-6 text-sm text-sidebar-foreground transition-colors hover:rounded-xl hover:bg-sidebar-accent hover:font-semibold hover:text-primary"
                      onClick={handleNavigate}
                      to={item.to}
                    >
                      {item.icon && <item.icon className="h-4 w-4" />}
                      <span>{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="bg-sidebar">
        <SidebarMenu>
          <SidebarMenuItem>
            <TermsAndConditionsModal>
              <SidebarMenuButton className="flex items-center gap-3 px-4 py-3 text-sm text-green-600 underline transition-colors hover:rounded-xl hover:bg-sidebar-accent hover:font-semibold hover:text-green-700">
                <span>Legal</span>
              </SidebarMenuButton>
            </TermsAndConditionsModal>
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarRail />
      </SidebarFooter>
    </Sidebar>
  )
}
