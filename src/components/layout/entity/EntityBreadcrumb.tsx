import { Fragment, useMemo } from "react"
import { Link } from "@tanstack/react-router"

import { cn } from "@/lib/utils"
import { useGlobalStore } from "@/data/global/global.store"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

export function EntityBreadcrumb() {
  const breadcrumbs = useGlobalStore((state) => state.breadcrumbs)
  const normalizedBreadcrumbs = useMemo(() => {
    let path = ""
    return breadcrumbs.map((breadcrumb) => {
      const cleanPath = breadcrumb.path
        .replace(/\s+/g, "")
        .replace(/\/+/g, "/")
        .replace(/^\/+|\/+$/g, "")

      path += cleanPath ? `/${cleanPath}` : "/"

      // remove duplicate '/' if we have more than one '/' side by side
      path = path.replace(/\/\/+/g, "/")

      return {
        ...breadcrumb,
        path,
      }
    })
  }, [breadcrumbs])

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {normalizedBreadcrumbs.map((breadcrumb, i) => {
          const isLastItem = i === normalizedBreadcrumbs.length - 1
          return (
            <Fragment key={`${breadcrumb.key}-${breadcrumb.path}`}>
              {i > 0 && <BreadcrumbSeparator />}
              <BreadcrumbItem>
                {isLastItem ? (
                  <span
                    className={cn(
                      "text-muted-foreground/80", // Optional: Style for the last breadcrumb
                    )}
                  >
                    {breadcrumb.label}
                  </span>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link className={cn("underline")} to={breadcrumb.path}>
                      {breadcrumb.label}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </Fragment>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
