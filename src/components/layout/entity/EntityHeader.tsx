import { useMemo } from "react"
import { <PERSON> } from "lucide-react"
import { useAuth0 } from "@auth0/auth0-react"

import { useGlobalStore } from "@/data/global/global.store"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

export function EntityHeader() {
  const { logout, user } = useAuth0()
  const account = useGlobalStore((state) => state.account)
  const icon = useGlobalStore((state) => state.icon)
  const breadcrumbs = useGlobalStore((state) => state.breadcrumbs)

  const userInitials = useMemo(() => {
    if (!user?.nickname) return "-"
    return user?.nickname
      .split(".")
      .slice(0, 3)
      .map((s) => s?.[0]?.toUpperCase() ?? "")
      .join("")
  }, [user?.nickname])

  // First, store the current label in a variable for easier checking
  const currentLabel = breadcrumbs[breadcrumbs?.length - 1]?.label
  const isAddPayee = currentLabel === "Add Payee"
  const isSendPayments = currentLabel === "Send Payments"

  return (
    <header className="z-20 flex shrink-0 items-center gap-2 bg-background transition-[width,height] ease-linear">
      <section className="content-container">
        <div
          className={`flex h-16 items-center justify-between ${
            isAddPayee || isSendPayments ? "justify-normal" : ""
          }`}
        >
          <div
            className={`flex items-center gap-4 ${isAddPayee || isSendPayments ? "mx-auto mt-10" : ""}`}
          >
            <div className="hidden ps-4 md:block">
              {/* <EntityBreadcrumb /> */}
              <div className="flex flex-1 items-center justify-between">
                <div className="flex flex-none items-center gap-3">
                  {account ? (
                    <h1 className="text-2xl font-semibold">{account.name}</h1>
                  ) : (
                    <div className="flex items-center gap-4">
                      {/* {icon && createElement(icon, { className: "h-5 w-5" })} */}
                      <h1 className={`text-2xl font-semibold`}>
                        {currentLabel}
                      </h1>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* <DropdownMenu>
              <DropdownMenuTrigger asChild className="bg-background">
                <Button
                  aria-label="Notifications"
                  className="rounded-full p-0 relative"
                  size="icon"
                  variant="ghost"
                >
                   <Bell className="h-5 w-5 text-muted-foreground" /> 
                  <span className="absolute top-0 right-0 h-2 w-2 bg-destructive rounded-full" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>No new notifications</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu> */}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className="rounded-full bg-muted text-foreground hover:bg-muted"
                  data-testid="avatar-button"
                  variant="secondary"
                  size="icon"
                >
                  {userInitials}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  id="logout-button"
                  onClick={() =>
                    logout({
                      logoutParams: {
                        returnTo: window.location.origin,
                      },
                    })
                  }
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </section>
    </header>
  )
}
