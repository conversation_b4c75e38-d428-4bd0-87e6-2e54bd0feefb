import { SidebarProvider } from "@/components/ui/sidebar"

interface DefaultLayoutProps extends React.PropsWithChildren {
  Header?: React.ReactNode
  Sidebar: React.ReactNode
}

const SIDEBAR_WIDTH = "20rem"
const SIDEBAR_WIDTH_ICON = "4rem"
const SIDEBAR_WIDTH_MOBILE = "18rem"

const sidebarStyle: React.CSSProperties & { [key: string]: string | number } = {
  "--sidebar-width": SIDEBAR_WIDTH,
  "--sidebar-width-mobile": SIDEBAR_WIDTH_MOBILE,
  "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
}

export function DefaultLayout(props: DefaultLayoutProps) {
  return (
    <div className="relative flex min-h-svh flex-col">
      <SidebarProvider style={sidebarStyle}>
        {props.Sidebar}

        <main className="relative flex min-h-svh flex-1 flex-col overflow-x-clip peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:overflow-x-auto md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow">
          {props.Header}
          {props.children}
        </main>
      </SidebarProvider>
    </div>
  )
}
