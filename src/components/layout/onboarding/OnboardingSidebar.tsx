import { useMemo } from "react"
import { ChevronDown, CommandIcon, FileText } from "lucide-react"
import { Link, useSearch } from "@tanstack/react-router"

import { FileRouteTypes } from "@/routeTree.gen"
import { useEntityAccessQry } from "@/data/onboarding/onboarding.query"
import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { useRolePermissions } from "@/hooks/useRolePermissions"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarFooter,
  SidebarHeader as CoreSidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import ArgentexIcon2 from "@/components/base/icons/ArgentexIcon2"
import ArgentexIcon from "@/components/base/icons/ArgentexIcon"
import { TermsAndConditionsModal } from "@/components/modals/TermsAndConditionsModal"
import { EntityStatus } from "@/client/onboarding/types.gen"

type url = FileRouteTypes["to"]

const link = (status: EntityStatus): url => {
  if (["PreSubmission", "Onboarding", "ReadyForOnboarding"].includes(status))
    return "/onboarding/$entityId"

  return "/$entityId"
}

function SidebarHeader() {
  const { entity } = useLoaderData()
  const { data, isLoading } = useEntityAccessQry()

  const currentEntityName = useMemo(() => {
    if (!data || !entity.id) return null

    const currentEntity = data.find(
      (e) => e.entityId?.toLocaleLowerCase() === entity.id!.toLocaleLowerCase(),
    )
    return currentEntity?.entityName
  }, [data, entity])

  return (
    <CoreSidebarHeader className="bg-sidebar">
      <SidebarMenu className="gap-y-0">
        <SidebarMenuItem className="flex flex-row items-center justify-center">
          <SidebarMenuButton
            asChild
            className="flex-1 group-data-[collapsible=icon]:!size-12"
            size="lg"
          >
            <Link
              className="relative flex aspect-square size-12 items-center rounded-lg bg-sidebar text-sidebar-primary"
              to="/"
            >
              <div className="flex w-full items-start gap-2 p-2 pl-3 delay-100 group-data-[state=expanded]:visible group-data-[state=collapsed]:invisible">
                <ArgentexIcon className="text-sidebar-primary" />
              </div>
              <div className="absolute flex h-full w-full items-center delay-100 group-data-[state=collapsed]:invisible group-data-[state=expanded]:invisible">
                <ArgentexIcon2 className="[&>path:first-of-type]:fill-transparent group-hover/menu-item:[&>path:nth-child(2)]:fill-sidebar-primary" />
              </div>
            </Link>
          </SidebarMenuButton>
          <SidebarTrigger className="h-12 w-12 rounded-lg" />
        </SidebarMenuItem>

        <SidebarMenuItem className="px-3 py-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton className="w-full rounded-xl border border-border bg-background px-6 py-5 text-sm font-medium text-foreground shadow-sm focus-visible:border-ring/20 focus-visible:outline-none focus-visible:ring-[0.5px] focus-visible:ring-ring/30 group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:border-none group-data-[state=collapsed]:bg-transparent group-data-[state=collapsed]:p-0 group-data-[state=collapsed]:shadow-none">
                <div className="flex w-full items-center justify-between group-data-[state=collapsed]:hidden">
                  <span className="truncate">
                    {currentEntityName || "Select Entity"}
                  </span>
                  <ChevronDown className="ml-2 h-4 w-4 flex-shrink-0" />
                </div>
                <div className="hidden group-data-[state=collapsed]:flex group-data-[state=collapsed]:h-8 group-data-[state=collapsed]:w-8 group-data-[state=collapsed]:items-center group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:rounded-full group-data-[state=collapsed]:bg-primary/10 group-data-[state=collapsed]:text-primary">
                  <span className="p-4 text-xs font-semibold">
                    {currentEntityName?.charAt(0) || "E"}
                  </span>
                </div>
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[--radix-popper-anchor-width] p-1">
              <ScrollArea className="h-72">
                {!isLoading &&
                  data?.map((entity) => {
                    return (
                      <Link
                        className=""
                        key={entity.entityId!}
                        params={{ entityId: entity.entityId! }}
                        to={link(entity.entityStatus!)}
                      >
                        <DropdownMenuItem
                          className="w-full cursor-pointer px-3 py-2"
                          key={entity.entityId}
                        >
                          <span>{entity.entityName}</span>
                        </DropdownMenuItem>
                      </Link>
                    )
                  })}
              </ScrollArea>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    </CoreSidebarHeader>
  )
}

export function OnboardingSidebar() {
  const { entity } = useLoaderData()
  const { data } = useEntityAccessQry()
  const search = useSearch({ from: "/_auth/onboarding/$entityId/" })
  const { getPermission } = useRolePermissions()

  // Check if entity is submitted (not PreSubmission)
  const isEntitySubmitted = useMemo(() => {
    if (!data || !entity.id) return false

    const currentEntity = data.find(
      (e) => e.entityId?.toLowerCase() === entity.id!.toLowerCase(),
    )

    return (
      currentEntity?.entityStatus &&
      !["PreSubmission"].includes(currentEntity.entityStatus)
    )
  }, [data, entity.id])

  const isAdmin = useMemo(() => {
    return getPermission("UserManagement.Add")
  }, [getPermission])

  return (
    <Sidebar className="border-none" collapsible="icon">
      <SidebarHeader />

      <SidebarContent>
        {isEntitySubmitted && (
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton
                    asChild
                    isActive={search.view === "review"}
                  >
                    <Link
                      to="/onboarding/$entityId"
                      params={{ entityId: entity.id! }}
                      search={{ view: "review" }}
                      activeProps={{
                        className:
                          "bg-sidebar-accent text-primary font-semibold rounded-xl",
                      }}
                      className="flex items-center gap-3 px-4 py-6 text-sm text-sidebar-foreground transition-colors hover:rounded-xl hover:bg-sidebar-accent hover:font-semibold hover:text-primary"
                    >
                      <FileText className="h-4 w-4" />
                      <span>Review</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                {isAdmin && (
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      isActive={search.view === "user-admin"}
                    >
                      <Link
                        to="/onboarding/$entityId"
                        params={{ entityId: entity.id! }}
                        search={{ view: "user-admin" }}
                        activeProps={{
                          className:
                            "bg-sidebar-accent text-primary font-semibold rounded-xl",
                        }}
                        className="flex items-center gap-3 px-4 py-6 text-sm text-sidebar-foreground transition-colors hover:rounded-xl hover:bg-sidebar-accent hover:font-semibold hover:text-primary"
                      >
                        <CommandIcon className="h-4 w-4" />
                        <span>User Admin</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>

      <SidebarRail />

      <SidebarFooter className="bg-sidebar">
        <SidebarMenu>
          <SidebarMenuItem>
            <TermsAndConditionsModal>
              <SidebarMenuButton className="flex items-center gap-3 px-4 py-3 text-sm text-green-600 underline transition-colors hover:rounded-xl hover:bg-sidebar-accent hover:font-semibold hover:text-green-700">
                <span>Legal</span>
              </SidebarMenuButton>
            </TermsAndConditionsModal>
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarRail />
      </SidebarFooter>
    </Sidebar>
  )
}
