import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { vi, describe, it, expect, beforeEach } from "vitest"

import { SidebarProvider } from "@/components/ui/sidebar"
import { OnboardingSidebar } from "@/components/layout/onboarding/OnboardingSidebar"

// Mock the router
vi.mock("@tanstack/react-router", () => ({
  useNavigate: () => vi.fn(),
  useSearch: () => ({ view: undefined }),
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
  })),
}))

// Mock entity access query
const mockUseEntityAccessQry = vi.fn()
vi.mock("@/data/onboarding/onboarding.query", () => ({
  useEntityAccessQry: () => mockUseEntityAccessQry(),
}))

// Mock role permissions hook
const mockGetPermission = vi.fn()
vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(() => ({
    getPermission: mockGetPermission,
  })),
}))

describe("OnboardingSidebar Role Permissions", () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    vi.clearAllMocks()
  })

  const renderOnboardingSidebar = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <SidebarProvider>
          <OnboardingSidebar />
        </SidebarProvider>
      </QueryClientProvider>,
    )
  }

  describe("Admin User", () => {
    beforeEach(() => {
      // Mock submitted entity
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: "test-entity",
            entityName: "Test Entity",
            entityStatus: "Submitted", // Not PreSubmission
            roles: ["Administrator"],
          },
        ],
        isLoading: false,
      })

      // Mock admin permissions - always returns true for admins
      mockGetPermission.mockReturnValue(true)
    })

    it("should show both Review and User Admin links for admin users on submitted entities", () => {
      renderOnboardingSidebar()

      expect(screen.getByText("Review")).toBeInTheDocument()
      expect(screen.getByText("User Admin")).toBeInTheDocument()
    })

    it("should call getPermission with UserManagement.Add permission", () => {
      renderOnboardingSidebar()

      expect(mockGetPermission).toHaveBeenCalledWith("UserManagement.Add")
    })
  })

  describe("Non-Admin User", () => {
    beforeEach(() => {
      // Mock submitted entity
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: "test-entity",
            entityName: "Test Entity",
            entityStatus: "Submitted", // Not PreSubmission
            roles: ["Viewer"],
          },
        ],
        isLoading: false,
      })

      // Mock non-admin permissions - returns false for User Admin
      mockGetPermission.mockReturnValue(false)
    })

    it("should show only Review link for non-admin users on submitted entities", () => {
      renderOnboardingSidebar()

      expect(screen.getByText("Review")).toBeInTheDocument()
      expect(screen.queryByText("User Admin")).not.toBeInTheDocument()
    })
  })

  describe("PreSubmission Entity", () => {
    beforeEach(() => {
      // Mock PreSubmission entity
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: "test-entity",
            entityName: "Test Entity",
            entityStatus: "PreSubmission",
            roles: ["Administrator"],
          },
        ],
        isLoading: false,
      })

      // Mock admin permissions
      mockGetPermission.mockReturnValue(true)
    })

    it("should not show Review or User Admin links for PreSubmission entities", () => {
      renderOnboardingSidebar()

      expect(screen.queryByText("Review")).not.toBeInTheDocument()
      expect(screen.queryByText("User Admin")).not.toBeInTheDocument()
    })
  })

  describe("API Failure Scenarios", () => {
    beforeEach(() => {
      // Mock submitted entity
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: "test-entity",
            entityName: "Test Entity",
            entityStatus: "Submitted",
            roles: ["Administrator"],
          },
        ],
        isLoading: false,
      })
    })

    it("should handle permission API failures gracefully for admin users", () => {
      // Admin users should still have access even if permission API fails
      mockGetPermission.mockReturnValue(true)

      renderOnboardingSidebar()

      expect(screen.getByText("Review")).toBeInTheDocument()
      expect(screen.getByText("User Admin")).toBeInTheDocument()
    })

    it("should handle permission API failures gracefully for non-admin users", () => {
      // Non-admin users should be denied access if permission API fails
      mockGetPermission.mockReturnValue(false)

      renderOnboardingSidebar()

      expect(screen.getByText("Review")).toBeInTheDocument()
      expect(screen.queryByText("User Admin")).not.toBeInTheDocument()
    })
  })
})
