import { render, screen } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { SidebarProvider } from "@/components/ui/sidebar"
import { OnboardingSidebar } from "@/components/layout/onboarding/OnboardingSidebar"

// Mock the router
vi.mock("@tanstack/react-router", () => ({
  useNavigate: () => vi.fn(),
  useSearch: () => ({ view: undefined }),
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "test-entity",
    },
  })),
}))

// Mock the entity access query
const mockUseEntityAccessQry = vi.fn()
vi.mock("@/data/onboarding/onboarding.query", () => ({
  useEntityAccessQry: () => mockUseEntityAccessQry(),
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithProviders = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>
      <SidebarProvider>{ui}</SidebarProvider>
    </QueryClientProvider>,
  )
}

describe("OnboardingSidebar Admin Links", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("shows Review and User Admin links for submitted entity with admin user", () => {
    mockUseEntityAccessQry.mockReturnValue({
      data: [
        {
          entityId: "test-entity",
          entityName: "Test Entity",
          entityStatus: "ReadyForOnboarding", // Submitted status
          roles: ["Administrator"],
        },
      ],
      isLoading: false,
    })

    renderWithProviders(<OnboardingSidebar />)

    expect(screen.getByText("Review")).toBeInTheDocument()
    expect(screen.getByText("User Admin")).toBeInTheDocument()
  })

  it("hides Review and User Admin links for PreSubmission entity even with admin user", () => {
    mockUseEntityAccessQry.mockReturnValue({
      data: [
        {
          entityId: "test-entity",
          entityName: "Test Entity",
          entityStatus: "PreSubmission", // Not submitted
          roles: ["Administrator"],
        },
      ],
      isLoading: false,
    })

    renderWithProviders(<OnboardingSidebar />)

    expect(screen.queryByText("Review")).not.toBeInTheDocument()
    expect(screen.queryByText("User Admin")).not.toBeInTheDocument()
  })

  it("shows Review link but hides User Admin link for submitted entity with non-admin user", () => {
    mockUseEntityAccessQry.mockReturnValue({
      data: [
        {
          entityId: "test-entity",
          entityName: "Test Entity",
          entityStatus: "ReadyForOnboarding", // Submitted status
          roles: ["Viewer"], // Not admin
        },
      ],
      isLoading: false,
    })

    renderWithProviders(<OnboardingSidebar />)

    expect(screen.getByText("Review")).toBeInTheDocument()
    expect(screen.queryByText("User Admin")).not.toBeInTheDocument()
  })

  it("shows admin links for Client status with admin user", () => {
    mockUseEntityAccessQry.mockReturnValue({
      data: [
        {
          entityId: "test-entity",
          entityName: "Test Entity",
          entityStatus: "Client", // Active client status
          roles: ["Administrator"],
        },
      ],
      isLoading: false,
    })

    renderWithProviders(<OnboardingSidebar />)

    expect(screen.getByText("Review")).toBeInTheDocument()
    expect(screen.getByText("User Admin")).toBeInTheDocument()
  })

  it("shows admin links for Onboarding status with admin user", () => {
    mockUseEntityAccessQry.mockReturnValue({
      data: [
        {
          entityId: "test-entity",
          entityName: "Test Entity",
          entityStatus: "Onboarding", // Processing status
          roles: ["Administrator"],
        },
      ],
      isLoading: false,
    })

    renderWithProviders(<OnboardingSidebar />)

    expect(screen.getByText("Review")).toBeInTheDocument()
    expect(screen.getByText("User Admin")).toBeInTheDocument()
  })

  it("shows only Review link for submitted entity with non-admin user (Viewer)", () => {
    mockUseEntityAccessQry.mockReturnValue({
      data: [
        {
          entityId: "test-entity",
          entityName: "Test Entity",
          entityStatus: "Client", // Active client status
          roles: ["Viewer"], // Not admin
        },
      ],
      isLoading: false,
    })

    renderWithProviders(<OnboardingSidebar />)

    expect(screen.getByText("Review")).toBeInTheDocument()
    expect(screen.queryByText("User Admin")).not.toBeInTheDocument()
  })
})
