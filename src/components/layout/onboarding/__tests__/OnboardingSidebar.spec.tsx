import userEvent from "@testing-library/user-event"
import { render, screen, within } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { useGlobalStore } from "@/data/global/global.store"
import { SidebarProvider } from "@/components/ui/sidebar"
import { OnboardingSidebar } from "@/components/layout/onboarding/OnboardingSidebar"

vi.mock("@tanstack/react-router", () => ({
  useNavigate: () => vi.fn(),
  useSearch: () => ({ view: undefined }),
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
  useLoaderData: vi.fn(() => ({
    entity: {
      id: "1",
    },
  })),
}))

vi.mock("@/data/entity/entity.query", () => ({
  useEntitiesQuery: vi.fn(() => ({
    data: {
      results: [
        { id: "1", name: "Entity 1" },
        { id: "2", name: "Entity 2" },
      ],
    },
    isLoading: false,
  })),
}))

vi.mock("@/hooks/useRolePermissions", () => ({
  useRolePermissions: vi.fn(() => ({
    getPermission: vi.fn(() => true), // Mock admin permissions
  })),
}))

vi.mock("@/data/onboarding/onboarding.query", () => ({
  useEntityAccessQry: vi.fn(() => ({
    data: [
      {
        entityId: "1",
        entityName: "Test Entity 1",
        entityStatus: "PreSubmission",
      },
      {
        entityId: "2",
        entityName: "Test Entity 2",
        entityStatus: "Onboarding",
      },
    ],
    isLoading: false,
  })),
}))

beforeAll(() => {
  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
})

// Create a test query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

// Helper function to render with the query client
const renderWithClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
  )
}

const renderWithProviders = (ui: React.ReactElement) => {
  return renderWithClient(<SidebarProvider>{ui}</SidebarProvider>)
}

describe("OnboardingSidebar", () => {
  beforeEach(() => {
    useGlobalStore.setState({
      entity: { id: "1", legalEntity: { name: "Test Entity 1" } },
    })
  })

  it("renders the Argentex logo with home link", () => {
    renderWithProviders(<OnboardingSidebar />)
    const homeLink = screen.getByRole("link", { name: "" })
    const logo = screen.getByTestId("argentex-logo")
    expect(homeLink).toBeInTheDocument()
    expect(homeLink).toHaveAttribute("href", "/")
    expect(logo).toBeInTheDocument()
    expect(logo).toHaveClass("text-sidebar-primary")
  })

  it("displays the current entity name in the dropdown", () => {
    renderWithProviders(<OnboardingSidebar />)
    const entityButton = screen.getByRole("button", {
      name: /Test Entity 1/i,
    })
    expect(entityButton).toBeInTheDocument()
  })

  it("shows entity list when dropdown is clicked", async () => {
    renderWithProviders(<OnboardingSidebar />)
    const user = userEvent.setup()

    // Click the dropdown trigger
    const dropdownTrigger = screen.getByRole("button", {
      name: /Test Entity 1/i,
    })
    await user.click(dropdownTrigger)

    // Get the dropdown content
    const dropdownContent = screen.getByRole("menu")

    // Check if both entities are shown in the dropdown
    expect(
      within(dropdownContent).getByText("Test Entity 1"),
    ).toBeInTheDocument()
    expect(
      within(dropdownContent).getByText("Test Entity 2"),
    ).toBeInTheDocument()
  })
})
