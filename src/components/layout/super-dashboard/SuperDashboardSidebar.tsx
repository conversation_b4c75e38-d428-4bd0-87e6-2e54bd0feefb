import { useMemo } from "react"
import { Link } from "@tanstack/react-router"

import { getSuperDashboardNavItems } from "@/lib/constants/super-dashboard.navigation"
import {
  Sidebar,
  SidebarContent as CoreSidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader as CoreSidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import ArgentexIcon2 from "@/components/base/icons/ArgentexIcon2"
import ArgentexIcon from "@/components/base/icons/ArgentexIcon"
import { TermsAndConditionsModal } from "@/components/modals/TermsAndConditionsModal"

function SidebarHeader() {
  return (
    <CoreSidebarHeader className="bg-sidebar">
      <SidebarMenu className="gap-y-0">
        <SidebarMenuItem className="flex flex-row items-center justify-center">
          <SidebarMenuButton
            asChild
            className="flex-1 group-data-[collapsible=icon]:!size-12"
            size="lg"
          >
            <div className="relative flex aspect-square size-12 items-center rounded-lg bg-sidebar text-sidebar-primary">
              <div className="flex w-full items-start gap-2 p-2 pl-3 delay-100 group-data-[state=expanded]:visible group-data-[state=collapsed]:invisible">
                <ArgentexIcon className="text-sidebar-primary" />
              </div>
              <div className="absolute flex h-full w-full items-center delay-100 group-data-[state=collapsed]:invisible group-data-[state=expanded]:invisible">
                <ArgentexIcon2 className="[&>path:first-of-type]:fill-transparent group-hover/menu-item:[&>path:nth-child(2)]:fill-sidebar-primary" />
              </div>
            </div>
          </SidebarMenuButton>
          <SidebarTrigger className="h-12 w-12 rounded-lg" />
        </SidebarMenuItem>
      </SidebarMenu>
    </CoreSidebarHeader>
  )
}

function SidebarContent() {
  const navItems = useMemo(() => getSuperDashboardNavItems(), [])

  return (
    <CoreSidebarContent className="bg-sidebar">
      <SidebarGroup className="">
        <SidebarGroupContent>
          <SidebarMenu>
            {navItems.map((item) => (
              <SidebarMenuItem className="" key={item.label}>
                <SidebarMenuButton asChild>
                  <Link
                    activeOptions={{ exact: true }}
                    activeProps={{ className: "bg-background/10" }}
                    className="flex items-center gap-3 px-3 py-2 text-sm text-sidebar-foreground transition-colors hover:bg-background/80 group-data-[collapsible=icon]:ml-1.5"
                    to={item.to}
                  >
                    {item.icon && <item.icon className="h-4 w-4" />}
                    <span>{item.label}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </CoreSidebarContent>
  )
}

export function SuperDashboardSidebar() {
  return (
    <Sidebar collapsible="icon">
      <SidebarHeader />
      <SidebarContent />
      <SidebarFooter className="bg-sidebar">
        <SidebarMenu>
          <SidebarMenuItem>
            <TermsAndConditionsModal>
              <SidebarMenuButton className="flex items-center gap-3 px-4 py-3 text-sm text-green-600 underline transition-colors hover:rounded-xl hover:bg-sidebar-accent hover:font-semibold hover:text-green-700">
                <span>Legal</span>
              </SidebarMenuButton>
            </TermsAndConditionsModal>
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarRail />
      </SidebarFooter>
    </Sidebar>
  )
}
