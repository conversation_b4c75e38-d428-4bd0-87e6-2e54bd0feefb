import { useEntityAccessQry } from "@/data/onboarding/onboarding.query"
import { DefaultLayout } from "@/components/layout/DefaultLayout"

import { SuperDashboardSidebar } from "./SuperDashboardSidebar"
import { SuperDashboardHeader } from "./SuperDashboardHeader"
import EntitiesTable from "./components/EntitiesTable"

export function SuperDashboardLayout() {
  const { data: entityAccess, isLoading, isError } = useEntityAccessQry()

  if (isLoading) return <></>

  if (isError) return <div>Error loading entities</div>

  return (
    <DefaultLayout
      Header={<SuperDashboardHeader />}
      Sidebar={<SuperDashboardSidebar />}
    >
      <div className="flex-1">
        <div className="content-container flex flex-1 flex-col overflow-x-hidden py-4">
          <EntitiesTable entities={entityAccess ?? []} />
        </div>
      </div>
    </DefaultLayout>
  )
}
