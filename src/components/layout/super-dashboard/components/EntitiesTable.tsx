import { useNavigate } from "@tanstack/react-router"

import { FileRouteTypes } from "@/routeTree.gen"
import { queryClient } from "@/main"
import { EntityStatus, GetEntityAccessDto } from "@/client/onboarding/types.gen"
import { queryKeys } from "@/lib/constants/query.constants"
import DataTable from "@/components/base/data-table"
import { name, role, status } from "./EntitiesTable.columns"

const FALLBACK_ORDER = 100

const statusOrder: Record<EntityStatus, number> = {
  Client: 1,
  PreSubmission: 2,
  ReadyForOnboarding: 3,
  Onboarding: 4,
}

type url = FileRouteTypes["to"]

const link = (status: EntityStatus): url => {
  if (["PreSubmission", "Onboarding", "ReadyForOnboarding"].includes(status))
    return "/onboarding/$entityId"

  return "/$entityId"
}

export default function EntitiesTable({
  entities,
}: {
  entities: GetEntityAccessDto[]
}) {
  const navigate = useNavigate()
  const sortEntities = entities.sort(
    (a, b) =>
      (statusOrder[a.entityStatus as EntityStatus] || FALLBACK_ORDER) -
      (statusOrder[b.entityStatus as EntityStatus] || FALLBACK_ORDER),
  )

  const handleClick = (entity: GetEntityAccessDto) => {
    // Invalidate accounts cache when changing entity
    queryClient.invalidateQueries({
      predicate: (query) => {
        // This will match both ["accounts"] and ["accounts", someEntityId]
        return query.queryKey[0] === queryKeys.global.accounts
      },
    })
    navigate({
      to: link(entity.entityStatus!),
      params: { entityId: entity?.entityId! },
    })
  }

  return (
    <div className="flex flex-col gap-y-4 overflow-hidden">
      <h1 className="text-xl font-semibold">Your entities</h1>

      <DataTable
        aria-label="Outbound table"
        columns={[name, status, role]}
        containerClassName="pr-2"
        data={sortEntities}
        loadingRows={12}
        maxHeight="sm"
        emptyState={{
          title: "No entities found",
          description: "You currently don't have any entities",
        }}
        onRowClick={handleClick}
        pagination
        stickyHeader
      />
    </div>
  )
}
