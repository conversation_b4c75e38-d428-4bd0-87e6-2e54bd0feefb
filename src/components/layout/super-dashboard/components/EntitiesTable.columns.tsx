import { cn } from "@/lib/utils"
import { column } from "@/components/base/data-table"
import { EntityStatus, GetEntityAccessDto } from "@/client/onboarding/types.gen"
import StatusBadge from "@/components/pages/_components/status-badge"
import { useMemo } from "react"
import { StatusVariant } from "@/components/pages/_components/status-badge/StatusBadge"
import { CircleDashed, Clock5 } from "lucide-react"

function Roles({ roles }: { roles: string[] }) {
  return (
    <div className="flex flex-wrap gap-1">
      {roles.map((role, i) => (
        <StatusBadge key={i} variant="neutral">
          {role}
        </StatusBadge>
      ))}
    </div>
  )
}

const MapEntityStatus = (status: EntityStatus | undefined) => {
  if (!status) return ""

  return {
    PreSubmission: "Draft",
    ReadyForOnboarding: "Submitted",
    Onboarding: "Pending",
    Client: "Active",
  }[status]
}

export const name = column.text<GetEntityAccessDto>("entityName", "Name", {
  headerClassName: cn("w-[300px]"),
})

export const status = column.custom<GetEntityAccessDto>(
  null,
  "Status",
  (entityStatus: EntityStatus) => {
    if (!entityStatus) return null

    const variant = useMemo(
      () =>
        ({
          Client: "info",
          PreSubmission: "neutral",
          ReadyForOnboarding: "success",
          Onboarding: "warning",
        })[entityStatus],
      [entityStatus],
    ) as StatusVariant

    const icon = useMemo(() => {
      const icons = {
        PreSubmission: CircleDashed,
        Onboarding: Clock5,
      } as const

      return entityStatus in icons
        ? icons[entityStatus as keyof typeof icons]
        : undefined
    }, [entityStatus])

    return (
      <StatusBadge
        icon={icon}
        variant={variant}
        text={MapEntityStatus(entityStatus)}
      />
    )
  },
  {
    key: "entityStatus",
    accessor: ({ entityStatus }) => entityStatus,
    headerClassName: cn("w-[200px]"),
    sortable: true,
  },
)

export const role = column.custom<GetEntityAccessDto>(
  "roles",
  "Role(s)",
  (roles: string[]) => {
    if (!roles) return null

    return <Roles roles={roles} />
  },
  {
    headerClassName: cn("w-[100px]"),
  },
)
