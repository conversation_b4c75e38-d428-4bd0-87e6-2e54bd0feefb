import { useMemo } from "react"
import { <PERSON> } from "lucide-react"
import { useAuth0 } from "@auth0/auth0-react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

export function SuperDashboardHeader() {
  const { logout, user } = useAuth0()

  const userName = user?.name?.split(" ")[0]
  const userInitials = useMemo(() => {
    if (!user?.nickname) return "-"
    return user?.nickname
      .split(".")
      .slice(0, 3)
      .map((s) => s?.[0]?.toUpperCase() ?? "")
      .join("")
  }, [user?.nickname])

  return (
    <header className="z-20 flex shrink-0 items-center gap-2 transition-[width,height] ease-linear">
      <section className="content-container">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center gap-4"></div>
          <div className="flex items-center gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild className="bg-background">
                <Button
                  aria-label="Notifications"
                  className="rounded-full p-0"
                  size="icon"
                  variant="ghost"
                >
                  <Bell className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>No new notifications</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className="rounded-full p-0"
                  data-testid="avatar-button"
                  size="icon"
                  variant="secondary"
                >
                  {userInitials}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {/* <DropdownMenuItem>Profile</DropdownMenuItem> */}
                {/* <DropdownMenuItem>Settings</DropdownMenuItem> */}
                <DropdownMenuItem
                  id="logout-button"
                  onClick={() =>
                    logout({
                      logoutParams: {
                        returnTo: window.location.origin,
                      },
                    })
                  }
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="flex flex-1 items-center justify-between pb-4">
          <div className="flex flex-none items-center gap-3">
            <h1 className="text-3xl font-semibold">
              Hello, <span>{userName}.</span>
            </h1>
          </div>
        </div>
      </section>
    </header>
  )
}
