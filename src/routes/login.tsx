import { createFileRoute, redirect, useSearch } from "@tanstack/react-router"

import { useLogin } from "@/hooks/use-login"

export const Route = createFileRoute("/login")({
  beforeLoad: ({ context, search }) => {
    if (context.auth?.isAuthenticated) {
      throw redirect({
        to: (search as any).redirect || "/",
      })
    }
  },
  component: RouteComponent,
})

function RouteComponent() {
  const { login } = useLogin()
  const search = useSearch({ from: "/login" })

  login((search as any)?.redirect)
}
