import { Suspense, useEffect } from "react"
import {
  createRootRouteWithContext,
  ErrorComponentProps,
  Outlet,
  useRouter,
} from "@tanstack/react-router"
import { QueryClient, useQueryErrorResetBoundary } from "@tanstack/react-query"

import { UseAuth } from "@/hooks/use-auth"
import { Toaster } from "@/components/ui/sonner"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

interface RouterContext {
  auth: UseAuth
  queryClient: QueryClient
}

export const Route = createRootRouteWithContext<RouterContext>()({
  component: Root,
  notFoundComponent: NotFound,
  errorComponent: ErrorComponent,
})

export function Root() {
  return (
    <main className="max-w-screen overflow-x-auto">
      <Suspense fallback={<LoadingSpinner size="8" />}>
        <Outlet />
      </Suspense>
      <Toaster />
    </main>
  )
}

function NotFound() {
  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <h1 className="text-2xl font-bold text-foreground">Page not found!</h1>
    </div>
  )
}

function ErrorComponent(_props: ErrorComponentProps) {
  const router = useRouter()
  const queryErrorResetBoundary = useQueryErrorResetBoundary()

  useEffect(() => {
    queryErrorResetBoundary.reset()
  }, [queryErrorResetBoundary])

  return (
    <div>
      <p>Something went wrong.</p>

      <Button
        onClick={() => {
          router.invalidate()
        }}
      >
        Retry
      </Button>
    </div>
  )
}
