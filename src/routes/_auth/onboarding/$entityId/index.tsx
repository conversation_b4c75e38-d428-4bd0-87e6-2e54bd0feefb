import { createFileRoute } from "@tanstack/react-router"

import { useLoaderData } from "@/data/onboarding/$entityId.loader"
import { OnboardingUserAdminProvider } from "@/contexts/OnboardingUserAdminContext"
import { UserAdminPage } from "@/components/pages/user-admin/Page"
import { OnboardingPage } from "@/components/pages/onboarding/Page"
import { OnboardingSidebar } from "@/components/layout/onboarding/OnboardingSidebar"
import { OnboardingHeader } from "@/components/layout/onboarding/OnboardingHeader"
import { DefaultLayout } from "@/components/layout/DefaultLayout"

type OnboardingSearch = {
  view?: "review" | "user-admin"
}

export const Route = createFileRoute("/_auth/onboarding/$entityId/")({
  validateSearch: (search: Record<string, unknown>): OnboardingSearch => {
    return {
      view: search.view as "review" | "user-admin" | undefined,
    }
  },
  component: RouteComponent,
})

function OnboardingUserAdminPage() {
  return (
    <div className="mb-10 flex flex-1">
      <UserAdminPage />
    </div>
  )
}

function RouteComponent() {
  const { view } = Route.useSearch()
  const { entity, entityAccess, user } = useLoaderData()

  // Create loader data for the context
  const loaderData = {
    entity,
    entities: entityAccess || [],
    entityId: entity.id!,
    user,
  }

  const renderContent = () => {
    switch (view) {
      case "review":
        return <OnboardingPage />
      case "user-admin":
        return <OnboardingUserAdminPage />
      default:
        return <OnboardingPage />
    }
  }

  return (
    <OnboardingUserAdminProvider loaderData={loaderData}>
      <DefaultLayout
        Header={<OnboardingHeader />}
        Sidebar={<OnboardingSidebar />}
      >
        <div className="ml-8 flex h-full flex-1">{renderContent()}</div>
      </DefaultLayout>
    </OnboardingUserAdminProvider>
  )
}
