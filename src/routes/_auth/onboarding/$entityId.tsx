import { createFileRoute, Outlet } from "@tanstack/react-router"

import {
  getCountriesQryOpts,
  getCurrenciesQryOpts,
  getEntityTypesQryOpts,
  getPurposeOfEntitiesQryOpts,
  getEntityRegulationQryOpts,
  getTransactionActivityTypesQryOpts,
  getStockExchangeQryOpts,
  getGicsClassificationIndustriesQryOpts,
  getUserRoleQryOpts,
  getSourceOfFundsQryOpts,
} from "@/data/onboarding/static-data.query"
import { loggedInUserQryOpts } from "@/data/onboarding/onboarding.query"
import { entityQryOpts } from "@/data/onboarding/entity-details.query"
import { getEntityAccessQryOpts } from "@/data/onboarding/entity-access.query"

export const Route = createFileRoute("/_auth/onboarding/$entityId")({
  loader: async ({ params: { entityId }, context: { queryClient } }) => {
    const [
      user,
      entity,
      entityAccess,
      staticDataCountries,
      staticDataCurrencies,
      staticDataEntityTypes,
      staticDataPurposeOfEntities,
      staticDataEntityRegulations,
      staticDataTransactionActivityTypes,
      staticDataStockExchange,
      staticDataGicsClassificationIndustries,
      staticDataUserRoles,
      staticSourceOfFunds,
    ] = await Promise.all([
      queryClient.ensureQueryData(loggedInUserQryOpts()),
      queryClient.ensureQueryData(entityQryOpts(entityId)),
      queryClient.ensureQueryData(getEntityAccessQryOpts(entityId)),
      queryClient.ensureQueryData(getCountriesQryOpts()),
      queryClient.ensureQueryData(getCurrenciesQryOpts()),
      queryClient.ensureQueryData(getEntityTypesQryOpts()),
      queryClient.ensureQueryData(getPurposeOfEntitiesQryOpts()),
      queryClient.ensureQueryData(getEntityRegulationQryOpts()),
      queryClient.ensureQueryData(getTransactionActivityTypesQryOpts()),
      queryClient.ensureQueryData(getStockExchangeQryOpts()),
      queryClient.ensureQueryData(getGicsClassificationIndustriesQryOpts()),
      queryClient.ensureQueryData(getUserRoleQryOpts()),
      queryClient.ensureQueryData(getSourceOfFundsQryOpts()),
    ])

    if (entity.id) localStorage.setItem("x-entity-id", entity.id)

    return {
      user,
      entity,
      entityAccess,
      staticDataCountries,
      staticDataCurrencies,
      staticDataEntityTypes,
      staticDataPurposeOfEntities,
      staticDataEntityRegulations,
      staticDataTransactionActivityTypes,
      staticDataStockExchange,
      staticDataGicsClassificationIndustries,
      staticDataUserRoles,
      staticSourceOfFunds,
    }
  },
  component: () => <Outlet />,
})
