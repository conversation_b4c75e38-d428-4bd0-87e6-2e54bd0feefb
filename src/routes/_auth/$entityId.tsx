import {
  Navigate,
  Outlet,
  createFileRoute,
  useLoaderData,
} from "@tanstack/react-router"

import { useNotification } from "@/hooks/use-notification"
import {
  getEntityAccessQryOpts,
  loggedInUserQryOpts,
} from "@/data/onboarding/onboarding.query"
import { entityQryOpts } from "@/data/onboarding/entity-details.query"
import { EntityLayout } from "@/components/layout/entity"
import { ErrorComponent } from "@/components/base/error/ErrorComponent"

export const Route = createFileRoute("/_auth/$entityId")({
  loader: async ({ params: { entityId }, context: { queryClient } }) => {
    const user = await queryClient.ensureQueryData(loggedInUserQryOpts())
    const entity = await queryClient.ensureQueryData(entityQryOpts(entityId))
    const entities = await queryClient.ensureQueryData(getEntityAccessQryOpts())

    if (entity.id) localStorage.setItem("x-entity-id", entity.id)

    return { user, entity, entities }
  },
  component: Layout,
  errorComponent: ErrorComponent,
})

function Layout() {
  useNotification()
  const { entity, entities } = useLoaderData({ from: Route.id })

  if (entities && entity && entities.length > 0) {
    const isClient =
      entities?.find(
        (e) =>
          e.entityId?.toLocaleLowerCase() === entity.id!.toLocaleLowerCase(),
      )?.entityStatus === "Client"

    if (!isClient) {
      return <Navigate to="/" />
    }
  }

  return (
    <EntityLayout>
      <Outlet />
    </EntityLayout>
  )
}
