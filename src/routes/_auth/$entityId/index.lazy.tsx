import { createLazyFileRoute, useLoaderData } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { DashboardPage } from "@/components/pages/dashboard/Page"

export const Route = createLazyFileRoute("/_auth/$entityId/")({
  component: EntityDashboard,
})

function EntityDashboard() {
  useNavigationIcon("")
  const { entity } = useLoaderData({ from: "/_auth/$entityId" })

  useBreadcrumb("dashboard", {
    path: "",
    label: entity?.legalEntity?.name || "Dashboard",
  })

  return <DashboardPage />
}
