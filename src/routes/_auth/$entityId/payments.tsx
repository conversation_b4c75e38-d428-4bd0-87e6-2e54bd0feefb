import { createFileRoute, Outlet } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"

type PaymentsSearchTab = "default" | "bulk"

export type PaymentsSearchFilterKeys =
  | "All"
  | "Draft"
  | "Pending"
  | "Sent"
  | "Failed"

export type PaymentsSearch = {
  tab?: "default" | "bulk"
  filter?: PaymentsSearchFilterKeys
  paymentId?: string
  bulkPaymentId?: string
  fromRoute?: string
  from?: string
  fromCurrency?: string
  to?: string
}

export const Route = createFileRoute("/_auth/$entityId/payments")({
  validateSearch: (search: Record<string, unknown>): PaymentsSearch => {
    // validate and parse the search params into a typed state
    return {
      tab: (search.tab as PaymentsSearchTab) || "default",
      filter: (search.filter as PaymentsSearchFilterKeys) || "All",
    }
  },
  component: RouteComponent,
})

function RouteComponent() {
  useNavigationIcon("/payments")

  useBreadcrumb("payments", { path: "payments", label: "Payments" })
  return <Outlet />
}
