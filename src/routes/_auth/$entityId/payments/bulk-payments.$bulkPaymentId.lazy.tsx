import React from "react"
import { createLazyFileRoute } from "@tanstack/react-router"

import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { useBulkPaymentByIdQuery } from "@/data/payments/payments.query"
import { PaymentsListingPage } from "@/components/pages/payments/Page"

export const Route = createLazyFileRoute(
  "/_auth/$entityId/payments/bulk-payments/$bulkPaymentId",
)({
  component: RouteComponent,
})

function RouteComponent() {
  const { entityId, bulkPaymentId } = Route.useParams()

  const { data: bulkPayment } = useBulkPaymentByIdQuery(bulkPaymentId)

  useBreadcrumb("bulk-payment-payments-" + bulkPaymentId, {
    path: `bulk-payments/${bulkPaymentId}`,
    label: bulkPayment ? `Bulk - ${bulkPayment.fileName}` : `Bulk Payments`,
  })

  return (
    <PaymentsListingPage bulkUploadId={bulkPaymentId} entityId={entityId} />
  )
}
