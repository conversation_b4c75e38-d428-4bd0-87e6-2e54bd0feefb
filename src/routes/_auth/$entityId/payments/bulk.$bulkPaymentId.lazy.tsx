import { createLazyFileRoute } from "@tanstack/react-router"

import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { BulkPaymentDetailsPage } from "@/components/pages/bulk-payment/BulkPaymentDetailsPage"

export const Route = createLazyFileRoute(
  "/_auth/$entityId/payments/bulk/$bulkPaymentId",
)({
  component: RouteComponent,
})

function RouteComponent() {
  const { entityId, bulkPaymentId } = Route.useParams()
  useBreadcrumb("bulk-payment", {
    path: `bulk/${bulkPaymentId}`,
    label: "Add payment file",
  })
  return <BulkPaymentDetailsPage entityId={entityId} id={bulkPaymentId} />
}
