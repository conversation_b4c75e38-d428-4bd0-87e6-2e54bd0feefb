import { createLazyFileRoute } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { SendPaymentsPage } from "@/components/pages/send-payments/Page"

export const Route = createLazyFileRoute("/_auth/$entityId/payments/send")({
  component: RouteComponent,
})

function RouteComponent() {
  useNavigationIcon("/payments")

  useBreadcrumb("create-payment", { path: "send", label: "Send Payments" })

  const { entityId } = Route.useParams()
  return <SendPaymentsPage entityId={entityId} />
}
