import { createLazyFileRoute } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { ApprovePaymentsPage } from "@/components/pages/approve-payments/Page"

export const Route = createLazyFileRoute(
  "/_auth/$entityId/payments/$paymentId",
)({
  component: RouteComponent,
})

function RouteComponent() {
  useNavigationIcon("/payments")

  useBreadcrumb("payment-details", {
    path: "send",
    label: "Approve payment",
  })

  const { entityId, paymentId } = Route.useParams()

  return <ApprovePaymentsPage entityId={entityId} paymentId={paymentId} />
}
