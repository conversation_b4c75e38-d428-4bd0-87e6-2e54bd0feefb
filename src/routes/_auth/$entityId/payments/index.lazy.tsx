import { createLazyFileRoute } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { PaymentsListingPage } from "@/components/pages/payments/Page"

export const Route = createLazyFileRoute("/_auth/$entityId/payments/")({
  component: RouteComponent,
})

function RouteComponent() {
  useNavigationIcon("/payments")
  const { entityId } = Route.useParams()

  return <PaymentsListingPage entityId={entityId} />
}
