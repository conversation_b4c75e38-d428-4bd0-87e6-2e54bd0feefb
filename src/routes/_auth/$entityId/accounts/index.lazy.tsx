import { createLazyFileRoute, useParams } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useAccountsListQuery } from "@/data/account/account.query"
import { AccountsPage } from "@/components/pages/accounts/Page"
import { LoadingSpinner } from "@/components/base/loading-spinner/LoadingSpinner"

export const Route = createLazyFileRoute("/_auth/$entityId/accounts/")({
  component: RouteComponent,
})

function RouteComponent() {
  const { entityId } = useParams({ from: "/_auth/$entityId/accounts" })
  const { data: accounts, isLoading } = useAccountsListQuery()
  useNavigationIcon("/accounts")

  if (isLoading) {
    return (
      <div className="mt-1 flex h-96 w-full items-center justify-center">
        <LoadingSpinner size="8" />
      </div>
    )
  }

  return <AccountsPage accounts={accounts} entityId={entityId} />
}
