import { createLazyFileRoute } from "@tanstack/react-router"

import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { AddNewUserForm } from "@/components/pages/add-new-user/components/AddNewUserForm"

export const Route = createLazyFileRoute(
  "/_auth/$entityId/user-admin/add-new-user",
)({
  component: RouteComponent,
})

function RouteComponent() {
  useBreadcrumb("add-user", { path: "add-new-user", label: "Add new user" })

  return <AddNewUserForm />
}
