import { createLazyFileRoute } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { TradeListingPage } from "@/components/pages/trade/TradeListingPage"

export const Route = createLazyFileRoute("/_auth/$entityId/trades/")({
  component: TradesIndexPage,
})

function TradesIndexPage() {
  useNavigationIcon("/trades")
  const { entityId } = Route.useParams()
  return <TradeListingPage entityId={entityId} />
}
