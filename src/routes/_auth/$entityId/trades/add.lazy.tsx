import { createLazyFileRoute } from "@tanstack/react-router"

import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { AddTradePage } from "@/components/pages/trade/AddTradePage"

export const Route = createLazyFileRoute("/_auth/$entityId/trades/add")({
  component: AddTradesPage,
})

function AddTradesPage() {
  useBreadcrumb("add-trade", { path: "trades/add", label: "" })

  return <AddTradePage />
}
