import { createFileRoute, Outlet } from "@tanstack/react-router"

import { useBreadcrumb } from "@/hooks/use-breadcrumb"

type UserAdminSearchTab = "default"

export type UserAdminFilterKeys =
  | "All"
  | "Draft"
  | "Pending"
  | "Sent"
  | "Failed"

type UserAdminSearch = {
  tab?: UserAdminSearchTab
  filter?: UserAdminFilterKeys
}

export const Route = createFileRoute("/_auth/$entityId/user-admin")({
  validateSearch: (search: Record<string, unknown>): UserAdminSearch => {
    // validate and parse the search params into a typed state
    return {
      tab: (search.tab as UserAdminSearchTab) || "default",
      filter: (search.filter as UserAdminFilterKeys) || "All",
    }
  },
  component: RouteComponent,
})

function RouteComponent() {
  useBreadcrumb("user-admin", { path: "user-admin", label: "User admin" })

  return <Outlet />
}
