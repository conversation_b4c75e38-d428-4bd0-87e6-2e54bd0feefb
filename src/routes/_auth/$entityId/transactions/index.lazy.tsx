import { createLazyFileRoute } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { TransactionsPage } from "@/components/pages/transactions/TransactionsPage"

export const Route = createLazyFileRoute("/_auth/$entityId/transactions/")({
  component: RouteComponent,
})

function RouteComponent() {
  useNavigationIcon("/transactions")

  useBreadcrumb("transactions", {
    path: "transactions",
    label: "Transactions",
  })
  const { entityId } = Route.useParams()
  return <TransactionsPage entityId={entityId} />
}
