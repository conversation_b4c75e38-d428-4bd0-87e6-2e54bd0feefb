import { createFileRoute, Outlet } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"

export const Route = createFileRoute("/_auth/$entityId/trades")({
  component: TradesPage,
})

function TradesPage() {
  useNavigationIcon("/trades")

  useBreadcrumb("trades", { path: "trades", label: "Trades" })

  return <Outlet />
}
