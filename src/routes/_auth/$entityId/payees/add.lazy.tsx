import { createLazyFileRoute } from "@tanstack/react-router"

import { useBreadcrumb } from "@/hooks/use-breadcrumb"
import { AddPayeePage } from "@/components/pages/add-payee/AddPayeePage"

export const Route = createLazyFileRoute("/_auth/$entityId/payees/add")({
  component: RouteComponent,
})

function RouteComponent() {
  const { entityId } = Route.useParams()
  useBreadcrumb("add payee", { path: "payees/add", label: "Add Payee" })
  return <AddPayeePage entityId={entityId} />
}
