import { createFileRoute, Outlet } from "@tanstack/react-router"

import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useBreadcrumb } from "@/hooks/use-breadcrumb"

export const Route = createFileRoute("/_auth/$entityId/payees")({
  component: RouteComponent,
})
function RouteComponent() {
  useNavigationIcon("/payees")

  useBreadcrumb("payees", { path: "payees", label: "Payees" })
  return <Outlet />
}
