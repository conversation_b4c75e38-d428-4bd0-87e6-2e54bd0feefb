import { createFileRoute, redirect } from "@tanstack/react-router"

import loadDevTools from "@/dev-tools"

export const Route = createFileRoute("/_auth")({
  beforeLoad: ({ context, location }) => {
    if (!context.auth.isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      })
    }

    // init dev tools only for auth users
    loadDevTools(context.auth)
  },
})
