import ReactDOM from "react-dom/client"
import React from "react"
import { Router<PERSON>rovider, createRouter } from "@tanstack/react-router"
import { QueryClient } from "@tanstack/react-query"
import { Auth0Provider, Auth0ProviderOptions } from "@auth0/auth0-react"

// Import the generated route tree
import { routeTree } from "./routeTree.gen"
import ReactQueryProvider from "./providers/ReactQueryProvider"
import "./index.css"
import "./instrument"
import { useAuth } from "./hooks/use-auth"
import { useApiClients } from "./hooks/use-api-clients"

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 3,
      staleTime: 1000,
    },
  },
})

// Create a new router instance
export const router = createRouter({
  routeTree,
  defaultPreload: false,
  context: {
    auth: undefined!,
    queryClient,
  },
  defaultPreloadStaleTime: 30000, // 30 seconds to prevent lazy loading
  defaultPreloadDelay: 0, // No delay for preloading
})

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

const providerConfig: Auth0ProviderOptions = {
  domain: import.meta.env.VITE_AUTH0_DOMAIN,
  clientId: import.meta.env.VITE_AUTH0_CLIENT_ID,
  authorizationParams: {
    redirect_uri: window.location.origin,
    audience: import.meta.env.VITE_AUTH0_AUDIENCE,
    scope: "openid profile email offline_access",
  },
  useRefreshTokens: true,
}

export function RouterProviderWithContext() {
  const auth = useAuth()

  // we initiate our clients to include token headers into our axios interceptors
  useApiClients()

  if (auth.isLoading) return null

  return (
    <ReactQueryProvider client={queryClient}>
      <RouterProvider context={{ auth }} router={router} />
    </ReactQueryProvider>
  )
}

function App() {
  return (
    <Auth0Provider {...providerConfig}>
      <RouterProviderWithContext />
    </Auth0Provider>
  )
}

const rootElement = document.getElementById("root")!

if (!rootElement?.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)

  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
  )
}
