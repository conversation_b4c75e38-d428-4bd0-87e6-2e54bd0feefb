/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as AuthImport } from './routes/_auth'
import { Route as AuthIndexImport } from './routes/_auth/index'
import { Route as AuthEntityIdImport } from './routes/_auth/$entityId'
import { Route as AuthOnboardingEntityIdImport } from './routes/_auth/onboarding/$entityId'
import { Route as AuthEntityIdUserAdminImport } from './routes/_auth/$entityId/user-admin'
import { Route as AuthEntityIdTradesImport } from './routes/_auth/$entityId/trades'
import { Route as AuthEntityIdPaymentsImport } from './routes/_auth/$entityId/payments'
import { Route as AuthEntityIdPayeesImport } from './routes/_auth/$entityId/payees'
import { Route as AuthEntityIdAccountsImport } from './routes/_auth/$entityId/accounts'
import { Route as AuthOnboardingEntityIdIndexImport } from './routes/_auth/onboarding/$entityId/index'

// Create Virtual Routes

const AuthEntityIdIndexLazyImport = createFileRoute('/_auth/$entityId/')()
const AuthEntityIdUserAdminIndexLazyImport = createFileRoute(
  '/_auth/$entityId/user-admin/',
)()
const AuthEntityIdTransactionsIndexLazyImport = createFileRoute(
  '/_auth/$entityId/transactions/',
)()
const AuthEntityIdTradesIndexLazyImport = createFileRoute(
  '/_auth/$entityId/trades/',
)()
const AuthEntityIdPaymentsIndexLazyImport = createFileRoute(
  '/_auth/$entityId/payments/',
)()
const AuthEntityIdPayeesIndexLazyImport = createFileRoute(
  '/_auth/$entityId/payees/',
)()
const AuthEntityIdAccountsIndexLazyImport = createFileRoute(
  '/_auth/$entityId/accounts/',
)()
const AuthEntityIdUserAdminAddNewUserLazyImport = createFileRoute(
  '/_auth/$entityId/user-admin/add-new-user',
)()
const AuthEntityIdTradesAddLazyImport = createFileRoute(
  '/_auth/$entityId/trades/add',
)()
const AuthEntityIdPaymentsSendLazyImport = createFileRoute(
  '/_auth/$entityId/payments/send',
)()
const AuthEntityIdPaymentsPaymentIdLazyImport = createFileRoute(
  '/_auth/$entityId/payments/$paymentId',
)()
const AuthEntityIdPayeesAddLazyImport = createFileRoute(
  '/_auth/$entityId/payees/add',
)()
const AuthEntityIdPaymentsBulkBulkPaymentIdLazyImport = createFileRoute(
  '/_auth/$entityId/payments/bulk/$bulkPaymentId',
)()
const AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyImport = createFileRoute(
  '/_auth/$entityId/payments/bulk-payments/$bulkPaymentId',
)()

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const AuthIndexRoute = AuthIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)

const AuthEntityIdRoute = AuthEntityIdImport.update({
  id: '/$entityId',
  path: '/$entityId',
  getParentRoute: () => AuthRoute,
} as any)

const AuthEntityIdIndexLazyRoute = AuthEntityIdIndexLazyImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthEntityIdRoute,
} as any).lazy(() =>
  import('./routes/_auth/$entityId/index.lazy').then((d) => d.Route),
)

const AuthOnboardingEntityIdRoute = AuthOnboardingEntityIdImport.update({
  id: '/onboarding/$entityId',
  path: '/onboarding/$entityId',
  getParentRoute: () => AuthRoute,
} as any)

const AuthEntityIdUserAdminRoute = AuthEntityIdUserAdminImport.update({
  id: '/user-admin',
  path: '/user-admin',
  getParentRoute: () => AuthEntityIdRoute,
} as any)

const AuthEntityIdTradesRoute = AuthEntityIdTradesImport.update({
  id: '/trades',
  path: '/trades',
  getParentRoute: () => AuthEntityIdRoute,
} as any)

const AuthEntityIdPaymentsRoute = AuthEntityIdPaymentsImport.update({
  id: '/payments',
  path: '/payments',
  getParentRoute: () => AuthEntityIdRoute,
} as any)

const AuthEntityIdPayeesRoute = AuthEntityIdPayeesImport.update({
  id: '/payees',
  path: '/payees',
  getParentRoute: () => AuthEntityIdRoute,
} as any)

const AuthEntityIdAccountsRoute = AuthEntityIdAccountsImport.update({
  id: '/accounts',
  path: '/accounts',
  getParentRoute: () => AuthEntityIdRoute,
} as any)

const AuthEntityIdUserAdminIndexLazyRoute =
  AuthEntityIdUserAdminIndexLazyImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthEntityIdUserAdminRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/user-admin/index.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthEntityIdTransactionsIndexLazyRoute =
  AuthEntityIdTransactionsIndexLazyImport.update({
    id: '/transactions/',
    path: '/transactions/',
    getParentRoute: () => AuthEntityIdRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/transactions/index.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthEntityIdTradesIndexLazyRoute =
  AuthEntityIdTradesIndexLazyImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthEntityIdTradesRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/trades/index.lazy').then((d) => d.Route),
  )

const AuthEntityIdPaymentsIndexLazyRoute =
  AuthEntityIdPaymentsIndexLazyImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthEntityIdPaymentsRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/payments/index.lazy').then((d) => d.Route),
  )

const AuthEntityIdPayeesIndexLazyRoute =
  AuthEntityIdPayeesIndexLazyImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthEntityIdPayeesRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/payees/index.lazy').then((d) => d.Route),
  )

const AuthEntityIdAccountsIndexLazyRoute =
  AuthEntityIdAccountsIndexLazyImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthEntityIdAccountsRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/accounts/index.lazy').then((d) => d.Route),
  )

const AuthOnboardingEntityIdIndexRoute =
  AuthOnboardingEntityIdIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthOnboardingEntityIdRoute,
  } as any)

const AuthEntityIdUserAdminAddNewUserLazyRoute =
  AuthEntityIdUserAdminAddNewUserLazyImport.update({
    id: '/add-new-user',
    path: '/add-new-user',
    getParentRoute: () => AuthEntityIdUserAdminRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/user-admin/add-new-user.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthEntityIdTradesAddLazyRoute = AuthEntityIdTradesAddLazyImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthEntityIdTradesRoute,
} as any).lazy(() =>
  import('./routes/_auth/$entityId/trades/add.lazy').then((d) => d.Route),
)

const AuthEntityIdPaymentsSendLazyRoute =
  AuthEntityIdPaymentsSendLazyImport.update({
    id: '/send',
    path: '/send',
    getParentRoute: () => AuthEntityIdPaymentsRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/payments/send.lazy').then((d) => d.Route),
  )

const AuthEntityIdPaymentsPaymentIdLazyRoute =
  AuthEntityIdPaymentsPaymentIdLazyImport.update({
    id: '/$paymentId',
    path: '/$paymentId',
    getParentRoute: () => AuthEntityIdPaymentsRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/payments/$paymentId.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthEntityIdPayeesAddLazyRoute = AuthEntityIdPayeesAddLazyImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthEntityIdPayeesRoute,
} as any).lazy(() =>
  import('./routes/_auth/$entityId/payees/add.lazy').then((d) => d.Route),
)

const AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute =
  AuthEntityIdPaymentsBulkBulkPaymentIdLazyImport.update({
    id: '/bulk/$bulkPaymentId',
    path: '/bulk/$bulkPaymentId',
    getParentRoute: () => AuthEntityIdPaymentsRoute,
  } as any).lazy(() =>
    import('./routes/_auth/$entityId/payments/bulk.$bulkPaymentId.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute =
  AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyImport.update({
    id: '/bulk-payments/$bulkPaymentId',
    path: '/bulk-payments/$bulkPaymentId',
    getParentRoute: () => AuthEntityIdPaymentsRoute,
  } as any).lazy(() =>
    import(
      './routes/_auth/$entityId/payments/bulk-payments.$bulkPaymentId.lazy'
    ).then((d) => d.Route),
  )

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/_auth/$entityId': {
      id: '/_auth/$entityId'
      path: '/$entityId'
      fullPath: '/$entityId'
      preLoaderRoute: typeof AuthEntityIdImport
      parentRoute: typeof AuthImport
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexImport
      parentRoute: typeof AuthImport
    }
    '/_auth/$entityId/accounts': {
      id: '/_auth/$entityId/accounts'
      path: '/accounts'
      fullPath: '/$entityId/accounts'
      preLoaderRoute: typeof AuthEntityIdAccountsImport
      parentRoute: typeof AuthEntityIdImport
    }
    '/_auth/$entityId/payees': {
      id: '/_auth/$entityId/payees'
      path: '/payees'
      fullPath: '/$entityId/payees'
      preLoaderRoute: typeof AuthEntityIdPayeesImport
      parentRoute: typeof AuthEntityIdImport
    }
    '/_auth/$entityId/payments': {
      id: '/_auth/$entityId/payments'
      path: '/payments'
      fullPath: '/$entityId/payments'
      preLoaderRoute: typeof AuthEntityIdPaymentsImport
      parentRoute: typeof AuthEntityIdImport
    }
    '/_auth/$entityId/trades': {
      id: '/_auth/$entityId/trades'
      path: '/trades'
      fullPath: '/$entityId/trades'
      preLoaderRoute: typeof AuthEntityIdTradesImport
      parentRoute: typeof AuthEntityIdImport
    }
    '/_auth/$entityId/user-admin': {
      id: '/_auth/$entityId/user-admin'
      path: '/user-admin'
      fullPath: '/$entityId/user-admin'
      preLoaderRoute: typeof AuthEntityIdUserAdminImport
      parentRoute: typeof AuthEntityIdImport
    }
    '/_auth/onboarding/$entityId': {
      id: '/_auth/onboarding/$entityId'
      path: '/onboarding/$entityId'
      fullPath: '/onboarding/$entityId'
      preLoaderRoute: typeof AuthOnboardingEntityIdImport
      parentRoute: typeof AuthImport
    }
    '/_auth/$entityId/': {
      id: '/_auth/$entityId/'
      path: '/'
      fullPath: '/$entityId/'
      preLoaderRoute: typeof AuthEntityIdIndexLazyImport
      parentRoute: typeof AuthEntityIdImport
    }
    '/_auth/$entityId/payees/add': {
      id: '/_auth/$entityId/payees/add'
      path: '/add'
      fullPath: '/$entityId/payees/add'
      preLoaderRoute: typeof AuthEntityIdPayeesAddLazyImport
      parentRoute: typeof AuthEntityIdPayeesImport
    }
    '/_auth/$entityId/payments/$paymentId': {
      id: '/_auth/$entityId/payments/$paymentId'
      path: '/$paymentId'
      fullPath: '/$entityId/payments/$paymentId'
      preLoaderRoute: typeof AuthEntityIdPaymentsPaymentIdLazyImport
      parentRoute: typeof AuthEntityIdPaymentsImport
    }
    '/_auth/$entityId/payments/send': {
      id: '/_auth/$entityId/payments/send'
      path: '/send'
      fullPath: '/$entityId/payments/send'
      preLoaderRoute: typeof AuthEntityIdPaymentsSendLazyImport
      parentRoute: typeof AuthEntityIdPaymentsImport
    }
    '/_auth/$entityId/trades/add': {
      id: '/_auth/$entityId/trades/add'
      path: '/add'
      fullPath: '/$entityId/trades/add'
      preLoaderRoute: typeof AuthEntityIdTradesAddLazyImport
      parentRoute: typeof AuthEntityIdTradesImport
    }
    '/_auth/$entityId/user-admin/add-new-user': {
      id: '/_auth/$entityId/user-admin/add-new-user'
      path: '/add-new-user'
      fullPath: '/$entityId/user-admin/add-new-user'
      preLoaderRoute: typeof AuthEntityIdUserAdminAddNewUserLazyImport
      parentRoute: typeof AuthEntityIdUserAdminImport
    }
    '/_auth/onboarding/$entityId/': {
      id: '/_auth/onboarding/$entityId/'
      path: '/'
      fullPath: '/onboarding/$entityId/'
      preLoaderRoute: typeof AuthOnboardingEntityIdIndexImport
      parentRoute: typeof AuthOnboardingEntityIdImport
    }
    '/_auth/$entityId/accounts/': {
      id: '/_auth/$entityId/accounts/'
      path: '/'
      fullPath: '/$entityId/accounts/'
      preLoaderRoute: typeof AuthEntityIdAccountsIndexLazyImport
      parentRoute: typeof AuthEntityIdAccountsImport
    }
    '/_auth/$entityId/payees/': {
      id: '/_auth/$entityId/payees/'
      path: '/'
      fullPath: '/$entityId/payees/'
      preLoaderRoute: typeof AuthEntityIdPayeesIndexLazyImport
      parentRoute: typeof AuthEntityIdPayeesImport
    }
    '/_auth/$entityId/payments/': {
      id: '/_auth/$entityId/payments/'
      path: '/'
      fullPath: '/$entityId/payments/'
      preLoaderRoute: typeof AuthEntityIdPaymentsIndexLazyImport
      parentRoute: typeof AuthEntityIdPaymentsImport
    }
    '/_auth/$entityId/trades/': {
      id: '/_auth/$entityId/trades/'
      path: '/'
      fullPath: '/$entityId/trades/'
      preLoaderRoute: typeof AuthEntityIdTradesIndexLazyImport
      parentRoute: typeof AuthEntityIdTradesImport
    }
    '/_auth/$entityId/transactions/': {
      id: '/_auth/$entityId/transactions/'
      path: '/transactions'
      fullPath: '/$entityId/transactions'
      preLoaderRoute: typeof AuthEntityIdTransactionsIndexLazyImport
      parentRoute: typeof AuthEntityIdImport
    }
    '/_auth/$entityId/user-admin/': {
      id: '/_auth/$entityId/user-admin/'
      path: '/'
      fullPath: '/$entityId/user-admin/'
      preLoaderRoute: typeof AuthEntityIdUserAdminIndexLazyImport
      parentRoute: typeof AuthEntityIdUserAdminImport
    }
    '/_auth/$entityId/payments/bulk-payments/$bulkPaymentId': {
      id: '/_auth/$entityId/payments/bulk-payments/$bulkPaymentId'
      path: '/bulk-payments/$bulkPaymentId'
      fullPath: '/$entityId/payments/bulk-payments/$bulkPaymentId'
      preLoaderRoute: typeof AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyImport
      parentRoute: typeof AuthEntityIdPaymentsImport
    }
    '/_auth/$entityId/payments/bulk/$bulkPaymentId': {
      id: '/_auth/$entityId/payments/bulk/$bulkPaymentId'
      path: '/bulk/$bulkPaymentId'
      fullPath: '/$entityId/payments/bulk/$bulkPaymentId'
      preLoaderRoute: typeof AuthEntityIdPaymentsBulkBulkPaymentIdLazyImport
      parentRoute: typeof AuthEntityIdPaymentsImport
    }
  }
}

// Create and export the route tree

interface AuthEntityIdAccountsRouteChildren {
  AuthEntityIdAccountsIndexLazyRoute: typeof AuthEntityIdAccountsIndexLazyRoute
}

const AuthEntityIdAccountsRouteChildren: AuthEntityIdAccountsRouteChildren = {
  AuthEntityIdAccountsIndexLazyRoute: AuthEntityIdAccountsIndexLazyRoute,
}

const AuthEntityIdAccountsRouteWithChildren =
  AuthEntityIdAccountsRoute._addFileChildren(AuthEntityIdAccountsRouteChildren)

interface AuthEntityIdPayeesRouteChildren {
  AuthEntityIdPayeesAddLazyRoute: typeof AuthEntityIdPayeesAddLazyRoute
  AuthEntityIdPayeesIndexLazyRoute: typeof AuthEntityIdPayeesIndexLazyRoute
}

const AuthEntityIdPayeesRouteChildren: AuthEntityIdPayeesRouteChildren = {
  AuthEntityIdPayeesAddLazyRoute: AuthEntityIdPayeesAddLazyRoute,
  AuthEntityIdPayeesIndexLazyRoute: AuthEntityIdPayeesIndexLazyRoute,
}

const AuthEntityIdPayeesRouteWithChildren =
  AuthEntityIdPayeesRoute._addFileChildren(AuthEntityIdPayeesRouteChildren)

interface AuthEntityIdPaymentsRouteChildren {
  AuthEntityIdPaymentsPaymentIdLazyRoute: typeof AuthEntityIdPaymentsPaymentIdLazyRoute
  AuthEntityIdPaymentsSendLazyRoute: typeof AuthEntityIdPaymentsSendLazyRoute
  AuthEntityIdPaymentsIndexLazyRoute: typeof AuthEntityIdPaymentsIndexLazyRoute
  AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute: typeof AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute
  AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute: typeof AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute
}

const AuthEntityIdPaymentsRouteChildren: AuthEntityIdPaymentsRouteChildren = {
  AuthEntityIdPaymentsPaymentIdLazyRoute:
    AuthEntityIdPaymentsPaymentIdLazyRoute,
  AuthEntityIdPaymentsSendLazyRoute: AuthEntityIdPaymentsSendLazyRoute,
  AuthEntityIdPaymentsIndexLazyRoute: AuthEntityIdPaymentsIndexLazyRoute,
  AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute:
    AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute,
  AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute:
    AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute,
}

const AuthEntityIdPaymentsRouteWithChildren =
  AuthEntityIdPaymentsRoute._addFileChildren(AuthEntityIdPaymentsRouteChildren)

interface AuthEntityIdTradesRouteChildren {
  AuthEntityIdTradesAddLazyRoute: typeof AuthEntityIdTradesAddLazyRoute
  AuthEntityIdTradesIndexLazyRoute: typeof AuthEntityIdTradesIndexLazyRoute
}

const AuthEntityIdTradesRouteChildren: AuthEntityIdTradesRouteChildren = {
  AuthEntityIdTradesAddLazyRoute: AuthEntityIdTradesAddLazyRoute,
  AuthEntityIdTradesIndexLazyRoute: AuthEntityIdTradesIndexLazyRoute,
}

const AuthEntityIdTradesRouteWithChildren =
  AuthEntityIdTradesRoute._addFileChildren(AuthEntityIdTradesRouteChildren)

interface AuthEntityIdUserAdminRouteChildren {
  AuthEntityIdUserAdminAddNewUserLazyRoute: typeof AuthEntityIdUserAdminAddNewUserLazyRoute
  AuthEntityIdUserAdminIndexLazyRoute: typeof AuthEntityIdUserAdminIndexLazyRoute
}

const AuthEntityIdUserAdminRouteChildren: AuthEntityIdUserAdminRouteChildren = {
  AuthEntityIdUserAdminAddNewUserLazyRoute:
    AuthEntityIdUserAdminAddNewUserLazyRoute,
  AuthEntityIdUserAdminIndexLazyRoute: AuthEntityIdUserAdminIndexLazyRoute,
}

const AuthEntityIdUserAdminRouteWithChildren =
  AuthEntityIdUserAdminRoute._addFileChildren(
    AuthEntityIdUserAdminRouteChildren,
  )

interface AuthEntityIdRouteChildren {
  AuthEntityIdAccountsRoute: typeof AuthEntityIdAccountsRouteWithChildren
  AuthEntityIdPayeesRoute: typeof AuthEntityIdPayeesRouteWithChildren
  AuthEntityIdPaymentsRoute: typeof AuthEntityIdPaymentsRouteWithChildren
  AuthEntityIdTradesRoute: typeof AuthEntityIdTradesRouteWithChildren
  AuthEntityIdUserAdminRoute: typeof AuthEntityIdUserAdminRouteWithChildren
  AuthEntityIdIndexLazyRoute: typeof AuthEntityIdIndexLazyRoute
  AuthEntityIdTransactionsIndexLazyRoute: typeof AuthEntityIdTransactionsIndexLazyRoute
}

const AuthEntityIdRouteChildren: AuthEntityIdRouteChildren = {
  AuthEntityIdAccountsRoute: AuthEntityIdAccountsRouteWithChildren,
  AuthEntityIdPayeesRoute: AuthEntityIdPayeesRouteWithChildren,
  AuthEntityIdPaymentsRoute: AuthEntityIdPaymentsRouteWithChildren,
  AuthEntityIdTradesRoute: AuthEntityIdTradesRouteWithChildren,
  AuthEntityIdUserAdminRoute: AuthEntityIdUserAdminRouteWithChildren,
  AuthEntityIdIndexLazyRoute: AuthEntityIdIndexLazyRoute,
  AuthEntityIdTransactionsIndexLazyRoute:
    AuthEntityIdTransactionsIndexLazyRoute,
}

const AuthEntityIdRouteWithChildren = AuthEntityIdRoute._addFileChildren(
  AuthEntityIdRouteChildren,
)

interface AuthOnboardingEntityIdRouteChildren {
  AuthOnboardingEntityIdIndexRoute: typeof AuthOnboardingEntityIdIndexRoute
}

const AuthOnboardingEntityIdRouteChildren: AuthOnboardingEntityIdRouteChildren =
  {
    AuthOnboardingEntityIdIndexRoute: AuthOnboardingEntityIdIndexRoute,
  }

const AuthOnboardingEntityIdRouteWithChildren =
  AuthOnboardingEntityIdRoute._addFileChildren(
    AuthOnboardingEntityIdRouteChildren,
  )

interface AuthRouteChildren {
  AuthEntityIdRoute: typeof AuthEntityIdRouteWithChildren
  AuthIndexRoute: typeof AuthIndexRoute
  AuthOnboardingEntityIdRoute: typeof AuthOnboardingEntityIdRouteWithChildren
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthEntityIdRoute: AuthEntityIdRouteWithChildren,
  AuthIndexRoute: AuthIndexRoute,
  AuthOnboardingEntityIdRoute: AuthOnboardingEntityIdRouteWithChildren,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthRouteWithChildren
  '/login': typeof LoginRoute
  '/$entityId': typeof AuthEntityIdRouteWithChildren
  '/': typeof AuthIndexRoute
  '/$entityId/accounts': typeof AuthEntityIdAccountsRouteWithChildren
  '/$entityId/payees': typeof AuthEntityIdPayeesRouteWithChildren
  '/$entityId/payments': typeof AuthEntityIdPaymentsRouteWithChildren
  '/$entityId/trades': typeof AuthEntityIdTradesRouteWithChildren
  '/$entityId/user-admin': typeof AuthEntityIdUserAdminRouteWithChildren
  '/onboarding/$entityId': typeof AuthOnboardingEntityIdRouteWithChildren
  '/$entityId/': typeof AuthEntityIdIndexLazyRoute
  '/$entityId/payees/add': typeof AuthEntityIdPayeesAddLazyRoute
  '/$entityId/payments/$paymentId': typeof AuthEntityIdPaymentsPaymentIdLazyRoute
  '/$entityId/payments/send': typeof AuthEntityIdPaymentsSendLazyRoute
  '/$entityId/trades/add': typeof AuthEntityIdTradesAddLazyRoute
  '/$entityId/user-admin/add-new-user': typeof AuthEntityIdUserAdminAddNewUserLazyRoute
  '/onboarding/$entityId/': typeof AuthOnboardingEntityIdIndexRoute
  '/$entityId/accounts/': typeof AuthEntityIdAccountsIndexLazyRoute
  '/$entityId/payees/': typeof AuthEntityIdPayeesIndexLazyRoute
  '/$entityId/payments/': typeof AuthEntityIdPaymentsIndexLazyRoute
  '/$entityId/trades/': typeof AuthEntityIdTradesIndexLazyRoute
  '/$entityId/transactions': typeof AuthEntityIdTransactionsIndexLazyRoute
  '/$entityId/user-admin/': typeof AuthEntityIdUserAdminIndexLazyRoute
  '/$entityId/payments/bulk-payments/$bulkPaymentId': typeof AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute
  '/$entityId/payments/bulk/$bulkPaymentId': typeof AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute
}

export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/': typeof AuthIndexRoute
  '/$entityId': typeof AuthEntityIdIndexLazyRoute
  '/$entityId/payees/add': typeof AuthEntityIdPayeesAddLazyRoute
  '/$entityId/payments/$paymentId': typeof AuthEntityIdPaymentsPaymentIdLazyRoute
  '/$entityId/payments/send': typeof AuthEntityIdPaymentsSendLazyRoute
  '/$entityId/trades/add': typeof AuthEntityIdTradesAddLazyRoute
  '/$entityId/user-admin/add-new-user': typeof AuthEntityIdUserAdminAddNewUserLazyRoute
  '/onboarding/$entityId': typeof AuthOnboardingEntityIdIndexRoute
  '/$entityId/accounts': typeof AuthEntityIdAccountsIndexLazyRoute
  '/$entityId/payees': typeof AuthEntityIdPayeesIndexLazyRoute
  '/$entityId/payments': typeof AuthEntityIdPaymentsIndexLazyRoute
  '/$entityId/trades': typeof AuthEntityIdTradesIndexLazyRoute
  '/$entityId/transactions': typeof AuthEntityIdTransactionsIndexLazyRoute
  '/$entityId/user-admin': typeof AuthEntityIdUserAdminIndexLazyRoute
  '/$entityId/payments/bulk-payments/$bulkPaymentId': typeof AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute
  '/$entityId/payments/bulk/$bulkPaymentId': typeof AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_auth': typeof AuthRouteWithChildren
  '/login': typeof LoginRoute
  '/_auth/$entityId': typeof AuthEntityIdRouteWithChildren
  '/_auth/': typeof AuthIndexRoute
  '/_auth/$entityId/accounts': typeof AuthEntityIdAccountsRouteWithChildren
  '/_auth/$entityId/payees': typeof AuthEntityIdPayeesRouteWithChildren
  '/_auth/$entityId/payments': typeof AuthEntityIdPaymentsRouteWithChildren
  '/_auth/$entityId/trades': typeof AuthEntityIdTradesRouteWithChildren
  '/_auth/$entityId/user-admin': typeof AuthEntityIdUserAdminRouteWithChildren
  '/_auth/onboarding/$entityId': typeof AuthOnboardingEntityIdRouteWithChildren
  '/_auth/$entityId/': typeof AuthEntityIdIndexLazyRoute
  '/_auth/$entityId/payees/add': typeof AuthEntityIdPayeesAddLazyRoute
  '/_auth/$entityId/payments/$paymentId': typeof AuthEntityIdPaymentsPaymentIdLazyRoute
  '/_auth/$entityId/payments/send': typeof AuthEntityIdPaymentsSendLazyRoute
  '/_auth/$entityId/trades/add': typeof AuthEntityIdTradesAddLazyRoute
  '/_auth/$entityId/user-admin/add-new-user': typeof AuthEntityIdUserAdminAddNewUserLazyRoute
  '/_auth/onboarding/$entityId/': typeof AuthOnboardingEntityIdIndexRoute
  '/_auth/$entityId/accounts/': typeof AuthEntityIdAccountsIndexLazyRoute
  '/_auth/$entityId/payees/': typeof AuthEntityIdPayeesIndexLazyRoute
  '/_auth/$entityId/payments/': typeof AuthEntityIdPaymentsIndexLazyRoute
  '/_auth/$entityId/trades/': typeof AuthEntityIdTradesIndexLazyRoute
  '/_auth/$entityId/transactions/': typeof AuthEntityIdTransactionsIndexLazyRoute
  '/_auth/$entityId/user-admin/': typeof AuthEntityIdUserAdminIndexLazyRoute
  '/_auth/$entityId/payments/bulk-payments/$bulkPaymentId': typeof AuthEntityIdPaymentsBulkPaymentsBulkPaymentIdLazyRoute
  '/_auth/$entityId/payments/bulk/$bulkPaymentId': typeof AuthEntityIdPaymentsBulkBulkPaymentIdLazyRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/login'
    | '/$entityId'
    | '/'
    | '/$entityId/accounts'
    | '/$entityId/payees'
    | '/$entityId/payments'
    | '/$entityId/trades'
    | '/$entityId/user-admin'
    | '/onboarding/$entityId'
    | '/$entityId/'
    | '/$entityId/payees/add'
    | '/$entityId/payments/$paymentId'
    | '/$entityId/payments/send'
    | '/$entityId/trades/add'
    | '/$entityId/user-admin/add-new-user'
    | '/onboarding/$entityId/'
    | '/$entityId/accounts/'
    | '/$entityId/payees/'
    | '/$entityId/payments/'
    | '/$entityId/trades/'
    | '/$entityId/transactions'
    | '/$entityId/user-admin/'
    | '/$entityId/payments/bulk-payments/$bulkPaymentId'
    | '/$entityId/payments/bulk/$bulkPaymentId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/'
    | '/$entityId'
    | '/$entityId/payees/add'
    | '/$entityId/payments/$paymentId'
    | '/$entityId/payments/send'
    | '/$entityId/trades/add'
    | '/$entityId/user-admin/add-new-user'
    | '/onboarding/$entityId'
    | '/$entityId/accounts'
    | '/$entityId/payees'
    | '/$entityId/payments'
    | '/$entityId/trades'
    | '/$entityId/transactions'
    | '/$entityId/user-admin'
    | '/$entityId/payments/bulk-payments/$bulkPaymentId'
    | '/$entityId/payments/bulk/$bulkPaymentId'
  id:
    | '__root__'
    | '/_auth'
    | '/login'
    | '/_auth/$entityId'
    | '/_auth/'
    | '/_auth/$entityId/accounts'
    | '/_auth/$entityId/payees'
    | '/_auth/$entityId/payments'
    | '/_auth/$entityId/trades'
    | '/_auth/$entityId/user-admin'
    | '/_auth/onboarding/$entityId'
    | '/_auth/$entityId/'
    | '/_auth/$entityId/payees/add'
    | '/_auth/$entityId/payments/$paymentId'
    | '/_auth/$entityId/payments/send'
    | '/_auth/$entityId/trades/add'
    | '/_auth/$entityId/user-admin/add-new-user'
    | '/_auth/onboarding/$entityId/'
    | '/_auth/$entityId/accounts/'
    | '/_auth/$entityId/payees/'
    | '/_auth/$entityId/payments/'
    | '/_auth/$entityId/trades/'
    | '/_auth/$entityId/transactions/'
    | '/_auth/$entityId/user-admin/'
    | '/_auth/$entityId/payments/bulk-payments/$bulkPaymentId'
    | '/_auth/$entityId/payments/bulk/$bulkPaymentId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_auth",
        "/login"
      ]
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/$entityId",
        "/_auth/",
        "/_auth/onboarding/$entityId"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_auth/$entityId": {
      "filePath": "_auth/$entityId.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/$entityId/accounts",
        "/_auth/$entityId/payees",
        "/_auth/$entityId/payments",
        "/_auth/$entityId/trades",
        "/_auth/$entityId/user-admin",
        "/_auth/$entityId/",
        "/_auth/$entityId/transactions/"
      ]
    },
    "/_auth/": {
      "filePath": "_auth/index.tsx",
      "parent": "/_auth"
    },
    "/_auth/$entityId/accounts": {
      "filePath": "_auth/$entityId/accounts.tsx",
      "parent": "/_auth/$entityId",
      "children": [
        "/_auth/$entityId/accounts/"
      ]
    },
    "/_auth/$entityId/payees": {
      "filePath": "_auth/$entityId/payees.tsx",
      "parent": "/_auth/$entityId",
      "children": [
        "/_auth/$entityId/payees/add",
        "/_auth/$entityId/payees/"
      ]
    },
    "/_auth/$entityId/payments": {
      "filePath": "_auth/$entityId/payments.tsx",
      "parent": "/_auth/$entityId",
      "children": [
        "/_auth/$entityId/payments/$paymentId",
        "/_auth/$entityId/payments/send",
        "/_auth/$entityId/payments/",
        "/_auth/$entityId/payments/bulk-payments/$bulkPaymentId",
        "/_auth/$entityId/payments/bulk/$bulkPaymentId"
      ]
    },
    "/_auth/$entityId/trades": {
      "filePath": "_auth/$entityId/trades.tsx",
      "parent": "/_auth/$entityId",
      "children": [
        "/_auth/$entityId/trades/add",
        "/_auth/$entityId/trades/"
      ]
    },
    "/_auth/$entityId/user-admin": {
      "filePath": "_auth/$entityId/user-admin.tsx",
      "parent": "/_auth/$entityId",
      "children": [
        "/_auth/$entityId/user-admin/add-new-user",
        "/_auth/$entityId/user-admin/"
      ]
    },
    "/_auth/onboarding/$entityId": {
      "filePath": "_auth/onboarding/$entityId.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/onboarding/$entityId/"
      ]
    },
    "/_auth/$entityId/": {
      "filePath": "_auth/$entityId/index.lazy.tsx",
      "parent": "/_auth/$entityId"
    },
    "/_auth/$entityId/payees/add": {
      "filePath": "_auth/$entityId/payees/add.lazy.tsx",
      "parent": "/_auth/$entityId/payees"
    },
    "/_auth/$entityId/payments/$paymentId": {
      "filePath": "_auth/$entityId/payments/$paymentId.lazy.tsx",
      "parent": "/_auth/$entityId/payments"
    },
    "/_auth/$entityId/payments/send": {
      "filePath": "_auth/$entityId/payments/send.lazy.tsx",
      "parent": "/_auth/$entityId/payments"
    },
    "/_auth/$entityId/trades/add": {
      "filePath": "_auth/$entityId/trades/add.lazy.tsx",
      "parent": "/_auth/$entityId/trades"
    },
    "/_auth/$entityId/user-admin/add-new-user": {
      "filePath": "_auth/$entityId/user-admin/add-new-user.lazy.tsx",
      "parent": "/_auth/$entityId/user-admin"
    },
    "/_auth/onboarding/$entityId/": {
      "filePath": "_auth/onboarding/$entityId/index.tsx",
      "parent": "/_auth/onboarding/$entityId"
    },
    "/_auth/$entityId/accounts/": {
      "filePath": "_auth/$entityId/accounts/index.lazy.tsx",
      "parent": "/_auth/$entityId/accounts"
    },
    "/_auth/$entityId/payees/": {
      "filePath": "_auth/$entityId/payees/index.lazy.tsx",
      "parent": "/_auth/$entityId/payees"
    },
    "/_auth/$entityId/payments/": {
      "filePath": "_auth/$entityId/payments/index.lazy.tsx",
      "parent": "/_auth/$entityId/payments"
    },
    "/_auth/$entityId/trades/": {
      "filePath": "_auth/$entityId/trades/index.lazy.tsx",
      "parent": "/_auth/$entityId/trades"
    },
    "/_auth/$entityId/transactions/": {
      "filePath": "_auth/$entityId/transactions/index.lazy.tsx",
      "parent": "/_auth/$entityId"
    },
    "/_auth/$entityId/user-admin/": {
      "filePath": "_auth/$entityId/user-admin/index.lazy.tsx",
      "parent": "/_auth/$entityId/user-admin"
    },
    "/_auth/$entityId/payments/bulk-payments/$bulkPaymentId": {
      "filePath": "_auth/$entityId/payments/bulk-payments.$bulkPaymentId.lazy.tsx",
      "parent": "/_auth/$entityId/payments"
    },
    "/_auth/$entityId/payments/bulk/$bulkPaymentId": {
      "filePath": "_auth/$entityId/payments/bulk.$bulkPaymentId.lazy.tsx",
      "parent": "/_auth/$entityId/payments"
    }
  }
}
ROUTE_MANIFEST_END */
