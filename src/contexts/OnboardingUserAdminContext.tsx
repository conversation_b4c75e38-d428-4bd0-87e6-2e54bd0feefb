import { createContext, useContext, ReactNode } from "react"

interface OnboardingUserAdminContextType {
  entityId: string
  entity: any
  entities: any[]
  user?: any
}

const OnboardingUserAdminContext =
  createContext<OnboardingUserAdminContextType | null>(null)

interface OnboardingUserAdminProviderProps {
  children: ReactNode
  loaderData: OnboardingUserAdminContextType
}

export function OnboardingUserAdminProvider({
  children,
  loaderData,
}: OnboardingUserAdminProviderProps) {
  return (
    <OnboardingUserAdminContext.Provider value={loaderData}>
      {children}
    </OnboardingUserAdminContext.Provider>
  )
}

export function useOnboardingUserAdminContext() {
  const context = useContext(OnboardingUserAdminContext)
  if (!context) {
    throw new Error(
      "useOnboardingUserAdminContext must be used within OnboardingUserAdminProvider",
    )
  }
  return context
}
