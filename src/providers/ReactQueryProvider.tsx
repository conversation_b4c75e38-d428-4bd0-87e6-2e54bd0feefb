import { PropsWithChildren } from "react"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

interface ReactQueryProviderProps extends PropsWithChildren {
  client: QueryClient
}

export default function ReactQueryProvider({
  children,
  client,
}: ReactQueryProviderProps) {
  return (
    <QueryClientProvider client={client}>
      {children}
      {import.meta.env.VITE_ENV === "development" && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  )
}
