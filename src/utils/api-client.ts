import {
  AxiosInstance,
  AxiosResponse,
  HttpStatusCode,
  InternalAxiosRequestConfig,
} from "axios"
import { Auth0ContextInterface } from "@auth0/auth0-react"

import { authSessionManager } from "./auth-session-manager"

type Auth0Methods = Pick<
  Auth0ContextInterface,
  "getAccessTokenSilently" | "logout" | "loginWithRedirect"
>

interface ApiClientConfig {
  auth0: Auth0Methods
  client: AxiosInstance
}

export function createApiClient({
  auth0,
  client,
}: ApiClientConfig): AxiosInstance {
  // Request interceptor
  client.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      // Check session validity before making request
      if (!authSessionManager.isSessionValid()) {
        console.log(
          "[API Client]: Session expired due to inactivity, rejecting request",
        )

        await auth0.logout({
          logoutParams: { returnTo: window.location.origin },
        })

        throw new Error("Session expired due to inactivity")
      }

      // Add entity ID if available
      const entityId = localStorage.getItem("x-entity-id")

      if (entityId) {
        config.headers["x-entity-id"] = entityId
      }

      try {
        // Get access token - this will also check session validity
        const accessToken = await auth0
          .getAccessTokenSilently({
            cacheMode: "cache-only",
          })
          .catch(async (cacheError) => {
            console.log("[API Client]: Cache miss, fetching fresh token")

            if (cacheError.error === "missing_refresh_token") {
              auth0.loginWithRedirect()
              return null
            }

            return await auth0.getAccessTokenSilently({
              cacheMode: "off",
            })
          })

        if (accessToken) {
          config.headers["Authorization"] = `Bearer ${accessToken}`
          // Record Auth0 server interaction on successful token acquisition
          authSessionManager.recordAuthInteraction()
        }
      } catch (error) {
        console.error("[API Client]: Token acquisition failed:", error)

        return Promise.reject(error)
      }

      return config
    },
    (error) => Promise.reject(error),
  )

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Don't record API response as Auth0 interaction
      // Only record when we actually interact with Auth0 server
      return response
    },
    async (error) => {
      if (error.response?.status === HttpStatusCode.Unauthorized) {
        localStorage.removeItem("x-entity-id")

        await auth0.logout({
          logoutParams: { returnTo: window.location.origin },
        })
      }

      return Promise.reject(error)
    },
  )

  return client
}
