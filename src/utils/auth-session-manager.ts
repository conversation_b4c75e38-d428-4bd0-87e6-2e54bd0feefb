import { SESSION_TIMEOUT } from "@/data/global/global"

// Track last auth server interaction
let lastAuthServerInteraction = Date.now()

export const authSessionManager = {
  // Record when we interact with Auth0 server
  recordAuthInteraction: () => {
    lastAuthServerInteraction = Date.now()
  },

  // Check if session is still valid based on last Auth0 interaction
  isSessionValid: () => {
    const now = Date.now()
    return now - lastAuthServerInteraction < SESSION_TIMEOUT
  },

  // Get remaining time
  getTimeRemaining: () => {
    const now = Date.now()
    const elapsed = now - lastAuthServerInteraction
    return Math.max(0, SESSION_TIMEOUT - elapsed)
  },

  // Initialize session tracking
  init: () => {
    lastAuthServerInteraction = Date.now()
  },

  // Get last interaction time (for debugging)
  getLastInteractionTime: () => lastAuthServerInteraction,

  // Reset session (alias for recordAuthInteraction for backward compatibility)
  resetSession: () => {
    authSessionManager.recordAuthInteraction()
  },
}
