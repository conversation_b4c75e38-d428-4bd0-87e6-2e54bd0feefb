import { SESSION_TIMEOUT } from "@/data/global/global"

let lastActivityTime = Date.now()
let sessionCheckInterval: NodeJS.Timeout | null = null

export const updateLastActivity = () => {
  lastActivityTime = Date.now()
  console.log("[Auth Activity]: Session activity updated")
}

export const isSessionExpired = () => {
  const now = Date.now()
  const timeSinceLastActivity = now - lastActivityTime

  return timeSinceLastActivity > SESSION_TIMEOUT
}

export const getTimeUntilExpiry = () => {
  const now = Date.now()
  const timeSinceLastActivity = now - lastActivityTime

  return Math.max(0, SESSION_TIMEOUT - timeSinceLastActivity)
}

export const startSessionCheck = (onExpired: () => void) => {
  // Clear any existing interval
  if (sessionCheckInterval) {
    clearInterval(sessionCheckInterval)
  }

  // Check every 30 seconds
  sessionCheckInterval = setInterval(() => {
    if (isSessionExpired()) {
      console.log("[Auth Activity]: Session expired due to inactivity")
      onExpired()
    }
  }, 30000)

  // Return cleanup function
  return () => {
    if (sessionCheckInterval) {
      clearInterval(sessionCheckInterval)
      sessionCheckInterval = null
    }
  }
}
