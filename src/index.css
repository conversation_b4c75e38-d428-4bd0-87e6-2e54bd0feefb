@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --background: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    --input: 214.3 31.8% 91.4%;
    --muted: 228 45% 96%;
    --muted-foreground: 215.4 16.3% 45%;
    --radius: 0.5rem;
    --ring: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --primary: 177 82% 29%;
    --primary-darker: 177 82% 10%;
    --primary-foreground: 210 40% 98%;
    --primary-light: 174.38, 62.75%, 40%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --sidebar-accent: 240 4.8% 93%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-background: 0 0% 96%;
    --sidebar-border: 220 13% 91%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --info: 195 65% 51%;

    --success: 142 76% 36%;
    --success-foreground: 138 76% 97%;

    --warning: 26 51% 51%;
    --warning-foreground: 48 96% 98%;
    --warning-light: 25 95% 96%;

    --weak: 210, 40%, 98%, 1;
    --weak-foreground: 215, 22%, 35%, 1;
    --weak-border: 214, 32%, 91%, 1;

    --sidebar-accent-strong: 226 29% 20%;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-light: 174.38, 62.75%, 40%;
    --primary-darker: 177 82% 10%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --warning: 48 96% 89%;
    --warning-foreground: 38 92% 50%;
    --warning-light: 48 96% 98%;
    --alert-success: 120 25% 65%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .content-container {
    @apply mx-auto w-full max-w-7xl px-4 md:px-6 lg:px-8;
  }

  .inset-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

@layer utilities {
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }
  .scrollbar-hidden {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}
