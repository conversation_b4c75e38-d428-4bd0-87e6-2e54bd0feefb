import * as Sentry from "@sentry/react"

Sentry.init({
  // debug: true <= useful if you run into any issues needing more info
  dsn: "https://<EMAIL>/4508205287997520",
  integrations: [
    Sentry.browserTracingIntegration(),
    Sentry.browserProfilingIntegration(),
    Sentry.browserApiErrorsIntegration(),
    Sentry.extraErrorDataIntegration(),
    Sentry.replayIntegration(),
  ],
  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for tracing.
  tracesSampleRate: 1.0,
  // Make sure to edit to any endpoints including localhost if you test locally
  tracePropagationTargets: [
    /^\//,
    /^https:\/\/app-onboarding-agp-dev\.azurewebsites\.net\/api/,
    /^https:\/\/ca-portal-bff-agp-dev\.yellowbush-31265d50\.uksouth\.azurecontainerapps\.io\/api/,
    /^https:\/\/portal\.dev\.argentexgp\.net\/api/,
    /^https:\/\/portal\.dev\.argentexgp\.net/,
    /^http:\/\/localhost:4200/,
  ],
  // Capture Replay for 10% of all sessions,
  // plus for 100% of sessions with an error
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
})
