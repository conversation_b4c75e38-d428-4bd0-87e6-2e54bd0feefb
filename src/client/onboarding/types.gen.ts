// This file is auto-generated by @hey-api/openapi-ts

export type GetAddressListDto = {
    id?: string;
    addressType?: AddressType;
    line1?: string;
    line2?: string | null;
    line3?: string | null;
    cityOrTown?: string;
    postalCode?: string | null;
    stateOrProvince?: string | null;
    countryCode?: string;
};

export type AddressType = 'RegisteredAddress' | 'TradingAddress' | 'ResidentialAddress' | 'Other';

export type GetAddressDto = {
    id?: string;
    addressType?: AddressType;
    line1?: string;
    line2?: string | null;
    line3?: string | null;
    cityOrTown?: string;
    postalCode?: string | null;
    stateOrProvince?: string | null;
    countryCode?: string;
};

export type CreateAddressResponseDto = {
    id?: string;
    addressType?: AddressType;
    line1?: string;
    line2?: string | null;
    line3?: string | null;
    cityOrTown?: string;
    postalCode?: string | null;
    stateOrProvince?: string | null;
    countryCode?: string;
};

export type ProblemDetails = {
    type?: string | null;
    title?: string | null;
    status?: number | null;
    detail?: string | null;
    instance?: string | null;
    [key: string]: unknown | (string | null) | (string | null) | (number | null) | (string | null) | (string | null) | undefined;
};

export type CreateAddressRequestDto = {
    addressType?: AddressType;
    line1?: string;
    line2?: string | null;
    line3?: string | null;
    cityOrTown?: string;
    postalCode?: string | null;
    stateOrProvince?: string | null;
    countryCode?: string;
};

export type UpdateAddressDto = {
    addressType?: AddressType;
    line1?: string;
    line2?: string | null;
    line3?: string | null;
    cityOrTown?: string;
    postalCode?: string | null;
    stateOrProvince?: string | null;
    countryCode?: string;
};

export type UpdateDocumentResponseDto = {
    id?: string;
    name?: string;
    category?: DocumentCategory;
    expiryDate?: string | null;
};

export type DocumentCategory = 'SourceOfFunds' | 'ReasonForTrade' | 'ProofOfExistence' | 'StructureChart' | 'SanctionsPolicy' | 'AmlPolicy' | 'ExportLicence' | 'IdentityAndVerification' | 'ProofOfAddress' | 'LP5_LP7' | 'FundProspectus' | 'FinancialStatements' | 'AMLRiskAssessment' | 'ApplicationForm' | 'LimitedPartnershipAgreement' | 'RegisterOfDirectors' | 'ProofOfIdentity' | 'Invoice' | 'TermsAndConditions' | 'Other';

export type UpdateDocumentRequestDto = {
    name?: string;
    category?: DocumentCategory;
    expiryDate?: string | null;
};

export type CreateDocumentResponseDto = {
    id?: string;
    name?: string;
    category?: DocumentCategory;
    expiryDate?: string | null;
    links?: CreateDocumentResponseLinksDto;
};

export type CreateDocumentResponseLinksDto = {
    file?: string | null;
};

export type CreateDocumentRequestDto = {
    name?: string;
    category?: DocumentCategory;
    expiryDate?: string | null;
};

export type GetDocumentDto = {
    id?: string;
    name?: string;
    category?: DocumentCategory;
    originalFileName?: string | null;
    mimeType?: string | null;
    uploadDateTime?: string | null;
    expiryDate?: string | null;
    lastModifiedByUserId?: string;
    lastModifiedByApplication?: ModifiedByApplication;
};

export type ModifiedByApplication = 'Api' | 'InternalArgentex' | 'Seeded';

export type GetEntityDocumentsDto = {
    id?: string;
    name?: string;
    category?: DocumentCategory;
    uploadDateTime?: string | null;
    expiryDate?: string | null;
    originalFileName?: string;
};

export type CreateEntityResponseDto = {
    id?: string;
    entityType?: EntityType;
    licencingArgentexLegalEntityId?: ArgentexLegalEntityId | null;
    categorisation?: Categorisation | null;
    categorisationReason?: CategorisationReason | null;
    emirClassification?: EmirClassification | null;
    emirCorporateSectorCodes?: Array<string> | null;
    website?: string | null;
    phoneNumber?: string | null;
    legalEntity?: CreateEntityLegalEntityResponseDto | null;
    naturalEntity?: CreateEntityNaturalEntityResponseDto | null;
    lastModifiedByUserId?: string;
    accessToEntity?: Array<CreateEntityAccessToEntityResposneDto>;
};

export type EntityType = 'LimitedCompany' | 'PublicLimitedCompany' | 'SoleTrader' | 'Charity' | 'LimitedPartnership' | 'LimitedLiabilityPartnership' | 'Trust' | 'Partnership' | 'UnknownLegalEntity' | 'OtherLegalEntity' | 'Individual';

export type ArgentexLegalEntityId = 'GB' | 'NL' | 'AU' | 'UAE';

export type Categorisation = 'EMIR_START' | 'PerSeProfessional' | 'ElectiveProfessional' | 'EMoney' | 'EMIR_END' | 'AU_START' | 'Wholesale' | 'AU_END' | 'UAE_START' | 'Retail' | 'DeemedProfessional' | 'AssessedProfessional' | 'UAE_END';

export type CategorisationReason = 'INTRINSIC_PROFESSIONAL_REASONS_START' | 'FinancialThresolds' | 'Regulation' | 'INTRINSIC_PROFESSIONAL_REASONS_END' | 'NON_INTRINSIC_PROFESSIONAL_REASONS_START' | 'OptUp' | 'NON_INTRINSIC_PROFESSIONAL_REASONS_END' | 'AUSTRALIA_REASONS_START' | 'NetWealthIncomeTest' | 'ProfessionalInvestorTest' | 'EmployeeTest' | 'ProuductValueTest' | 'AUSTRALIA_REASONS_END';

export type EmirClassification = 'FinancialCounterparty' | 'NonFinancialCounterparty' | 'Other';

export type CreateEntityLegalEntityResponseDto = {
    name?: string | null;
    tradingNames?: Array<string>;
    jurisdictionOfIncorporationCountryCode?: string | null;
    purposeOfEntity?: PurposeOfEntity | null;
    registrationNumber?: string | null;
    regulation?: EntityRegulation | null;
    isMicroEnterprise?: boolean | null;
    dateOfIncorporation?: string | null;
    sectorCode?: number | null;
    industryGroupCode?: number | null;
    industryCode?: number | null;
    subIndustryCode?: number | null;
    sectorDescription?: string | null;
    industryGroupDescription?: string | null;
    industryDescription?: string | null;
    subIndustryDescription?: string | null;
};

export type PurposeOfEntity = 'Spv' | 'HoldingCompany' | 'CoInvest' | 'TradingCompany' | 'Charity' | 'Fund' | 'OperatingCompany' | 'PortfolioCompany' | 'Other';

export type EntityRegulation = 'FCA' | 'PRA' | 'JFSC' | 'GFSC' | 'IOMFSA' | 'ESMA' | 'ASIC' | 'CCSF' | 'AMF' | 'Bafin' | 'FINMA' | 'BVIFSC' | 'CIMA' | 'Other' | 'NotRegulated';

export type CreateEntityNaturalEntityResponseDto = {
    givenName?: string | null;
    familyName?: string | null;
    familyNameFirst?: boolean | null;
    dateOfBirth?: string | null;
    nationalityCountryCode?: string | null;
};

export type CreateEntityAccessToEntityResposneDto = {
    userId?: string;
    approverLevel?: LoginEntityApproverLevel | null;
    roles?: Array<string>;
};

export type LoginEntityApproverLevel = 'LevelA' | 'LevelB' | 'LevelC';

export type CreateEntityRequestDto = {
    entityType?: EntityType;
    website?: string | null;
    phoneNumber?: string | null;
    legalEntity?: CreateEntityLegalEntityDto | null;
    naturalEntity?: CreateEntityNaturalEntityDto | null;
};

export type CreateEntityLegalEntityDto = {
    name?: string;
    tradingNames?: Array<string> | null;
    jurisdictionOfIncorporationCountryCode?: string | null;
    purposeOfEntity?: PurposeOfEntity | null;
    registrationNumber?: string | null;
    regulation?: EntityRegulation | null;
    isMicroEnterprise?: boolean | null;
    gicsCode?: number | null;
    dateOfIncorporation?: string | null;
};

export type CreateEntityNaturalEntityDto = {
    givenName?: string;
    familyName?: string;
    familyNameFirst?: boolean;
    dateOfBirth?: string | null;
    nationalityCountryCode?: string | null;
};

export type UpdateEntityRequestDto = {
    entityType?: EntityType;
    website?: string | null;
    phoneNumber?: string | null;
    legalEntity?: UpdateEntityLegalEntityDto | null;
    naturalEntity?: UpdateEntityNaturalEntityDto | null;
};

export type UpdateEntityLegalEntityDto = {
    name?: string;
    tradingNames?: Array<string> | null;
    jurisdictionOfIncorporationCountryCode?: string | null;
    purposeOfEntity?: PurposeOfEntity | null;
    registrationNumber?: string | null;
    regulation?: EntityRegulation | null;
    isMicroEnterprise?: boolean | null;
    gicsCode?: number | null;
    dateOfIncorporation?: string | null;
    stockExchange?: StockExchange | null;
};

export type StockExchange = 'LondonStockExchange' | 'Euronext' | 'FrankfurtStockExchange' | 'NasdaqNordicAndBalticExchanges' | 'AustralianSecuritiesExchange' | 'NotPubliclyListed' | 'Other';

export type UpdateEntityNaturalEntityDto = {
    givenName?: string;
    familyName?: string;
    familyNameFirst?: boolean;
    dateOfBirth?: string | null;
    nationalityCountryCode?: string | null;
};

export type GetEntityResponseDto = {
    id?: string;
    displayId?: number | null;
    entityType?: EntityType;
    licencingArgentexLegalEntityId?: ArgentexLegalEntityId | null;
    categorisation?: Categorisation | null;
    categorisationReason?: CategorisationReason | null;
    emirClassification?: EmirClassification | null;
    emirCorporateSectorCodes?: Array<string> | null;
    website?: string | null;
    phoneNumber?: string | null;
    legalEntity?: CreateEntityLegalEntityResponseDto2 | null;
    naturalEntity?: CreateEntityNaturalEntityResponseDto2 | null;
    lastModifiedByUserId?: string;
    lastModifiedDateTime?: string | null;
    lastModifiedByEmail?: string | null;
};

export type CreateEntityLegalEntityResponseDto2 = {
    name?: string | null;
    tradingNames?: Array<string>;
    jurisdictionOfIncorporationCountryCode?: string | null;
    purposeOfEntity?: PurposeOfEntity | null;
    registrationNumber?: string | null;
    regulation?: EntityRegulation | null;
    isMicroEnterprise?: boolean | null;
    dateOfIncorporation?: string | null;
    stockExchange?: StockExchange | null;
    sectorCode?: number | null;
    industryGroupCode?: number | null;
    industryCode?: number | null;
    subIndustryCode?: number | null;
    sectorDescription?: string | null;
    industryGroupDescription?: string | null;
    industryDescription?: string | null;
    subIndustryDescription?: string | null;
};

export type CreateEntityNaturalEntityResponseDto2 = {
    givenName?: string | null;
    familyName?: string | null;
    familyNameFirst?: boolean | null;
    dateOfBirth?: string | null;
    nationalityCountryCode?: string | null;
};

export type SubmitEntityRequestDto = {
    signedTermsAndConditionsIds?: Array<string>;
};

export type GetEntityAccessDto = {
    entityId?: string;
    entityName?: string;
    entityStatus?: EntityStatus;
    approverLevel?: LoginEntityApproverLevel | null;
    roles?: Array<string> | null;
};

export type EntityStatus = 'PreSubmission' | 'ReadyForOnboarding' | 'Onboarding' | 'Client';

export type UpdateEntityAccessRequestDto = {
    approverLevel?: LoginEntityApproverLevel | null;
    roles?: Array<string>;
};

export type CreateEntityAccessResponseDto = {
    entityAccessId?: string;
    userLoginId?: string;
    entityId?: string;
    approverLevel?: LoginEntityApproverLevel | null;
    roles?: Array<CreateEntityAccessResponseRoleDto>;
};

export type CreateEntityAccessResponseRoleDto = {
    id?: string;
    name?: string;
};

export type CreateEntityAccessRequestDto = {
    entityId?: string;
    userLoginId?: string;
    approverLevel?: LoginEntityApproverLevel | null;
    roles?: Array<string>;
};

export type GetExpectedTransactionActivityDto = {
    expectedTransactionCurrencies?: Array<string>;
    expectedTransactionJurisdictions?: Array<string>;
    fxPercentageOfInboundValue?: number | null;
    fxPercentageOfOutboundValue?: number | null;
    furtherDetails?: string | null;
    yearlyNumberOfPaymentsInbound?: number | null;
    yearlyNumberOfPaymentsOutbound?: number | null;
    yearlyValueOfPaymentsInbound?: number | null;
    yearlyValueOfPaymentsOutbound?: number | null;
    yearlyValueOfPaymentsCurrencyCode?: string;
    maximumTransactionSize?: number | null;
    activityTypes?: Array<GetExpectedTransactionActivityTypeDto>;
};

export type GetExpectedTransactionActivityTypeDto = {
    transactionActivityTypeCode?: number;
    transactionActivityTypeName?: string;
    transactionActivitySubTypeCode?: number | null;
    transactionActivitySubTypeName?: string | null;
};

export type SetExpectedTransactionActivityDto = {
    expectedTransactionCurrencies?: Array<string>;
    expectedTransactionJurisdictions?: Array<string>;
    fxPercentageOfInboundValue?: number | null;
    fxPercentageOfOutboundValue?: number | null;
    furtherDetails?: string | null;
    yearlyNumberOfPaymentsInbound?: number | null;
    yearlyNumberOfPaymentsOutbound?: number | null;
    yearlyValueOfPaymentsInbound?: number | null;
    yearlyValueOfPaymentsOutbound?: number | null;
    maximumTransactionSize?: number | null;
    activityTypes?: Array<SetExpectedTransactionActivityTypeDto>;
};

export type SetExpectedTransactionActivityTypeDto = {
    code?: number;
};

export type CreateAnnualFinancialInformationResponseDto = {
    id?: string;
    entityId?: string;
    financialYearEnding?: number;
    currencyCode?: string;
    assetsUnderManagement?: number | null;
    balanceSheet?: number | null;
    annualNetTurnover?: number | null;
    ownFunds?: number | null;
    assetsUnderManagementDisplay?: string | null;
    balanceSheetDisplay?: string | null;
    annualNetTurnoverDisplay?: string | null;
    ownFundsDisplay?: string | null;
    dateProvided?: string;
};

export type CreateAnnualFinancialInformationRequestDto = {
    financialYearEnding?: number;
    currencyCode?: string;
    assetsUnderManagement?: number | null;
    balanceSheet?: number | null;
    annualNetTurnover?: number | null;
    ownFunds?: number | null;
};

export type GetAnnualFinancialInformationDto = {
    id?: string;
    entityId?: string;
    financialYearEnding?: number;
    currencyCode?: string;
    assetsUnderManagement?: number | null;
    balanceSheet?: number | null;
    annualNetTurnover?: number | null;
    ownFunds?: number | null;
    assetsUnderManagementDisplay?: string | null;
    balanceSheetDisplay?: string | null;
    annualNetTurnoverDisplay?: string | null;
    ownFundsDisplay?: string | null;
    dateProvided?: string;
};

export type UpdateAnnualFinancialInformationRequestDto = {
    assetsUnderManagement?: number | null;
    balanceSheet?: number | null;
    annualNetTurnover?: number | null;
    ownFunds?: number | null;
};

export type UpdateFinancialSourcesRequest = {
    sourceOfFunds?: SourceOfFunds | null;
    sourceOfWealth?: string | null;
};

export type SourceOfFunds = 'Revenue' | 'CapitalInjections' | 'LoanProceeds' | 'Dividends' | 'AssetOrInvestmentSaleProceeds' | 'GovernmentOrInstitutionalGrants' | 'IntercompanyTransfers' | 'Other';

export type GetFinancialSourcesDto = {
    sourceOfFunds?: SourceOfFunds | null;
    sourceOfWealth?: string | null;
};

export type GetInvestmentDto = {
    assetClass?: AssetClass | null;
    investmentStrategy?: string | null;
    investmentSectors?: InvestmentSector | null;
    natureOfBusiness?: string | null;
    investmentGeographies?: Array<InvestmentGeographyDto>;
    jurisdictionalExposures?: Array<JurisdictionalExposureDto>;
};

export type AssetClass = 'PrivateEquity' | 'PrivateCredit' | 'VentureCapital' | 'RealEstate' | 'Infrastructure' | 'Debt' | 'None' | 'Other';

export type InvestmentSector = 'Energy' | 'Materials' | 'Industrials' | 'ConsumerDiscretionary' | 'ConsumerStaples' | 'HealthCare' | 'Financials' | 'InformationTechnology' | 'CommunicationServices' | 'Utilities' | 'RealEstate' | 'Other' | 'None';

export type InvestmentGeographyDto = {
    countryCode?: string;
    countryName?: string;
};

export type JurisdictionalExposureDto = {
    countryCode?: string;
    countryName?: string;
};

export type UpsertInvestmentRequestDto = {
    assetClass?: AssetClass | null;
    investmentStrategy?: string | null;
    investmentSectors?: InvestmentSector | null;
    natureOfBusiness?: string | null;
    investmentGeographyCountryCodes?: Array<string> | null;
    jurisdictionalExposureCountryCodes?: Array<string> | null;
};

export type EnumValueDto = {
    key?: string;
    display?: string;
};

export type GetEntityTypesDto = {
    key?: string;
    display?: string;
    category?: string;
};

export type GetCountryListDto = {
    code?: string;
    name?: string;
    codeIso3?: string;
    codeIsoNumeric?: number;
    dialCode?: string;
    active?: boolean;
    displayGroup?: number | null;
    order?: number;
};

export type GetCurrencyListDto = {
    code?: string;
    iso4217NumericCode?: number;
    description?: string;
    symbol?: string;
    symbolPosition?: CurrencySymbolPosition;
    numberOfDecimalPlaces?: number;
    active?: boolean;
};

export type CurrencySymbolPosition = 'BeforeAmount' | 'AfterAmount' | 'InPlaceOfDecimalSeparator';

export type GetGicsClassificationsSectorDto = {
    sectorCode?: number;
    sectorName?: string;
    active?: boolean;
    industryGroups?: Array<GetGicsClassificationsIndustryGroupDto>;
};

export type GetGicsClassificationsIndustryGroupDto = {
    industryGroupCode?: number;
    industryGroupName?: string;
    active?: boolean;
    industries?: Array<GetGicsClassificationsIndustryDto>;
};

export type GetGicsClassificationsIndustryDto = {
    industryCode?: number;
    industryName?: string;
    active?: boolean;
    subIndustries?: Array<GetGicsClassificationsSubIndustryDto>;
};

export type GetGicsClassificationsSubIndustryDto = {
    subIndustryCode?: number;
    subIndustryName?: string;
    active?: boolean;
};

export type GetGicsIndustriesFlatOrderedDto = {
    industryCode?: number;
    industryName?: string;
    active?: boolean;
};

export type GetTransactionActivityTypesDto = {
    code?: number;
    name?: string;
    active?: boolean;
    subTypes?: Array<GetTransactionActivitySubTypesDto>;
};

export type GetTransactionActivitySubTypesDto = {
    code?: number;
    name?: string;
    active?: boolean;
};

export type GetLatestTermsAndConditionsResponseDto = {
    latestTermsAndConditions?: Array<LatestTermsAndConditions>;
};

export type LatestTermsAndConditions = {
    termsAndConditionsId?: string;
    documentId?: string;
    documentName?: string;
    versionNumber?: number;
    type?: DocumentType;
};

export type DocumentType = 'TermsAndConditions' | 'CountryAddendum' | 'Supplementary';

export type GetAllTermsAndConditionsResponseDto = {
    allTermsAndConditions?: Array<AllTermsAndConditions>;
};

export type AllTermsAndConditions = {
    termsAndConditionsId?: string;
    documentId?: string;
    documentName?: string;
    versionNumber?: number;
    type?: DocumentType;
    requiresAgreement?: boolean;
};

export type GetAllUsersWithAccessToEntityResponseDto = {
    id?: string;
    email?: string;
    entityAccess?: GetAllUsersWithAccessToEntityDto;
    status?: UserLoginStatus;
    displayName?: string;
};

export type GetAllUsersWithAccessToEntityDto = {
    entityAccessId?: string;
    roles?: Array<GetAllUsersWithAccessToEntityRoleDto>;
};

export type GetAllUsersWithAccessToEntityRoleDto = {
    name?: string;
};

export type UserLoginStatus = 'Pending' | 'Active';

export type GetAllUsersWithAccessToEntitiesForAdminUserDto = {
    id?: string;
    email?: string;
    phoneNumber?: string;
    status?: UserLoginStatus;
    entities?: Array<GetAllUsersWithAccessToEntitiesForAdminUserEntityDto>;
    displayName?: string;
};

export type GetAllUsersWithAccessToEntitiesForAdminUserEntityDto = {
    id?: string;
    displayName?: string;
};

export type GetSpecificUserDto = {
    id?: string;
    email?: string;
    phoneNumber?: string;
    status?: UserLoginStatus;
    entities?: Array<GetSpecificUserEntityDto>;
    displayName?: string;
};

export type GetSpecificUserEntityDto = {
    id?: string;
    roles?: Array<GetSpecificUserEntityRoleDto>;
    approverLevel?: LoginEntityApproverLevel | null;
    displayName?: string;
};

export type GetSpecificUserEntityRoleDto = {
    role?: string;
};

export type GetUserDto = {
    id?: string;
    ownerEntityId?: string;
    email?: string;
    phoneNumber?: string;
    status?: UserLoginStatus;
    displayName?: string;
};

export type UpdateUserResponseDto = {
    id?: string;
    email?: string;
    phoneNumber?: string;
    status?: UserLoginStatus;
    displayName?: string;
};

export type AddNewLoginWithRolesToExistingEntityResponseDto = {
    userLoginId?: string;
    ownerEntityId?: string;
    ownerEntityDisplayName?: string;
    email?: string;
    userStatus?: UserLoginStatus | null;
    accessToEntities?: Array<AddNewLoginWithRolesToExistingEntityAccessResponseDto>;
};

export type AddNewLoginWithRolesToExistingEntityAccessResponseDto = {
    entityAccessId?: string;
    entityId?: string;
    entityDisplayName?: string;
    approverLevel?: LoginEntityApproverLevel | null;
    roles?: Array<string>;
};

export type AddNewLoginWithRolesToExistingEntityRequestDto = {
    email?: string;
    phoneNumber?: string;
    givenName?: string;
    familyName?: string;
    familyNameFirst?: boolean;
    approverLevel?: LoginEntityApproverLevel | null;
    roles?: Array<string>;
};

export type AddressesGetAllData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Addresses';
};

export type AddressesGetAllErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type AddressesGetAllError = AddressesGetAllErrors[keyof AddressesGetAllErrors];

export type AddressesGetAllResponses = {
    /**
     * Succeed.
     */
    200: Array<GetAddressListDto>;
};

export type AddressesGetAllResponse = AddressesGetAllResponses[keyof AddressesGetAllResponses];

export type AddressesPostData = {
    body: CreateAddressRequestDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Addresses';
};

export type AddressesPostErrors = {
    /**
     * Validation errors.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type AddressesPostError = AddressesPostErrors[keyof AddressesPostErrors];

export type AddressesPostResponses = {
    /**
     * Succeed.
     */
    201: CreateAddressResponseDto;
};

export type AddressesPostResponse = AddressesPostResponses[keyof AddressesPostResponses];

export type AddressesDeleteData = {
    body?: never;
    path: {
        entityId: string;
        addressId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Addresses/{addressId}';
};

export type AddressesDeleteErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Address of the entity could not be found.
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type AddressesDeleteError = AddressesDeleteErrors[keyof AddressesDeleteErrors];

export type AddressesDeleteResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type AddressesDeleteResponse = AddressesDeleteResponses[keyof AddressesDeleteResponses];

export type AddressesGetData = {
    body?: never;
    path: {
        entityId: string;
        addressId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Addresses/{addressId}';
};

export type AddressesGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type AddressesGetError = AddressesGetErrors[keyof AddressesGetErrors];

export type AddressesGetResponses = {
    /**
     * Succeed.
     */
    200: GetAddressDto;
};

export type AddressesGetResponse = AddressesGetResponses[keyof AddressesGetResponses];

export type AddressesPutData = {
    body: UpdateAddressDto;
    path: {
        entityId: string;
        addressId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Addresses/{addressId}';
};

export type AddressesPutErrors = {
    /**
     * Validation errors.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Address of the entity could not be found.
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type AddressesPutError = AddressesPutErrors[keyof AddressesPutErrors];

export type AddressesPutResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type AddressesPutResponse = AddressesPutResponses[keyof AddressesPutResponses];

export type DocumentsUnlinkDocumentEntityData = {
    body?: never;
    path: {
        documentId: string;
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Documents/{documentId}';
};

export type DocumentsUnlinkDocumentEntityErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * The resource is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsUnlinkDocumentEntityError = DocumentsUnlinkDocumentEntityErrors[keyof DocumentsUnlinkDocumentEntityErrors];

export type DocumentsUnlinkDocumentEntityResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type DocumentsUnlinkDocumentEntityResponse = DocumentsUnlinkDocumentEntityResponses[keyof DocumentsUnlinkDocumentEntityResponses];

export type DocumentsLinkDocumentEntityData = {
    body?: never;
    path: {
        documentId: string;
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Documents/{documentId}';
};

export type DocumentsLinkDocumentEntityErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * The resource is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsLinkDocumentEntityError = DocumentsLinkDocumentEntityErrors[keyof DocumentsLinkDocumentEntityErrors];

export type DocumentsLinkDocumentEntityResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type DocumentsLinkDocumentEntityResponse = DocumentsLinkDocumentEntityResponses[keyof DocumentsLinkDocumentEntityResponses];

export type DocumentsGetDocumentMetadataData = {
    body?: never;
    path: {
        documentId: string;
    };
    query?: never;
    url: '/api/v1/Documents/{documentId}/Metadata';
};

export type DocumentsGetDocumentMetadataErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsGetDocumentMetadataError = DocumentsGetDocumentMetadataErrors[keyof DocumentsGetDocumentMetadataErrors];

export type DocumentsGetDocumentMetadataResponses = {
    /**
     * Succeed.
     */
    200: GetDocumentDto;
};

export type DocumentsGetDocumentMetadataResponse = DocumentsGetDocumentMetadataResponses[keyof DocumentsGetDocumentMetadataResponses];

export type DocumentsPutDocumentMetadataData = {
    body: UpdateDocumentRequestDto;
    path: {
        documentId: string;
    };
    query?: never;
    url: '/api/v1/Documents/{documentId}/Metadata';
};

export type DocumentsPutDocumentMetadataErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsPutDocumentMetadataError = DocumentsPutDocumentMetadataErrors[keyof DocumentsPutDocumentMetadataErrors];

export type DocumentsPutDocumentMetadataResponses = {
    /**
     * Succeed.
     */
    200: UpdateDocumentResponseDto;
};

export type DocumentsPutDocumentMetadataResponse = DocumentsPutDocumentMetadataResponses[keyof DocumentsPutDocumentMetadataResponses];

export type DocumentsGetEntityDocumentsData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: {
        documentCategory?: Array<DocumentCategory> | null;
    };
    url: '/api/v1/Entities/{entityId}/Documents/Metadata';
};

export type DocumentsGetEntityDocumentsErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsGetEntityDocumentsError = DocumentsGetEntityDocumentsErrors[keyof DocumentsGetEntityDocumentsErrors];

export type DocumentsGetEntityDocumentsResponses = {
    /**
     * Succeed.
     */
    200: Array<GetEntityDocumentsDto>;
};

export type DocumentsGetEntityDocumentsResponse = DocumentsGetEntityDocumentsResponses[keyof DocumentsGetEntityDocumentsResponses];

export type DocumentsPostDocumentMetadataData = {
    body: CreateDocumentRequestDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Documents/Metadata';
};

export type DocumentsPostDocumentMetadataErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsPostDocumentMetadataError = DocumentsPostDocumentMetadataErrors[keyof DocumentsPostDocumentMetadataErrors];

export type DocumentsPostDocumentMetadataResponses = {
    /**
     * Succeed.
     */
    201: CreateDocumentResponseDto;
};

export type DocumentsPostDocumentMetadataResponse = DocumentsPostDocumentMetadataResponses[keyof DocumentsPostDocumentMetadataResponses];

export type DocumentsDownloadDocumentFileData = {
    body?: never;
    path: {
        documentId: string;
    };
    query?: never;
    url: '/api/v1/Documents/{documentId}/File';
};

export type DocumentsDownloadDocumentFileErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsDownloadDocumentFileError = DocumentsDownloadDocumentFileErrors[keyof DocumentsDownloadDocumentFileErrors];

export type DocumentsDownloadDocumentFileResponses = {
    /**
     * Succeed.
     */
    200: Blob | File;
};

export type DocumentsDownloadDocumentFileResponse = DocumentsDownloadDocumentFileResponses[keyof DocumentsDownloadDocumentFileResponses];

export type DocumentsPostDocumentFileData = {
    body?: {
        file?: (Blob | File) | null;
    };
    path: {
        documentId: string;
    };
    query?: never;
    url: '/api/v1/Documents/{documentId}/File';
};

export type DocumentsPostDocumentFileErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type DocumentsPostDocumentFileError = DocumentsPostDocumentFileErrors[keyof DocumentsPostDocumentFileErrors];

export type DocumentsPostDocumentFileResponses = {
    /**
     * Succeed.
     */
    201: unknown;
};

export type EntitiesCreateEntityData = {
    body: CreateEntityRequestDto;
    path?: never;
    query?: never;
    url: '/api/v1/Entities';
};

export type EntitiesCreateEntityErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntitiesCreateEntityError = EntitiesCreateEntityErrors[keyof EntitiesCreateEntityErrors];

export type EntitiesCreateEntityResponses = {
    /**
     * Created.
     */
    201: CreateEntityResponseDto;
};

export type EntitiesCreateEntityResponse = EntitiesCreateEntityResponses[keyof EntitiesCreateEntityResponses];

export type EntitiesGetEntityData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}';
};

export type EntitiesGetEntityErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntitiesGetEntityError = EntitiesGetEntityErrors[keyof EntitiesGetEntityErrors];

export type EntitiesGetEntityResponses = {
    /**
     * Succeed.
     */
    200: GetEntityResponseDto;
};

export type EntitiesGetEntityResponse = EntitiesGetEntityResponses[keyof EntitiesGetEntityResponses];

export type EntitiesUpdateEntityData = {
    body: UpdateEntityRequestDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}';
};

export type EntitiesUpdateEntityErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntitiesUpdateEntityError = EntitiesUpdateEntityErrors[keyof EntitiesUpdateEntityErrors];

export type EntitiesUpdateEntityResponses = {
    /**
     * Updated.
     */
    200: unknown;
};

export type EntitiesSubmitEntityV2Data = {
    body: SubmitEntityRequestDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Submissions';
};

export type EntitiesSubmitEntityV2Errors = {
    /**
     * Validation errors.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntitiesSubmitEntityV2Error = EntitiesSubmitEntityV2Errors[keyof EntitiesSubmitEntityV2Errors];

export type EntitiesSubmitEntityV2Responses = {
    /**
     * Succeed.
     */
    200: unknown;
};

export type EntityAccessGetEntityAccessData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/EntityAccess';
};

export type EntityAccessGetEntityAccessErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * The resource is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntityAccessGetEntityAccessError = EntityAccessGetEntityAccessErrors[keyof EntityAccessGetEntityAccessErrors];

export type EntityAccessGetEntityAccessResponses = {
    /**
     * Succeed.
     */
    200: Array<GetEntityAccessDto>;
};

export type EntityAccessGetEntityAccessResponse = EntityAccessGetEntityAccessResponses[keyof EntityAccessGetEntityAccessResponses];

export type EntityAccessCreateEntityAccessData = {
    body: CreateEntityAccessRequestDto;
    path?: never;
    query?: never;
    url: '/api/v1/EntityAccess';
};

export type EntityAccessCreateEntityAccessErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntityAccessCreateEntityAccessError = EntityAccessCreateEntityAccessErrors[keyof EntityAccessCreateEntityAccessErrors];

export type EntityAccessCreateEntityAccessResponses = {
    /**
     * Succeed.
     */
    200: CreateEntityAccessResponseDto;
};

export type EntityAccessCreateEntityAccessResponse = EntityAccessCreateEntityAccessResponses[keyof EntityAccessCreateEntityAccessResponses];

export type EntityAccessDeleteEntityAccessData = {
    body?: never;
    path: {
        entityAccessId: string;
    };
    query?: never;
    url: '/api/v1/EntityAccess/{entityAccessId}';
};

export type EntityAccessDeleteEntityAccessErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * The resource is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntityAccessDeleteEntityAccessError = EntityAccessDeleteEntityAccessErrors[keyof EntityAccessDeleteEntityAccessErrors];

export type EntityAccessDeleteEntityAccessResponses = {
    /**
     * Succeed.
     */
    200: unknown;
};

export type EntityAccessUpdateEntityAccessData = {
    body: UpdateEntityAccessRequestDto;
    path: {
        entityAccessId: string;
    };
    query?: never;
    url: '/api/v1/EntityAccess/{entityAccessId}';
};

export type EntityAccessUpdateEntityAccessErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type EntityAccessUpdateEntityAccessError = EntityAccessUpdateEntityAccessErrors[keyof EntityAccessUpdateEntityAccessErrors];

export type EntityAccessUpdateEntityAccessResponses = {
    /**
     * Succeed.
     */
    200: unknown;
};

export type ExpectedTransactionActivityGetData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/ExpectedTransactionActivity';
};

export type ExpectedTransactionActivityGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type ExpectedTransactionActivityGetError = ExpectedTransactionActivityGetErrors[keyof ExpectedTransactionActivityGetErrors];

export type ExpectedTransactionActivityGetResponses = {
    /**
     * Succeed.
     */
    200: GetExpectedTransactionActivityDto;
};

export type ExpectedTransactionActivityGetResponse = ExpectedTransactionActivityGetResponses[keyof ExpectedTransactionActivityGetResponses];

export type ExpectedTransactionActivityPutData = {
    body: SetExpectedTransactionActivityDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/ExpectedTransactionActivity';
};

export type ExpectedTransactionActivityPutErrors = {
    /**
     * Bad Request.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type ExpectedTransactionActivityPutError = ExpectedTransactionActivityPutErrors[keyof ExpectedTransactionActivityPutErrors];

export type ExpectedTransactionActivityPutResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type ExpectedTransactionActivityPutResponse = ExpectedTransactionActivityPutResponses[keyof ExpectedTransactionActivityPutResponses];

export type FinancialInformationGetData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation';
};

export type FinancialInformationGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type FinancialInformationGetError = FinancialInformationGetErrors[keyof FinancialInformationGetErrors];

export type FinancialInformationGetResponses = {
    /**
     * Succeed.
     */
    200: Array<GetAnnualFinancialInformationDto>;
};

export type FinancialInformationGetResponse = FinancialInformationGetResponses[keyof FinancialInformationGetResponses];

export type FinancialInformationPostData = {
    body: CreateAnnualFinancialInformationRequestDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation';
};

export type FinancialInformationPostErrors = {
    /**
     * Validation errors.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Duplicate.
     */
    409: ProblemDetails;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type FinancialInformationPostError = FinancialInformationPostErrors[keyof FinancialInformationPostErrors];

export type FinancialInformationPostResponses = {
    /**
     * Succeed.
     */
    201: CreateAnnualFinancialInformationResponseDto;
};

export type FinancialInformationPostResponse = FinancialInformationPostResponses[keyof FinancialInformationPostResponses];

export type FinancialInformationDeleteData = {
    body?: never;
    path: {
        entityId: string;
        yearEnding: number;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation/{yearEnding}';
};

export type FinancialInformationDeleteErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Financial information is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type FinancialInformationDeleteError = FinancialInformationDeleteErrors[keyof FinancialInformationDeleteErrors];

export type FinancialInformationDeleteResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type FinancialInformationDeleteResponse = FinancialInformationDeleteResponses[keyof FinancialInformationDeleteResponses];

export type FinancialInformationPutData = {
    body: UpdateAnnualFinancialInformationRequestDto;
    path: {
        entityId: string;
        yearEnding: number;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation/{yearEnding}';
};

export type FinancialInformationPutErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Financial information is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type FinancialInformationPutError = FinancialInformationPutErrors[keyof FinancialInformationPutErrors];

export type FinancialInformationPutResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type FinancialInformationPutResponse = FinancialInformationPutResponses[keyof FinancialInformationPutResponses];

export type FinancialInformationGetSourcesData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/FinancialInformation/Sources';
};

export type FinancialInformationGetSourcesErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Entity is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type FinancialInformationGetSourcesError = FinancialInformationGetSourcesErrors[keyof FinancialInformationGetSourcesErrors];

export type FinancialInformationGetSourcesResponses = {
    /**
     * Succeed.
     */
    200: GetFinancialSourcesDto;
};

export type FinancialInformationGetSourcesResponse = FinancialInformationGetSourcesResponses[keyof FinancialInformationGetSourcesResponses];

export type FinancialInformationPutSourcesData = {
    body: UpdateFinancialSourcesRequest;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/FinancialInformation/Sources';
};

export type FinancialInformationPutSourcesErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Entity is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type FinancialInformationPutSourcesError = FinancialInformationPutSourcesErrors[keyof FinancialInformationPutSourcesErrors];

export type FinancialInformationPutSourcesResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type FinancialInformationPutSourcesResponse = FinancialInformationPutSourcesResponses[keyof FinancialInformationPutSourcesResponses];

export type InvestmentsGetData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Investments/{entityId}';
};

export type InvestmentsGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type InvestmentsGetError = InvestmentsGetErrors[keyof InvestmentsGetErrors];

export type InvestmentsGetResponses = {
    /**
     * Succeed.
     */
    200: GetInvestmentDto;
};

export type InvestmentsGetResponse = InvestmentsGetResponses[keyof InvestmentsGetResponses];

export type InvestmentsPutData = {
    body: UpsertInvestmentRequestDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Investments/{entityId}';
};

export type InvestmentsPutErrors = {
    /**
     * Validation errors.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type InvestmentsPutError = InvestmentsPutErrors[keyof InvestmentsPutErrors];

export type InvestmentsPutResponses = {
    /**
     * Succeed.
     */
    204: void;
};

export type InvestmentsPutResponse = InvestmentsPutResponses[keyof InvestmentsPutResponses];

export type StaticDataGetEntityTypesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/EntityType';
};

export type StaticDataGetEntityTypesErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataGetEntityTypesError = StaticDataGetEntityTypesErrors[keyof StaticDataGetEntityTypesErrors];

export type StaticDataGetEntityTypesResponses = {
    /**
     * Succeed.
     */
    200: Array<GetEntityTypesDto>;
};

export type StaticDataGetEntityTypesResponse = StaticDataGetEntityTypesResponses[keyof StaticDataGetEntityTypesResponses];

export type StaticDataGetCountryListData = {
    body?: never;
    path?: never;
    query?: {
        includeInactive?: boolean;
    };
    url: '/api/v1/StaticData/Country';
};

export type StaticDataGetCountryListErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataGetCountryListError = StaticDataGetCountryListErrors[keyof StaticDataGetCountryListErrors];

export type StaticDataGetCountryListResponses = {
    /**
     * Succeed.
     */
    200: Array<GetCountryListDto>;
};

export type StaticDataGetCountryListResponse = StaticDataGetCountryListResponses[keyof StaticDataGetCountryListResponses];

export type StaticDataGetCurrencyListData = {
    body?: never;
    path?: never;
    query?: {
        includeInactive?: boolean;
    };
    url: '/api/v1/StaticData/Currency';
};

export type StaticDataGetCurrencyListErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataGetCurrencyListError = StaticDataGetCurrencyListErrors[keyof StaticDataGetCurrencyListErrors];

export type StaticDataGetCurrencyListResponses = {
    /**
     * Succeed.
     */
    200: Array<GetCurrencyListDto>;
};

export type StaticDataGetCurrencyListResponse = StaticDataGetCurrencyListResponses[keyof StaticDataGetCurrencyListResponses];

export type StaticDataGetGicsClassificationListData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/GicsClassification';
};

export type StaticDataGetGicsClassificationListErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataGetGicsClassificationListError = StaticDataGetGicsClassificationListErrors[keyof StaticDataGetGicsClassificationListErrors];

export type StaticDataGetGicsClassificationListResponses = {
    /**
     * Succeed.
     */
    200: Array<GetGicsClassificationsSectorDto>;
};

export type StaticDataGetGicsClassificationListResponse = StaticDataGetGicsClassificationListResponses[keyof StaticDataGetGicsClassificationListResponses];

export type StaticDataGetGicsIndustriesListData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/GicsClassification/Industries';
};

export type StaticDataGetGicsIndustriesListErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataGetGicsIndustriesListError = StaticDataGetGicsIndustriesListErrors[keyof StaticDataGetGicsIndustriesListErrors];

export type StaticDataGetGicsIndustriesListResponses = {
    /**
     * Succeed.
     */
    200: Array<GetGicsIndustriesFlatOrderedDto>;
};

export type StaticDataGetGicsIndustriesListResponse = StaticDataGetGicsIndustriesListResponses[keyof StaticDataGetGicsIndustriesListResponses];

export type StaticDataGetTransactionActivityTypesListData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/TransactionActivityTypes';
};

export type StaticDataGetTransactionActivityTypesListErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataGetTransactionActivityTypesListError = StaticDataGetTransactionActivityTypesListErrors[keyof StaticDataGetTransactionActivityTypesListErrors];

export type StaticDataGetTransactionActivityTypesListResponses = {
    /**
     * Succeed.
     */
    200: Array<GetTransactionActivityTypesDto>;
};

export type StaticDataGetTransactionActivityTypesListResponse = StaticDataGetTransactionActivityTypesListResponses[keyof StaticDataGetTransactionActivityTypesListResponses];

export type TermsAndConditionsGetData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/LatestTermsAndConditions';
};

export type TermsAndConditionsGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type TermsAndConditionsGetError = TermsAndConditionsGetErrors[keyof TermsAndConditionsGetErrors];

export type TermsAndConditionsGetResponses = {
    /**
     * Succeed.
     */
    200: GetLatestTermsAndConditionsResponseDto;
};

export type TermsAndConditionsGetResponse = TermsAndConditionsGetResponses[keyof TermsAndConditionsGetResponses];

export type TermsAndConditionsDownloadTermsAndConditionsData = {
    body?: never;
    path: {
        termsAndConditionsId: string;
    };
    query?: never;
    url: '/api/v1/TermsAndConditions/{termsAndConditionsId}/File';
};

export type TermsAndConditionsDownloadTermsAndConditionsErrors = {
    /**
     * Validation errors.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type TermsAndConditionsDownloadTermsAndConditionsError = TermsAndConditionsDownloadTermsAndConditionsErrors[keyof TermsAndConditionsDownloadTermsAndConditionsErrors];

export type TermsAndConditionsDownloadTermsAndConditionsResponses = {
    /**
     * Succeed.
     */
    200: Blob | File;
};

export type TermsAndConditionsDownloadTermsAndConditionsResponse = TermsAndConditionsDownloadTermsAndConditionsResponses[keyof TermsAndConditionsDownloadTermsAndConditionsResponses];

export type TermsAndConditionsTermsAndConditionsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/TermsAndConditions';
};

export type TermsAndConditionsTermsAndConditionsErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type TermsAndConditionsTermsAndConditionsError = TermsAndConditionsTermsAndConditionsErrors[keyof TermsAndConditionsTermsAndConditionsErrors];

export type TermsAndConditionsTermsAndConditionsResponses = {
    /**
     * Succeed.
     */
    200: GetAllTermsAndConditionsResponseDto;
};

export type TermsAndConditionsTermsAndConditionsResponse = TermsAndConditionsTermsAndConditionsResponses[keyof TermsAndConditionsTermsAndConditionsResponses];

export type UsersGetAllUsersWithAccessToEntityData = {
    body?: never;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Users';
};

export type UsersGetAllUsersWithAccessToEntityErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * The resource is not found!
     */
    404: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type UsersGetAllUsersWithAccessToEntityError = UsersGetAllUsersWithAccessToEntityErrors[keyof UsersGetAllUsersWithAccessToEntityErrors];

export type UsersGetAllUsersWithAccessToEntityResponses = {
    /**
     * Succeed.
     */
    200: Array<GetAllUsersWithAccessToEntityResponseDto>;
};

export type UsersGetAllUsersWithAccessToEntityResponse = UsersGetAllUsersWithAccessToEntityResponses[keyof UsersGetAllUsersWithAccessToEntityResponses];

export type UsersAddNewLoginWithRolesToExistingEntityData = {
    body: AddNewLoginWithRolesToExistingEntityRequestDto;
    path: {
        entityId: string;
    };
    query?: never;
    url: '/api/v1/Entities/{entityId}/Users';
};

export type UsersAddNewLoginWithRolesToExistingEntityErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type UsersAddNewLoginWithRolesToExistingEntityError = UsersAddNewLoginWithRolesToExistingEntityErrors[keyof UsersAddNewLoginWithRolesToExistingEntityErrors];

export type UsersAddNewLoginWithRolesToExistingEntityResponses = {
    /**
     * Created.
     */
    201: AddNewLoginWithRolesToExistingEntityResponseDto;
};

export type UsersAddNewLoginWithRolesToExistingEntityResponse = UsersAddNewLoginWithRolesToExistingEntityResponses[keyof UsersAddNewLoginWithRolesToExistingEntityResponses];

export type UsersGetAllUsersWithAccessToEntitiesForAdminUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/Users';
};

export type UsersGetAllUsersWithAccessToEntitiesForAdminUserErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type UsersGetAllUsersWithAccessToEntitiesForAdminUserError = UsersGetAllUsersWithAccessToEntitiesForAdminUserErrors[keyof UsersGetAllUsersWithAccessToEntitiesForAdminUserErrors];

export type UsersGetAllUsersWithAccessToEntitiesForAdminUserResponses = {
    /**
     * Succeed.
     */
    200: Array<GetAllUsersWithAccessToEntitiesForAdminUserDto>;
};

export type UsersGetAllUsersWithAccessToEntitiesForAdminUserResponse = UsersGetAllUsersWithAccessToEntitiesForAdminUserResponses[keyof UsersGetAllUsersWithAccessToEntitiesForAdminUserResponses];

export type UsersGetUserData = {
    body?: never;
    path: {
        userId: string;
    };
    query?: never;
    url: '/api/v1/Users/<USER>';
};

export type UsersGetUserErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type UsersGetUserError = UsersGetUserErrors[keyof UsersGetUserErrors];

export type UsersGetUserResponses = {
    /**
     * Succeed.
     */
    200: GetSpecificUserDto;
};

export type UsersGetUserResponse = UsersGetUserResponses[keyof UsersGetUserResponses];

export type UsersGetCurrentUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/Users/<USER>';
};

export type UsersGetCurrentUserErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type UsersGetCurrentUserError = UsersGetCurrentUserErrors[keyof UsersGetCurrentUserErrors];

export type UsersGetCurrentUserResponses = {
    /**
     * Succeed.
     */
    200: GetUserDto;
};

export type UsersGetCurrentUserResponse = UsersGetCurrentUserResponses[keyof UsersGetCurrentUserResponses];

export type UsersUpdateCurrentUserData = {
    body?: never;
    path?: never;
    query?: {
        phoneNumber?: string;
    };
    url: '/api/v1/Users/<USER>';
};

export type UsersUpdateCurrentUserErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type UsersUpdateCurrentUserError = UsersUpdateCurrentUserErrors[keyof UsersUpdateCurrentUserErrors];

export type UsersUpdateCurrentUserResponses = {
    /**
     * Succeed.
     */
    200: UpdateUserResponseDto;
};

export type UsersUpdateCurrentUserResponse = UsersUpdateCurrentUserResponses[keyof UsersUpdateCurrentUserResponses];

export type StaticDataAddressTypeGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/AddressType';
};

export type StaticDataAddressTypeGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataAddressTypeGetError = StaticDataAddressTypeGetErrors[keyof StaticDataAddressTypeGetErrors];

export type StaticDataAddressTypeGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataAddressTypeGetResponse = StaticDataAddressTypeGetResponses[keyof StaticDataAddressTypeGetResponses];

export type StaticDataAssetClassGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/AssetClass';
};

export type StaticDataAssetClassGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataAssetClassGetError = StaticDataAssetClassGetErrors[keyof StaticDataAssetClassGetErrors];

export type StaticDataAssetClassGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataAssetClassGetResponse = StaticDataAssetClassGetResponses[keyof StaticDataAssetClassGetResponses];

export type StaticDataCurrencySymbolPositionGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/CurrencySymbolPosition';
};

export type StaticDataCurrencySymbolPositionGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataCurrencySymbolPositionGetError = StaticDataCurrencySymbolPositionGetErrors[keyof StaticDataCurrencySymbolPositionGetErrors];

export type StaticDataCurrencySymbolPositionGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataCurrencySymbolPositionGetResponse = StaticDataCurrencySymbolPositionGetResponses[keyof StaticDataCurrencySymbolPositionGetResponses];

export type StaticDataDocumentCategoryGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/DocumentCategory';
};

export type StaticDataDocumentCategoryGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataDocumentCategoryGetError = StaticDataDocumentCategoryGetErrors[keyof StaticDataDocumentCategoryGetErrors];

export type StaticDataDocumentCategoryGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataDocumentCategoryGetResponse = StaticDataDocumentCategoryGetResponses[keyof StaticDataDocumentCategoryGetResponses];

export type StaticDataEntityRegulationGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/EntityRegulation';
};

export type StaticDataEntityRegulationGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataEntityRegulationGetError = StaticDataEntityRegulationGetErrors[keyof StaticDataEntityRegulationGetErrors];

export type StaticDataEntityRegulationGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataEntityRegulationGetResponse = StaticDataEntityRegulationGetResponses[keyof StaticDataEntityRegulationGetResponses];

export type StaticDataEntityStatusGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/EntityStatus';
};

export type StaticDataEntityStatusGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataEntityStatusGetError = StaticDataEntityStatusGetErrors[keyof StaticDataEntityStatusGetErrors];

export type StaticDataEntityStatusGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataEntityStatusGetResponse = StaticDataEntityStatusGetResponses[keyof StaticDataEntityStatusGetResponses];

export type StaticDataEntityTypeCategoryGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/EntityTypeCategory';
};

export type StaticDataEntityTypeCategoryGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataEntityTypeCategoryGetError = StaticDataEntityTypeCategoryGetErrors[keyof StaticDataEntityTypeCategoryGetErrors];

export type StaticDataEntityTypeCategoryGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataEntityTypeCategoryGetResponse = StaticDataEntityTypeCategoryGetResponses[keyof StaticDataEntityTypeCategoryGetResponses];

export type StaticDataInvestmentSectorGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/InvestmentSector';
};

export type StaticDataInvestmentSectorGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataInvestmentSectorGetError = StaticDataInvestmentSectorGetErrors[keyof StaticDataInvestmentSectorGetErrors];

export type StaticDataInvestmentSectorGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataInvestmentSectorGetResponse = StaticDataInvestmentSectorGetResponses[keyof StaticDataInvestmentSectorGetResponses];

export type StaticDataInvestorTypeGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/InvestorType';
};

export type StaticDataInvestorTypeGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataInvestorTypeGetError = StaticDataInvestorTypeGetErrors[keyof StaticDataInvestorTypeGetErrors];

export type StaticDataInvestorTypeGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataInvestorTypeGetResponse = StaticDataInvestorTypeGetResponses[keyof StaticDataInvestorTypeGetResponses];

export type StaticDataPurposeOfEntityGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/PurposeOfEntity';
};

export type StaticDataPurposeOfEntityGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataPurposeOfEntityGetError = StaticDataPurposeOfEntityGetErrors[keyof StaticDataPurposeOfEntityGetErrors];

export type StaticDataPurposeOfEntityGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataPurposeOfEntityGetResponse = StaticDataPurposeOfEntityGetResponses[keyof StaticDataPurposeOfEntityGetResponses];

export type StaticDataRelationshipTypeGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/RelationshipType';
};

export type StaticDataRelationshipTypeGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataRelationshipTypeGetError = StaticDataRelationshipTypeGetErrors[keyof StaticDataRelationshipTypeGetErrors];

export type StaticDataRelationshipTypeGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataRelationshipTypeGetResponse = StaticDataRelationshipTypeGetResponses[keyof StaticDataRelationshipTypeGetResponses];

export type StaticDataLoginEntityApproverLevelGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/LoginEntityApproverLevel';
};

export type StaticDataLoginEntityApproverLevelGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataLoginEntityApproverLevelGetError = StaticDataLoginEntityApproverLevelGetErrors[keyof StaticDataLoginEntityApproverLevelGetErrors];

export type StaticDataLoginEntityApproverLevelGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataLoginEntityApproverLevelGetResponse = StaticDataLoginEntityApproverLevelGetResponses[keyof StaticDataLoginEntityApproverLevelGetResponses];

export type StaticDataUserRoleGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/UserRole';
};

export type StaticDataUserRoleGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataUserRoleGetError = StaticDataUserRoleGetErrors[keyof StaticDataUserRoleGetErrors];

export type StaticDataUserRoleGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataUserRoleGetResponse = StaticDataUserRoleGetResponses[keyof StaticDataUserRoleGetResponses];

export type StaticDataStockExchangeGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/StockExchange';
};

export type StaticDataStockExchangeGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataStockExchangeGetError = StaticDataStockExchangeGetErrors[keyof StaticDataStockExchangeGetErrors];

export type StaticDataStockExchangeGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataStockExchangeGetResponse = StaticDataStockExchangeGetResponses[keyof StaticDataStockExchangeGetResponses];

export type StaticDataSourceOfFundsGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/StaticData/SourceOfFunds';
};

export type StaticDataSourceOfFundsGetErrors = {
    /**
     * Bad Request. The input parameters are invalid.
     */
    400: ProblemDetails;
    /**
     * Unauthorized. Authentication is required.
     */
    401: unknown;
    /**
     * Forbidden. You do not have permission to access this resource.
     */
    403: unknown;
    /**
     * Internal Server Error. An unexpected error occurred.
     */
    500: unknown;
};

export type StaticDataSourceOfFundsGetError = StaticDataSourceOfFundsGetErrors[keyof StaticDataSourceOfFundsGetErrors];

export type StaticDataSourceOfFundsGetResponses = {
    /**
     * Succeed.
     */
    200: Array<EnumValueDto>;
};

export type StaticDataSourceOfFundsGetResponse = StaticDataSourceOfFundsGetResponses[keyof StaticDataSourceOfFundsGetResponses];

export type ClientOptions = {
    baseURL: 'https://ca-entity-ext-agp-dev.yellowbush-31265d50.uksouth.azurecontainerapps.io' | (string & {});
};