// This file is auto-generated by @hey-api/openapi-ts

import { type Options as ClientOptions, type TDataShape, type Client, formDataBodySerializer } from '@hey-api/client-axios';
import type { AddressesGetAllData, AddressesGetAllResponse, AddressesGetAllError, AddressesPostData, AddressesPostResponse, AddressesPostError, AddressesDeleteData, AddressesDeleteResponse, AddressesDeleteError, AddressesGetData, AddressesGetResponse, AddressesGetError, AddressesPutData, AddressesPutResponse, AddressesPutError, DocumentsUnlinkDocumentEntityData, DocumentsUnlinkDocumentEntityResponse, DocumentsUnlinkDocumentEntityError, DocumentsLinkDocumentEntityData, DocumentsLinkDocumentEntityResponse, DocumentsLinkDocumentEntityError, DocumentsGetDocumentMetadataData, DocumentsGetDocumentMetadataResponse, DocumentsGetDocumentMetadataError, DocumentsPutDocumentMetadataData, DocumentsPutDocumentMetadataResponse, DocumentsPutDocumentMetadataError, DocumentsGetEntityDocumentsData, DocumentsGetEntityDocumentsResponse, DocumentsGetEntityDocumentsError, DocumentsPostDocumentMetadataData, DocumentsPostDocumentMetadataResponse, DocumentsPostDocumentMetadataError, DocumentsDownloadDocumentFileData, DocumentsDownloadDocumentFileResponse, DocumentsDownloadDocumentFileError, DocumentsPostDocumentFileData, DocumentsPostDocumentFileError, EntitiesCreateEntityData, EntitiesCreateEntityResponse, EntitiesCreateEntityError, EntitiesGetEntityData, EntitiesGetEntityResponse, EntitiesGetEntityError, EntitiesUpdateEntityData, EntitiesUpdateEntityError, EntitiesSubmitEntityV2Data, EntitiesSubmitEntityV2Error, EntityAccessGetEntityAccessData, EntityAccessGetEntityAccessResponse, EntityAccessGetEntityAccessError, EntityAccessCreateEntityAccessData, EntityAccessCreateEntityAccessResponse, EntityAccessCreateEntityAccessError, EntityAccessDeleteEntityAccessData, EntityAccessDeleteEntityAccessError, EntityAccessUpdateEntityAccessData, EntityAccessUpdateEntityAccessError, ExpectedTransactionActivityGetData, ExpectedTransactionActivityGetResponse, ExpectedTransactionActivityGetError, ExpectedTransactionActivityPutData, ExpectedTransactionActivityPutResponse, ExpectedTransactionActivityPutError, FinancialInformationGetData, FinancialInformationGetResponse, FinancialInformationGetError, FinancialInformationPostData, FinancialInformationPostResponse, FinancialInformationPostError, FinancialInformationDeleteData, FinancialInformationDeleteResponse, FinancialInformationDeleteError, FinancialInformationPutData, FinancialInformationPutResponse, FinancialInformationPutError, FinancialInformationGetSourcesData, FinancialInformationGetSourcesResponse, FinancialInformationGetSourcesError, FinancialInformationPutSourcesData, FinancialInformationPutSourcesResponse, FinancialInformationPutSourcesError, InvestmentsGetData, InvestmentsGetResponse, InvestmentsGetError, InvestmentsPutData, InvestmentsPutResponse, InvestmentsPutError, StaticDataGetEntityTypesData, StaticDataGetEntityTypesResponse, StaticDataGetEntityTypesError, StaticDataGetCountryListData, StaticDataGetCountryListResponse, StaticDataGetCountryListError, StaticDataGetCurrencyListData, StaticDataGetCurrencyListResponse, StaticDataGetCurrencyListError, StaticDataGetGicsClassificationListData, StaticDataGetGicsClassificationListResponse, StaticDataGetGicsClassificationListError, StaticDataGetGicsIndustriesListData, StaticDataGetGicsIndustriesListResponse, StaticDataGetGicsIndustriesListError, StaticDataGetTransactionActivityTypesListData, StaticDataGetTransactionActivityTypesListResponse, StaticDataGetTransactionActivityTypesListError, TermsAndConditionsGetData, TermsAndConditionsGetResponse, TermsAndConditionsGetError, TermsAndConditionsDownloadTermsAndConditionsData, TermsAndConditionsDownloadTermsAndConditionsResponse, TermsAndConditionsDownloadTermsAndConditionsError, TermsAndConditionsTermsAndConditionsData, TermsAndConditionsTermsAndConditionsResponse, TermsAndConditionsTermsAndConditionsError, UsersGetAllUsersWithAccessToEntityData, UsersGetAllUsersWithAccessToEntityResponse, UsersGetAllUsersWithAccessToEntityError, UsersAddNewLoginWithRolesToExistingEntityData, UsersAddNewLoginWithRolesToExistingEntityResponse, UsersAddNewLoginWithRolesToExistingEntityError, UsersGetAllUsersWithAccessToEntitiesForAdminUserData, UsersGetAllUsersWithAccessToEntitiesForAdminUserResponse, UsersGetAllUsersWithAccessToEntitiesForAdminUserError, UsersGetUserData, UsersGetUserResponse, UsersGetUserError, UsersGetCurrentUserData, UsersGetCurrentUserResponse, UsersGetCurrentUserError, UsersUpdateCurrentUserData, UsersUpdateCurrentUserResponse, UsersUpdateCurrentUserError, StaticDataAddressTypeGetData, StaticDataAddressTypeGetResponse, StaticDataAddressTypeGetError, StaticDataAssetClassGetData, StaticDataAssetClassGetResponse, StaticDataAssetClassGetError, StaticDataCurrencySymbolPositionGetData, StaticDataCurrencySymbolPositionGetResponse, StaticDataCurrencySymbolPositionGetError, StaticDataDocumentCategoryGetData, StaticDataDocumentCategoryGetResponse, StaticDataDocumentCategoryGetError, StaticDataEntityRegulationGetData, StaticDataEntityRegulationGetResponse, StaticDataEntityRegulationGetError, StaticDataEntityStatusGetData, StaticDataEntityStatusGetResponse, StaticDataEntityStatusGetError, StaticDataEntityTypeCategoryGetData, StaticDataEntityTypeCategoryGetResponse, StaticDataEntityTypeCategoryGetError, StaticDataInvestmentSectorGetData, StaticDataInvestmentSectorGetResponse, StaticDataInvestmentSectorGetError, StaticDataInvestorTypeGetData, StaticDataInvestorTypeGetResponse, StaticDataInvestorTypeGetError, StaticDataPurposeOfEntityGetData, StaticDataPurposeOfEntityGetResponse, StaticDataPurposeOfEntityGetError, StaticDataRelationshipTypeGetData, StaticDataRelationshipTypeGetResponse, StaticDataRelationshipTypeGetError, StaticDataLoginEntityApproverLevelGetData, StaticDataLoginEntityApproverLevelGetResponse, StaticDataLoginEntityApproverLevelGetError, StaticDataUserRoleGetData, StaticDataUserRoleGetResponse, StaticDataUserRoleGetError, StaticDataStockExchangeGetData, StaticDataStockExchangeGetResponse, StaticDataStockExchangeGetError, StaticDataSourceOfFundsGetData, StaticDataSourceOfFundsGetResponse, StaticDataSourceOfFundsGetError } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Get All Addresses for Entity.
 */
export const addressesGetAll = <ThrowOnError extends boolean = false>(options: Options<AddressesGetAllData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<AddressesGetAllResponse, AddressesGetAllError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Addresses',
        ...options
    });
};

/**
 * Creates a new address for given entity Id.
 */
export const addressesPost = <ThrowOnError extends boolean = false>(options: Options<AddressesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AddressesPostResponse, AddressesPostError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Addresses',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Deletes the adress of the entity.
 */
export const addressesDelete = <ThrowOnError extends boolean = false>(options: Options<AddressesDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<AddressesDeleteResponse, AddressesDeleteError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Addresses/{addressId}',
        ...options
    });
};

/**
 * Get Entity address.
 */
export const addressesGet = <ThrowOnError extends boolean = false>(options: Options<AddressesGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<AddressesGetResponse, AddressesGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Addresses/{addressId}',
        ...options
    });
};

/**
 * Updates the adress of the entity.
 */
export const addressesPut = <ThrowOnError extends boolean = false>(options: Options<AddressesPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<AddressesPutResponse, AddressesPutError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Addresses/{addressId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Unlinks document from the entity
 */
export const documentsUnlinkDocumentEntity = <ThrowOnError extends boolean = false>(options: Options<DocumentsUnlinkDocumentEntityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DocumentsUnlinkDocumentEntityResponse, DocumentsUnlinkDocumentEntityError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Documents/{documentId}',
        ...options
    });
};

/**
 * Links document to the entity
 */
export const documentsLinkDocumentEntity = <ThrowOnError extends boolean = false>(options: Options<DocumentsLinkDocumentEntityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<DocumentsLinkDocumentEntityResponse, DocumentsLinkDocumentEntityError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Documents/{documentId}',
        ...options
    });
};

/**
 * Gets metadata for the document
 */
export const documentsGetDocumentMetadata = <ThrowOnError extends boolean = false>(options: Options<DocumentsGetDocumentMetadataData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<DocumentsGetDocumentMetadataResponse, DocumentsGetDocumentMetadataError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Documents/{documentId}/Metadata',
        ...options
    });
};

/**
 * Updates document for all linked entities
 */
export const documentsPutDocumentMetadata = <ThrowOnError extends boolean = false>(options: Options<DocumentsPutDocumentMetadataData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<DocumentsPutDocumentMetadataResponse, DocumentsPutDocumentMetadataError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Documents/{documentId}/Metadata',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Returns all document metadata linked to an entity
 */
export const documentsGetEntityDocuments = <ThrowOnError extends boolean = false>(options: Options<DocumentsGetEntityDocumentsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<DocumentsGetEntityDocumentsResponse, DocumentsGetEntityDocumentsError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Documents/Metadata',
        ...options
    });
};

/**
 * Creates a new document
 */
export const documentsPostDocumentMetadata = <ThrowOnError extends boolean = false>(options: Options<DocumentsPostDocumentMetadataData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<DocumentsPostDocumentMetadataResponse, DocumentsPostDocumentMetadataError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Documents/Metadata',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Downloads the document
 */
export const documentsDownloadDocumentFile = <ThrowOnError extends boolean = false>(options: Options<DocumentsDownloadDocumentFileData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<DocumentsDownloadDocumentFileResponse, DocumentsDownloadDocumentFileError, ThrowOnError>({
        responseType: 'blob',
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Documents/{documentId}/File',
        ...options
    });
};

/**
 * Uploads a file for the newly created document
 */
export const documentsPostDocumentFile = <ThrowOnError extends boolean = false>(options: Options<DocumentsPostDocumentFileData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, DocumentsPostDocumentFileError, ThrowOnError>({
        ...formDataBodySerializer,
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Documents/{documentId}/File',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

/**
 * Create a new Entity and assign admin access.
 */
export const entitiesCreateEntity = <ThrowOnError extends boolean = false>(options: Options<EntitiesCreateEntityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<EntitiesCreateEntityResponse, EntitiesCreateEntityError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Get entity.
 */
export const entitiesGetEntity = <ThrowOnError extends boolean = false>(options: Options<EntitiesGetEntityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<EntitiesGetEntityResponse, EntitiesGetEntityError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}',
        ...options
    });
};

/**
 * Update an existing entity.
 */
export const entitiesUpdateEntity = <ThrowOnError extends boolean = false>(options: Options<EntitiesUpdateEntityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<unknown, EntitiesUpdateEntityError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const entitiesSubmitEntityV2 = <ThrowOnError extends boolean = false>(options: Options<EntitiesSubmitEntityV2Data, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, EntitiesSubmitEntityV2Error, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Submissions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Gets all Entities that the logged-in User has access to
 */
export const entityAccessGetEntityAccess = <ThrowOnError extends boolean = false>(options?: Options<EntityAccessGetEntityAccessData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<EntityAccessGetEntityAccessResponse, EntityAccessGetEntityAccessError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/EntityAccess',
        ...options
    });
};

/**
 * Adds existing user to the given entity with roles
 */
export const entityAccessCreateEntityAccess = <ThrowOnError extends boolean = false>(options: Options<EntityAccessCreateEntityAccessData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<EntityAccessCreateEntityAccessResponse, EntityAccessCreateEntityAccessError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/EntityAccess',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Deletes the entity access with given Id
 */
export const entityAccessDeleteEntityAccess = <ThrowOnError extends boolean = false>(options: Options<EntityAccessDeleteEntityAccessData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, EntityAccessDeleteEntityAccessError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/EntityAccess/{entityAccessId}',
        ...options
    });
};

/**
 * Updates the entity access with given values.
 */
export const entityAccessUpdateEntityAccess = <ThrowOnError extends boolean = false>(options: Options<EntityAccessUpdateEntityAccessData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<unknown, EntityAccessUpdateEntityAccessError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/EntityAccess/{entityAccessId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const expectedTransactionActivityGet = <ThrowOnError extends boolean = false>(options: Options<ExpectedTransactionActivityGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ExpectedTransactionActivityGetResponse, ExpectedTransactionActivityGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/ExpectedTransactionActivity',
        ...options
    });
};

export const expectedTransactionActivityPut = <ThrowOnError extends boolean = false>(options: Options<ExpectedTransactionActivityPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ExpectedTransactionActivityPutResponse, ExpectedTransactionActivityPutError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/ExpectedTransactionActivity',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const financialInformationGet = <ThrowOnError extends boolean = false>(options: Options<FinancialInformationGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FinancialInformationGetResponse, FinancialInformationGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation',
        ...options
    });
};

export const financialInformationPost = <ThrowOnError extends boolean = false>(options: Options<FinancialInformationPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<FinancialInformationPostResponse, FinancialInformationPostError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const financialInformationDelete = <ThrowOnError extends boolean = false>(options: Options<FinancialInformationDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<FinancialInformationDeleteResponse, FinancialInformationDeleteError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation/{yearEnding}',
        ...options
    });
};

export const financialInformationPut = <ThrowOnError extends boolean = false>(options: Options<FinancialInformationPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<FinancialInformationPutResponse, FinancialInformationPutError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/FinancialInformation/AnnualFinancialInformation/{yearEnding}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const financialInformationGetSources = <ThrowOnError extends boolean = false>(options: Options<FinancialInformationGetSourcesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<FinancialInformationGetSourcesResponse, FinancialInformationGetSourcesError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/FinancialInformation/Sources',
        ...options
    });
};

export const financialInformationPutSources = <ThrowOnError extends boolean = false>(options: Options<FinancialInformationPutSourcesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<FinancialInformationPutSourcesResponse, FinancialInformationPutSourcesError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/FinancialInformation/Sources',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Get the investment for the entity.
 */
export const investmentsGet = <ThrowOnError extends boolean = false>(options: Options<InvestmentsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<InvestmentsGetResponse, InvestmentsGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Investments/{entityId}',
        ...options
    });
};

/**
 * Update or create investment for the entity.
 */
export const investmentsPut = <ThrowOnError extends boolean = false>(options: Options<InvestmentsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<InvestmentsPutResponse, InvestmentsPutError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Investments/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const staticDataGetEntityTypes = <ThrowOnError extends boolean = false>(options?: Options<StaticDataGetEntityTypesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataGetEntityTypesResponse, StaticDataGetEntityTypesError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/EntityType',
        ...options
    });
};

export const staticDataGetCountryList = <ThrowOnError extends boolean = false>(options?: Options<StaticDataGetCountryListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataGetCountryListResponse, StaticDataGetCountryListError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/Country',
        ...options
    });
};

export const staticDataGetCurrencyList = <ThrowOnError extends boolean = false>(options?: Options<StaticDataGetCurrencyListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataGetCurrencyListResponse, StaticDataGetCurrencyListError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/Currency',
        ...options
    });
};

export const staticDataGetGicsClassificationList = <ThrowOnError extends boolean = false>(options?: Options<StaticDataGetGicsClassificationListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataGetGicsClassificationListResponse, StaticDataGetGicsClassificationListError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/GicsClassification',
        ...options
    });
};

export const staticDataGetGicsIndustriesList = <ThrowOnError extends boolean = false>(options?: Options<StaticDataGetGicsIndustriesListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataGetGicsIndustriesListResponse, StaticDataGetGicsIndustriesListError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/GicsClassification/Industries',
        ...options
    });
};

export const staticDataGetTransactionActivityTypesList = <ThrowOnError extends boolean = false>(options?: Options<StaticDataGetTransactionActivityTypesListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataGetTransactionActivityTypesListResponse, StaticDataGetTransactionActivityTypesListError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/TransactionActivityTypes',
        ...options
    });
};

/**
 * Get the latest terms and conditions for the entity.
 */
export const termsAndConditionsGet = <ThrowOnError extends boolean = false>(options: Options<TermsAndConditionsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<TermsAndConditionsGetResponse, TermsAndConditionsGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/LatestTermsAndConditions',
        ...options
    });
};

/**
 * Download the terms and conditions document.
 */
export const termsAndConditionsDownloadTermsAndConditions = <ThrowOnError extends boolean = false>(options: Options<TermsAndConditionsDownloadTermsAndConditionsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<TermsAndConditionsDownloadTermsAndConditionsResponse, TermsAndConditionsDownloadTermsAndConditionsError, ThrowOnError>({
        responseType: 'blob',
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/TermsAndConditions/{termsAndConditionsId}/File',
        ...options
    });
};

/**
 * Get all of the latest terms and conditions
 */
export const termsAndConditionsTermsAndConditions = <ThrowOnError extends boolean = false>(options?: Options<TermsAndConditionsTermsAndConditionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<TermsAndConditionsTermsAndConditionsResponse, TermsAndConditionsTermsAndConditionsError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/TermsAndConditions',
        ...options
    });
};

/**
 * Provides all users with access to the given entity Id.
 */
export const usersGetAllUsersWithAccessToEntity = <ThrowOnError extends boolean = false>(options: Options<UsersGetAllUsersWithAccessToEntityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<UsersGetAllUsersWithAccessToEntityResponse, UsersGetAllUsersWithAccessToEntityError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Users',
        ...options
    });
};

/**
 * Create a new user to access an entity and assign roles to that user.
 */
export const usersAddNewLoginWithRolesToExistingEntity = <ThrowOnError extends boolean = false>(options: Options<UsersAddNewLoginWithRolesToExistingEntityData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<UsersAddNewLoginWithRolesToExistingEntityResponse, UsersAddNewLoginWithRolesToExistingEntityError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Entities/{entityId}/Users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Provides all users for all entities for which the logged-in user is an administrator.
 */
export const usersGetAllUsersWithAccessToEntitiesForAdminUser = <ThrowOnError extends boolean = false>(options?: Options<UsersGetAllUsersWithAccessToEntitiesForAdminUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<UsersGetAllUsersWithAccessToEntitiesForAdminUserResponse, UsersGetAllUsersWithAccessToEntitiesForAdminUserError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Users',
        ...options
    });
};

/**
 * Get details of a specific user
 */
export const usersGetUser = <ThrowOnError extends boolean = false>(options: Options<UsersGetUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<UsersGetUserResponse, UsersGetUserError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Users/<USER>',
        ...options
    });
};

/**
 * Get logged in user.
 */
export const usersGetCurrentUser = <ThrowOnError extends boolean = false>(options?: Options<UsersGetCurrentUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<UsersGetCurrentUserResponse, UsersGetCurrentUserError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Users/<USER>',
        ...options
    });
};

/**
 * Update logged in user's phone number.
 */
export const usersUpdateCurrentUser = <ThrowOnError extends boolean = false>(options?: Options<UsersUpdateCurrentUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<UsersUpdateCurrentUserResponse, UsersUpdateCurrentUserError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/Users/<USER>',
        ...options
    });
};

export const staticDataAddressTypeGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataAddressTypeGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataAddressTypeGetResponse, StaticDataAddressTypeGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/AddressType',
        ...options
    });
};

export const staticDataAssetClassGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataAssetClassGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataAssetClassGetResponse, StaticDataAssetClassGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/AssetClass',
        ...options
    });
};

export const staticDataCurrencySymbolPositionGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataCurrencySymbolPositionGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataCurrencySymbolPositionGetResponse, StaticDataCurrencySymbolPositionGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/CurrencySymbolPosition',
        ...options
    });
};

export const staticDataDocumentCategoryGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataDocumentCategoryGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataDocumentCategoryGetResponse, StaticDataDocumentCategoryGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/DocumentCategory',
        ...options
    });
};

export const staticDataEntityRegulationGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataEntityRegulationGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataEntityRegulationGetResponse, StaticDataEntityRegulationGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/EntityRegulation',
        ...options
    });
};

export const staticDataEntityStatusGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataEntityStatusGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataEntityStatusGetResponse, StaticDataEntityStatusGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/EntityStatus',
        ...options
    });
};

export const staticDataEntityTypeCategoryGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataEntityTypeCategoryGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataEntityTypeCategoryGetResponse, StaticDataEntityTypeCategoryGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/EntityTypeCategory',
        ...options
    });
};

export const staticDataInvestmentSectorGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataInvestmentSectorGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataInvestmentSectorGetResponse, StaticDataInvestmentSectorGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/InvestmentSector',
        ...options
    });
};

export const staticDataInvestorTypeGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataInvestorTypeGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataInvestorTypeGetResponse, StaticDataInvestorTypeGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/InvestorType',
        ...options
    });
};

export const staticDataPurposeOfEntityGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataPurposeOfEntityGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataPurposeOfEntityGetResponse, StaticDataPurposeOfEntityGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/PurposeOfEntity',
        ...options
    });
};

export const staticDataRelationshipTypeGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataRelationshipTypeGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataRelationshipTypeGetResponse, StaticDataRelationshipTypeGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/RelationshipType',
        ...options
    });
};

export const staticDataLoginEntityApproverLevelGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataLoginEntityApproverLevelGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataLoginEntityApproverLevelGetResponse, StaticDataLoginEntityApproverLevelGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/LoginEntityApproverLevel',
        ...options
    });
};

export const staticDataUserRoleGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataUserRoleGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataUserRoleGetResponse, StaticDataUserRoleGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/UserRole',
        ...options
    });
};

export const staticDataStockExchangeGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataStockExchangeGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataStockExchangeGetResponse, StaticDataStockExchangeGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/StockExchange',
        ...options
    });
};

export const staticDataSourceOfFundsGet = <ThrowOnError extends boolean = false>(options?: Options<StaticDataSourceOfFundsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StaticDataSourceOfFundsGetResponse, StaticDataSourceOfFundsGetError, ThrowOnError>({
        security: [
            {
                name: 'Authorization',
                type: 'apiKey'
            }
        ],
        url: '/api/v1/StaticData/SourceOfFunds',
        ...options
    });
};