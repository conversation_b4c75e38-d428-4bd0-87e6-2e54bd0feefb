import { DocumentsMetadata } from "@/data/onboarding/onboarding.interface"
import { documentsDownloadDocumentFile } from "@/client/onboarding/sdk.gen"

export function useDocumentDownload() {
  const handleDownload = async (
    docMeta: DocumentsMetadata,
    localFiles?: File[],
  ) => {
    // Check if this is a new document (no ID) but we have a matching local file
    if (!docMeta.id && localFiles && localFiles.length > 0) {
      // Find the matching file by name
      const matchingFile = localFiles.find((file) => file.name === docMeta.name)

      if (matchingFile) {
        // Create a URL for the local file
        const url = URL.createObjectURL(matchingFile)

        // Create download link
        const link = document.createElement("a")
        link.href = url
        link.download = matchingFile.name
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Cleanup
        setTimeout(() => {
          URL.revokeObjectURL(url)
        }, 100)

        return
      }

      alert("This document hasn't been saved yet and can't be downloaded.")
      return
    }

    if (!docMeta.id) {
      alert("Document cannot be downloaded. It may not be saved yet.")
      return
    }

    try {
      // Use the SDK function to download the document file
      const response = await documentsDownloadDocumentFile({
        path: {
          documentId: docMeta.id,
        },
        responseType: "blob",
      })

      if (response.status === 200 && response.data) {
        const blob = response.data

        // Create download URL
        const url = URL.createObjectURL(blob)

        // Create and trigger download
        const link = document.createElement("a")
        link.href = url
        // If filename not available, use a generic name with document ID
        link.download = docMeta.name || `document-${docMeta.id}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Cleanup
        setTimeout(() => {
          URL.revokeObjectURL(url)
        }, 100)
      } else {
        alert("Failed to download document. Please try again.")
      }
    } catch (_error: unknown) {
      alert("Failed to download document. Please try again.")
    }
  }

  return {
    handleDownload,
  }
}
