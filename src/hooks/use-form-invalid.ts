import { useEffect, useRef } from "react"
import { FormApi } from "@tanstack/react-form"

export function useFormInvalid(
  form: FormApi<any, any>,
  onInvalid?: () => void,
) {
  const wasValid = useRef(form.store.state.isValid)
  useEffect(() => {
    const unsubscribe = form.store.subscribe(() => {
      const { isValid } = form.store.state
      if (wasValid.current && !isValid) {
        onInvalid?.()
      }
      wasValid.current = isValid
    })
    return unsubscribe
  }, [])
}
