import { useOnboardingUserAdminContext } from "@/contexts/OnboardingUserAdminContext"
import { useLoaderData as useEntityLoaderData } from "@/data/$entityId.loader"

/**
 * Hook that works in both entity and onboarding contexts
 * First tries to get data from onboarding context, falls back to entity context
 */
export function useUserAdminLoaderData() {
  try {
    // Try to get data from onboarding context first
    const onboardingContext = useOnboardingUserAdminContext()
    return onboardingContext
  } catch {
    // Fall back to entity context
    const entityData = useEntityLoaderData()
    return {
      entityId: entityData.entityId,
      entity: entityData.entity,
      entities: [], // Entity context doesn't have entities array
      user: entityData.user,
    }
  }
}
