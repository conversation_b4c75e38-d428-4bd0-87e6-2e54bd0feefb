import { useMemo } from "react"
import { useAuth0 } from "@auth0/auth0-react"

import { createApiClient } from "@/utils/api-client"
import { onboardingClient as onboarding } from "@/client/onboarding"
import { bankingClient as banking } from "@/client/banking"

export function useApiClients() {
  const auth0 = useAuth0()

  const bankingClient = useMemo(
    () =>
      createApiClient({
        auth0,
        client: banking,
      }),
    [auth0],
  )

  const onboardingClient = useMemo(
    () =>
      createApiClient({
        auth0,
        client: onboarding,
      }),
    [auth0],
  )

  return {
    bankingClient,
    onboardingClient,
  }
}
