import React from "react"

import { useToggle } from "./use-toggle"

export function useMultiSelector<T extends string>(coreSelectItems: T[] = []) {
  const [isOpen, toggle] = useToggle()
  const [selectItems, setSelectItems] = React.useState(coreSelectItems)

  const handleSelectedValue = (updater: T | ((prevItems: T[]) => T[])) => {
    setSelectItems((prevItems) => {
      if (isUpdaterFunction<T>(updater)) {
        return updater(prevItems)
      }

      return prevItems.includes(updater)
        ? prevItems.filter((item) => item !== updater) // Remove item
        : [...prevItems, updater] // Add item;
    })
  }

  return { isOpen, toggle, selectItems, setSelectItems, handleSelectedValue }
}

const isUpdaterFunction = <T>(
  updater: any,
): updater is (prevItems: T[]) => T[] => typeof updater === "function"
