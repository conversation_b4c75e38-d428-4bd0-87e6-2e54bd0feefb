import { useEffect, useRef } from "react"

import { useGlobalStore } from "@/data/global/global.store"
import { IBreadcrumbItem } from "@/data/global/global.interface"

export function useBreadcrumb(
  key: string,
  item: Omit<IBreadcrumbItem, "timestamp" | "key">,
) {
  const { setBreadcrumbs } = useGlobalStore()
  const timestampRef = useRef<number>(performance.now())

  useEffect(() => {
    const newItem = {
      ...item,
      key,
      timestamp: timestampRef.current,
    }

    setBreadcrumbs((prev) => {
      const filtered = prev.filter((crumb) => crumb.key !== key)
      return [...filtered, newItem].sort(
        (a, b) => (a.timestamp || 0) - (b.timestamp || 0),
      )
    })

    return () => {
      setBreadcrumbs((prev) => prev.filter((crumb) => crumb.key !== key))
    }
  }, [key, item.path, item.label])
}
