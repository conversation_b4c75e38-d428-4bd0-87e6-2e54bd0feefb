import { useEntityAccessQry } from "@/data/onboarding/onboarding.query"
import { useRolesAndPermissionsQuery } from "@/data/user-admin/user-admin.query"
import { useUserAdminLoaderData } from "@/hooks/useUserAdminLoaderData"
import { ROLES } from "@/lib/constants/roles.constants"
export interface RoleAccess {
  [key: string]: boolean | undefined
}

export function useRolePermissions() {
  const { entityId } = useUserAdminLoaderData()
  const { data: rolesAndPermissions } = useRolesAndPermissionsQuery() // should take param x-entity-id
  const { data: entityAcceess } = useEntityAccessQry()

  /**
   * Check if the current user has a specific permission
   * @param permissionKey - The permission key to check
   * @returns true if user has permission, false otherwise
   *
   * Special behavior:
   * - Administrators always have all permissions, even if API fails
   * - Non-administrators are denied access if API fails to load permissions
   */
  function getPermission(permissionKey: string): boolean {
    // Get user's roles for the current entity
    const accessRoleOfUser =
      entityAcceess?.filter((s) => s.entityId == entityId)[0]?.roles || []
    // seems like usually one role is assigned
    const accessRole = accessRoleOfUser[0] || ""

    // If user is Administrator, grant all permissions even if API fails
    if (accessRole === ROLES.ADMINISTRATOR) {
      return true
    }

    // If API failed to load permissions, deny access for non-administrators
    if (!rolesAndPermissions) return false

    const accessCategory = rolesAndPermissions.filter(
      (s) => s.category === "Access / User management",
    )[0]
    const permissions = accessCategory?.permissions

    if (!permissions) return false

    const roleAccesses = permissions.filter(
      (s) => s.permission == permissionKey,
    )[0]?.roleAccess as RoleAccess

    if (!roleAccesses) return false

    const roleAccess = roleAccesses.hasOwnProperty(accessRole)
      ? roleAccesses[accessRole]
      : (false as boolean)
    return !!roleAccess
  }

  return {
    getPermission,
  }
}
