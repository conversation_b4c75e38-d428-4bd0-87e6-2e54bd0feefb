import { useEffect } from "react"

import { useAuth0 } from "@auth0/auth0-react"
import { useState, useRef } from "react"
import * as signalR from "@microsoft/signalr"

const baseUrl = import.meta.env.VITE_BANKING_API_URL

export const useNotification = () => {
  const { getAccessTokenSilently } = useAuth0()
  const [connection, setConnection] = useState<signalR.HubConnection | null>(
    null,
  )
  const initialized = useRef(false)

  useEffect(() => {
    if (initialized.current) return
    initialized.current = true
    getAccessTokenSilently().then((accessToken) => {
      let connection = new signalR.HubConnectionBuilder()
        .withUrl(`${baseUrl}/hubs/notifications`, {
          accessTokenFactory: () => accessToken,
          transport: signalR.HttpTransportType.ServerSentEvents,
        })
        .withAutomaticReconnect()
        .build()

      setConnection(connection)
    })

    return () => {
      connection?.stop()
    }
  }, [])

  useEffect(() => {
    if (!connection) return

    connection.start().then(() => {
      console.log("Connected to notifications hub")

      connection.on("ReceiveMessage", (message) => {
        console.log("Received message", message)
      })

      connection.onreconnected((connectionId) => {
        console.log("Reconnected to notifications hub", connectionId)
      })

      connection.onreconnecting((error) => {
        console.log("Reconnecting to notifications hub", error)
      })

      connection.onclose((error) => {
        console.log("Disconnected from notifications hub", error)
      })

      connection.on("Error", (error) => {
        console.log("Error in notifications hub", error)
      })
    })
  }, [connection])
}
