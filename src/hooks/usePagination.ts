import { useState } from "react"

export interface PaginationConfig {
  pageSize?: number
  showSizeSelector?: boolean
  pageSizeOptions?: number[]
  showPageInfo?: boolean
  showFirstLast?: boolean
}

export interface UsePaginationProps {
  totalItems: number
  initialPageSize?: number
  initialPage?: number
  onPageChange?: (page: number) => void
  onPrevious?: () => void
  onNext?: () => void
  onPageSizeChange?: (pageSize: number) => void
  currentPage?: number // For controlled pagination
}

export interface PaginationReturnType {
  currentPage: number
  pageSize: number
  totalPages: number
  totalItems: number
  startIndex: number
  endIndex: number
  canPreviousPage: boolean
  canNextPage: boolean
  goToPage: (page: number) => void
  nextPage: () => void
  previousPage: () => void
  setPageSize: (pageSize: number) => void
  goToFirstPage: () => void
  goToLastPage: () => void
  getPageItems: <T>(items: T[]) => T[]
  getPaginationInfo: () => {
    startItem: number
    endItem: number
    hasItems: boolean
  }
  getVisiblePages: () => (string | number)[]
}

export function usePagination({
  totalItems,
  initialPageSize = 10,
  initialPage = 1,
  onPageChange,
  onPageSizeChange,
  onNext,
  onPrevious,
  currentPage: controlledCurrentPage,
}: UsePaginationProps): PaginationReturnType {
  const [internalCurrentPage, setInternalCurrentPage] = useState(initialPage)
  const [pageSize, setInternalPageSize] = useState(initialPageSize)

  const currentPage = controlledCurrentPage ?? internalCurrentPage
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize))

  // Ensure current page is within bounds
  const safePage = Math.min(Math.max(1, currentPage), totalPages)

  const startIndex = (safePage - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, totalItems)

  const canPreviousPage = safePage > 1
  const canNextPage = safePage < totalPages

  const goToPage = (page: number) => {
    const newPage = Math.min(Math.max(1, page), totalPages)

    if (onPageChange) onPageChange(newPage)

    setInternalCurrentPage(newPage)
  }

  const nextPage = () => {
    if (canNextPage) {
      if (controlledCurrentPage) {
        return onNext?.()
      }

      goToPage(safePage + 1)
    }
  }

  const previousPage = () => {
    if (canPreviousPage) {
      if (controlledCurrentPage) {
        return onPrevious?.()
      }

      goToPage(safePage - 1)
    }
  }

  const goToFirstPage = () => goToPage(1)
  const goToLastPage = () => goToPage(totalPages)

  function setPageSize(newPageSize: number) {
    setInternalPageSize(newPageSize)

    if (onPageSizeChange) {
      onPageSizeChange(newPageSize)
    }

    // Reset to first page when page size changes
    goToPage(1)
  }

  function getPageItems<T>(items: T[]): T[] {
    return items.slice(startIndex, endIndex)
  }

  function getPaginationInfo() {
    return {
      startItem: Math.min(startIndex + 1, totalItems),
      endItem: Math.min(endIndex, totalItems),
      hasItems: totalItems > 0,
    }
  }

  function getVisiblePages() {
    const delta = 2
    const range = []
    const rangeWithDots = []

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i)
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, "...")
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push("...", totalPages)
    } else {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots.filter(
      (item, index, arr) => arr.indexOf(item) === index,
    )
  }

  return {
    currentPage: safePage,
    pageSize,
    totalPages,
    totalItems,
    startIndex,
    endIndex,
    canPreviousPage,
    canNextPage,
    goToPage,
    nextPage,
    previousPage,
    setPageSize,
    goToFirstPage,
    goToLastPage,
    getPageItems,
    getPaginationInfo,
    getVisiblePages,
  }
}
