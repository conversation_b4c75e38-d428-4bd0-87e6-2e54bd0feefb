import { useMemo } from "react"

import { useEntityAccessQry } from "@/data/onboarding/onboarding.query"

export function useIsOnBoarding() {
  const { data: entityAccess, isLoading, isError } = useEntityAccessQry()

  const onboardingEntities = useMemo(() => {
    return (
      entityAccess?.filter(
        (e) =>
          e.entityStatus === "PreSubmission" || e.entityStatus === "Onboarding",
      ) ?? []
    )
  }, [entityAccess])

  return { onboardingEntities, isLoading, isError }
}
