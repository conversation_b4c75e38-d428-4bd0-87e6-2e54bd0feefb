import { useMemo } from "react"

import { useCurrenciesQuery } from "@/data/global/global.query"

/**
 * Hook for getting currency full names
 * @param currencyCode Optional currency code to get name for immediately
 * @returns Function to get currency names and loading/error states
 */
export function useCurrencyName() {
  const { data: currencies, isLoading, isError } = useCurrenciesQuery()

  // Function to get currency full name
  const getCurrencyName = useMemo(() => {
    return (code: string): string => {
      const currency = currencies?.find((c) => c.code === code)
      return currency?.description || code
    }
  }, [currencies])

  return {
    getCurrencyName,
    isLoading,
    isError,
  }
}
