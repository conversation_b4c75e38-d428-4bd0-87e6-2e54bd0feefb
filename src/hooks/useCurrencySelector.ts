import { useMemo } from "react"

import { Currency } from "@/lib/constants/currency.constants"
import { useAccountStore } from "@/data/account/account.store"
import { Account } from "@/data/account/account.interface"

/**
 * Hook for currency selection and management
 * @param accounts Optional accounts to extract available currencies from
 * @returns Currency selection state and handlers
 */
export function useCurrencySelector(accounts?: Account[]) {
  const { displayCurrency, setDisplayCurrency } = useAccountStore()

  // Extract unique currencies from all accounts
  const availableCurrencies = useMemo(() => {
    if (!accounts?.length) return []

    const currencies = accounts.flatMap((account) =>
      account.balances.map((balance) => balance.currency),
    )

    return [...new Set(currencies)] as Currency[]
  }, [accounts])

  return {
    displayCurrency,
    setDisplayCurrency,
    availableCurrencies,
  }
}
