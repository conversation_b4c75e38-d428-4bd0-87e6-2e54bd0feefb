import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"

import { getNavItemIcon } from "@/lib/constants/entity-navigation"
import { useNavigationIcon } from "@/hooks/use-navigation-icon"
import { useGlobalStore } from "@/data/global/global.store"

vi.mock("@/data/global/global.store", () => ({
  useGlobalStore: vi.fn(),
}))

vi.mock("@/lib/constants/entity-navigation", () => ({
  getNavItemIcon: vi.fn(),
}))

describe("useNavigationIcon", () => {
  const mockSetIcon = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useGlobalStore as any).mockImplementation(
      (selector: (state: { setIcon: typeof mockSetIcon }) => unknown) =>
        selector({ setIcon: mockSetIcon }),
    )
  })

  it("should set icon on mount", () => {
    const mockPath = "/test-path"
    const mockIcon = "test-icon"
    ;(getNavItemIcon as any).mockReturnValue(mockIcon)

    renderHook(() => useNavigationIcon(mockPath))

    expect(getNavItemIcon).toHaveBeenCalledWith(mockPath)
    expect(mockSetIcon).toHaveBeenCalledWith(mockIcon)
  })

  it("should update icon when path changes", () => {
    const initialPath = "/initial-path"
    const newPath = "/new-path"
    const initialIcon = "initial-icon"
    const newIcon = "new-icon"

    ;(getNavItemIcon as any)
      .mockReturnValueOnce(initialIcon)
      .mockReturnValueOnce(newIcon)

    const { rerender } = renderHook((path) => useNavigationIcon(path), {
      initialProps: initialPath,
    })

    expect(getNavItemIcon).toHaveBeenCalledWith(initialPath)
    expect(mockSetIcon).toHaveBeenCalledWith(initialIcon)

    rerender(newPath)

    expect(getNavItemIcon).toHaveBeenCalledWith(newPath)
    expect(mockSetIcon).toHaveBeenCalledWith(newIcon)
  })

  it("should clear icon on unmount", () => {
    const mockPath = "/test-path"
    const mockIcon = "test-icon"
    ;(getNavItemIcon as any).mockReturnValue(mockIcon)

    const { unmount } = renderHook(() => useNavigationIcon(mockPath))

    expect(mockSetIcon).toHaveBeenCalledWith(mockIcon)

    unmount()

    expect(mockSetIcon).toHaveBeenLastCalledWith(undefined)
  })

  it("should not set icon if getNavItemIcon returns undefined", () => {
    const mockPath = "/test-path"
    ;(getNavItemIcon as any).mockReturnValue(undefined)

    renderHook(() => useNavigationIcon(mockPath))

    expect(getNavItemIcon).toHaveBeenCalledWith(mockPath)
    expect(mockSetIcon).toHaveBeenCalledWith(undefined)
  })
})
