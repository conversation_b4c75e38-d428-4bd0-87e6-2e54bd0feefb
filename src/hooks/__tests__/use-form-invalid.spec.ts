import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"
import { FormApi } from "@tanstack/react-form"

import { useFormInvalid } from "@/hooks/use-form-invalid"

describe("useFormInvalid", () => {
  let mockForm: FormApi<any, any>
  let mockSubscribe: ReturnType<typeof vi.fn>
  let mockUnsubscribe: ReturnType<typeof vi.fn>
  let stateChangeCallback: ((state: any) => void) | null = null

  beforeEach(() => {
    mockUnsubscribe = vi.fn()
    mockSubscribe = vi.fn((callback) => {
      stateChangeCallback = callback
      return mockUnsubscribe
    })

    mockForm = {
      store: {
        state: { isValid: true },
        subscribe: mockSubscribe,
      },
    } as unknown as Form<PERSON><PERSON><any, any>
  })

  it("should subscribe to form store on mount", () => {
    renderHook(() => useFormInvalid(mockForm))
    expect(mockSubscribe).toHaveBeenCalled()
  })

  it("should unsubscribe from form store on unmount", () => {
    const { unmount } = renderHook(() => useFormInvalid(mockForm))
    unmount()
    expect(mockUnsubscribe).toHaveBeenCalled()
  })

  it("should call onInvalid when form becomes invalid", () => {
    const onInvalid = vi.fn()
    renderHook(() => useFormInvalid(mockForm, onInvalid))

    // Initially valid
    expect(onInvalid).not.toHaveBeenCalled()

    // Simulate form becoming invalid
    mockForm.store.state.isValid = false
    stateChangeCallback?.({})

    expect(onInvalid).toHaveBeenCalledTimes(1)
  })

  it("should not call onInvalid when form remains invalid", () => {
    const onInvalid = vi.fn()
    mockForm.store.state.isValid = false
    renderHook(() => useFormInvalid(mockForm, onInvalid))

    stateChangeCallback?.({})

    expect(onInvalid).not.toHaveBeenCalled()
  })

  it("should not call onInvalid when form becomes valid", () => {
    const onInvalid = vi.fn()
    mockForm.store.state.isValid = false
    renderHook(() => useFormInvalid(mockForm, onInvalid))

    mockForm.store.state.isValid = true
    stateChangeCallback?.({})

    expect(onInvalid).not.toHaveBeenCalled()
  })

  it("should handle undefined onInvalid callback", () => {
    renderHook(() => useFormInvalid(mockForm))

    expect(() => {
      mockForm.store.state.isValid = false
      stateChangeCallback?.({})
    }).not.toThrow()
  })

  it("should track validity state changes correctly", () => {
    const onInvalid = vi.fn()
    renderHook(() => useFormInvalid(mockForm, onInvalid))

    mockForm.store.state.isValid = false
    stateChangeCallback?.({})
    expect(onInvalid).toHaveBeenCalledTimes(1)

    mockForm.store.state.isValid = true
    stateChangeCallback?.({})
    expect(onInvalid).toHaveBeenCalledTimes(1)

    mockForm.store.state.isValid = false
    stateChangeCallback?.({})
    expect(onInvalid).toHaveBeenCalledTimes(2)
  })
})
