import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook, act } from "@testing-library/react"

import { useMediaQuery } from "@/hooks/useMediaQuery"

describe("useMediaQuery", () => {
  let mockMatchMedia: ReturnType<typeof vi.fn>
  let mockAddEventListener: ReturnType<typeof vi.fn>
  let mockRemoveEventListener: ReturnType<typeof vi.fn>
  let mediaQueryCallback: ((e: { matches: boolean }) => void) | null = null

  beforeEach(() => {
    mockAddEventListener = vi.fn((event, callback) => {
      mediaQueryCallback = callback
    })
    mockRemoveEventListener = vi.fn()

    mockMatchMedia = vi.fn((query) => ({
      matches: false,
      addEventListener: mockAddEventListener,
      removeEventListener: mockRemoveEventListener,
    }))

    // Mock window.matchMedia
    Object.defineProperty(window, "matchMedia", {
      writable: true,
      value: mockMatchMedia,
    })
  })

  it("should call matchMedia with the provided query", () => {
    const query = "(min-width: 768px)"
    renderHook(() => useMediaQuery(query))
    expect(mockMatchMedia).toHaveBeenCalledWith(query)
  })

  it("should add event listener on mount", () => {
    renderHook(() => useMediaQuery("(min-width: 768px)"))
    expect(mockAddEventListener).toHaveBeenCalledWith(
      "change",
      expect.any(Function),
    )
  })

  it("should remove event listener on unmount", () => {
    const { unmount } = renderHook(() => useMediaQuery("(min-width: 768px)"))
    unmount()
    expect(mockRemoveEventListener).toHaveBeenCalledWith(
      "change",
      expect.any(Function),
    )
  })

  it("should return initial matches value", () => {
    mockMatchMedia.mockImplementation((query) => ({
      matches: true,
      addEventListener: mockAddEventListener,
      removeEventListener: mockRemoveEventListener,
    }))

    const { result } = renderHook(() => useMediaQuery("(min-width: 768px)"))
    expect(result.current).toBe(true)
  })

  it("should not update matches if component is unmounted", () => {
    const { result, unmount } = renderHook(() =>
      useMediaQuery("(min-width: 768px)"),
    )
    expect(result.current).toBe(false)

    unmount()
    mediaQueryCallback?.({ matches: true })
    expect(result.current).toBe(false)
  })
})
