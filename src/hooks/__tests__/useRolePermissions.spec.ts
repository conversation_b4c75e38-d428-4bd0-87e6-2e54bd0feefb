import { renderHook } from "@testing-library/react"
import { describe, it, expect, vi, beforeEach } from "vitest"

import { ROLES } from "@/lib/constants/roles.constants"

// Mock the dependencies
const mockUseEntityAccessQry = vi.fn()
const mockUseRolesAndPermissionsQuery = vi.fn()
const mockUseUserAdminLoaderData = vi.fn()

vi.mock("@/data/onboarding/onboarding.query", () => ({
  useEntityAccessQry: () => mockUseEntityAccessQry(),
}))

vi.mock("@/data/user-admin/user-admin.query", () => ({
  useRolesAndPermissionsQuery: () => mockUseRolesAndPermissionsQuery(),
}))

vi.mock("@/hooks/useUserAdminLoaderData", () => ({
  useUserAdminLoaderData: () => mockUseUserAdminLoaderData(),
}))

// Import after mocking
const { useRolePermissions } = await import("../useRolePermissions")

describe("useRolePermissions", () => {
  const mockEntityId = "test-entity-id"
  const mockPermissionKey = "test-permission"

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseUserAdminLoaderData.mockReturnValue({ entityId: mockEntityId })
  })

  describe("Administrator permissions", () => {
    beforeEach(() => {
      // Mock entity access with Administrator role
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: mockEntityId,
            roles: [ROLES.ADMINISTRATOR],
          },
        ],
        error: null,
      })
    })

    it("should grant all permissions to Administrator when API is working", () => {
      // Mock successful permissions API
      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: [
          {
            category: "Access / User management",
            permissions: [
              {
                permission: mockPermissionKey,
                roleAccess: {
                  Administrator: true,
                  Approver: false,
                  Submitter: false,
                  Viewer: false,
                },
              },
            ],
          },
        ],
        error: null,
      })

      const { result } = renderHook(() => useRolePermissions())

      expect(result.current.getPermission(mockPermissionKey)).toBe(true)
    })

    it("should grant all permissions to Administrator even when permissions API fails", () => {
      // Mock failed permissions API
      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: null,
        error: new Error("API failed"),
      })

      const { result } = renderHook(() => useRolePermissions())

      // Administrator should still have permissions even when API fails
      expect(result.current.getPermission(mockPermissionKey)).toBe(true)
      expect(result.current.getPermission("any-other-permission")).toBe(true)
    })

    it("should grant all permissions to Administrator even when entity access API fails", () => {
      // Mock failed entity access API but still return Administrator role
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: mockEntityId,
            roles: [ROLES.ADMINISTRATOR],
          },
        ],
        error: new Error("Entity access API failed"),
      })

      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: null,
        error: null,
      })

      const { result } = renderHook(() => useRolePermissions())

      expect(result.current.getPermission(mockPermissionKey)).toBe(true)
    })
  })

  describe("Non-Administrator permissions", () => {
    beforeEach(() => {
      // Mock entity access with non-Administrator role
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: mockEntityId,
            roles: [ROLES.VIEWER],
          },
        ],
        error: null,
      })
    })

    it("should check permissions normally for non-Administrator when API is working", () => {
      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: [
          {
            category: "Access / User management",
            permissions: [
              {
                permission: mockPermissionKey,
                roleAccess: {
                  Administrator: true,
                  Approver: false,
                  Submitter: false,
                  Viewer: true, // Viewer has this permission
                },
              },
            ],
          },
        ],
        error: null,
      })

      const { result } = renderHook(() => useRolePermissions())

      expect(result.current.getPermission(mockPermissionKey)).toBe(true)
    })

    it("should deny permissions to non-Administrator when permissions API fails", () => {
      // Mock failed permissions API
      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: null,
        error: new Error("API failed"),
      })

      const { result } = renderHook(() => useRolePermissions())

      // Non-Administrator should be denied when API fails
      expect(result.current.getPermission(mockPermissionKey)).toBe(false)
    })

    it("should deny permissions when user has no role", () => {
      // Mock entity access with no roles
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: mockEntityId,
            roles: [],
          },
        ],
        error: null,
      })

      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: [
          {
            category: "Access / User management",
            permissions: [
              {
                permission: mockPermissionKey,
                roleAccess: {
                  Administrator: true,
                  Approver: false,
                  Submitter: false,
                  Viewer: false,
                },
              },
            ],
          },
        ],
        error: null,
      })

      const { result } = renderHook(() => useRolePermissions())

      expect(result.current.getPermission(mockPermissionKey)).toBe(false)
    })
  })

  describe("Edge cases", () => {
    it("should handle missing entity access data", () => {
      mockUseEntityAccessQry.mockReturnValue({
        data: null,
        error: null,
      })

      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: [],
        error: null,
      })

      const { result } = renderHook(() => useRolePermissions())

      expect(result.current.getPermission(mockPermissionKey)).toBe(false)
    })

    it("should handle entity not found in access data", () => {
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: "different-entity-id",
            roles: [ROLES.ADMINISTRATOR],
          },
        ],
        error: null,
      })

      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: [],
        error: null,
      })

      const { result } = renderHook(() => useRolePermissions())

      expect(result.current.getPermission(mockPermissionKey)).toBe(false)
    })

    it("should handle missing permission in roles data", () => {
      mockUseEntityAccessQry.mockReturnValue({
        data: [
          {
            entityId: mockEntityId,
            roles: [ROLES.VIEWER],
          },
        ],
        error: null,
      })

      mockUseRolesAndPermissionsQuery.mockReturnValue({
        data: [
          {
            category: "Access / User management",
            permissions: [
              {
                permission: "different-permission",
                roleAccess: {
                  Administrator: true,
                  Viewer: true,
                },
              },
            ],
          },
        ],
        error: null,
      })

      const { result } = renderHook(() => useRolePermissions())

      expect(result.current.getPermission(mockPermissionKey)).toBe(false)
    })
  })
})
