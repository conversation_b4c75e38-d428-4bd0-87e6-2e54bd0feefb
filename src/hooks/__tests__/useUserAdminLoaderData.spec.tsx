import { renderHook } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

import { OnboardingUserAdminProvider } from "@/contexts/OnboardingUserAdminContext"
import { useUserAdminLoaderData } from "@/hooks/useUserAdminLoaderData"

// Mock the entity loader
vi.mock("@/data/$entityId.loader", () => ({
  useLoaderData: vi.fn(() => ({
    entityId: "entity-from-main-context",
    entity: { id: "entity-from-main-context" },
    user: { id: "user-1" },
  })),
}))

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
)

describe("useUserAdminLoaderData", () => {
  it("should use onboarding context when available", () => {
    const mockLoaderData = {
      entityId: "onboarding-entity",
      entity: { id: "onboarding-entity" },
      entities: [{ entityId: "onboarding-entity", roles: ["Administrator"] }],
      user: null,
    }

    const onboardingWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        <OnboardingUserAdminProvider loaderData={mockLoaderData}>
          {children}
        </OnboardingUserAdminProvider>
      </QueryClientProvider>
    )

    const { result } = renderHook(() => useUserAdminLoaderData(), {
      wrapper: onboardingWrapper,
    })

    expect(result.current.entityId).toBe("onboarding-entity")
    expect(result.current.entity.id).toBe("onboarding-entity")
    expect(result.current.entities).toHaveLength(1)
    expect(result.current.user).toBeNull()
  })

  it("should fall back to entity context when onboarding context is not available", () => {
    const { result } = renderHook(() => useUserAdminLoaderData(), {
      wrapper,
    })

    expect(result.current.entityId).toBe("entity-from-main-context")
    expect(result.current.entity.id).toBe("entity-from-main-context")
    expect(result.current.entities).toEqual([])
    expect(result.current.user.id).toBe("user-1")
  })
})
