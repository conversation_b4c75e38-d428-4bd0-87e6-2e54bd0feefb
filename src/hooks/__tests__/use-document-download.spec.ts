import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"

import { useDocumentDownload } from "@/hooks/use-document-download"
import { DocumentsMetadata } from "@/data/onboarding/onboarding.interface"
import { documentsDownloadDocumentFile } from "@/client/onboarding/sdk.gen"

// Mock the SDK function
vi.mock("@/client/onboarding/sdk.gen", () => ({
  documentsDownloadDocumentFile: vi.fn(),
}))

// Mock URL static methods
// @ts-ignore
global.URL.createObjectURL = vi.fn()
// @ts-ignore
global.URL.revokeObjectURL = vi.fn()

// Mock renderHook to avoid React DOM errors
vi.mock("@testing-library/react", async () => {
  const actual = await vi.importActual("@testing-library/react")
  return {
    ...actual,
    renderHook: (callback: () => any) => {
      const result = { current: callback() }
      return { result }
    },
  }
})

describe("useDocumentDownload", () => {
  // Mocks
  let mockDocMeta: DocumentsMetadata
  let mockLocalFiles: File[]
  let mockBlob: Blob
  let mockResponse: any
  let createElementSpy: any
  let appendChildSpy: any
  let removeChildSpy: any
  let documentClickSpy: ReturnType<typeof vi.fn>
  let consoleSpy: any
  let alertSpy: any

  // Setup before each test
  beforeEach(() => {
    vi.useFakeTimers()

    // Mock document metadata
    mockDocMeta = {
      id: "doc-123",
      name: "test-document.pdf",
    }

    // Mock local files
    mockLocalFiles = [
      new File(["test content"], "test-document.pdf", {
        type: "application/pdf",
      }),
    ]

    // Mock blob response
    mockBlob = new Blob(["mock file content"], { type: "application/pdf" })

    // Mock SDK response
    mockResponse = {
      status: 200,
      statusText: "OK",
      headers: {},
      config: {} as any,
      data: mockBlob,
    }

    // Reset the mocks
    vi.mocked(documentsDownloadDocumentFile).mockReset()
    vi.mocked(documentsDownloadDocumentFile).mockResolvedValue(mockResponse)
    vi.mocked(URL.createObjectURL).mockReset()
    vi.mocked(URL.createObjectURL).mockReturnValue("mock-url")
    vi.mocked(URL.revokeObjectURL).mockReset()

    // Mock Document methods
    const mockLink = {
      href: "",
      download: "",
      click: vi.fn(),
    }
    createElementSpy = vi
      .spyOn(document, "createElement")
      .mockReturnValue(mockLink as any)
    appendChildSpy = vi
      .spyOn(document.body, "appendChild")
      .mockImplementation(() => null as any)
    removeChildSpy = vi
      .spyOn(document.body, "removeChild")
      .mockImplementation(() => null as any)
    documentClickSpy = vi.fn()

    // Mock console and alerts to avoid cluttering test output
    consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {})
    alertSpy = vi.spyOn(window, "alert").mockImplementation(() => {})
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.restoreAllMocks()
  })

  describe("handleDownload", () => {
    it("should download a document using the SDK when document has ID", async () => {
      // Get the hook
      const { result } = renderHook(() => useDocumentDownload())

      // Call the handleDownload function
      await result.current.handleDownload(mockDocMeta)

      // Check that the SDK function was called with correct params
      expect(documentsDownloadDocumentFile).toHaveBeenCalledWith({
        path: { documentId: "doc-123" },
        responseType: "blob",
      })

      // Check that URL and link operations happened
      expect(URL.createObjectURL).toHaveBeenCalledWith(mockBlob)
      expect(createElementSpy).toHaveBeenCalledWith("a")
      expect(appendChildSpy).toHaveBeenCalled()

      // Check that cleanup happened
      vi.runAllTimers()
      expect(URL.revokeObjectURL).toHaveBeenCalled()
    })

    it("should download a local file when document has no ID but matching local file exists", async () => {
      // Document with no ID
      const localDocMeta: DocumentsMetadata = {
        name: "test-document.pdf",
      }

      // Render the hook
      const { result } = renderHook(() => useDocumentDownload())

      // Call the handleDownload function with local files
      await result.current.handleDownload(localDocMeta, mockLocalFiles)

      // Check that SDK was not called (using local file instead)
      expect(documentsDownloadDocumentFile).not.toHaveBeenCalled()

      // Check that URL was created from the matching local file
      expect(URL.createObjectURL).toHaveBeenCalled()
      expect(createElementSpy).toHaveBeenCalledWith("a")
      expect(appendChildSpy).toHaveBeenCalled()

      // Check that cleanup happened
      vi.runAllTimers()
      expect(URL.revokeObjectURL).toHaveBeenCalled()
    })

    it("should show alert when document has no ID and no matching local file", async () => {
      // Document with no ID
      const localDocMeta: DocumentsMetadata = {
        name: "non-existent.pdf",
      }

      // Render the hook
      const { result } = renderHook(() => useDocumentDownload())

      // Call the handleDownload function with non-matching local files
      await result.current.handleDownload(localDocMeta, mockLocalFiles)

      // Check that SDK was not called
      expect(documentsDownloadDocumentFile).not.toHaveBeenCalled()

      // Check that alert was shown
      expect(alertSpy).toHaveBeenCalled()
      expect(alertSpy.mock.calls[0][0]).toContain("hasn't been saved yet")
    })

    it("should show alert when document has no ID and no local files provided", async () => {
      // Document with no ID
      const localDocMeta: DocumentsMetadata = {
        name: "test-document.pdf",
      }

      // Render the hook
      const { result } = renderHook(() => useDocumentDownload())

      // Call the handleDownload function without local files
      await result.current.handleDownload(localDocMeta)

      // Check that SDK was not called
      expect(documentsDownloadDocumentFile).not.toHaveBeenCalled()

      // Check that alert was shown
      expect(alertSpy).toHaveBeenCalled()
      expect(alertSpy.mock.calls[0][0]).toContain("may not be saved yet")
    })

    it("should show alert when API call fails", async () => {
      // Mock API failure
      vi.mocked(documentsDownloadDocumentFile).mockRejectedValue(
        new Error("API error"),
      )

      // Render the hook
      const { result } = renderHook(() => useDocumentDownload())

      // Call the handleDownload function
      await result.current.handleDownload(mockDocMeta)

      // Check that SDK was called
      expect(documentsDownloadDocumentFile).toHaveBeenCalled()

      // Check that alert was shown for error
      expect(alertSpy).toHaveBeenCalled()
      expect(alertSpy.mock.calls[0][0]).toContain("Failed to download document")
    })

    it("should show alert when API returns non-200 status", async () => {
      // Mock non-success response
      vi.mocked(documentsDownloadDocumentFile).mockResolvedValue({
        status: 404,
        statusText: "Not Found",
        headers: {},
        config: {} as any,
        data: undefined as unknown as Blob,
      })

      // Render the hook
      const { result } = renderHook(() => useDocumentDownload())

      // Call the handleDownload function
      await result.current.handleDownload(mockDocMeta)

      // Check that SDK was called
      expect(documentsDownloadDocumentFile).toHaveBeenCalled()

      // Check that alert was shown for bad status
      expect(alertSpy).toHaveBeenCalled()
      expect(alertSpy.mock.calls[0][0]).toContain("Failed to download document")
    })

    it("should use generic filename when document name is not available", async () => {
      // Document with ID but no name
      const noNameDocMeta = {
        id: "doc-123",
      } as DocumentsMetadata

      // Render the hook
      const { result } = renderHook(() => useDocumentDownload())

      // Call the handleDownload function
      await result.current.handleDownload(noNameDocMeta)

      // Check SDK was called
      expect(documentsDownloadDocumentFile).toHaveBeenCalled()

      // Get the link element created
      const mockLink = vi.mocked(document.createElement).mock.results[0].value

      // Check that generic filename was used
      expect(mockLink.download).toBe(`document-${noNameDocMeta.id}.pdf`)
    })
  })
})
