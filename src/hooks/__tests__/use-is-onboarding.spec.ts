import { describe, it, expect, vi, beforeEach } from "vitest"
import { renderHook } from "@testing-library/react"

import { useIsOnBoarding } from "@/hooks/use-is-onboarding"
import { useEntityAccessQry } from "@/data/onboarding/onboarding.query"

// Mock the query hook
vi.mock("@/data/onboarding/onboarding.query", () => ({
  useEntityAccessQry: vi.fn(),
}))

describe("useIsOnBoarding Hook", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should return onboarding entities when entity status is PreSubmission", () => {
    const mockEntityAccess = [
      {
        entityId: "entity1",
        entityName: "Company A",
        entityStatus: "PreSubmission",
        roles: ["Admin"],
      },
      {
        entityId: "entity2",
        entityName: "Company B",
        entityStatus: "Active",
        roles: ["User"],
      },
      {
        entityId: "entity3",
        entityName: "Company C",
        entityStatus: "Onboarding",
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: mockEntityAccess,
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(2)
    expect(result.current.onboardingEntities[0].entityId).toBe("entity1")
    expect(result.current.onboardingEntities[1].entityId).toBe("entity3")
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it("should return onboarding entities when entity status is Onboarding", () => {
    const mockEntityAccess = [
      {
        entityId: "entity1",
        entityName: "Company A",
        entityStatus: "Onboarding",
        roles: ["Admin"],
      },
      {
        entityId: "entity2",
        entityName: "Company B",
        entityStatus: "Rejected",
        roles: ["User"],
      },
    ]

    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: mockEntityAccess,
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(1)
    expect(result.current.onboardingEntities[0].entityId).toBe("entity1")
    expect(result.current.onboardingEntities[0].entityStatus).toBe("Onboarding")
  })

  it("should return empty array when no entities are in onboarding status", () => {
    const mockEntityAccess = [
      {
        entityId: "entity1",
        entityName: "Company A",
        entityStatus: "Active",
        roles: ["Admin"],
      },
      {
        entityId: "entity2",
        entityName: "Company B",
        entityStatus: "Suspended",
        roles: ["User"],
      },
      {
        entityId: "entity3",
        entityName: "Company C",
        entityStatus: "Rejected",
        roles: ["Admin"],
      },
    ]

    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: mockEntityAccess,
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(0)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it("should return empty array when entityAccess is undefined", () => {
    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(0)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it("should return empty array when entityAccess is null", () => {
    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: null,
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(0)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it("should return empty array when entityAccess is empty array", () => {
    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(0)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it("should handle loading state", () => {
    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(0)
    expect(result.current.isLoading).toBe(true)
    expect(result.current.isError).toBe(false)
  })

  it("should handle error state", () => {
    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(0)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(true)
  })

  it("should handle mixed entity statuses correctly", () => {
    const mockEntityAccess = [
      {
        entityId: "entity1",
        entityName: "PreSubmission Company",
        entityStatus: "PreSubmission",
        roles: ["Admin"],
      },
      {
        entityId: "entity2",
        entityName: "Active Company",
        entityStatus: "Active",
        roles: ["User"],
      },
      {
        entityId: "entity3",
        entityName: "Onboarding Company",
        entityStatus: "Onboarding",
        roles: ["Admin"],
      },
      {
        entityId: "entity4",
        entityName: "Rejected Company",
        entityStatus: "Rejected",
        roles: ["User"],
      },
      {
        entityId: "entity5",
        entityName: "Another PreSubmission",
        entityStatus: "PreSubmission",
        roles: ["Manager"],
      },
    ]

    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: mockEntityAccess,
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(3)

    const onboardingEntityIds = result.current.onboardingEntities.map(
      (e) => e.entityId,
    )
    expect(onboardingEntityIds).toContain("entity1")
    expect(onboardingEntityIds).toContain("entity3")
    expect(onboardingEntityIds).toContain("entity5")

    expect(onboardingEntityIds).not.toContain("entity2")
    expect(onboardingEntityIds).not.toContain("entity4")
  })

  it("should preserve entity data structure in filtered results", () => {
    const mockEntityAccess = [
      {
        entityId: "entity1",
        entityName: "Test Company",
        entityStatus: "PreSubmission",
        roles: ["Admin", "Signatory"],
        createdAt: "2024-01-01",
        updatedAt: "2024-01-15",
      },
    ]

    vi.mocked(useEntityAccessQry).mockReturnValue({
      data: mockEntityAccess,
      isLoading: false,
      isError: false,
    } as any)

    const { result } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(1)

    const entity = result.current.onboardingEntities[0]
    expect(entity.entityId).toBe("entity1")
    expect(entity.entityName).toBe("Test Company")
    expect(entity.entityStatus).toBe("PreSubmission")
    expect(entity.roles).toEqual(["Admin", "Signatory"])
  })

  it("should update when entityAccess data changes", () => {
    const initialData = [
      {
        entityId: "entity1",
        entityName: "Company A",
        entityStatus: "PreSubmission",
        roles: ["Admin"],
      },
    ]

    const updatedData = [
      {
        entityId: "entity1",
        entityName: "Company A",
        entityStatus: "Active", // Status changed
        roles: ["Admin"],
      },
      {
        entityId: "entity2",
        entityName: "Company B",
        entityStatus: "Onboarding", // New onboarding entity
        roles: ["User"],
      },
    ]

    const mockQuery = vi.mocked(useEntityAccessQry)

    // Initial render
    mockQuery.mockReturnValue({
      data: initialData,
      isLoading: false,
      isError: false,
    } as any)

    const { result, rerender } = renderHook(() => useIsOnBoarding())

    expect(result.current.onboardingEntities).toHaveLength(1)
    expect(result.current.onboardingEntities[0].entityId).toBe("entity1")

    // Update data
    mockQuery.mockReturnValue({
      data: updatedData,
      isLoading: false,
      isError: false,
    } as any)

    rerender()

    expect(result.current.onboardingEntities).toHaveLength(1)
    expect(result.current.onboardingEntities[0].entityId).toBe("entity2")
  })
})
