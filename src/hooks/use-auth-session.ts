import { useEffect, useCallback } from "react"
import { useAuth0 } from "@auth0/auth0-react"
import { authSessionManager } from "../utils/auth-session-manager"

export function useAuthSession() {
  const { isAuthenticated, logout } = useAuth0()

  const checkSessionValidity = useCallback(async () => {
    if (!isAuthenticated) return

    if (!authSessionManager.isSessionValid()) {
      console.log(
        "[Auth Session]: Session expired due to inactivity - forcing logout",
      )

      await logout({
        logoutParams: {
          returnTo: window.location.origin,
        },
      })
    }
  }, [isAuthenticated, logout])

  useEffect(() => {
    if (!isAuthenticated) {
      return
    }

    // Initialize session when authenticated
    authSessionManager.init()

    // Check session validity on visibility change (when user comes back to tab)
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        checkSessionValidity()
      }
    }

    // Check session validity on focus (when user comes back to window)
    const handleFocus = () => {
      checkSessionValidity()
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)
    window.addEventListener("focus", handleFocus)

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
      window.removeEventListener("focus", handleFocus)
    }
  }, [isAuthenticated, checkSessionValidity])

  return {
    recordAuthInteraction: authSessionManager.recordAuthInteraction,
    isSessionValid: authSessionManager.isSessionValid,
    getTimeRemaining: authSessionManager.getTimeRemaining,
    checkSessionValidity,
  }
}
