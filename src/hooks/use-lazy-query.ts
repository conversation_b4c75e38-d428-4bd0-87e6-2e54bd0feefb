import { useCallback, useMemo, useState } from "react"
import {
  FetchQueryOptions,
  QueryFunction,
  QueryKey,
  useQueryClient,
} from "@tanstack/react-query"

import { useToggle } from "./use-toggle"

export type QueryFn<
  TQueryFnData = unknown,
  TQueryKey extends QueryKey = QueryKey,
> = QueryFunction<TQueryFnData, TQueryKey>

// New type for parameterized query functions
export type ParameterizedQueryFn<
  TParams extends any[],
  TQueryFnData = unknown,
> = (...params: TParams) => Promise<TQueryFnData>

type Handlers = {
  onSuccess?: () => void
  onError?: (e: unknown) => void
  onSettled?: () => void
}

// Original hook for standard TanStack Query functions
export function useLazyQuery<
  TQueryFnData = unknown,
  TError = unknown,
  TData = TQueryFnData,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,
>(
  queryFn: QueryFn<TQueryFnData, TQueryKey>,
  options?: Omit<
    FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
    "queryKey" | "queryFn"
  >,
) {
  const queryClient = useQueryClient()

  const [data, setData] = useState<TData>()
  const [isLoading, { on: startLoading, off: stopLoading }] = useToggle()

  const trigger = useCallback(
    async (
      queryKey: TQueryKey,
      handlers?: { onError?: (e: unknown) => void },
    ) => {
      startLoading()

      return await queryClient
        .ensureQueryData({ queryKey, queryFn, ...options })
        .then((res) => {
          setData(res)
          stopLoading()

          return res
        })
        .catch((error) => {
          stopLoading()

          if (handlers?.onError) {
            handlers.onError(error)
          }

          throw error
        })
    },
    [options, queryClient, queryFn, startLoading, stopLoading],
  )

  const result = useMemo(
    () => ({
      trigger,
      isLoading,
      data,
    }),
    [data, isLoading, trigger],
  )

  return result
}

export function useLazyParameterizedQuery<
  TParams extends any[],
  TQueryFnData = unknown,
  TError = unknown,
  TData = TQueryFnData,
>(
  queryFn: ParameterizedQueryFn<TParams, TQueryFnData>,
  keyBuilder: (...params: TParams) => QueryKey,
  options?: Omit<
    FetchQueryOptions<TQueryFnData, TError, TData, QueryKey>,
    "queryKey" | "queryFn"
  >,
) {
  const queryClient = useQueryClient()
  const [data, setData] = useState<TData>()
  const [isLoading, { on: startLoading, off: stopLoading }] = useToggle()

  const trigger = useCallback(
    async (
      ...args: [
        ...TParams,
        {
          onSuccess?: () => void
          onError?: (e: unknown) => void
          onSettled?: () => void
        }?,
      ]
    ) => {
      startLoading()

      // Extract handlers from the last argument if it's an object with onError
      const lastArg = args[args.length - 1]
      const hasHandlers =
        lastArg &&
        typeof lastArg === "object" &&
        ["onError", "onSuccess", "onSettled"].some((key) => key in lastArg)

      const params = hasHandlers
        ? (args.slice(0, -1) as TParams)
        : (args as unknown as TParams)

      const handlers = hasHandlers ? (lastArg as Handlers) : undefined

      const queryKey = keyBuilder(...params)

      // Create a TanStack Query compatible function
      const tanstackQueryFn: QueryFunction<TQueryFnData, QueryKey> = () =>
        queryFn(...params)

      return await queryClient
        .ensureQueryData({
          queryKey,
          queryFn: tanstackQueryFn,
          ...options,
        })
        .then((res) => {
          setData(res)
          stopLoading()

          if (handlers?.onSuccess) {
            handlers.onSuccess()
          }

          return res
        })
        .catch((error) => {
          stopLoading()

          if (handlers?.onError) {
            handlers.onError(error)
          }

          throw error
        })
        .finally(() => {
          if (handlers?.onSettled) {
            handlers.onSettled()
          }
        })
    },
    [startLoading, keyBuilder, queryClient, options, queryFn, stopLoading],
  )

  const result = useMemo(
    () => ({
      trigger,
      isLoading,
      data,
    }),
    [data, isLoading, trigger],
  )

  return result
}
