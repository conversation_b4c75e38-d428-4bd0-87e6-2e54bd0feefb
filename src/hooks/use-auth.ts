import { useEffect, useCallback, useRef } from "react"
import { useAuth0 } from "@auth0/auth0-react"

import { authSessionManager } from "@/utils/auth-session-manager"

import { useAuthSession } from "./use-auth-session"

export function useAuth() {
  const auth0 = useAuth0()
  const { recordAuthInteraction } = useAuthSession()
  const tokenFetchedRef = useRef(false)

  const {
    isAuthenticated,
    user,
    isLoading,
    loginWithRedirect,
    logout,
    getAccessTokenSilently,
    ...rest
  } = auth0

  // Modify user object to include user_id
  const modifiedUser = user
    ? {
        ...user,
        user_id: user?.sub?.replace("auth0|", ""),
      }
    : user

  // Initial token fetch when user becomes authenticated
  useEffect(() => {
    const fetchInitialToken = async () => {
      try {
        if (!isAuthenticated || tokenFetchedRef.current) return

        console.log("[Auth]: Fetching initial access token")

        await getAccessTokenSilently()

        tokenFetchedRef.current = true

        // Record Auth0 server interaction after successful token fetch
        recordAuthInteraction()
      } catch (error) {
        console.error("Failed to fetch initial access token:", error)
        logout({ logoutParams: { returnTo: window.location.origin } })
      }
    }

    if (isAuthenticated) {
      fetchInitialToken()
    } else {
      tokenFetchedRef.current = false
    }
  }, [isAuthenticated, getAccessTokenSilently, logout, recordAuthInteraction])

  // Check session validity before any Auth0 operation
  const safeGetAccessTokenSilently = useCallback(
    async (options?: any) => {
      // Check if session is still valid before making Auth0 call
      if (!authSessionManager.isSessionValid()) {
        console.log("[Auth]: Session expired, forcing logout")

        logout({ logoutParams: { returnTo: window.location.origin } })

        throw new Error("Session expired")
      }

      try {
        const token = await getAccessTokenSilently(options)

        // Record successful Auth0 server interaction
        recordAuthInteraction()

        return token
      } catch (error) {
        console.error("[Auth]: Token fetch failed:", error)
        throw error
      }
    },
    [getAccessTokenSilently, logout, recordAuthInteraction],
  )

  const logoutUser = useCallback(
    () => logout({ logoutParams: { returnTo: window.location.origin } }),
    [logout],
  )

  const login = useCallback(() => {
    // Record Auth0 interaction when initiating login
    recordAuthInteraction()
    return loginWithRedirect()
  }, [loginWithRedirect, recordAuthInteraction])

  return {
    isAuthenticated,
    isLoading,
    user: modifiedUser,
    login,
    logout: logoutUser,
    loginWithRedirect,
    getAccessTokenSilently: safeGetAccessTokenSilently,
    recordAuthInteraction,
    ...rest,
  }
}

export type UseAuth = ReturnType<typeof useAuth>
