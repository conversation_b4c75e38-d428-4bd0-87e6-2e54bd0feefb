import { defineConfig, mergeConfig } from "vitest/config"

import viteConfig from "./vite.config.ts"

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      globals: true,
      environment: "jsdom",
      css: false,
      setupFiles: ["./vitest.setup.ts"],
      coverage: {
        provider: "v8",
        reporter: ["text", "json", "html"],
        reportsDirectory: "./coverage",
        clean: true,
        exclude: [
          "src/components/ui/**/*",
          "src/components/base/icons/**/*",
          "src/components/**/archive/**/*",
          "dist",
          "**/*.d.ts",
          "**/*.{stories,spec,gen,config,interface}.{js,jsx,ts,tsx}",
          "**/constants.ts",
          "src/tests/**/*",
          "**/*.spec.ts",
          "**/*/interface.{js,jsx,ts,tsx}",
        ],
        thresholds: {
          autoUpdate: true,
          lines: 95.24,
        },
      },
      include: ["src/**/*.{test,spec}.{js,jsx,ts,tsx}"],
    },
  }),
)
