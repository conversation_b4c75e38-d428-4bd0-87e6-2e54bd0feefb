@description('The location of the deployed application.')
param location string = 'westeurope'

@description('The platform name of the deployed application.')
param platform string = 'agp'

@description('The environment name of the deployed application.')
param environment string

@description('The URL of the repository to deploy the application from.')
param repositoryUrl string

@description('The branch to deploy the application to Production environment.')
param targetEnvDeploymentBranch string


var appServiceShortName = 'portal-fe'
var appServiceName = 'portal-frontend'
var appServiceFullName = '${appServiceShortName}-${platform}-${environment}'
var commonTags = {
  AppService: appServiceName
  Platform: platform
  Environment: environment
  Location: location
  DeployedBy: 'Bicep'
}

// Get the User-Assigned Identity to use for Container App
resource userAssignedIdentity 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-07-31-preview' existing = {
  name: 'id-${appServiceFullName}'
}

// Create a new Static Web App
resource staticWebApp 'Microsoft.Web/staticSites@2024-04-01' = {
  name: 'stapp-${appServiceFullName}'
  location: location
  sku: {
      name: 'Standard'
      tier: 'Standard'
  }
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${userAssignedIdentity.id}' : {}
    }
  }
  properties: {
    stagingEnvironmentPolicy:'Enabled'
    allowConfigFileUpdates: true
    repositoryUrl: repositoryUrl
    branch: targetEnvDeploymentBranch
    buildProperties: {
        appLocation: '/dist'
        apiLocation: ''
        outputLocation: ''
    }
  }
  tags: commonTags
}

output staticWebAppDefaultDomainUrl string = staticWebApp.properties.defaultHostname
