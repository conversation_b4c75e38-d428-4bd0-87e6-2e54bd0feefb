trigger:
  batch: "true"
  branches:
    include:
      - main

pool:
  vmImage: "ubuntu-22.04"

resources:
  repositories:
    - repository: TemplateProject
      type: git
      name: AGP-Infra/Template-Pipeline-ACA
      ref: refs/tags/v0.6.0

variables:
  - template: pipeline-templates/vars/global.yaml@TemplateProject
  - name: APP_SERVICE_NAME
    value: "portal-frontend"
  - name: APP_SERVICE_SHORT_NAME
    value: "portal-fe"
  - name: COMPONENT_NAME
    value: "portal-frontend"
  - name: COMPONENT_SHORT_NAME
    value: "portal-fe"

stages:
  - stage: Build
    displayName: "Build"
    variables:
      - group: staticwebapp-dev
    jobs:
      - template: pipeline-templates/build/react/build.yaml@TemplateProject
        parameters:
          NodeJsVersion: "22.x"
          AdditionalNpmTaskName: "copy-files"
          RunTests: "true"
          RunCodeQuality: "false"
          RunCodeSecurity: "false"

  - stage: Build_Deploy_Dev
    displayName: "Build & Deploy to Dev"
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'main'))
    variables:
      - template: pipeline-templates/vars/dev.yaml@TemplateProject
      - group: staticwebapp-dev
    jobs:
      - template: pipeline-templates/build/react/build.yaml@TemplateProject
        parameters:
          NodeJsVersion: "22.x"
          AdditionalNpmTaskName: "copy-files"
          RunTests: "false"
          RunCodeQuality: "false"
          RunCodeSecurity: "false"
      - template: pipeline-templates/deploy/static-web-app/static-web-app-task.yaml@TemplateProject
        parameters:
          targetEnv: ${{ variables['TARGET_ENV'] }}
          azureServiceConnectionName: ${{ variables['AZURE_SERVICE_CONNECTION_NAME'] }}
          targetAzureLocation: ${{ variables['AZURE_LOCATION'] }}
          targetSubscriptionId: ${{ variables['SUBSCRIPTION_ID'] }}
          targetResourceGroupName: ${{ variables['TARGET_RESOURCE_GROUP'] }}
          StaticWebAppDeploymentToken: ${{ variables['STATIC_WEB_APP_DEPLOYMENT_TOKEN'] }}

  - stage: Build_Deploy_Stg
    displayName: "Build & Deploy to Stg"
    dependsOn:
      - Build_Deploy_Dev
    condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'main'))
    variables:
      - template: pipeline-templates/vars/stg.yaml@TemplateProject
      - group: staticwebapp-stg
    jobs:
      - template: pipeline-templates/build/react/build.yaml@TemplateProject
        parameters:
          NodeJsVersion: "22.x"
          AdditionalNpmTaskName: "copy-files"
          RunTests: "false"
          RunCodeQuality: "false"
          RunCodeSecurity: "false"

      - template: pipeline-templates/deploy/static-web-app/static-web-app-task.yaml@TemplateProject
        parameters:
          targetEnv: ${{ variables['TARGET_ENV'] }}
          azureServiceConnectionName: ${{ variables['AZURE_SERVICE_CONNECTION_NAME'] }}
          targetAzureLocation: ${{ variables['AZURE_LOCATION'] }}
          targetSubscriptionId: ${{ variables['SUBSCRIPTION_ID'] }}
          targetResourceGroupName: ${{ variables['TARGET_RESOURCE_GROUP'] }}
          StaticWebAppDeploymentToken: ${{ variables['STATIC_WEB_APP_DEPLOYMENT_TOKEN'] }}

  - stage: Build_Deploy_Prd
    displayName: "Build & Deploy to Prd"
    dependsOn:
      - Build_Deploy_Stg
    condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'main'))
    variables:
      - template: pipeline-templates/vars/prd.yaml@TemplateProject
      - group: staticwebapp-prd
    jobs:
      - template: pipeline-templates/build/react/build.yaml@TemplateProject
        parameters:
          NodeJsVersion: "22.x"
          AdditionalNpmTaskName: "copy-files"
          RunTests: "false"
          RunCodeQuality: "false"
          RunCodeSecurity: "false"
      - template: pipeline-templates/deploy/static-web-app/static-web-app-task.yaml@TemplateProject
        parameters:
          targetEnv: ${{ variables['TARGET_ENV'] }}
          azureServiceConnectionName: ${{ variables['AZURE_SERVICE_CONNECTION_NAME'] }}
          targetAzureLocation: ${{ variables['AZURE_LOCATION'] }}
          targetSubscriptionId: ${{ variables['SUBSCRIPTION_ID'] }}
          targetResourceGroupName: ${{ variables['TARGET_RESOURCE_GROUP'] }}
          StaticWebAppDeploymentToken: ${{ variables['STATIC_WEB_APP_DEPLOYMENT_TOKEN'] }}
