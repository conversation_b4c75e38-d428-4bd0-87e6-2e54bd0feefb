# Introduction

The AGP Platform (React) is the new platform built completely from scratch in React (Typescript), in order to address the significant shortcomings of the current system, which is outdated and no longer meets the company's needs. Read more about the motivations behind the [AGP Platform](https://argentex.atlassian.net/wiki/x/L4B4Bw).

# 🛠️ Tech Stack

- Frontend Framework: React
- Routing: File-based routing (Tanstack Router)
- TypeScript: For type-safe code
- Testing: Vitest + Testing Library
- Styling: Tailwind CSS

# Prerequisites

Ensure you have the latest version:

1. Node `>=20`
2. Yarn
3. Prettier
4. VPN (Sonicwall Mobile Connect [MAC] / Sonicwall Net Extender [Windows]) to access API if you're working from home

# Getting Started

To kick things off, install the dependencies:

```sh
$ yarn install
```

Fire up the FE using:

```sh
$ yarn dev
```

# Build

To build the project, simply run:

```sh
$ yarn build
```

# Onboarding client

For onboarding, we use onboarding client code generated. This means anytime there is an update on onboarding api,
run this command to update the code generation

```sh
$ yarn gen:onboarding-api
```

# Test

We use Vitest as the testing framework. You can run the entire test suite:

```sh
$ yarn test
```

Or you can use `--watch` mode to test only changed files:

```sh
$ yarn test:watch
```

Now you can modify your source code or the test files, Vitest smartly searches the module graph and only reruns the related tests, just like how HMR works in Vite!

# Folder structure

> [!NOTE]
> This is not Nextjs framework

The folder structure is inspired by Nextjs:

or, less commonly, into the `doc` folder.

    .
    ├── ...
    ├── infra                       # Azure pipeline config
    ├── public                      # Static assets
    ├── src                         # Main application logic
    │   ├── assets                  # Images, Fonts, etc.
    │   ├── components              # Main section for our components
    │   │   ├── base                # Generic folder
    │   │   ├── layout              # Structure of the page
    │   │   ├── pages               # Actual design for each page
    │   │   └── ui                  # List of reusable UI components
    │   ├── data                    # Defintion/Interface of each domain/page
    │   ├── hooks                   # Reusable custom hooks
    │   ├── lib                     # Wrapper for external libs
    │   ├── routes                  # Entry point to protected pages
    │   ├── tests                   # test suite
    │   ├── main.tsx                # Entry point of the application
    │   └── ...
    └── ...

# API

The FE communicates with our intermediary/proxy service known as BFF (aka Backend-for-frontend).

The API doc can be found in Swagger [PortalHubBackend Api](https://services.dev.argentexgp.com/portal-bff/swagger/index.html).

We will soon also have a Postman collection.

# Documentation

Sometimes, we'll have complex user flows which can be hard to remember, or one-off manual tasks that we run to help with testing purposes. You can refer to our [WIKI Overview](https://argentex.atlassian.net/wiki/x/cwDNBw) page which should
help with step-by-step guide.

# Contribute

We are in early stages and always open for contributions, as long as it meets the coding standards.
