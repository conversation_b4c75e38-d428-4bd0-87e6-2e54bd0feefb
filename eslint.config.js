import tseslint from "typescript-eslint"
import globals from "globals"
import { defineConfig, globalIgnores } from "eslint/config"
import pluginReactRefresh from "eslint-plugin-react-refresh"
import pluginReactHooks from "eslint-plugin-react-hooks"
import pluginReact from "eslint-plugin-react"
import pluginPerfectionist from "eslint-plugin-perfectionist"
import config<PERSON>lint<PERSON><PERSON><PERSON> from "eslint-config-prettier"
import js from "@eslint/js"

const baseConfig = [
  js.configs.recommended,
  configEslintPrettier,
  ...tseslint.configs.recommended,
  {
    ...pluginReact.configs.flat.recommended,
    languageOptions: {
      ...pluginReact.configs.flat.recommended.languageOptions,
      globals: {
        ...globals.serviceworker,
      },
    },
  },
  {
    plugins: {
      "react-hooks": pluginReactHooks,
      "react-refresh": pluginReactRefresh,
    },
    settings: { react: { version: "detect" } },
    rules: {
      ...pluginReactHooks.configs.recommended.rules,
      // React scope no longer necessary with new JSX transform.
      "react/react-in-jsx-scope": "off",
    },
  },
]

export default defineConfig([
  ...baseConfig,
  {
    plugins: {
      perfectionist: pluginPerfectionist,
    },
    rules: {
      "import/order": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          args: "all",
          argsIgnorePattern: "^_",
          caughtErrors: "all",
          caughtErrorsIgnorePattern: "^_",
          destructuredArrayIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          ignoreRestSiblings: true,
        },
      ],
      "perfectionist/sort-jsx-props": [
        "error",
        {
          type: "alphabetical",
          order: "asc",
          fallbackSort: { type: "unsorted" },
          ignoreCase: true,
          specialCharacters: "keep",
          ignorePattern: [],
          partitionByNewLine: false,
          newlinesBetween: "ignore",
          useConfigurationIf: {},
          groups: [],
          customGroups: {},
        },
      ],
      "perfectionist/sort-imports": [
        "error",
        {
          customGroups: { type: {}, value: {} },
          environment: "node",
          groups: [
            "type",
            ["builtin", "external"],
            "internal-type",
            "internal",
            ["parent-type", "sibling-type", "index-type"],
            ["parent", "sibling", "index"],
            "object",
            "unknown",
          ],
          ignoreCase: true,
          internalPattern: ["^(~|@)/.+"],
          maxLineLength: undefined,
          newlinesBetween: "always",
          order: "desc",
          partitionByComment: false,
          partitionByNewLine: false,
          specialCharacters: "keep",
          type: "alphabetical",
        },
      ],

      semi: ["error", "never"],
    },
  },
  {
    files: ["src/components/ui/**/*.{ts,tsx}"],
    rules: {
      "react/prop-types": "off",
    },
  },
  globalIgnores([
    "dist/",
    "src/client/onboarding/",
    "src/tests/**/*",
    "!src/tests/**/*/",
    "src/hooks/**/*",
    "!src/hooks/**/*/",
    "src/components/**/*",
    "!src/components/**/*/",
    "src/data/**/*",
    "!src/data/**/*/",
    "src/routes/**/*",
    "!src/routes/**/*/",

    // base
    "!src/components/base/pagination/**/*",
    "!src/components/base/form/**/*",
    "!src/components/base/data-table/**/*",
    "!src/components/base/add-user-wizard/**/*",

    // hooks
    "!src/hooks/use-auth.ts",

    // shad/cn
    "!src/components/ui/**/*",

    // Onboarding
    "!src/components/pages/payments/**/*",
    "!src/components/pages/onboarding/**/*",
    "!src/components/pages/user-admin/**/*",
    "!src/components/pages/_components/**/*",

    "!src/data/onboarding/**/*",
    "!src/routes/_auth/**/*",

    // test
    "!src/tests/components/pages/user-admin/**/*",
  ]),
])
